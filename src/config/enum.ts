export const CloudTypes = new Map([
  [2, "阿里云"],
  [3, "华为云"],
  [4, "腾讯云"],
  [5, "亚马逊云"]
]);

export const CloudTypesColors = new Map([
  [2, "warning"],
  [3, "danger"],
  [4, "primary"],
  [5, "warning"]
]);
export const NonCloudHostTypes = new Map([
  [0, "虚拟机"],
  [1, "物理机"]
]);

export const HostTypes = new Map<number, string>([
  ...NonCloudHostTypes,
  ...CloudTypes
]);
export const HostTypeColors = CloudTypesColors;
export const WorkloadTypes = new Map([
  ["Deployment", "无状态服务"],
  ["StatefulSet", "有状态服务"],
  ["DaemonSet", "守护进程集"],
  ["Job", "一次性任务"],
  ["CronJob", "定时任务"]
]);

export const WorkloadTypeColors = new Map([
  ["Deployment", "primary"],
  ["StatefulSet", "warning"],
  ["DaemonSet", "success"],
  ["Job", "info"],
  ["CronJob", "danger"]
]);

export const TaskStatus = new Map([
  [0, "初始化"],
  [1, "任务就绪"],
  [2, "执行中"],
  [3, "执行成功"],
  [4, "执行失败"]
]);

export const TaskStatusColors = new Map([
  [0, "info"],
  [1, "warning"],
  [2, "primary"],
  [3, "success"],
  [4, "danger"]
]);

export const TaskTypes = new Map([
  ["ansible", "Ansible任务"],
  ["shell", "shell脚本"]
]);

export const HostStatus = new Map([
  [1, "运行中"],
  [2, "已停止"],
  [3, "创建中"],
  [4, "删除中"],
  [5, "错误"],
  [6, "已回收"]
]);
export const HostPingStatus = new Map([
  [true, "监控"],
  [false, "不监控"]
]);

export const HostStatusColors = new Map([
  [0, "info"],
  [1, "success"],
  [2, "danger"],
  [3, "warning"],
  [4, "warning"],
  [5, "warning"],
  [6, "primary"]
]);

export const ChargeTypes = new Map([
  [0, ""],
  [1, "包年包月"],
  [2, "按量计费"]
]);

export const ChargeTypeColors = new Map([
  [1, "primary"],
  [2, "warning"]
]);

export const ContactTypes = new Map([
  ["email", "邮件"],
  ["meiyou_notice", "美柚工作通知"],
  ["dingtalk_robot", "钉钉机器人"]
]);

export const ContactTypeColors = new Map([
  ["email", "warning"],
  ["meiyou_notice", "danger"],
  ["dingtalk_robot", "primary"]
]);

export const MessageStatus = new Map([
  [0, "未发送"],
  [1, "发送中"],
  [2, "发送成功"],
  [3, "发送失败"]
]);

export const MessageStatuColors = new Map([
  [0, "info"],
  [1, "primary"],
  [2, "success"],
  [3, "danger"]
]);

export const ENVs = new Map([
  [0, "开发环境"],
  [1, "测试环境"],
  [2, "预发环境"],
  [3, "生产环境"]
]);

export const ENVColors = new Map([
  [0, "info"],
  [1, "success"],
  [2, "warning"],
  [3, "danger"]
]);

export const PrometheusInstanceTypes = new Map([
  ["kubernetes", "Kubernetes"],
  ["fping", "fping"],
  ["tidb", "Tidb"],
  ["n9e", "n9e"],
  ["other", "其他"]
]);

export const RoleTypes = new Map([
  ["qa", "测试"],
  ["developer", "开发"],
  ["op", "运维"],
  ["pm", "项目经理"],
  ["workflow_admin", "工单管理员"]
]);

export const MySQLInstanceTypes = new Map([
  ["mysql", "MySQL"],
  ["tidb", "Tidb"]
]);

export const CloudPlatforms = ["阿里云", "华为云", "腾讯云", "其他"];
