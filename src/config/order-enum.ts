export const OrderTypes = new Map([
  ["server", "服务器申请工单"],
  ["permission", "权限申请工单"],
  ["permission_dgc", "DGC权限申请工单"],
  ["permission_aliyun", "阿里云权限申请工单"],
  ["domain", "域名申请工单"],
  ["mysql", "数据库工单"],
  ["service", "业务需求工单"],
  ["mongodb", "MongoDB工单"],
  ["gitlab", "Gitlab权限工单"],
  ["cloud_account", "云账号工单"],
  ["desktop", "桌面技术支持工单"]
]);

export enum OrderExtType {
  ServerOrderType = 1,
  PermissionOrderType = 2,
  DomainOrderType = 3,
  DatabaseOrderType = 4,
  ServiceNeedOrderType = 5,
  MongoDBOrderType = 6,
  GitlabOrderType = 7,
  CloudAccountOrderType = 8,
  DesktopOPSOrderType = 9
}

export const OrderExtTypes = new Map([
  [1, "服务器申请工单"],
  [2, "权限申请工单"],
  [3, "域名申请工单"],
  [4, "数据库工单"],
  [5, "业务需求工单"],
  [6, "MongoDB工单"],
  [7, "Gitlab权限工单"],
  [8, "云账号权限申请工单"],
  [9, "桌面技术支持工单"]
]);

export const OrderType = {
  ServerOrderType: "server",
  PermissionOrderType: "permission",
  PermissionOrderDGCType: "permission_dgc",
  DomainOrderType: "domain",
  MySQLSensitiveTemplate: "database_sensitive",
  MySQLOrderType: "database",
  ServiceNeedOrderType: "service_need",
  MongoDBOrderType: "mongodb",
  GitlabOrderType: "gitlab",
  CloudAccountOrderType: "cloud_account",
  DesktopOPSOrderType: "desktop_ops"
};

export const ENVs = new Map([
  [1, "测试环境"],
  [2, "预发环境"],
  [3, "生产环境"]
]);

export enum ENV {
  DevEnv = 0,
  TestEnv,
  PreEnv,
  ProdEnv
}

export const ENVColors = new Map([
  [1, "warning"],
  [2, "primary"],
  [3, "danger"],
  [4, "info"]
]);

export const OrderStatuses = new Map([
  [1, "审批中"],
  [2, "已完成"],
  [3, "驳回"],
  [4, "撤销"]
]);

export const OrderStatusColors = new Map([
  [1, "success"],
  [2, "primary"],
  [3, "danger"],
  [4, "info"]
]);

export enum OrderStatus {
  Approving = 1, // 审批中
  Completed, // 已完成
  Rejected, // 已驳回
  Canceled // 已取消
}

export const PermissionOrderTypes = [
  "DGC(数据仓库)",
  "系统权限申请",
  "k8s权限申请",
  "阿里云子账号",
  "华为云子账号",
  "应用发布权限",
  "Wiki权限",
  "OSS权限",
  "CDN权限",
  "其他权限"
];

export const MySQLOrderTypes = [
  "数据库SQL执行",
  "数据库应用账号开通",
  "新建数据库",
  "个人账号开通"
];

export const ServiceneedOrderTypes = [
  "业务部署",
  "业务问题排查",
  "业务配置变更",
  "业务下线",
  "其他"
];

export const GitlabPermissions = new Map([
  [10, "Guest"],
  [20, "Reporter"],
  [30, "Developer"],
  [40, "Maintainer"]
]);
