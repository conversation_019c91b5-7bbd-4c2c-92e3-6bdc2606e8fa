export enum AssetType {
  // 系统
  System = 1,
  // 域名
  Domain,
  // 负载均衡/高防
  Loadbalancer,
  // 主机
  Host,
  // APP
  App,
  // k8s workload
  K8SApp,
  // MySQL
  MySQL,
  // Mongodb
  MongoDB,
  // redis
  Redis,
  // 消息队列服务：kafka、rocketMQ等
  MQ,
  // 公网IP
  PublicIP,
  // k8s pod
  K8SPod,
  // 内网域名
  PrivateDomainAssetType,
  //  公网域名
  PublicDomainAssetType,
  // 高防实例
  CloudDDosProtectionAssetType,
  // 高防域名
  CloudDDosDomainAssetType,
  // k8s service
  K8SService,
  // proxysql
  ProxySQL,
  // codis-proxy
  CodisProxy,
  // redis-server、pika、codis-server
  RedisServer

}

export const AssetTypeMap = {
  [AssetType.System]: "系统",
  [AssetType.Domain]: "域名",
  [AssetType.Loadbalancer]: "负载均衡",
  [AssetType.Host]: "主机",
  [AssetType.App]: "应用",
  [AssetType.K8SApp]: "K8S应用",
  [AssetType.MySQL]: "MySQL",
  [AssetType.MongoDB]: "MongoDB",
  [AssetType.Redis]: "Redis",
  [AssetType.MQ]: "消息队列",
  [AssetType.PublicIP]: "公网IP",
  [AssetType.K8SPod]: "K8S Pod",
  [AssetType.PrivateDomainAssetType]: "内网域名",
  [AssetType.PublicDomainAssetType]: "公网域名",
  [AssetType.CloudDDosProtectionAssetType]: "高防实例",
  [AssetType.CloudDDosDomainAssetType]: "高防域名",
  [AssetType.K8SService]: "K8S服务",
  [AssetType.ProxySQL]: "ProxySQL",
  [AssetType.CodisProxy]: "Codis Proxy"
};

export const AssetTypeColors = {
  [AssetType.System]: "primary",
  [AssetType.Domain]: "success",
  [AssetType.Loadbalancer]: "warning",
  [AssetType.Host]: "primary",
  [AssetType.App]: "danger",
  [AssetType.K8SApp]: "primary",
  [AssetType.MySQL]: "success",
  [AssetType.MongoDB]: "warning",
  [AssetType.Redis]: "danger",
  [AssetType.MQ]: "info",
  [AssetType.PublicIP]: "primary",
  [AssetType.K8SPod]: "success",
  [AssetType.PrivateDomainAssetType]: "warning",
  [AssetType.PublicDomainAssetType]: "danger",
  [AssetType.CloudDDosProtectionAssetType]: "primary",
  [AssetType.CloudDDosDomainAssetType]: "success",
  [AssetType.K8SService]: "info",
  [AssetType.ProxySQL]: "warning",
  [AssetType.CodisProxy]: "danger"
};

export const assetTypeLinks = {
  [AssetType.Loadbalancer]: "/asset/loadbalancer?host=",
  [AssetType.Host]: "/asset/host?ip=",
  [AssetType.PublicIP]: "/asset/public-ip?ip=",
  [AssetType.K8SPod]: "/k8s/pod?ip=",
  [AssetType.PrivateDomainAssetType]: "/asset/private-domain?ip=",
  [AssetType.PublicDomainAssetType]: "/asset/public-domain?ip=",
  [AssetType.CloudDDosProtectionAssetType]: "/asset/ddos/instance?keyword=",
  [AssetType.CloudDDosDomainAssetType]: "/asset/ddos/domain?keyword=",
  [AssetType.K8SService]: "/k8s/service?keyword=",
};
