<template>
  <div class="full-search">
    <el-button type="primary" @click="openDialog">
      <iconify-icon-online icon="ri:search-2-line" />
    </el-button>

    <el-dialog
      v-model="dialogVisible"
      title="IP全局搜索"
      width="70%"
      :modal-append-to-body="false"
      destroy-on-close
      class="search-dialog"
      :close-on-click-modal="true"
      :show-close="false"
    >
      <template #header="{ titleId, titleClass }">
        <div class="dialog-header">
          <h4 :id="titleId" :class="titleClass">
            <iconify-icon-online
              icon="ri:search-2-line"
              class="search-title-icon"
            />
            IP全局搜索
          </h4>
        </div>
      </template>
      <div class="search-container">
        <div class="search-input-wrapper">
          <el-input
            v-model="searchQuery"
            placeholder="输入IP、主机名或其他关键词进行搜索..."
            clearable
            class="search-input"
            size="large"
            @keyup.enter="performSearch"
            @input="debouncedCheckAndSearch"
            @blur="trimSpaces"
          >
            <template #prefix>
              <iconify-icon-online icon="ri:search-line" class="search-icon" />
            </template>
          </el-input>
        </div>
        <el-button
          type="primary"
          class="search-button"
          :loading="searching"
          size="large"
          @click="performSearch"
        >
          搜索
        </el-button>
      </div>
      <div class="search-options">
        <el-checkbox-group v-model="selectedTypes">
          <el-checkbox label="host" size="large">
            <span class="checkbox-label">
              <iconify-icon-online icon="ri:computer-line" />
              主机
            </span>
          </el-checkbox>
          <el-checkbox label="loadbalancer" size="large">
            <span class="checkbox-label">
              <iconify-icon-online icon="ri:cloud-line" />
              负载均衡
            </span>
          </el-checkbox>
          <el-checkbox label="eip" size="large">
            <span class="checkbox-label">
              <iconify-icon-online icon="ri:global-line" />
              公网IP
            </span>
          </el-checkbox>
          <el-checkbox label="pod" size="large">
            <span class="checkbox-label">
              <iconify-icon-online icon="ri:server-line" />
              容器
            </span>
          </el-checkbox>
          <el-checkbox label="svc" size="large">
            <span class="checkbox-label">
              <iconify-icon-online icon="ri:server-line" />
              服务
            </span>
          </el-checkbox>

          <el-checkbox label="ddos" size="large">
            <span class="checkbox-label">
              <iconify-icon-online icon="ri:earth-line" />
              高防
            </span>
          </el-checkbox>
          <el-checkbox label="private_domain" size="large">
            <span class="checkbox-label">
              <iconify-icon-online icon="ri:link" />
              内网域名
            </span>
          </el-checkbox>
          <el-checkbox label="public_domain" size="large">
            <span class="checkbox-label">
              <iconify-icon-online icon="ri:earth-line" />
              外网域名
            </span>
          </el-checkbox>
        </el-checkbox-group>
      </div>
      <el-table
        v-if="searchResults.length > 0"
        :data="searchResults"
        style="margin-top: 20px"
        size="small"
        border
        highlight-current-row
        class="custom-table search-result-table"
      >
        <el-table-column prop="ip" label="IP" width="120">
          <template #default="{ row }">
            <router-link
              v-if="getLink(row.asset_type) !== ''"
              :to="getLink(row.asset_type)"
              class="link"
              @click.stop="closeDialog"
            >
              {{ row.ip }}
            </router-link>
            <span v-else>{{ row.ip }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="名称">
          <template #default="{ row }">
            <router-link
              v-if="getLink(row.asset_type) !== ''"
              :to="getLink(row.asset_type)"
              class="link"
              @click.stop="closeDialog"
            >
              {{ row.name }}
            </router-link>
            <span v-else>{{ row.name }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="account_name" label="账户名" />
        <el-table-column prop="ext_info" label="扩展信息" />
        <el-table-column prop="asset_type_name" label="资产类型">
          <template #default="{ row }">
            <el-tag
              :type="AssetTypeColors[row.asset_type] || 'info'"
              size="large"
              class="mr-2 font-bold"
              effect="dark"
            >
              {{ AssetTypeMap[row.asset_type] }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="asset_cloud_url" label="云资产链接">
          <template #default="{ row }">
            <a
              v-if="row.asset_cloud_url"
              :href="row.asset_cloud_url"
              target="_blank"
            >
              <iconify-icon-online
                icon="ri:link"
                style="margin-right: 5px; font-size: 16px; font-weight: bold"
              />
            </a>
            <span v-else>-</span>
          </template>
        </el-table-column>
      </el-table>

      <div
        v-else-if="searchPerformed && searchResults.length === 0"
        class="search-result-empty"
      >
        <iconify-icon-online
          icon="ri:search-line"
          class="text-4xl text-gray-400 mb-4"
        />
        <p class="text-lg">没有找到匹配的资产</p>
        <p class="text-sm text-gray-500">请尝试其他搜索关键词</p>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button
            type="default"
            size="large"
            class="cancel-button"
            @click="dialogVisible = false"
          >
            关闭
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { getSearchDataAPI, type AssetInfo } from "@/api/search";
import {
  assetTypeLinks,
  AssetTypeColors,
  AssetTypeMap
} from "@/config/asset-type";
import { ElMessage } from "element-plus";

const searchQuery = ref("");
const searchResults = ref<AssetInfo[]>([]);
const searchPerformed = ref(false);
const dialogVisible = ref(false);
const searching = ref(false);
const selectedTypes = ref([
  "host",
  "loadbalancer",
  "eip",
  "pod",
  "svc",
  "ddos"
]); // 默认选中主机、负载均衡和公网IP、高防
let debounceTimeout: NodeJS.Timeout | null = null;

function openDialog() {
  searchQuery.value = "";
  searchResults.value = [];
  searchPerformed.value = false;
  dialogVisible.value = true;
}

function closeDialog() {
  dialogVisible.value = false;
}

async function performSearch() {
  if (!searchQuery.value.trim()) {
    ElMessage.warning("请输入搜索关键词");
    return;
  }

  if (selectedTypes.value.length === 0) {
    ElMessage.warning("请至少选择一个资产类型");
    return;
  }

  searching.value = true;
  try {
    const response = await getSearchDataAPI(
      searchQuery.value.trim(),
      selectedTypes.value
    );
    if (response.success && response.data) {
      searchResults.value = response.data;
    } else {
      searchResults.value = [];
    }
    searchPerformed.value = true;
  } catch (error) {
    console.error("搜索失败:", error);
    ElMessage.error("搜索失败，请重试");
  } finally {
    searching.value = false;
  }
}

function debouncedCheckAndSearch() {
  // 如果搜索查询为空，清空搜索结果
  if (!searchQuery.value) {
    searchResults.value = [];
    searchPerformed.value = false;
    return;
  }

  // 取消之前的防抖定时器
  if (debounceTimeout) {
    clearTimeout(debounceTimeout);
  }

  // 设置新的防抖定时器
  debounceTimeout = setTimeout(() => {
    performSearch();
  }, 500);
}

function trimSpaces() {
  searchQuery.value = searchQuery.value.trim();
}

function getLink(asset_type: number): string {
  const link = assetTypeLinks[asset_type];
  if (link === "") {
    return "";
  }
  return link + searchQuery.value;
}
</script>

<style lang="scss" scoped>
.full-search {
  padding: 20px;
}

.search-dialog {
  :deep(.el-dialog) {
    overflow: hidden;
    border-radius: 12px;
    box-shadow:
      0 12px 32px 4px rgb(0 0 0 / 4%),
      0 8px 20px rgb(0 0 0 / 8%);

    .el-dialog__header {
      padding: 0;
      margin: 0;
    }

    .el-dialog__body {
      padding: 24px;
    }

    .el-dialog__footer {
      padding: 0;
      margin: 0;
    }
  }
}

.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  background: linear-gradient(to right, var(--el-color-primary-light-9), white);
  border-bottom: 1px solid var(--el-border-color-lighter);

  h4 {
    display: flex;
    gap: 8px;
    align-items: center;
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--el-text-color-primary);

    .search-title-icon {
      font-size: 20px;
      color: var(--el-color-primary);
    }
  }
}

.dialog-footer {
  padding: 20px 24px;
  text-align: right;
  background-color: var(--el-fill-color-light);
  border-top: 1px solid var(--el-border-color-lighter);

  .cancel-button {
    min-width: 100px;
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
      transform: translateY(-1px);
    }
  }
}

.search-container {
  display: flex;
  gap: 12px;
  align-items: center;
  margin-bottom: 16px;

  .search-input-wrapper {
    flex: 1;
    min-width: 0;

    .search-input {
      :deep(.el-input__wrapper) {
        box-shadow: 0 0 0 1px var(--el-border-color) inset;
        transition: all 0.3s ease;

        &:hover {
          box-shadow: 0 0 0 1px var(--el-color-primary-light-3) inset;
        }

        &.is-focus {
          box-shadow: 0 0 0 1px var(--el-color-primary) inset;
        }
      }
    }

    .search-icon {
      font-size: 18px;
      color: var(--el-text-color-secondary);
    }
  }

  .search-button {
    flex-shrink: 0;
    min-width: 90px;
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
      transform: translateY(-1px);
    }
  }
}

.search-options {
  padding: 16px;
  margin: 20px 0;
  background-color: var(--el-fill-color-light);
  border-radius: 8px;

  .el-checkbox-group {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;

    .el-checkbox {
      margin-right: 0;

      .checkbox-label {
        display: flex;
        gap: 6px;
        align-items: center;
        color: var(--el-text-color-regular);

        :deep(.iconify-icon) {
          font-size: 16px;
        }
      }
    }
  }
}

.search-result-table {
  margin-top: 20px;
  overflow: hidden;
  font-size: 14px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgb(0 0 0 / 4%);

  :deep(.el-table__header) {
    font-size: 14px;
    font-weight: 600;
    background-color: var(--el-fill-color-light);

    th {
      height: 50px;
      color: var(--el-text-color-primary);
      background-color: var(--el-fill-color-light);
    }
  }

  :deep(.el-table__body) {
    font-size: 14px;

    td {
      padding: 12px 0;
    }
  }

  :deep(.el-table__row) {
    height: 50px;
    transition: all 0.3s ease;

    &:hover {
      background-color: var(--el-fill-color-lighter);
    }
  }

  .link {
    font-size: 14px;
    color: var(--el-color-primary);
    text-decoration: none;
    transition: all 0.3s ease;

    &:hover {
      color: var(--el-color-primary-light-3);
      text-decoration: none;
    }
  }

  :deep(.el-tag) {
    height: 28px;
    padding: 0 12px;
    font-size: 13px;
    font-weight: 500;
    line-height: 28px;
    border-radius: 4px;
  }
}

.search-result-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
  color: var(--el-text-color-secondary);

  .text-4xl {
    margin-bottom: 1.5rem;
    font-size: 5rem;
    color: var(--el-text-color-placeholder);
  }

  .text-lg {
    margin: 8px 0;
    font-size: 1.125rem;
    font-weight: 500;
  }

  .text-sm {
    font-size: 0.875rem;
  }

  .text-gray-500 {
    color: var(--el-text-color-secondary);
  }
}
</style>
