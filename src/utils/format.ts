/**
 * 格式化存储显示（MB转GB或TB）
 * @param storage 存储大小（MB）
 * @returns 格式化后的存储大小（GB或TB）
 */
export function formatStorage(bytes: number): string {
  const units = ["B", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];
  if (bytes === 0) return "0.00 B";

  let unitIndex = 0;
  // 循环除以 1024，直到数值小于 1024 或达到最大单位（YB）
  while (bytes >= 1024 && unitIndex < units.length - 1) {
    bytes /= 1024;
    unitIndex++;
  }

  // 保留两位小数，自动补零（如 1 → "1.00"）
  return `${bytes.toFixed(2)} ${units[unitIndex]}`;
}

/**
 * 格式化带宽显示
 * @param bps 带宽大小（bps）
 * @returns 格式化后的带宽大小（bps、Kbps、Mbps、Gbps）
 */
export function formatBandwidth(bps: number): string {
  if (!bps) return "0 bps";

  const units = ["bps", "Kbps", "Mbps", "Gbps"];
  let value = bps;
  let unitIndex = 0;

  while (value >= 1000 && unitIndex < units.length - 1) {
    value /= 1000;
    unitIndex++;
  }

  return `${value.toFixed(2)} ${units[unitIndex]}`;
}
/**
 * 方法一：正则表达式实现（推荐）
 * 支持整数、小数、负数
 * @param num 需要格式化的数字或数字字符串
 * @returns 千分位格式化后的字符串
 */
export const formatThousands = (num: number | string): string => {
  // 转换为字符串并清理非数字字符（保留负号和小数点）
  let str = num.toString().replace(/[^0-9\-.]/g, "");

  // 处理负号
  const isNegative = str.startsWith("-");
  if (isNegative) str = str.slice(1);

  // 分割整数和小数部分
  const [integerPart, decimalPart] = str.split(".");

  // 处理空值情况
  if (!integerPart) return "0";

  // 千分位格式化整数部分
  const formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ",");

  // 合并结果
  return `${isNegative ? "-" : ""}${formattedInteger}${decimalPart ? `.${decimalPart}` : ""}`;
};
