export const parmaQueryNumber = (val: string | string[]) => {
  if (typeof val === "string") {
    const t = Number(val);
    if (!Number.isNaN(t)) {
      return t;
    }
  } else if (Array.isArray(val)) {
    const t = Number(val[0]);
    if (!Number.isNaN(t)) {
      return t;
    }
  }
  return undefined;
};

export const parmaQueryString = (val: string | string[]) => {
  if (typeof val === "string") {
    return val;
  } else if (Array.isArray(val)) {
    return val.length > 0 ? val[0] : "";
  }
  return undefined;
};
