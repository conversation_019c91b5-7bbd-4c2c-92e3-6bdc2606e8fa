<template>
  <div class="main">
    <el-card class="search-card">
      <template #header>
        <div class="card-header">
          <span>个人设置</span>
        </div>
      </template>

      <el-form
        ref="formRef"
        :model="userForm"
        :rules="rules"
        label-width="100px"
        class="settings-form"
      >
        <el-form-item label="用户名" prop="username">
          <el-input v-model="userForm.username" disabled />
        </el-form-item>

        <el-form-item label="姓名" prop="name">
          <el-input v-model="userForm.name" disabled />
        </el-form-item>

        <el-form-item label="邮箱" prop="email">
          <el-input v-model="userForm.email" disabled />
        </el-form-item>

        <el-form-item label="手机号" prop="phone">
          <el-input v-model="userForm.phone" />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" class="save-button" @click="handleSubmit">
            <span>保存修改</span>
          </el-button>
        </el-form-item>
      </el-form>
      <el-form-item label="修改密码">
        <el-button
          type="primary"
          class="change-password-button"
          @click="showChangePassword = true"
        >
          修改密码
        </el-button>
      </el-form-item>
    </el-card>

    <!-- 修改密码对话框 -->
    <el-dialog v-model="showChangePassword" title="修改密码" width="500px">
      <el-form
        ref="passwordFormRef"
        :model="passwordForm"
        :rules="passwordRules"
        label-width="100px"
      >
        <el-form-item label="原密码" prop="oldPassword">
          <el-input
            v-model="passwordForm.oldPassword"
            type="password"
            show-password
          />
        </el-form-item>

        <el-form-item label="新密码" prop="newPassword">
          <el-input
            v-model="passwordForm.newPassword"
            type="password"
            show-password
          />
        </el-form-item>

        <el-form-item label="确认新密码" prop="confirmPassword">
          <el-input
            v-model="passwordForm.confirmPassword"
            type="password"
            show-password
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showChangePassword = false">取消</el-button>
          <el-button
            type="primary"
            class="confirm-button"
            @click="handleChangePassword"
          >
            确认修改
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { ElMessage } from "element-plus";
import type { FormInstance, FormRules } from "element-plus";
import {
  getPersonalInfo,
  UpdatePersonalPasswordAPI,
  UpdatePersonalPhoneAPI,
  type User
} from "@/api/auth/user";

const formRef = ref<FormInstance>();
const passwordFormRef = ref<FormInstance>();
const showChangePassword = ref(false);

// 表单数据
const userForm = reactive({
  username: "",
  name: "",
  email: "",
  phone: ""
});

// 密码表单数据
const passwordForm = reactive({
  oldPassword: "",
  newPassword: "",
  confirmPassword: ""
});

// 表单验证规则
const rules = reactive<FormRules>({
  username: [
    { required: true, message: "请输入用户名", trigger: "blur" },
    { min: 2, max: 20, message: "长度在 2 到 20 个字符", trigger: "blur" }
  ],
  phone: [
    { required: true, message: "请输入手机号", trigger: "blur" },
    { pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号", trigger: "blur" }
  ]
});

// 密码验证规则
const passwordRules = reactive<FormRules>({
  oldPassword: [{ required: true, message: "请输入原密码", trigger: "blur" }],
  newPassword: [
    { required: true, message: "请输入新密码", trigger: "blur" },
    { min: 6, message: "密码长度不能小于6位", trigger: "blur" }
  ],
  confirmPassword: [
    { required: true, message: "请再次输入新密码", trigger: "blur" },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error("两次输入的密码不一致"));
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ]
});

// 初始化表单数据
const initUserForm = () => {
  getPersonalInfo().then(res => {
    if (res.success) {
      const personal = res.data as User;
      userForm.username = personal.username;
      userForm.name = personal.name;
      userForm.phone = personal.phone;
      userForm.email = personal.email;
    }
  });
};

// 组件挂载时初始化数据
onMounted(() => {
  initUserForm();
});

// 提交个人信息
const handleSubmit = async () => {
  if (!formRef.value) return;

  await formRef.value.validate(valid => {
    if (valid) {
      UpdatePersonalPhoneAPI({ phone: userForm.phone })
        .then(res => {
          if (res.success) {
            ElMessage.success("保存成功");
          } else {
            ElMessage.error(res.msg || "保存失败，请重试");
          }
        })
        .catch(() => {
          ElMessage.error("保存失败，请重试");
        });
    }
  });
};

// 修改密码
const handleChangePassword = async () => {
  if (!passwordFormRef.value) return;

  await passwordFormRef.value.validate(valid => {
    if (valid) {
      UpdatePersonalPasswordAPI({
        old_password: passwordForm.oldPassword,
        password: passwordForm.newPassword
      })
        .then(res => {
          if (res.success) {
            ElMessage.success("密码修改成功");
            showChangePassword.value = false;
            // 重置表单
            passwordForm.oldPassword = "";
            passwordForm.newPassword = "";
            passwordForm.confirmPassword = "";
          } else {
            ElMessage.error(res.msg || "密码修改失败，请重试");
          }
        })
        .catch(() => {
          ElMessage.error("密码修改失败，请重试");
        });
    }
  });
};
</script>

<style scoped lang="scss">
.main {
  padding: 20px;
  border-radius: 12px;
}

.search-card {
  margin-bottom: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 8%);
  transition: box-shadow 0.3s ease;

  &:hover {
    box-shadow: 0 6px 20px rgb(0 0 0 / 12%);
  }
}

.settings-form {
  display: block;
  gap: 16px;
  align-items: stretch;

  :deep(.el-form-item) {
    margin-bottom: 16px;
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: #606266;
  }

  .el-input {
    width: 100%;

    :deep(input) {
      border-color: #e4e7ed;
      border-radius: 8px;

      &:focus {
        border-color: #409eff;
        box-shadow: 0 0 0 1px rgb(64 158 255 / 20%);
      }
    }
  }
}

.save-button,
.change-password-button,
.confirm-button {
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
    transform: translateY(-2px);
  }
}

.save-button {
  color: white;
  background-color: #409eff;
  border-color: #409eff;
}

:deep(.el-dialog) {
  overflow: hidden;
  border: 1px solid #e4e7ed;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 4%);
}

:deep(.dialog-footer) {
  padding: 10px 0;
}
</style>
