<template>
  <el-card shadow="never">
    <el-form
      ref="ruleFormRef"
      :model="newFormInline.form"
      label-width="auto"
      style="max-width: 600px; margin: auto"
      label-position="top"
      status-icon
      size="large"
    >
      <el-form-item
        v-for="item in readOnlyFields"
        :key="item.field"
        :label="item.label"
      >
        <el-input v-model="newFormInline.row[item.field]" disabled />
      </el-form-item>

      <el-form-item label="手动设置上级领导" prop="manual_leader_id">
        <el-select
          v-model="newFormInline.form.manual_leader_id"
          filterable
          allow-create
          default-first-option
          placeholder="请选择用户"
        >
          <el-option :value="0" label="OA自动获取" />

          <el-option
            v-for="(item, key) in allUsers"
            :key="key"
            :label="item.name + '(' + item.username + ')'"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        label="手机"
        prop="phone"
        :rules="[{ max: 255, message: '请输入最大255个字符', trigger: 'blur' }]"
      >
        <el-input
          v-model="newFormInline.form.phone"
          placeholder="请输入手机号码"
          show-word-limit
          maxlength="255"
          clearable
          style="border-radius: 4px"
        />
      </el-form-item>

      <el-form-item label="身份">
        <el-switch
          v-model="newFormInline.form.is_admin"
          :style="{
            '--el-switch-off-color': '#409eff',
            '--el-switch-on-color': '#ff4949'
          }"
          inline-prompt
          active-text="管理员"
          inactive-text="普通用户"
        />
      </el-form-item>

      <el-form-item label="角色">
        <el-select
          v-model="newFormInline.form.roles"
          multiple
          filterable
          allow-create
          default-first-option
          placeholder="请选择角色"
        >
          <el-option
            v-for="(item, key) in RoleTypes"
            :key="key"
            :label="item[1]"
            :value="item[0]"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="状态">
        <el-switch
          v-model="newFormInline.form.is_disabled"
          inline-prompt
          :style="{
            '--el-switch-off-color': '#409eff',
            '--el-switch-on-color': '#ff4949'
          }"
          active-text="禁用"
          inactive-text="启用"
        />
      </el-form-item>
    </el-form>
  </el-card>
</template>

<script setup lang="ts">
import { defineProps, onBeforeMount, ref } from "vue";
import type { User, UpdateUserForm } from "@/api/auth/user";
import type { FormInstance } from "element-plus";
import { RoleTypes } from "@/config/enum";
import { getAllUsersAPI } from "@/api/auth/user";
import { message } from "@/utils/message";

export interface FormProps {
  formInline: {
    row: User;
    form: UpdateUserForm;
  };
}

const readOnlyFields = [
  { label: "用户", field: "name" },
  { label: "用户名", field: "username" },
  { label: "工号", field: "sn" },
  { label: "邮箱", field: "email" }
];

const props = withDefaults(defineProps<FormProps>(), {
  formInline: () => ({
    row: undefined,
    form: undefined
  })
});

const newFormInline = ref(props.formInline);
const ruleFormRef = ref<FormInstance>();

defineExpose({ ruleFormRef });

const allUsers = ref<User[]>([]);

function getAllUser() {
  getAllUsersAPI()
    .then(res => {
      if (res.success) {
        allUsers.value = res.data as User[];
      } else {
        message(res.msg, { type: "error" });
      }
    })
    .catch(error => {
      message(error, { type: "error" });
    });
}
onBeforeMount(async () => {
  getAllUser();
});
</script>

<style scoped>
.el-form-item {
  margin-bottom: 20px; /* 增加间距 */
}

.el-input {
  border-radius: 4px; /* 圆角 */
}

.el-switch {
  margin-top: 10px; /* 增加开关的上边距 */
}
</style>
