import dayjs from "dayjs";
import type { PaginationProps } from "@pureadmin/table";
import { reactive, ref, onMounted, h } from "vue";
import {
  type BatchGroupForm,
  type UpdateUserForm,
  type User,
  batGroupUsersAPI,
  getUsersList,
  updateUser
} from "@/api/auth/user";
import { addDrawer } from "@/components/ReDrawer/index";
import Uform from "./Uform.vue";
import GroupForm from "./GroupForm.vue";
import { message } from "@/utils/message";
import { getAllGroupsAPI, type Group } from "@/api/auth/group";
import { RoleTypes } from "@/config/enum";

export function useRole() {
  const form = reactive({
    keyword: undefined,
    is_admin: undefined,
    is_disabled: undefined,
    group_id: undefined
  });
  const dataList = ref<User[]>([]);
  const loading = ref(true);
  const groups = ref<Group[]>([]);
  function getAllGroups() {
    getAllGroupsAPI()
      .then(res => {
        if (res.success) {
          groups.value = res.data;
        } else {
          message(res.msg, { type: "error" });
        }
      })
      .catch(error => {
        message(error.msg, { type: "error" });
      });
  }
  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true,
    pageSizes: [10, 20, 30, 40, 50, 100]
  });
  const columns: TableColumnList = [
    {
      type: "selection",
      align: "left"
    },
    {
      label: "姓名(工号)/用户名",
      prop: "username",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <div style="white-space: pre-line;">
          <p style="margin: 3px; font-weight: bold;">
            <el-text type="info">
              {row.name}({row.sn})
            </el-text>
          </p>
          <p style="margin: 3px;">
            <el-text type="info">{row.username}</el-text>
          </p>
        </div>
      )
    },
    {
      label: "邮箱/手机",
      prop: "email",
      minWidth: 200,
      cellRenderer: ({ row }) => (
        <>
          <div style="display: flex; align-items: center; margin: 5px;">
            <iconify-icon-online icon="ic:outline-email" />
            <span>{row.email}</span>
          </div>
          {row.phone ? (
            <div style="display: flex; align-items: center; margin: 5px;">
              <iconify-icon-online icon="fluent:phone-12-regular" />
              <span>{row.phone}</span>
            </div>
          ) : (
            <div>
              <div style="display: flex; align-items: center; margin: 5px;">
                <iconify-icon-online icon="fluent:phone-12-regular" />
                <el-text type="info">未设置</el-text>
              </div>
            </div>
          )}
        </>
      )
    },
    {
      label: "身份/角色",
      prop: "is_admin",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <div style="white-space: pre-line;">
          <p>
            {row.is_admin ? (
              <el-text type="danger">管理员</el-text>
            ) : (
              <el-text type="primary">普通用户</el-text>
            )}
          </p>
          {row.roles?.map(role => (
            <el-tag type="warning" size="large" style="margin: 5px;">
              {RoleTypes.get(role)}
            </el-tag>
          ))}
        </div>
      )
    },
    {
      label: "状态",
      prop: "is_disabled",
      width: 100,
      cellRenderer: ({ row }) => (
        <div style="display: flex; align-items: center;">
          {row.is_disabled ? (
            <el-text type="danger">禁用</el-text>
          ) : (
            <el-text type="success">启用</el-text>
          )}
        </div>
      )
    },
    {
      label: "上级领导",
      prop: "leader_id",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <div style="white-space: pre-line;">
          <p style="margin: 3px; font-weight: bold;">
            <el-text type="info">
              {row.leader?.name}({row.leader?.sn})
            </el-text>
          </p>
          <p style="margin: 3px;">
            <el-text type="info">{row.leader?.username}</el-text>
          </p>
        </div>
      )
    },
    {
      label: "分组",
      prop: "groups",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <div style="margin: 3px;">
          {row.groups?.map(d => (
            <el-tag type="info" size="large" style="margin: 3px;" key={d.id}>
              {d.name}
            </el-tag>
          ))}
        </div>
      )
    },
    {
      label: "部门",
      prop: "center",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <div style="white-space: pre-line; margin: 3px;">
          <p>
            <el-text type="primary">
              {row.departments
                ?.filter(d => d.family === "unit")
                .map(d => d.name)
                .join("、")}
            </el-text>
          </p>
          <p>
            <el-text type="primary">
              {row.departments
                ?.filter(d => d.family === "center")
                .map(d => d.name)
                .join("、")}
            </el-text>
          </p>
          <p>
            <el-text type="primary">
              {row.departments
                ?.filter(d => d.family === "team")
                .map(d => d.name)
                .join("、")}
            </el-text>
          </p>
        </div>
      )
    },
    {
      label: "备注",
      prop: "remark",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <div style="white-space: pre-line; margin: 3px;">{row.remark}</div>
      )
    },
    {
      label: "时间",
      prop: "created_at",
      minWidth: 120,
      cellRenderer: ({ row }) => (
        <div style="white-space: pre-line; margin: 5px;">
          <span>
            创建时间：{dayjs(row.created_at).format("YYYY-MM-DD HH:mm:ss")}
          </span>
          {row.last_login_time && (
            <div style="white-space: pre-line; margin-top: 5px;">
              <el-text type="primary">
                登陆时间：
                {dayjs(row.last_login_time).format("YYYY-MM-DD HH:mm:ss")}
              </el-text>
            </div>
          )}
        </div>
      )
    },
    {
      label: "操作",
      prop: "op",
      minWidth: 120,
      cellRenderer: ({ row }) => (
        <el-button type="primary" link onClick={() => editUser(row)}>
          <iconify-icon-online icon="ep:edit" style="margin:3px;" />
          编辑
        </el-button>
      )
    }
  ];
  const editUserForm = ref<UpdateUserForm>();
  const childrenRef = ref(null);
  function editUser(row: User) {
    editUserForm.value = {
      phone: row.phone,
      is_admin: row.is_admin,
      is_disabled: row.is_disabled,
      manual_leader_id: row.manual_leader_id,
      roles: row.roles ? row.roles : []
    };
    addDrawer({
      headerRenderer: ({ titleId, titleClass }) => (
        <div class="flex flex-row justify-between">
          <h4 id={titleId} class={titleClass}>
            编辑用户
            <b>
              {row.name} ({row.username})
            </b>
          </h4>
        </div>
      ),
      contentRenderer: () =>
        h(Uform, {
          formInline: {
            row: row,
            form: editUserForm.value
          },
          ref: childrenRef
        }),
      beforeSure: done => {
        if (!childrenRef.value.ruleFormRef) return;
        childrenRef.value.ruleFormRef.validate((valid: boolean) => {
          if (valid) {
            updateUser(row.id, editUserForm.value)
              .then(res => {
                if (res.success) {
                  message(res.msg, { type: "success" });
                  done();
                  onSearch();
                  editUserForm.value = undefined;
                } else {
                  message(res.msg, { type: "error" });
                }
              })
              .catch(error => {
                message(error, { type: "error" });
              });
          } else {
            message("请检查表单数据", { type: "warning" });
          }
        });
      }
    });
  }

  function handleSizeChange(val: number) {
    pagination.pageSize = val;
    onSearch();
  }

  function handleCurrentChange(val: number) {
    pagination.currentPage = val;
    onSearch();
  }
  const batGroupForm = ref<BatchGroupForm>();
  function batchGroup(rows: any) {
    if (rows.length === 0) {
      message("请选择要操作的用户 ", { type: "warning" });
      return;
    }
    batGroupForm.value = {
      users_ids: rows.map(row => row.id),
      groups_ids: undefined,
      op: "append"
    };
    addDrawer({
      headerRenderer: ({ titleId, titleClass }) => (
        <div class="flex flex-row justify-between">
          <h4 id={titleId} class={titleClass}>
            批量分组
          </h4>
        </div>
      ),
      contentRenderer: () =>
        h(GroupForm, {
          formInline: {
            form: batGroupForm.value,
            rows: rows,
            syncFunc: getAllGroups,
            groups: groups.value
          },
          ref: childrenRef
        }),
      beforeSure: done => {
        if (childrenRef.value.ruleFormRef) {
          childrenRef.value.ruleFormRef
            .validate((valid: boolean) => {
              if (valid) {
                batGroupUsersAPI(batGroupForm.value)
                  .then(res => {
                    if (res.success) {
                      message(res.msg, { type: "success" });
                      done();
                      onSearch();
                      batGroupForm.value = undefined;
                    } else {
                      message(res.msg, { type: "error" });
                    }
                  })
                  .catch(error => {
                    message(error, { type: "error" });
                  });
              } else {
                message("请检查表单数据", { type: "error" });
              }
            })
            .catch(error => {
              message(error, { type: "error" });
            });
        }
      }
    });
  }
  const onSearch = () => {
    loading.value = true;
    getUsersList({
      page: pagination.currentPage,
      limit: pagination.pageSize,
      keyword: form.keyword,
      is_admin: form.is_admin,
      is_disabled: form.is_disabled,
      group_id: form.group_id
    })
      .then(res => {
        if (res.success) {
          dataList.value = res?.data as User[];
          pagination.total = res?.count;
          pagination.pageSize = pagination.pageSize;
          pagination.currentPage = pagination.currentPage;
        } else {
          dataList.value = [];
          pagination.total = 0;
          pagination.pageSize = pagination.pageSize;
          pagination.currentPage = pagination.currentPage;
        }
      })
      .catch(error => {
        message(error, { type: "error" });
      })
      .finally(() => {
        loading.value = false;
      });
  };

  const resetForm = formEl => {
    if (!formEl) return;
    formEl.resetFields();
    onSearch();
  };

  onMounted(() => {
    onSearch();
    getAllGroups();
  });

  return {
    form,
    loading,
    columns,
    dataList,
    pagination,
    onSearch,
    resetForm,
    handleSizeChange,
    handleCurrentChange,
    groups,
    getAllGroups,
    batchGroup
  };
}
