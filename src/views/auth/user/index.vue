<script setup lang="ts">
import { ref } from "vue";
import { useRole } from "./hook";
import { PureTableBar } from "@/components/RePureTableBar";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import type { User } from "@/api/auth/user";
import { useDebounceFn } from "@vueuse/core";

// 添加类型定义
interface FormRef extends HTMLFormElement {
  validate: (callback: (valid: boolean) => void) => void;
  resetFields: () => void;
}

defineOptions({
  name: "AuthUser"
});

const formRef = ref<FormRef>();
const tableRef = ref();
const multipleSelection = ref<User[]>([]);

// 使用类型化的事件处理函数
const handleSelectionChange = (val: User[]) => {
  multipleSelection.value = val;
};

const {
  form,
  loading,
  columns,
  dataList,
  pagination,
  onSearch,
  resetForm,
  handleSizeChange,
  handleCurrentChange,
  groups,
  getAllGroups,
  batchGroup
} = useRole();

// 添加搜索防抖
const debouncedSearch = useDebounceFn(onSearch, 300);
</script>

<template>
  <div class="main">
    <el-card
      shadow="never"
      class="search-card"
      style="
        margin-bottom: 20px;
        border-radius: 12px;
        box-shadow: 0 4px 16px rgb(0 0 0 / 8%);
        transition: box-shadow 0.3s ease;
      "
    >
      <el-form
        ref="formRef"
        :inline="true"
        :model="form"
        class="search-form"
        @submit.prevent
      >
        <el-form-item label="关键字" prop="keyword">
          <el-input
            v-model="form.keyword"
            placeholder="请输入关键字"
            clearable
            style="

              --el-input-border-radius: 8px;
              --el-input-hover-border-color: #4fc3f7;
              --el-input-focus-border-color: #29b6f6;

              width: 220px;
            "
            @keyup.enter="onSearch"
          />
        </el-form-item>
        <el-form-item label="身份" prop="is_admin">
          <el-select
            v-model="form.is_admin"
            label="身份"
            placeholder="请选择身份"
            clearable
            style="

              --el-select-border-radius: 8px;
              --el-select-hover-border-color: #4fc3f7;
              --el-select-focus-border-color: #29b6f6;

              width: 220px;
            "
            @change="onSearch"
          >
            <el-option label="管理员" :value="true">
              <el-text type="danger">管理员</el-text>
            </el-option>
            <el-option label="普通用户" :value="false">
              <el-text type="primary">普通用户</el-text>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="is_disabled">
          <el-select
            v-model="form.is_disabled"
            label="状态"
            placeholder="请选择状态"
            clearable
            style="

              --el-select-border-radius: 8px;
              --el-select-hover-border-color: #4fc3f7;
              --el-select-focus-border-color: #29b6f6;

              width: 220px;
            "
            @change="onSearch"
          >
            <el-option label="禁用" :value="true">
              <el-text type="danger">禁用</el-text>
            </el-option>
            <el-option label="启用" :value="false">
              <el-text type="success">启用</el-text>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="分组" prop="group_id">
          <el-select
            v-model="form.group_id"
            clearable
            filterable
            placeholder="请选择分组"
            style="

              --el-select-border-radius: 8px;
              --el-select-hover-border-color: #4fc3f7;
              --el-select-focus-border-color: #29b6f6;

              width: 220px;
            "
            @change="onSearch"
          >
            <el-option
              v-for="group in groups"
              :key="group.id"
              :label="group.name"
              :value="group.id"
            />
          </el-select>
          <el-button
            :icon="useRenderIcon('ep:refresh')"
            type="primary"
            link
            @click="getAllGroups"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            :icon="useRenderIcon('ri:search-line')"
            :loading="loading"
            class="search-button"
            style="
              background: linear-gradient(135deg, #409eff 0%, #4facff 100%);
              border: none;
              border-radius: 8px;
              box-shadow: 0 4px 12px rgb(64 158 255 / 30%);
              transition: all 0.3s ease;
            "
            @click="onSearch"
          >
            搜索
          </el-button>
          <el-button
            :icon="useRenderIcon('ep:refresh')"
            :loading="loading"
            class="reset-button"
            style="
              margin-left: 12px;
              color: #606266;
              border-color: #dcdfe6;
              border-radius: 8px;
              transition: all 0.3s ease;
            "
            @click="resetForm(formRef)"
          >
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 批量操作表单 -->
    <el-card
      v-if="multipleSelection.length > 0"
      shadow="never"
      class="batch-operation-card"
      style="
        margin-bottom: 20px;
        border-radius: 12px;
        box-shadow: 0 4px 16px rgb(0 0 0 / 8%);
        transition: box-shadow 0.3s ease;
      "
    >
      <el-form class="search-form batch-operation-form" inline>
        <el-form-item>
          <el-button
            type="warning"
            :icon="useRenderIcon('ep:price-tag')"
            :disabled="multipleSelection.length === 0"
            style="
              background: linear-gradient(135deg, #ffa726 0%, #ffb74d 100%);
              border: none;
              border-radius: 8px;
              box-shadow: 0 4px 12px rgb(255 167 38 / 30%);
              transition: all 0.3s ease;
            "
            @click="batchGroup(multipleSelection)"
          >
            设置分组
          </el-button>
          <span
            v-if="multipleSelection.length > 0"
            class="selected-info"
            style="margin-left: 12px; font-size: 14px; color: #606266"
          >
            已选择 {{ multipleSelection.length }} 个用户
          </span>
        </el-form-item>
      </el-form>
    </el-card>

    <PureTableBar
      title="用户列表"
      :columns="columns"
      @refresh="debouncedSearch"
    >
      <template v-slot="{ size, dynamicColumns }">
        <pure-table
          ref="tableRef"
          row-key="id"
          align-whole="left"
          table-layout="auto"
          :loading="loading"
          :size="size"
          :minHeight="500"
          :data="dataList"
          :columns="dynamicColumns"
          :pagination="{ ...pagination, size }"
          :header-cell-style="{
            fontWeight: '600'
          }"
          :row-style="{
            cursor: 'pointer',
            transition: 'background-color 0.2s ease'
          }"
          :cell-style="{
            padding: '12px 10px'
          }"
          border
          stripe
          highlight-current-row
          @page-size-change="handleSizeChange"
          @page-current-change="handleCurrentChange"
          @selection-change="handleSelectionChange"
        >
          <template #empty>
            <el-empty
              description="暂无数据"
              :image-size="120"
              style="padding: 40px 0"
            />
          </template>
        </pure-table>
      </template>
    </PureTableBar>
  </div>
</template>

<style scoped lang="scss">
.main {
  padding: 20px;
  border-radius: 12px;
}

.search-card {
  &:hover {
    box-shadow: 0 6px 20px rgb(0 0 0 / 12%);
  }
}

:deep(.pure-table) {
  overflow: hidden;
  border: 1px solid #e4e7ed;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 4%);

  .el-table__header {
    background: linear-gradient(135deg, #f5f7fa 0%, #f8f9fb 100%) !important;
  }

  .el-table__row {
    transition: all 0.2s ease;

    &:hover {
      background-color: #f5f7fa !important;
      box-shadow: 0 2px 8px rgb(0 0 0 / 6%);
    }
  }

  .el-table__cell {
    padding: 12px 10px !important;
  }
}
</style>
