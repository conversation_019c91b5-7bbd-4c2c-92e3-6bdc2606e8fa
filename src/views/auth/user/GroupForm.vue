<template>
  <div class="group-form">
    <el-card shadow="hover" class="form-card">
      <el-form
        ref="ruleFormRef"
        label-width="auto"
        :model="newFormInline.form"
        size="large"
        status-icon
        label-position="top"
      >
        <el-form-item label="选择用户">
          <el-card class="host-list-card">
            <div class="host-list">
              <div
                v-for="(item, index) in newFormInline.rows"
                :key="index"
                class="host-item"
              >
                <el-tag size="large" effect="plain">
                  {{ item.name }} ({{ item.username }})
                </el-tag>
              </div>
            </div>
          </el-card>
        </el-form-item>

        <!-- 操作选择 -->
        <el-form-item label="操作">
          <el-card>
            <el-select
              v-model="newFormInline.form.op"
              class="!w-[350px]"
              placeholder="请选择操作"
            >
              <el-option value="append" label="追加分组">
                <div class="flex items-center">
                  <el-tag
                    color="green"
                    style="margin-right: 8px"
                    size="small"
                  />
                  <span style="color: green">追加分组</span>
                </div>
              </el-option>
              <el-option value="replace" label="替换分组">
                <div class="flex items-center">
                  <el-tag
                    color="yellow"
                    style="margin-right: 8px"
                    size="small"
                  />
                  <span style="color: green">替换分组</span>
                </div>
              </el-option>
              <el-option value="delete" label="删除分组">
                <div class="flex items-center">
                  <el-tag color="red" style="margin-right: 8px" size="small" />
                  <span style="color: green">删除分组</span>
                </div>
              </el-option>
              <el-option value="clear" label="清空分组">
                <div class="flex items-center">
                  <el-tag color="red" style="margin-right: 8px" size="small" />
                  <span style="color: green">清空分组</span>
                </div>
              </el-option>
            </el-select>
          </el-card>
        </el-form-item>

        <el-form-item
          v-if="newFormInline.form.op !== 'clear'"
          label="分组"
          :rules="[{ required: true, message: '请选择分组', trigger: 'blur' }]"
          prop="groups_ids"
        >
          <el-card>
            <el-select
              v-model="newFormInline.form.groups_ids"
              class="!w-[350px]"
              filterable
              clearable
              multiple
              placeholder="请选择分组"
            >
              <el-option
                v-for="(item, index) in newFormInline.groups"
                :key="index"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
            <el-button
              :icon="useRenderIcon(Refresh)"
              type="primary"
              link
              @click="newFormInline.syncFunc()"
            />
          </el-card>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { defineProps, ref } from "vue";
import type { FormInstance } from "element-plus";
import Refresh from "@iconify-icons/ep/refresh";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import type { BatchGroupForm, User } from "@/api/auth/user";
import type { Group } from "@/api/auth/group";

export interface FormProps {
  formInline: {
    rows: User[];
    groups: Group[];
    syncFunc: Function;
    form: BatchGroupForm;
  };
}

const props = withDefaults(defineProps<FormProps>(), {
  formInline: () => ({
    rows: undefined,
    form: undefined,
    groups: undefined,
    syncFunc: undefined
  })
});

const newFormInline = ref(props.formInline);
const ruleFormRef = ref<FormInstance>();
defineExpose({ ruleFormRef });
</script>

<style scoped>
.group-form {
  padding: 20px; /* 增加内边距 */
}

.host-list {
  display: flex;
  flex-wrap: wrap;
}

.host-item {
  margin: 5px; /* 增加主机项之间的间距 */
}

.el-card {
  margin-bottom: 20px; /* 增加卡片之间的间距 */
}

.el-select {
  border-radius: 4px; /* 圆角 */
}

.el-button {
  margin-top: 10px; /* 增加按钮的上边距 */
}
</style>
