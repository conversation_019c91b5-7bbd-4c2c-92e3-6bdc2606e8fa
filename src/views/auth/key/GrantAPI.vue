<template>
  <div>
    <el-form :model="KeyDetail" label-width="120px">
      <el-form-item label="密钥名称">
        <el-input v-model="KeyDetail.name" disabled />
      </el-form-item>
      <el-form-item label="选择API">
        <el-select
          v-model="newForm.ids"
          multiple
          placeholder="请选择API"
          clearable
          filterable
          class="w-!250"
        >
          <el-option
            v-for="api in allAPIs"
            :key="api.id"
            :label="api.name"
            :value="api.id"
          />
        </el-select>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, defineProps } from "vue";
import { type Key, getKeyAPI, getAPIListAPI } from "@/api/auth/key"; // 假设这些 API 已经定义

const props = defineProps<{
  id: number;
  name: string;
  form: {
    ids: number[];
  };
}>();
const newForm = ref(props.form);

const KeyDetail = ref<Key>({
  name: props.name,
  id: props.id,
  secret: "",
  remark: "",
  apis: [],
  created_at: "",
  updated_at: ""
});
const allAPIs = ref([]);

onMounted(async () => {
  // 获取 Key 详情
  const keyResponse = await getKeyAPI(props.id);
  if (keyResponse.success) {
    KeyDetail.value = keyResponse.data as Key;
  }

  // 获取所有 API 接口
  const apiResponse = await getAPIListAPI();
  if (apiResponse.success) {
    allAPIs.value = apiResponse.data;
  }
});
</script>

<style scoped>
/* 添加样式 */
</style>
