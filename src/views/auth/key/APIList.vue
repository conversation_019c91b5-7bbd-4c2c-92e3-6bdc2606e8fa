<template>
  <el-card class="api-list-card">
    <h2>API 列表</h2>
    <el-table :data="apiList" style="width: 100%">
      <el-table-column prop="name" label="API 名称" />
      <el-table-column prop="uri" label="API 路径" />
    </el-table>
  </el-card>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { getAPIListAPI } from "@/api/auth/key"; // 修改为正确的 API 获取函数
import { message } from "@/utils/message";
import type { API } from "@/api/auth/key";

const apiList = ref<API[]>([]);

const fetchApiList = async () => {
  try {
    const res = await getAPIListAPI(); // 使用正确的 API 函数
    if (res.success) {
      apiList.value = res.data; // 假设返回的数据格式为 { success: true, data: [...] }
    } else {
      message(res.msg || "获取 API 列表失败", { type: "error" });
    }
  } catch (error) {
    message("获取 API 列表失败: " + error, { type: "error" });
  }
};

onMounted(() => {
  fetchApiList();
});
</script>

<style scoped>
.api-list-card {
  padding: 20px;
  margin: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgb(0 0 0 / 10%);
}

h2 {
  margin-bottom: 20px;
  font-size: 24px;
  color: #333;
}
</style>
