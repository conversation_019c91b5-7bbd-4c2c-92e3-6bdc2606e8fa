<template>
  <div>
    <el-card shadow="never">
      <el-form
        ref="ruleFormRef"
        :model="newFormInline.form"
        label-width="auto"
        style="max-width: 600px"
        label-position="top"
        status-icon
        size="large"
        @submit.prevent
      >
        <el-form-item
          label="名称"
          :rules="[
            { required: true, message: '请输入名称', trigger: 'blur' },
            { max: 255, message: '长度不能超过255个字符', trigger: 'blur' }
          ]"
        >
          <el-input
            v-model="newFormInline.form.name"
            show-word-limit
            maxlength="255"
            clearable
            placeholder="请输入名称"
          />
        </el-form-item>
        <el-form-item
          label="密钥"
          :rules="[
            { required: true, message: '请输入密钥', trigger: 'blur' },
            { max: 255, message: '长度不能超过255个字符', trigger: 'blur' }
          ]"
        >
          <el-input
            v-model="newFormInline.form.secret"
            show-word-limit
            maxlength="255"
            clearable
            placeholder="请输入密钥"
          />
        </el-form-item>
        <el-form-item
          label="备注"
          :rules="[
            { max: 255, message: '长度不能超过255个字符', trigger: 'blur' }
          ]"
        >
          <el-input
            v-model="newFormInline.form.remark"
            type="textarea"
            show-word-limit
            maxlength="255"
            clearable
            placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { defineProps, ref } from "vue";
import type { KeyForm } from "@/api/auth/key";
import type { FormInstance } from "element-plus";
export interface FormProps {
  formInline: {
    form: KeyForm;
  };
}
const props = withDefaults(defineProps<FormProps>(), {
  formInline: () => ({
    row: undefined,
    form: undefined
  })
});
const newFormInline = ref(props.formInline);
const ruleFormRef = ref<FormInstance>();
defineExpose({ ruleFormRef });
</script>
