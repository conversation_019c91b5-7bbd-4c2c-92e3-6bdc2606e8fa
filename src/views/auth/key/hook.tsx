import type { PaginationProps } from "@pureadmin/table";
import { reactive, ref, onMounted, h } from "vue";
import {
  createKeyAPI,
  deleteKeyAPI,
  getKeysAPI,
  grantKeyAPIsAPI,
  updateKeyAPI,
  type Key,
  type KeyForm
} from "@/api/auth/key";
import { addDrawer } from "@/components/ReDrawer/index";
import Uform from "./Uform.vue";
import { message } from "@/utils/message";
import { addDialog } from "@/components/ReDialog";
import GrantAPI from "./GrantAPI.vue";

export function useRole() {
  const form = reactive({
    keyword: undefined
  });
  const dataList = ref<Key[]>([]);
  const loading = ref(true);

  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true,
    pageSizes: [10, 20, 30, 40, 50, 100]
  });
  const columns: TableColumnList = [
    {
      label: "ID",
      prop: "id",
      minWidth: 50
    },
    {
      label: "名称",
      prop: "name",
      minWidth: 100
    },
    {
      label: "密钥",
      prop: "secret",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <div style="display: flex; align-items: center; margin: 5px; padding: 8px; border-radius: 4px; ">
          <span style="flex-grow: 1; white-space: pre-line; font-weight: 500; ">
            {row.showSecret ? row.secret : "******"}{" "}
            {/* 根据行的状态显示密钥或隐藏 */}
          </span>
          <el-button
            link
            onClick={() => (row.showSecret = !row.showSecret)}
            style="margin-left: 10px; color: #409EFF; transition: color 0.3s;"
          >
            {row.showSecret ? "隐藏" : "查看"}
          </el-button>
        </div>
      )
    },
    {
      label: "APIs",
      prop: "apis",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <div style="white-space: pre-line ; margin: 3px;">
          {row.apis.map(api => {
            return (
              <el-tag
                type="info"
                style="white-space: pre-line ; margin: 3px;"
                size="large"
              >
                {api.name}
              </el-tag>
            );
          })}
        </div>
      )
    },
    {
      label: "备注",
      prop: "remark",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <div style="white-space: pre-line ; margin: 3px;">{row.remark}</div>
      )
    },
    {
      label: "操作",
      prop: "op",
      minWidth: 120,
      cellRenderer: ({ row }) => (
        <>
          <el-button type="primary" link onClick={() => updateFunc(row)}>
            <iconify-icon-online icon="ep:edit" style="margin:3px;" />
            编辑
          </el-button>
          <el-button type="primary" link onClick={() => handleGrantAPI(row)}>
            <iconify-icon-online icon="ep:edit" style="margin:3px;" />
            授权API
          </el-button>
          <el-button type="danger" link onClick={() => deleteFunc(row)}>
            <iconify-icon-online icon="ep:delete" style="margin:3px;" />
            删除
          </el-button>
        </>
      )
    }
  ];
  const grantForm = ref({
    ids: []
  });
  function handleGrantAPI(row: Key) {
    grantForm.value.ids = row.apis.map(api => api.id);
    addDrawer({
      title: "授权API",
      contentRenderer: () =>
        h(GrantAPI, { id: row.id, name: row.name, form: grantForm.value }),
      beforeSure: done => {
        grantKeyAPIsAPI(row.id, {
          ids: grantForm.value.ids
        })
          .then(response => {
            if (response.success) {
              message(response.msg, { type: "success" });
              onSearch();
              done();
            } else {
              message(response.msg, { type: "error" });
            }
          })
          .catch(error => {
            message(error, { type: "error" });
          });
      }
    });
  }
  const updateFuncForm = ref<KeyForm>();
  const childrenRef = ref(null);
  function updateFunc(row: Key) {
    updateFuncForm.value = {
      name: row.name,
      secret: "",
      remark: row.remark
    };
    addDrawer({
      headerRenderer: ({ titleId, titleClass }) => (
        <div class="flex flex-row justify-between">
          <h4 id={titleId} class={titleClass}>
            编辑密钥
            <b>{row.name}</b>
          </h4>
        </div>
      ),
      contentRenderer: () =>
        h(Uform, {
          formInline: {
            form: updateFuncForm.value
          },
          ref: childrenRef
        }),
      beforeSure: done => {
        if (!childrenRef.value.ruleFormRef) return;
        childrenRef.value.ruleFormRef.validate((valid: boolean) => {
          if (valid) {
            updateKeyAPI(row.id, updateFuncForm.value)
              .then(res => {
                if (res.success) {
                  message(res.msg, { type: "success" });
                  done();
                  onSearch();
                  updateFuncForm.value = undefined;
                } else {
                  message(res.msg, { type: "error" });
                }
              })
              .catch(error => {
                message(error, { type: "error" });
              });
          } else {
            message("请检查表单数据", { type: "warning" });
          }
        });
      }
    });
  }

  function addFunc() {
    updateFuncForm.value = {
      name: "",
      secret: "",
      remark: ""
    };
    addDrawer({
      headerRenderer: ({ titleId, titleClass }) => (
        <div class="flex flex-row justify-between">
          <h4 id={titleId} class={titleClass}>
            添加用户分组
          </h4>
        </div>
      ),
      contentRenderer: () =>
        h(Uform, {
          formInline: {
            form: updateFuncForm.value
          },
          ref: childrenRef
        }),
      beforeSure: done => {
        if (!childrenRef.value.ruleFormRef) return;
        childrenRef.value.ruleFormRef.validate((valid: boolean) => {
          if (valid) {
            createKeyAPI(updateFuncForm.value)
              .then(res => {
                if (res.success) {
                  message(res.msg, { type: "success" });
                  done();
                  onSearch();
                  updateFuncForm.value = undefined;
                } else {
                  message(res.msg, { type: "error" });
                }
              })
              .catch(error => {
                message(error, { type: "error" });
              });
          } else {
            message("请检查表单数据", { type: "warning" });
          }
        });
      }
    });
  }
  function deleteFunc(row: Key) {
    addDialog({
      title: "",
      width: 500,
      sureBtnLoading: true,
      contentRenderer: () => (
        <p>
          确认删除：
          <b style="color:red">{row.name}</b>
        </p>
      ),
      beforeSure: done => {
        deleteKeyAPI(row.id)
          .then(res => {
            if (res.success) {
              done();
              message(res.msg, { type: "success" });
              onSearch();
            } else {
              message(res.msg, { type: "error" });
            }
          })
          .catch(error => {
            message(error, { type: "error" });
          })
          .finally(() => {
            done();
          });
      }
    });
  }
  function handleSizeChange(val: number) {
    pagination.pageSize = val;
    onSearch();
  }

  function handleCurrentChange(val: number) {
    pagination.currentPage = val;
    onSearch();
  }

  const onSearch = () => {
    loading.value = true;
    getKeysAPI({
      page: pagination.currentPage,
      limit: pagination.pageSize,
      keyword: form.keyword
    })
      .then(res => {
        if (res.success) {
          if (Array.isArray(res.data)) {
            dataList.value = res.data.map(item => ({
              ...item,
              showSecret: false
            })) as Key[];
          } else {
            dataList.value = [];
          }
          pagination.total = res?.count;
        } else {
          dataList.value = [];
          pagination.total = 0;
        }
      })
      .catch(error => {
        message(error, { type: "error" });
      })
      .finally(() => {
        loading.value = false;
      });
  };

  const resetForm = formEl => {
    if (!formEl) return;
    formEl.resetFields();
    onSearch();
  };

  onMounted(() => {
    onSearch();
  });

  return {
    form,
    loading,
    columns,
    dataList,
    pagination,
    onSearch,
    resetForm,
    handleSizeChange,
    handleCurrentChange,
    addFunc
  };
}
