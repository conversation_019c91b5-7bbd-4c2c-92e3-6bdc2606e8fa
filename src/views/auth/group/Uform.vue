<template>
  <div>
    <el-card shadow="never">
      <el-form
        ref="ruleFormRef"
        :model="newFormInline.form"
        label-width="auto"
        style="max-width: 600px"
        label-position="top"
        status-icon
        size="large"
        @submit.prevent
      >
        <el-form-item
          label="名称"
          :rules="[
            { required: true, message: '请输入名称', trigger: 'blur' },
            { max: 255, message: '长度不能超过255个字符', trigger: 'blur' }
          ]"
        >
          <el-input v-model="newFormInline.form.name" />
        </el-form-item>
        <el-form-item
          label="备注"
          :rules="[
            { max: 255, message: '长度不能超过255个字符', trigger: 'blur' }
          ]"
        >
          <el-input v-model="newFormInline.form.remark" type="textarea" />
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { defineProps, ref } from "vue";
import type { GroupForm } from "@/api/auth/group";
import type { FormInstance } from "element-plus";
export interface FormProps {
  formInline: {
    form: GroupForm;
  };
}
const props = withDefaults(defineProps<FormProps>(), {
  formInline: () => ({
    row: undefined,
    form: undefined
  })
});
const newFormInline = ref(props.formInline);
const ruleFormRef = ref<FormInstance>();
defineExpose({ ruleFormRef });
</script>
