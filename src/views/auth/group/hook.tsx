import type { PaginationProps } from "@pureadmin/table";
import { reactive, ref, onMounted, h } from "vue";
import {
  type Group,
  type GroupForm,
  createGroupAPI,
  deleteGroupAPI,
  getGroupsAPI,
  updateGroupAPI
} from "@/api/auth/group";
import { addDrawer } from "@/components/ReDrawer/index";
import Uform from "./Uform.vue";
import { message } from "@/utils/message";
import { addDialog } from "@/components/ReDialog";
export function useRole() {
  const form = reactive({
    keyword: undefined
  });
  const dataList = ref<Group[]>([]);
  const loading = ref(true);

  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true,
    pageSizes: [10, 20, 30, 40, 50, 100]
  });
  const columns: TableColumnList = [
    {
      label: "ID",
      prop: "id",
      minWidth: 50
    },
    {
      label: "名称",
      prop: "name",
      minWidth: 100
    },
    {
      label: "备注",
      prop: "remark",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <div style="white-space: pre-line ; margin: 3px;">{row.remark}</div>
      )
    },
    {
      label: "操作",
      prop: "op",
      minWidth: 120,
      cellRenderer: ({ row }) => (
        <>
          <el-button type="primary" link onClick={() => updateFunc(row)}>
            <iconify-icon-online icon="ep:edit" style="margin:3px;" />
            编辑
          </el-button>
          <el-button type="danger" link onClick={() => deleteFunc(row)}>
            <iconify-icon-online icon="ep:delete" style="margin:3px;" />
            删除
          </el-button>
        </>
      )
    }
  ];
  const updateFuncForm = ref<GroupForm>();
  const childrenRef = ref(null);
  function updateFunc(row: Group) {
    updateFuncForm.value = {
      name: row.name,
      remark: row.remark
    };
    addDrawer({
      headerRenderer: ({ titleId, titleClass }) => (
        <div class="flex flex-row justify-between">
          <h4 id={titleId} class={titleClass}>
            编辑用户分组
            <b>{row.name}</b>
          </h4>
        </div>
      ),
      contentRenderer: () =>
        h(Uform, {
          formInline: {
            form: updateFuncForm.value
          },
          ref: childrenRef
        }),
      beforeSure: done => {
        if (!childrenRef.value.ruleFormRef) return;
        childrenRef.value.ruleFormRef.validate((valid: boolean) => {
          if (valid) {
            updateGroupAPI(row.id, updateFuncForm.value)
              .then(res => {
                if (res.success) {
                  message(res.msg, { type: "success" });
                  done();
                  onSearch();
                  updateFuncForm.value = undefined;
                } else {
                  message(res.msg, { type: "error" });
                }
              })
              .catch(error => {
                message(error, { type: "error" });
              });
          } else {
            message("请检查表单数据", { type: "warning" });
          }
        });
      }
    });
  }

  function addFunc() {
    updateFuncForm.value = {
      name: "",
      remark: ""
    };
    addDrawer({
      headerRenderer: ({ titleId, titleClass }) => (
        <div class="flex flex-row justify-between">
          <h4 id={titleId} class={titleClass}>
            添加用户分组
          </h4>
        </div>
      ),
      contentRenderer: () =>
        h(Uform, {
          formInline: {
            form: updateFuncForm.value
          },
          ref: childrenRef
        }),
      beforeSure: done => {
        if (!childrenRef.value.ruleFormRef) return;
        childrenRef.value.ruleFormRef.validate((valid: boolean) => {
          if (valid) {
            createGroupAPI(updateFuncForm.value)
              .then(res => {
                if (res.success) {
                  message(res.msg, { type: "success" });
                  done();
                  onSearch();
                  updateFuncForm.value = undefined;
                } else {
                  message(res.msg, { type: "error" });
                }
              })
              .catch(error => {
                message(error, { type: "error" });
              });
          } else {
            message("请检查表单数据", { type: "warning" });
          }
        });
      }
    });
  }
  function deleteFunc(row: Group) {
    addDialog({
      title: "",
      width: 500,
      sureBtnLoading: true,
      contentRenderer: () => (
        <p>
          确认删除：
          <b style="color:red">{row.name}</b>
        </p>
      ),
      beforeSure: done => {
        deleteGroupAPI(row.id)
          .then(res => {
            if (res.success) {
              done();
              message(res.msg, { type: "success" });
              onSearch();
            } else {
              message(res.msg, { type: "error" });
            }
          })
          .catch(error => {
            message(error, { type: "error" });
          })
          .finally(() => {
            done();
          });
      }
    });
  }
  function handleSizeChange(val: number) {
    pagination.pageSize = val;
    onSearch();
  }

  function handleCurrentChange(val: number) {
    pagination.currentPage = val;
    onSearch();
  }

  const onSearch = () => {
    loading.value = true;
    getGroupsAPI({
      page: pagination.currentPage,
      limit: pagination.pageSize,
      keyword: form.keyword
    })
      .then(res => {
        if (res.success) {
          dataList.value = res?.data;
          pagination.total = res?.count;
          pagination.pageSize = pagination.pageSize;
          pagination.currentPage = pagination.currentPage;
        } else {
          dataList.value = [];
          pagination.total = 0;
          pagination.pageSize = pagination.pageSize;
          pagination.currentPage = pagination.currentPage;
        }
      })
      .catch(error => {
        message(error, { type: "error" });
      })
      .finally(() => {
        loading.value = false;
      });
  };

  const resetForm = formEl => {
    if (!formEl) return;
    formEl.resetFields();
    onSearch();
  };

  onMounted(() => {
    onSearch();
  });

  return {
    form,
    loading,
    columns,
    dataList,
    pagination,
    onSearch,
    resetForm,
    handleSizeChange,
    handleCurrentChange,
    addFunc
  };
}
