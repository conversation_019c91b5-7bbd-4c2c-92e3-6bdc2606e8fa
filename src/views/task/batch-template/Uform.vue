<template>
  <div>
    <el-card shadow="never">
      <el-form
        ref="ruleFormRef"
        label-width="auto"
        style="max-width: 600px"
        :model="newFormInline.form"
        size="large"
        status-icon
        label-position="top"
        @submit.prevent
      >
        <el-form-item
          label="名称"
          prop="name"
          :rules="[
            { required: true, message: '请输入名称', trigger: 'blur' },
            { max: 255, message: '请输入最大255', trigger: 'blur' }
          ]"
        >
          <el-input
            v-model="newFormInline.form.name"
            placeholder="请输入名称"
          />
        </el-form-item>
        <el-form-item
          label="任务类型"
          prop="task_type"
          :rules="[
            { required: true, message: '请选择任务类型', trigger: 'change' }
          ]"
        >
          <el-select v-model="newFormInline.form.task_type" class="!w-[150px]">
            <el-option
              v-for="(item, index) in TaskTypes"
              :key="index"
              :label="item[1]"
              :value="item[0]"
            >
              {{ item[1] }}
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item
          label="文件路径"
          prop="file_path"
          :rules="[
            { required: true, message: '请输入名称', trigger: 'blur' },
            { max: 255, message: '请输入最大255', trigger: 'blur' }
          ]"
        >
          <el-input
            v-model="newFormInline.form.file_path"
            placeholder="请输入文件路径"
          />
        </el-form-item>
        <el-form-item
          label="备注"
          prop="remark"
          :rules="[{ max: 255, message: '请输入最大255', trigger: 'blur' }]"
        >
          <el-input v-model="newFormInline.form.remark" type="textarea" />
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { defineProps, ref } from "vue";
import type { FormInstance } from "element-plus";
import type { BatchTemplateForm } from "@/api/task/batch-template";
import { TaskTypes } from "@/config/enum";
export interface FormProps {
  formInline: {
    form: BatchTemplateForm;
  };
}
const props = withDefaults(defineProps<FormProps>(), {
  formInline: () => ({ row: undefined, form: undefined })
});

const newFormInline = ref(props.formInline);
const ruleFormRef = ref<FormInstance>();
defineExpose({ ruleFormRef });
</script>
