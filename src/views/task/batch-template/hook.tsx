import dayjs from "dayjs";
import type { PaginationProps } from "@pureadmin/table";
import { reactive, ref, onMounted, h } from "vue";
import { message } from "@/utils/message";
import { addDrawer } from "@/components/ReDrawer";
import { addDialog } from "@/components/ReDialog";
import Uform from "./Uform.vue";
import type { BatchTemplate } from "@/api/task/batch-template";
import type { BatchTemplateForm } from "@/api/task/batch-template";
import {
  createBatchTemplateAPI,
  updateBatchTemplateAPI
} from "@/api/task/batch-template";
import { deleteBatchTemplateAPI } from "@/api/task/batch-template";
import { getBatchTemplatesAPI } from "@/api/task/batch-template";

export function useRole() {
  const form = reactive({
    keyword: undefined
  });
  const dataList = ref<BatchTemplate[]>([]);
  const loading = ref<boolean>(true);

  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true,
    pageSizes: [10, 20, 30, 40, 50, 100]
  });
  const columns: TableColumnList = [
    {
      label: "ID",
      prop: "id",
      minWidth: 100
    },
    {
      label: "名称",
      prop: "name",
      minWidth: 100
    },
    {
      label: "任务类型",
      prop: "task_type",
      minWidth: 100
    },
    {
      label: "文件路径",
      prop: "file_path",
      minWidth: 100
    },
    {
      label: "备注",
      prop: "remark",
      minWidth: 100
    },
    {
      label: "创建时间",
      prop: "created_at",
      minWidth: 80,
      cellRenderer: ({ row }) => (
        <p>{dayjs(row.created_at).format("YYYY-MM-DD HH:mm:ss")}</p>
      )
    },
    {
      label: "更新时间",
      prop: "updated_at",
      minWidth: 80,
      cellRenderer: ({ row }) => (
        <p>{dayjs(row.updated_at).format("YYYY-MM-DD HH:mm:ss")}</p>
      )
    },
    {
      label: "操作",
      prop: "action",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <div style="display: flex; align-items: center">
          <el-button type="primary" link onClick={() => updateFunc(row)}>
            编辑
          </el-button>
          <el-button type="danger" link onClick={() => deleteFunc(row)}>
            删除
          </el-button>
        </div>
      )
    }
  ];
  const editForm = ref<BatchTemplateForm>();
  const childrenRef = ref(null);
  function updateFunc(row: BatchTemplate) {
    editForm.value = {
      name: row.name,
      file_path: row.file_path,
      task_type: row.task_type,
      remark: row.remark
    };
    addDrawer({
      headerRenderer: ({ titleId, titleClass }) => (
        <div class="flex flex-row justify-between">
          <h4 id={titleId} class={titleClass}>
            编辑任务模板
            <b>{row.name}</b>
          </h4>
        </div>
      ),
      contentRenderer: () =>
        h(Uform, {
          formInline: {
            form: editForm.value
          },
          ref: childrenRef
        }),
      beforeSure: done => {
        if (childrenRef.value?.ruleFormRef) {
          childrenRef.value.ruleFormRef
            .validate((valid: boolean) => {
              if (valid) {
                updateBatchTemplateAPI(row.id, editForm.value)
                  .then(res => {
                    if (res.success) {
                      message(res.msg, { type: "success" });
                      done();
                      editForm.value = undefined;
                      onSearch();
                    } else {
                      message(res.msg, { type: "error" });
                    }
                  })
                  .catch(error => {
                    message(error, { type: "error" });
                  });
              } else {
                message("请检查表单数据", { type: "error" });
              }
            })
            .catch(error => {
              message("请检查表单数据:" + error, { type: "error" });
            });
        }
      }
    });
  }

  function addFunc() {
    editForm.value = {
      name: "",
      remark: "",
      file_path: "",
      task_type: "ansible"
    };
    addDrawer({
      headerRenderer: ({ titleId, titleClass }) => (
        <div class="flex flex-row justify-between">
          <h4 id={titleId} class={titleClass}>
            添加任务模板
          </h4>
        </div>
      ),
      contentRenderer: () =>
        h(Uform, {
          formInline: {
            form: editForm.value
          },
          ref: childrenRef
        }),
      beforeSure: done => {
        if (childrenRef.value?.ruleFormRef) {
          childrenRef.value.ruleFormRef.validate((valid: boolean) => {
            if (valid) {
              createBatchTemplateAPI(editForm.value)
                .then(res => {
                  if (res.success) {
                    message(res.msg, { type: "success" });
                    editForm.value = undefined;
                    onSearch();
                    done();
                  } else {
                    message(res.msg, { type: "error" });
                  }
                })
                .catch(error => {
                  message(error, { type: "error" });
                });
            } else {
              message("请检查表单数据", { type: "error" });
            }
          });
        } else {
          message("请检查表单", { type: "error" });
        }
      }
    });
  }

  function deleteFunc(row: BatchTemplate) {
    addDialog({
      title: "",
      width: 500,
      sureBtnLoading: true,
      contentRenderer: () => (
        <p>
          确认删除：
          <b style="color:red">{row.name}</b>
        </p>
      ),
      beforeSure: done => {
        deleteBatchTemplateAPI(row.id)
          .then(res => {
            if (res.success) {
              done();
              message(res.msg, { type: "success" });
              onSearch();
            } else {
              message(res.msg, { type: "error" });
            }
          })
          .catch(error => {
            message(error, { type: "error" });
          });
      }
    });
  }

  function handleSizeChange(val: number) {
    pagination.pageSize = val;
    onSearch();
  }

  function handleCurrentChange(val: number) {
    pagination.currentPage = val;
    onSearch();
  }

  async function onSearch() {
    loading.value = true;
    getBatchTemplatesAPI({
      page: pagination.currentPage,
      limit: pagination.pageSize,
      keyword: form.keyword
    })
      .then(res => {
        if (res.success) {
          dataList.value = res.data;
          pagination.total = res.count;
          pagination.pageSize = pagination.pageSize;
          pagination.currentPage = pagination.currentPage;
        } else {
          dataList.value = [];
          pagination.total = 0;
          pagination.pageSize = pagination.pageSize;
          pagination.currentPage = pagination.currentPage;
        }
      })
      .catch(() => {
        message("请求失败", { type: "error" });
      })
      .finally(() => {
        loading.value = false;
      });
  }

  const resetForm = formEl => {
    if (!formEl) return;
    formEl.resetFields();
    onSearch();
  };

  onMounted(() => {
    onSearch();
  });

  return {
    form,
    loading,
    columns,
    dataList,
    pagination,
    onSearch,
    resetForm,
    handleSizeChange,
    handleCurrentChange,
    addFunc
  };
}
