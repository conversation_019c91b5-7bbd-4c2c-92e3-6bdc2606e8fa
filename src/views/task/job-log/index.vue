<script setup lang="ts">
import { ref } from "vue";
import { useRole } from "./hook";
import { PureTableBar } from "@/components/RePureTableBar";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";

import Refresh from "@iconify-icons/ep/refresh";

defineOptions({
  name: "TaskJobLog"
});

const formRef = ref();
const tableRef = ref();

const {
  form,
  loading,
  columns,
  dataList,
  pagination,
  onSearch,
  resetForm,
  handleSizeChange,
  handleCurrentChange
} = useRole();
</script>

<template>
  <div class="main">
    <el-card
      shadow="never"
      class="search-card"
      style="
        margin-bottom: 20px;
        border-radius: 12px;
        box-shadow: 0 4px 16px rgb(0 0 0 / 8%);
        transition: box-shadow 0.3s ease;
      "
    >
      <el-form
        ref="formRef"
        :inline="true"
        :model="form"
        class="search-form"
        @submit.prevent
      >
        <el-form-item label="关键字" prop="name">
          <el-input
            v-model="form.name"
            placeholder="请输入关键字"
            clearable
            style="

              --el-input-border-radius: 8px;
              --el-input-hover-border-color: #4fc3f7;
              --el-input-focus-border-color: #29b6f6;

              width: 220px;
            "
            @keyup.enter="onSearch"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            :icon="useRenderIcon('ri:search-line')"
            :loading="loading"
            class="search-button"
            style="
              background: linear-gradient(135deg, #409eff 0%, #4facff 100%);
              border: none;
              border-radius: 8px;
              box-shadow: 0 4px 12px rgb(64 158 255 / 30%);
              transition: all 0.3s ease;
            "
            @click="onSearch"
          >
            搜索
          </el-button>
          <el-button
            :icon="useRenderIcon(Refresh)"
            class="reset-button"
            style="
              margin-left: 12px;
              color: #606266;
              border-color: #dcdfe6;
              border-radius: 8px;
              transition: all 0.3s ease;
            "
            @click="resetForm(formRef)"
          >
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <PureTableBar title="作业日志" :columns="columns" @refresh="onSearch">
      <template v-slot="{ size, dynamicColumns }">
        <pure-table
          ref="tableRef"
          row-key="id"
          align-whole="left"
          table-layout="auto"
          :loading="loading"
          :size="size"
          :minHeight="500"
          :data="dataList"
          :columns="dynamicColumns"
          :pagination="{ ...pagination, size }"
          :header-cell-style="{
            fontWeight: '600'
          }"
          :row-style="{
            cursor: 'pointer',
            transition: 'background-color 0.2s ease'
          }"
          :cell-style="{
            padding: '12px 10px'
          }"
          border
          stripe
          highlight-current-row
          @page-size-change="handleSizeChange"
          @page-current-change="handleCurrentChange"
        >
          <template #empty>
            <el-empty
              description="暂无数据"
              :image-size="120"
              style="padding: 40px 0"
            />
          </template>
        </pure-table>
      </template>
    </PureTableBar>
  </div>
</template>
<style scoped lang="scss">
.main {
  padding: 20px;
  border-radius: 12px;
}

.search-card {
  &:hover {
    box-shadow: 0 6px 20px rgb(0 0 0 / 12%);
  }
}

:deep(.pure-table) {
  overflow: hidden;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 4%);

  .el-table__header {
    background: linear-gradient(135deg, #f5f7fa 0%, #f8f9fb 100%) !important;
  }

  .el-table__row {
    transition: all 0.2s ease;

    &:hover {
      background-color: #f5f7fa !important;
      box-shadow: 0 2px 8px rgb(0 0 0 / 6%);
    }
  }

  .el-table__cell {
    padding: 12px 10px !important;
  }
}
</style>
