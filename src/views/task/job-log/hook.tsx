import dayjs from "dayjs";
import { getJobLogListAPI, type Job, type JobLog } from "@/api/task/job";
import type { PaginationProps } from "@pureadmin/table";
import { reactive, ref, onMounted } from "vue";
import { message } from "@/utils/message";
import { TaskStatusColors, TaskStatus } from "@/config/enum";

export function useRole() {
  const form = reactive({
    name: undefined
  });
  const dataList = ref<Job[] | JobLog[]>([]);
  const loading = ref(true);

  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true,
    pageSizes: [10, 20, 30, 40, 50, 100]
  });
  const columns: TableColumnList = [
    {
      label: "创建时间",
      prop: "created_at",
      width: 220,
      cellRenderer: ({ row }) => (
        <div style="display: flex; align-items: center">
          <iconify-icon-online icon="ep:timer" />
          <span style="margin-left: 10px">
            {dayjs(row.created_at).format("YYYY-MM-DD HH:mm:ss")}
          </span>
        </div>
      )
    },
    {
      label: "状态",
      prop: "status",
      width: 100,
      cellRenderer: ({ row }) => (
        <div style="display: flex; align-items: center">
          <el-text type={TaskStatusColors.get(row.status)}>
            {TaskStatus.get(row.status)}
          </el-text>
        </div>
      )
    },
    {
      label: "名称",
      prop: "name",
      width: 280
    },
    {
      label: "备注",
      prop: "result",
      minWidth: 300,
      cellRenderer: ({ row }) => (
        <div style="display: flex; align-items: center;white-space: pre-line;">
          {row.result}
        </div>
      )
    }
  ];

  function handleSizeChange(val: number) {
    pagination.pageSize = val;
    onSearch();
  }

  function handleCurrentChange(val: number) {
    pagination.currentPage = val;
    onSearch();
  }

  async function onSearch() {
    loading.value = true;
    getJobLogListAPI({
      page: pagination.currentPage,
      limit: pagination.pageSize,
      name: form.name
    })
      .then(res => {
        if (res.success) {
          dataList.value = res.data;
          pagination.total = res.count;
          pagination.pageSize = pagination.pageSize;
          pagination.currentPage = pagination.currentPage;
        } else {
          dataList.value = [];
          pagination.total = 0;
          pagination.pageSize = pagination.pageSize;
          pagination.currentPage = pagination.currentPage;
        }
      })
      .catch(() => {
        message("请求失败", { type: "error" });
      })
      .finally(() => {
        loading.value = false;
      });
  }

  const resetForm = formEl => {
    if (!formEl) return;
    formEl.resetFields();
    onSearch();
  };

  onMounted(() => {
    onSearch();
  });

  return {
    form,
    loading,
    columns,
    dataList,
    pagination,
    onSearch,
    resetForm,
    handleSizeChange,
    handleCurrentChange
  };
}
