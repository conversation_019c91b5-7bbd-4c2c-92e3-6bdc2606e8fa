<template>
  <div>
    <el-card shadow="never">
      <el-form
        ref="ruleFormRef"
        :model="newFormInline.form"
        :rules="rules"
        label-width="100px"
        class="enhanced-form"
        style="
          padding: 24px;
          border-radius: 12px;
          box-shadow: 0 4px 16px rgb(0 0 0 / 8%);
          transition: all 0.3s ease;
        "
      >
        <el-form-item label="主机" prop="ips">
          <div class="flex items-center gap-2 w-full">
            <el-select
              v-model="newFormInline.form.ips"
              placeholder="请选择主机"
              multiple
              filterable
              clearable
              style="

                --el-select-border-radius: 8px;
                --el-select-hover-border-color: #4fc3f7;
                --el-select-focus-border-color: #29b6f6;

                width: 100%;
              "
            >
              <el-option
                v-for="(item, index) in newFormInline.hosts"
                :key="index"
                :label="`${item.name} ${item.ip} ${item.public_ip}`"
                :value="item.ip"
              />
            </el-select>
            <el-button
              :icon="useRenderIcon('ep:refresh')"
              type="primary"
              link
              style="
                padding: 12px 24px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgb(79 195 247 / 30%);
                transition: all 0.3s ease;
              "
              @click="() => newFormInline.syncHosts()"
            />
          </div>
        </el-form-item>
        <el-form-item label="任务模板" prop="template_ids">
          <div class="flex items-center gap-2 w-full">
            <el-select
              v-model="newFormInline.form.template_ids"
              placeholder="请选择模板"
              multiple
              filterable
              clearable
              style="

                --el-select-border-radius: 8px;
                --el-select-hover-border-color: #4fc3f7;
                --el-select-focus-border-color: #29b6f6;

                width: 100%;
              "
            >
              <el-option
                v-for="(item, index) in newFormInline.templates"
                :key="index"
                :label="`${item.name}`"
                :value="item.id"
              />
            </el-select>
            <el-button
              :icon="useRenderIcon('ep:refresh')"
              type="primary"
              link
              style="
                padding: 12px 24px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgb(79 195 247 / 30%);
                transition: all 0.3s ease;
              "
              @click="evt => newFormInline.syncTemplates(evt)"
            />
          </div>
        </el-form-item>
        <el-form-item
          label="参数"
          prop="parameter"
          :rules="[
            { max: 255, message: '请输入最大255个字符', trigger: 'blur' },
            { validator: validateJSON, trigger: 'blur' }
          ]"
        >
          <el-input
            v-model="newFormInline.form.parameter"
            maxlength="255"
            show-word-limit
            type="textarea"
            placeholder="请输入参数, JSON格式"
            :rows="4"
            style="

              --el-input-border-radius: 8px;
              --el-input-hover-border-color: #4fc3f7;
              --el-input-focus-border-color: #29b6f6;
            "
          />
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { defineProps, ref } from "vue";
import type { FormInstance } from "element-plus";
import type { BatchTaskForm } from "@/api/task/batch-task";
import type { BatchTemplate } from "@/api/task/batch-template";
import type { Host } from "@/api/asset/host";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";

export interface FormProps {
  formInline: {
    form: BatchTaskForm;
    hosts?: Host[];
    templates?: BatchTemplate[];
    syncTemplates: Function;
    syncHosts: Function;
  };
}
const props = withDefaults(defineProps<FormProps>(), {
  formInline: () => ({
    templates: undefined,
    form: undefined,
    hosts: undefined,
    syncTemplates: undefined,
    syncHosts: undefined
  })
});

const newFormInline = ref(props.formInline);
const ruleFormRef = ref<FormInstance>();
defineExpose({ ruleFormRef });

// JSON 格式验证函数
const validateJSON = (rule: any, value: string, callback: Function) => {
  if (!value) {
    callback();
    return;
  }
  try {
    JSON.parse(value);
    callback();
  } catch (error) {
    callback(new Error("请输入合法的 JSON 格式"));
  }
};

const rules = {
  ips: [{ required: true, message: "请选择主机", trigger: "blur" }],
  template_ids: [{ required: true, message: "请选择模板", trigger: "blur" }]
};
</script>

<style scoped>
.enhanced-form {
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 8%);
  transition: all 0.3s ease;
}

.el-select {
  width: 100%;
}

:deep(.el-select__tags .el-tag) {
  height: 26px;
  padding: 0 8px;
  font-size: 14px;
  line-height: 26px;
}

:deep(.el-select-dropdown__item) {
  height: 34px;
  padding: 0 12px;
  font-size: 14px;
  line-height: 34px;
}

.el-form-item {
  margin-bottom: 16px;
}
</style>
