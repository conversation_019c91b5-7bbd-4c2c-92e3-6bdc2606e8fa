<template>
  <div class="main">
    <el-card class="search-card">
      <el-form
        ref="formRef"
        :inline="true"
        :model="form"
        class="search-form"
        @submit.prevent
      >
        <el-form-item label="关键字" prop="keyword">
          <el-input
            v-model="form.keyword"
            placeholder="请输入关键字"
            clearable
            @keyup.enter="onSearch"
          />
        </el-form-item>
        <el-form-item label="IP" prop="ip">
          <el-input
            v-model="form.ip"
            placeholder="请输入IP"
            clearable
            @keyup.enter="onSearch"
          />
        </el-form-item>
        <el-form-item label="任务类型" prop="task_type">
          <el-select
            v-model="form.task_type"
            filterable
            clearable
            @change="onSearch"
          >
            <el-option
              v-for="(item, index) in TaskTypes"
              :key="index"
              :label="item[1]"
              :value="item[0]"
            >
              {{ item[1] }}
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="任务状态" prop="status">
          <el-select
            v-model="form.status"
            filterable
            clearable
            @change="onSearch"
          >
            <el-option
              v-for="(item, index) in TaskStatus"
              :key="index"
              :label="item[1]"
              :value="item[0]"
            >
              {{ item[1] }}
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            :icon="useRenderIcon('ri:search-line')"
            :loading="loading"
            class="search-button"
            @click="onSearch"
          >
            搜索
          </el-button>
          <el-button
            :icon="useRenderIcon(Refresh)"
            class="reset-button"
            @click="resetForm(formRef)"
          >
            重置
          </el-button>
          <el-button
            type="primary"
            link
            :icon="useRenderIcon(Plus)"
            @click="addFunc"
          >
            添加
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card class="batch-operation-card">
      <el-form class="batch-operation-form" inline>
        <el-form-item>
          <el-button
            type="warning"
            link
            :icon="useRenderIcon('codicon:run-all')"
            :disabled="
              multipleSelection.length === 0 ||
              multipleSelection.some(task => task.status !== 0)
            "
            @click="() => batRunFunc(multipleSelection)"
          >
            批量执行
          </el-button>
          <span class="selected-info">{{ selectedInfo }}</span>
        </el-form-item>
      </el-form>
    </el-card>

    <PureTableBar title="任务列表" :columns="columns" @refresh="onSearch">
      <template v-slot="{ size, dynamicColumns }">
        <pure-table
          ref="tableRef"
          row-key="id"
          align-whole="left"
          table-layout="auto"
          :loading="loading"
          :size="size"
          :minHeight="500"
          :data="dataList"
          :columns="dynamicColumns"
          :pagination="{ ...pagination, size }"
          :header-cell-style="{
            fontWeight: '600',
            borderBottom: '2px solid #e4e7ed'
          }"
          :row-style="{
            cursor: 'pointer',
            transition: 'background-color 0.2s ease'
          }"
          :cell-style="{
            padding: '12px 10px'
          }"
          border
          stripe
          highlight-current-row
          @page-size-change="handleSizeChange"
          @page-current-change="handleCurrentChange"
          @selection-change="handleSelectionChange"
        >
          <template #default>
            <el-table-column
              type="selection"
              width="55"
              align="center"
              :selectable="row => row.status === 0"
            />
            <el-table-column
              v-for="item in dynamicColumns"
              :key="item.prop"
              v-bind="item"
            />
            <el-table-column label="操作" width="120">
              <template #default="scope">
                <el-button
                  link
                  type="primary"
                  style="
                    padding: 8px 12px;
                    font-weight: 600;
                    color: #4fc3f7;
                    background: linear-gradient(
                      to right,
                      rgb(79 195 247 / 10%) 0%,
                      rgb(79 195 247 / 5%) 100%
                    );
                    border-radius: 8px;
                    transition: all 0.3s ease;
                  "
                  @click="handleView(scope.row)"
                >
                  查看
                </el-button>
              </template>
            </el-table-column>
          </template>
        </pure-table>
      </template>
    </PureTableBar>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { useRole } from "./hook";
import { PureTableBar } from "@/components/RePureTableBar";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";

import Refresh from "@iconify-icons/ep/refresh";
import Plus from "@iconify-icons/ep/plus";
import { TaskStatus, TaskTypes } from "@/config/enum";
import type { BatchTask } from "@/api/task/batch-task";

defineOptions({
  name: "BatchTask"
});

const multipleSelection = ref<BatchTask[]>([]);

const handleSelectionChange = val => {
  multipleSelection.value = val;
};

const formRef = ref();
const tableRef = ref();

const {
  form,
  loading,
  columns,
  dataList,
  pagination,
  onSearch,
  resetForm,
  handleSizeChange,
  handleCurrentChange,
  addFunc,
  batRunFunc
} = useRole();

// 计算未执行状态的选择对象信息
const selectedInfo = computed(() => {
  const selectedTasks = multipleSelection.value.filter(
    task => task.status === 0
  );
  return selectedTasks.length > 0
    ? `已选择 ${selectedTasks.length} 个未执行任务`
    : "未选择任务";
});

const handleView = row => {
  console.log(row);
};
</script>

<style scoped lang="scss">
.main {
  padding: 20px;
  border-radius: 12px;
}

.search-card {
  margin-bottom: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 8%);
  transition: box-shadow 0.3s ease;

  &:hover {
    box-shadow: 0 6px 20px rgb(0 0 0 / 12%);
  }
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;

  :deep(.el-form-item) {
    margin-bottom: 0;
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: #606266;
  }

  .el-input,
  .el-select {
    width: 220px;

    :deep(input),
    :deep(.el-input__wrapper) {
      border-color: #e4e7ed;
      border-radius: 8px;

      &:focus {
        border-color: #409eff;
        box-shadow: 0 0 0 1px rgb(64 158 255 / 20%);
      }
    }
  }
}

.batch-operation-card {
  margin-bottom: 16px;
  border-radius: 12px;
}

.batch-operation-form {
  display: flex;
  gap: 16px;
  align-items: center;
}

.selected-info {
  margin-left: 10px;
  color: #606266;
}

.reset-button,
.search-button {
  color: white;
  background: linear-gradient(135deg, #409eff 0%, #4facff 100%);
  border: none;
  box-shadow: 0 4px 12px rgb(64 158 255 / 30%);
}

.reset-button {
  color: #606266;
  border-color: #dcdfe6;
}

.add-button {
  color: #409eff;
  background-color: rgb(64 158 255 / 10%);
  border-color: #409eff;
}

/* 美化表格样式 */
:deep(.pure-table) {
  .el-table__header {
    th {
      font-weight: 600;
      color: #303133;
      background-color: #f5f7fa;
      border-bottom: 2px solid #e4e7ed;
    }
  }

  .el-table__body {
    td {
      padding: 12px 10px;
      border-bottom: 1px solid #e4e7ed;
    }
  }

  .el-table__footer {
    th {
      font-weight: 600;
      color: #303133;
      background-color: #f5f7fa;
      border-top: 2px solid #e4e7ed;
    }
  }
}
</style>
