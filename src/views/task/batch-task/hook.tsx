import dayjs from "dayjs";
import type { PaginationProps } from "@pureadmin/table";
import { reactive, ref, onMounted, h } from "vue";
import { message } from "@/utils/message";
import { addDialog } from "@/components/ReDialog";
import {
  type BatchTask,
  type BatchTaskForm,
  createBatchTasksAPI,
  deleteBatchTaskAPI,
  getBatchTasksAPI,
  reRunBatchTaskAPI,
  runBatchTaskAPI,
  runBatchTasksAPI
} from "@/api/task/batch-task";
import { TaskStatusColors, TaskStatus } from "@/config/enum";
import { addDrawer } from "@/components/ReDrawer";
import Uform from "./Uform.vue";
import View from "./View.vue";
import {
  getAllBatchTemplatesAPI,
  type BatchTemplate
} from "@/api/task/batch-template";
import { getAllHostsAPI, type Host } from "@/api/asset/host";
import { RouterLink } from "vue-router";
import { DeleteFilled } from "@element-plus/icons-vue";
export function useRole() {
  const form = reactive({
    task_type: undefined,
    ip: undefined,
    status: undefined,
    keyword: undefined
  });
  const dataList = ref<BatchTask[]>([]);
  const loading = ref<boolean>(true);

  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true,
    pageSizes: [10, 20, 30, 40, 50, 100]
  });
  const columns: TableColumnList = [
    {
      type: "selection",
      align: "left",
      selectable: row => row.status === 0
    },
    {
      label: "名称",
      prop: "name",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <div style="font-weight: bold; color: #333;">{row.name}</div>
      )
    },
    {
      label: "主机",
      prop: "ip",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <div style="display: flex; align-items: center">
          <RouterLink
            to={{ name: "主机列表", query: { ip: row.ip } }}
            style="color: #409EFF; text-decoration: underline;"
          >
            <span style="margin-left: 10px">{row.ip}</span>
          </RouterLink>
        </div>
      )
    },
    {
      label: "任务类型",
      prop: "task_type",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <div style="white-space: pre-line;">
          <p style="font-weight: bold; color: #555;">{row.task_type}</p>
          <el-text type="info">{row.file_path}</el-text>
        </div>
      )
    },
    {
      label: "状态",
      prop: "status",
      width: 100,
      cellRenderer: ({ row }) => (
        <div style="display: flex; align-items: center">
          <el-tag
            type={TaskStatusColors.get(row.status)}
            style="margin-right: 5px;"
          >
            {TaskStatus.get(row.status)}
          </el-tag>
        </div>
      )
    },
    {
      label: "参数",
      prop: "parameter",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <div style="color: #666;">{row.parameter || "无参数"}</div>
      )
    },
    {
      label: "操作人",
      prop: "created_by",
      width: 250,
      cellRenderer: ({ row }) => (
        <div style="white-space: pre-line;">
          {row.created_by && (
            <p>
              {" "}
              创建者：<strong>{row.created_by}</strong>
            </p>
          )}
          {row.op_user && (
            <p>
              {" "}
              执行者：<strong>{row.op_user}</strong>
            </p>
          )}
        </div>
      )
    },
    {
      label: "执行时间",
      prop: "run_time",
      width: 250,
      cellRenderer: ({ row }) => (
        <div style="white-space: pre-line;">
          {row.run_time && (
            <p>
              执行：
              <strong>
                {dayjs(row.run_time).format("YYYY-MM-DD HH:mm:ss")}
              </strong>
            </p>
          )}
          {row.finished_time && (
            <p>
              完成：
              <strong>
                {dayjs(row.finished_time).format("YYYY-MM-DD HH:mm:ss")}
              </strong>
            </p>
          )}
        </div>
      )
    },
    {
      label: "记录时间",
      prop: "created_at",
      width: 250,
      cellRenderer: ({ row }) => (
        <div style="white-space: pre-line;">
          <p>
            创建：
            <strong>
              {dayjs(row.created_at).format("YYYY-MM-DD HH:mm:ss")}
            </strong>
          </p>
          <p>
            更新：
            <strong>
              {dayjs(row.updated_at).format("YYYY-MM-DD HH:mm:ss")}
            </strong>
          </p>
        </div>
      )
    },
    {
      label: "操作",
      prop: "action",
      width: 250,
      cellRenderer: ({ row }) => (
        <div style="display: flex; align-items: center">
          <el-button link type="info" onClick={() => showFunc(row)}>
            查看
          </el-button>
          {row.status === 0 && (
            <>
              <el-button type="success" link onClick={() => runFunc(row)}>
                运行
              </el-button>
              <el-button type="danger" link onClick={() => deleteFunc(row)}>
                删除
              </el-button>
            </>
          )}
          {row.status === 4 && (
            <el-button type="danger" link onClick={() => reRunFunc(row)}>
              重试
            </el-button>
          )}
        </div>
      )
    }
  ];

  function batRunFunc(rows: BatchTask[]) {
    if (rows.length === 0) {
      message("请选择要运行的批量任务", { type: "warning" });
      return;
    }
    const opRows = rows.filter(row => row.status === 0);
    addDialog({
      title: "批量运行",
      width: 500,
      sureBtnLoading: true,
      contentRenderer: () => (
        <p style="margin: 10px;">
          确认运行 <b style="color:red;">{opRows.length}</b> 个任务 ：
          {opRows.map(row => {
            return (
              <p style="margin: 10px;">
                <el-text type="warning">{row.name}</el-text>
              </p>
            );
          })}
        </p>
      ),
      beforeSure: done => {
        runBatchTasksAPI({
          task_ids: opRows.map(row => row.id)
        })
          .then(res => {
            if (res.success) {
              message(res.msg, { type: "success" });
              onSearch();
            } else {
              message(res.msg, { type: "error" });
            }
          })
          .catch(error => {
            message(error, { type: "error" });
          })
          .finally(() => {
            done();
          });
      }
    });
  }
  function deleteFunc(row: BatchTask) {
    addDialog({
      title: "确认删除",
      width: 500,
      sureBtnLoading: true,
      contentRenderer: () => (
        <div style="text-align: center; padding: 20px;">
          <el-icon style="font-size: 40px; color: #f56c6c;">
            <DeleteFilled />
          </el-icon>
          <p style="margin-top: 10px; font-size: 16px;">
            确认删除任务：<strong>{row.name}</strong> 吗？
          </p>
        </div>
      ),
      beforeSure: done => {
        deleteBatchTaskAPI(row.id)
          .then(res => {
            if (res.success) {
              message(res.msg, { type: "success" });
              onSearch();
            } else {
              message(res.msg, { type: "error" });
            }
          })
          .catch(error => {
            message(error, { type: "error" });
          })
          .finally(() => {
            done();
          });
      }
    });
  }

  const editForm = ref<BatchTaskForm>();
  const hosts = ref<Host[]>();
  const templates = ref<BatchTemplate[]>();
  const childrenRef = ref(null);
  function addFunc() {
    editForm.value = {
      template_ids: [],
      ips: [],
      parameter: ""
    };
    addDrawer({
      headerRenderer: ({ titleId, titleClass }) => (
        <div class="flex flex-row justify-between">
          <h4 id={titleId} class={titleClass}>
            添加任务
          </h4>
        </div>
      ),
      contentRenderer: () =>
        h(Uform, {
          formInline: {
            form: editForm.value,
            hosts: hosts.value,
            templates: templates.value,
            syncTemplates: syncBatchTemplates,
            syncHosts: syncHosts
          },
          ref: childrenRef
        }),
      beforeSure: done => {
        if (childrenRef.value?.ruleFormRef) {
          childrenRef.value.ruleFormRef.validate((valid: boolean) => {
            if (valid) {
              createBatchTasksAPI(editForm.value)
                .then(res => {
                  if (res.success) {
                    message(res.msg, { type: "success" });
                    editForm.value = undefined;
                    onSearch();
                    done();
                  } else {
                    message(res.msg, { type: "error" });
                  }
                })
                .catch(error => {
                  message(error, { type: "error" });
                });
            } else {
              message("请检查表单数据", { type: "error" });
            }
          });
        } else {
          message("请检查表单", { type: "error" });
        }
      }
    });
  }
  function showFunc(row: BatchTask) {
    addDrawer({
      title: "查看任务",
      hideFooter: true,
      contentRenderer: () => h(View, { formInline: { task: row } })
    });
  }
  function reRunFunc(row: BatchTask) {
    addDialog({
      title: "",
      width: 500,
      sureBtnLoading: true,
      contentRenderer: () => (
        <p>
          确认执行任务：
          <el-text type="primary">{row.name}</el-text>
        </p>
      ),
      beforeSure: done => {
        reRunBatchTaskAPI(row.id)
          .then(res => {
            if (res.success) {
              message(res.msg, { type: "success" });
              onSearch();
            } else {
              message(res.msg, { type: "error" });
            }
          })
          .catch(error => {
            message(error, { type: "error" });
          })
          .finally(() => {
            done();
          });
      }
    });
  }
  function runFunc(row: BatchTask) {
    addDialog({
      title: "",
      width: 500,
      sureBtnLoading: true,
      contentRenderer: () => (
        <p>
          确认执行任务：
          <el-text type="primary">{row.name}</el-text>
        </p>
      ),
      beforeSure: done => {
        runBatchTaskAPI(row.id)
          .then(res => {
            if (res.success) {
              message(res.msg, { type: "success" });
              onSearch();
            } else {
              message(res.msg, { type: "error" });
            }
          })
          .catch(error => {
            message(error, { type: "error" });
          })
          .finally(() => {
            done();
          });
      }
    });
  }

  function syncBatchTemplates() {
    getAllBatchTemplatesAPI()
      .then(res => {
        if (res.success) {
          templates.value = res.data;
        } else {
          message(res.msg, { type: "error" });
        }
      })
      .catch(error => {
        message(error, { type: "error" });
      });
  }
  function syncHosts() {
    getAllHostsAPI()
      .then(res => {
        if (res.success) {
          hosts.value = res.data;
        } else {
          message(res.msg, { type: "error" });
        }
      })
      .catch(error => {
        message(error, { type: "error" });
      });
  }
  function handleSizeChange(val: number) {
    pagination.pageSize = val;
    onSearch();
  }

  function handleCurrentChange(val: number) {
    pagination.currentPage = val;
    onSearch();
  }

  async function onSearch() {
    loading.value = true;
    getBatchTasksAPI({
      page: pagination.currentPage,
      limit: pagination.pageSize,
      keyword: form.keyword,
      ip: form.ip,
      status: form.status,
      task_type: form.task_type
    })
      .then(res => {
        if (res.success) {
          dataList.value = res.data;
          pagination.total = res.count;
          pagination.pageSize = pagination.pageSize;
          pagination.currentPage = pagination.currentPage;
        } else {
          dataList.value = [];
          pagination.total = 0;
          pagination.pageSize = pagination.pageSize;
          pagination.currentPage = pagination.currentPage;
        }
      })
      .catch(() => {
        message("请求失败", { type: "error" });
      })
      .finally(() => {
        loading.value = false;
      });
  }

  const resetForm = formEl => {
    if (!formEl) return;
    formEl.resetFields();
    onSearch();
  };

  onMounted(() => {
    onSearch();
    syncHosts();
    syncBatchTemplates();
  });

  return {
    form,
    loading,
    columns,
    dataList,
    pagination,
    onSearch,
    resetForm,
    handleSizeChange,
    handleCurrentChange,
    addFunc,
    batRunFunc
  };
}
