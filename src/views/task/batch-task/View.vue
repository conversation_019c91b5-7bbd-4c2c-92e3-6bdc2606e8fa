<template>
  <el-card
    shadow="never"
    class="task-view-card"
    style="
      padding: 24px;
      border-radius: 16px;
      box-shadow: 0 8px 24px rgb(0 0 0 / 6%);
    "
  >
    <div class="task-header">
      <h2
        style="
          display: flex;
          align-items: center;
          padding-bottom: 12px;
          margin-bottom: 16px;
          font-size: 20px;
          color: #303133;
          border-bottom: 2px solid #f0f2f5;
        "
      >
        <iconify-icon icon="ep:document" />
        任务详情
      </h2>
    </div>

    <el-descriptions
      :column="2"
      border
      class="enhanced-descriptions"
      style="

        --el-descriptions-cell-border-color: #e4e7ed;
        --el-descriptions-label-background-color: #f5f7fa;
      "
    >
      <el-descriptions-item
        label="任务名称"
        label-style="
          font-weight: 600;
          color: #606266;
        "
      >
        <strong>{{ newFormInline.task.name }}</strong>
      </el-descriptions-item>
      <el-descriptions-item
        label="任务类型"
        label-style="
          font-weight: 600;
          color: #606266;
        "
      >
        {{ newFormInline.task.task_type }}
      </el-descriptions-item>
      <el-descriptions-item
        label="执行状态"
        label-style="
          font-weight: 600;
          color: #606266;
        "
      >
        <el-tag
          :type="getStatusType(newFormInline.task.status)"
          effect="light"
          style="border-radius: 6px"
        >
          {{ TaskStatus.get(newFormInline.task.status) }}
        </el-tag>
      </el-descriptions-item>
      <el-descriptions-item
        label="IP"
        label-style="
          font-weight: 600;
          color: #606266;
        "
      >
        {{ newFormInline.task.ip }}
      </el-descriptions-item>
      <el-descriptions-item
        label="创建时间"
        label-style="
          font-weight: 600;
          color: #606266;
        "
      >
        {{ dayjs(newFormInline.task.created_at).format("YYYY-MM-DD HH:mm:ss") }}
      </el-descriptions-item>
      <el-descriptions-item
        label="更新时间"
        label-style="
          font-weight: 600;
          color: #606266;
        "
      >
        {{ dayjs(newFormInline.task.updated_at).format("YYYY-MM-DD HH:mm:ss") }}
      </el-descriptions-item>
      <el-descriptions-item
        label="创建者"
        label-style="
          font-weight: 600;
          color: #606266;
        "
      >
        {{ newFormInline.task.created_by }}
      </el-descriptions-item>
      <el-descriptions-item
        label="执行者"
        label-style="
          font-weight: 600;
          color: #606266;
        "
      >
        {{ newFormInline.task.op_user }}
      </el-descriptions-item>
    </el-descriptions>

    <div
      class="task-script-container"
      style="padding: 16px; margin-top: 16px; border-radius: 8px"
    >
      <h3
        style="
          display: flex;
          align-items: center;
          margin-bottom: 12px;
          color: #303133;
        "
      >
        <iconify-icon
          icon="ep:memo"
          style="margin-right: 8px; font-size: 18px; color: #4fc3f7"
        />
        参数
      </h3>
      <pre>{{ newFormInline.task.parameter }}</pre>
    </div>

    <div
      class="task-result-container"
      style="padding: 16px; margin-top: 16px; border-radius: 8px"
    >
      <h3
        style="
          display: flex;
          align-items: center;
          margin-bottom: 12px;
          color: #303133;
        "
      >
        <iconify-icon
          icon="ep:data-line"
          style="margin-right: 8px; font-size: 18px; color: #4fc3f7"
        />
        结果
      </h3>
      <pre style="white-space: pre-line">{{ newFormInline.task.result }}</pre>
    </div>

    <div
      class="task-actions"
      style="display: flex; justify-content: flex-end; margin-top: 16px"
    >
      <el-button
        link
        type="primary"
        size="large"
        style="
          color: #4fc3f7;
          border-color: #4fc3f7;
          border-radius: 8px;
          transition: all 0.3s ease;
        "
        @click="getBatchTask(newFormInline.task?.id)"
      >
        刷新
      </el-button>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import type { BatchTask } from "@/api/task/batch-task";
import { getBatchTaskAPI } from "@/api/task/batch-task";
import { TaskStatus } from "@/config/enum";
import { ref } from "vue";
import { message } from "@/utils/message";

export interface FormProps {
  formInline: {
    task: BatchTask;
  };
}
const props = withDefaults(defineProps<FormProps>(), {
  formInline: () => ({
    task: undefined
  })
});

const newFormInline = ref(props.formInline);

function getStatusType(status: number) {
  switch (status) {
    case 1:
    case 2:
      return "warning";
    case 3:
      return "success";
    case 4:
      return "danger";
    default:
      return "info";
  }
}

function getBatchTask(id: number) {
  getBatchTaskAPI(id)
    .then(res => {
      if (res.success) {
        newFormInline.value.task = res.data;
        message("刷新成功", { type: "success" });
      } else {
        message(res.msg, { type: "error" });
      }
    })
    .catch(error => {
      message(error, { type: "error" });
    });
}
</script>
