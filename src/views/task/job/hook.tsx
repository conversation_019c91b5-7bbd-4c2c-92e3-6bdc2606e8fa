import dayjs from "dayjs";
import { getJobListAPI, type Job, type JobLog } from "@/api/task/job";
import type { PaginationProps } from "@pureadmin/table";
import { reactive, ref, onMounted } from "vue";
import { message } from "@/utils/message";
import { TaskStatusColors, TaskStatus } from "@/config/enum";

export function useRole() {
  const form = reactive({
    keyword: undefined
  });
  const dataList = ref<Job[] | JobLog[]>([]);
  const loading = ref(true);

  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true,
    pageSizes: [10, 20, 30, 40, 50, 100]
  });
  const columns: TableColumnList = [
    {
      label: "名称",
      prop: "name"
    },
    {
      label: "状态",
      prop: "status",
      cellRenderer: ({ row }) => (
        <div style="display: flex; align-items: center">
          <el-text type={TaskStatusColors.get(row.status)}>
            {TaskStatus.get(row.status)}
          </el-text>
        </div>
      )
    },
    {
      label: "备注",
      prop: "remark"
    },
    {
      label: "更新时间",
      prop: "updated_at",
      cellRenderer: ({ row }) => (
        <div style="display: flex; align-items: center">
          <iconify-icon-online icon="ep:timer" />
          <span style="margin-left: 10px">
            {dayjs(row.updated_at).format("YYYY-MM-DD HH:mm:ss")}
          </span>
        </div>
      )
    }
  ];

  function handleSizeChange(val: number) {
    pagination.pageSize = val;
    onSearch();
  }

  function handleCurrentChange(val: number) {
    pagination.currentPage = val;
    onSearch();
  }

  async function onSearch() {
    loading.value = true;
    getJobListAPI({
      page: pagination.currentPage,
      limit: pagination.pageSize,
      keyword: form.keyword
    })
      .then(res => {
        if (res.success) {
          dataList.value = res.data;
          pagination.total = res.count;
          pagination.pageSize = pagination.pageSize;
          pagination.currentPage = pagination.currentPage;
        } else {
          dataList.value = [];
          pagination.total = 0;
          pagination.pageSize = pagination.pageSize;
          pagination.currentPage = pagination.currentPage;
        }
      })
      .catch(() => {
        message("请求失败", { type: "error" });
      })
      .finally(() => {
        loading.value = false;
      });
  }

  const resetForm = formEl => {
    if (!formEl) return;
    formEl.resetFields();
    onSearch();
  };

  onMounted(() => {
    onSearch();
  });

  return {
    form,
    loading,
    columns,
    dataList,
    pagination,
    onSearch,
    resetForm,
    handleSizeChange,
    handleCurrentChange
  };
}
