import { onMounted, ref } from "vue";
import { getDashboardDataAPI, type DashboardData } from "@/api/dashboard";
import { message } from "@/utils/message";

const dashboardData = ref<DashboardData>();

export function useWelcome() {
  function getDashboard() {
    getDashboardDataAPI()
      .then(res => {
        if (res.success) {
          dashboardData.value = res.data;
        } else {
          message(res.msg, { type: "error" });
        }
      })
      .catch(error => {
        message(error, { type: "error" });
      });
  }
  onMounted(() => {
    getDashboard();
  });
  return {
    dashboardData,
    getDashboard
  };
}
