<template>
  <div class="dashboard-container">
    <div class="welcome-section">
      <el-card class="welcome-message" shadow="never">
        <div class="welcome-content">
          <div class="welcome-row">
            <div class="greeting-section">
              <div class="greeting-header">
                <div class="user-profile">
                  <div class="avatar-wrapper">
                    <img
                      src="@/assets/user.png"
                      alt="用户头像"
                      class="user-avatar"
                    />
                  </div>
                  <div class="user-info">
                    <div class="date-info">
                      <span class="date">{{ currentDate }}</span>
                      <span class="week">{{ currentWeek }}</span>
                    </div>
                    <div class="greeting-text">
                      <span>{{ timeGreeting }}</span
                      >,
                      <span class="nickname">{{ nickname }}</span>
                    </div>
                    <div class="current-time">
                      <div class="time-icon">
                        <iconify-icon-online icon="mdi:clock-outline" />
                      </div>
                      <div class="digital-clock">
                        {{ currentTime }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="tao-quote">
              <div class="quote-content">
                <div class="quote-icon">
                  <iconify-icon-online icon="mdi:format-quote-open" />
                </div>
                <div class="quote-text">
                  <p class="main-quote">道可道，非常道；名可名，非常名</p>
                  <p class="sub-quote">无名天地之始；有名万物之母</p>
                  <p class="quote-source">—— 老子《道德经》</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <div class="dashboard-section">
      <div class="section-title">
        <h3>工作概览</h3>
        <p class="subtitle">快速查看您的工作状态</p>
      </div>

      <el-row :gutter="20" class="mt-16">
        <el-col :xs="24" :sm="8">
          <el-card
            class="dashboard-card workflow-card"
            shadow="never"
            :class="{
              'has-pending': dashboardData?.workflow_stats.can_approve_count > 0
            }"
          >
            <router-link to="/workflow/approver/orders" class="card-link">
              <div class="card-header">
                <div class="icon-wrapper">
                  <iconify-icon-online icon="mdi:check-circle" />
                </div>
                <div class="header-content">
                  <span class="title">审批工单</span>
                  <span class="subtitle">需要您处理的工单</span>
                </div>
              </div>
              <div class="card-content">
                <div class="stat-row">
                  <div class="stat-item">
                    <span class="stat-label">待审批</span>
                    <span class="stat-number">
                      {{ dashboardData?.workflow_stats.can_approve_count }}
                    </span>
                    <div
                      v-if="dashboardData?.workflow_stats.can_approve_count > 0"
                      class="pending-badge"
                    >
                      <el-tag type="warning" effect="plain" size="small"
                        >待处理</el-tag
                      >
                    </div>
                  </div>
                  <div class="stat-divider"></div>
                  <div class="stat-item">
                    <span class="stat-label">已审批</span>
                    <span class="stat-number">
                      {{ dashboardData?.workflow_stats.approved_count }}
                    </span>
                    <div
                      v-if="
                        dashboardData?.workflow_stats.can_approve_count ||
                        dashboardData?.workflow_stats.approved_count
                      "
                      class="stat-progress"
                    >
                      <el-progress
                        :percentage="
                          Math.round(
                            (dashboardData?.workflow_stats.approved_count /
                              (dashboardData?.workflow_stats.can_approve_count +
                                dashboardData?.workflow_stats.approved_count)) *
                              100
                          )
                        "
                        :stroke-width="4"
                        :show-text="false"
                        status="success"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </router-link>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="8">
          <el-card
            class="dashboard-card workflow-card evaluation-card"
            shadow="never"
            :class="{
              'has-pending':
                dashboardData?.workflow_stats.waiting_evaluation_count > 0
            }"
          >
            <router-link
              :to="{ path: '/workflow/my/order', query: { status: 100 } }"
              class="card-link"
            >
              <div class="card-header">
                <div class="icon-wrapper">
                  <iconify-icon-online icon="mdi:star" />
                </div>
                <div class="header-content">
                  <span class="title">待评价工单</span>
                  <span class="subtitle">需要您评价的工单</span>
                </div>
              </div>
              <div class="card-content">
                <div class="stat-row">
                  <div class="stat-item">
                    <span class="stat-label">待评价</span>
                    <span class="stat-number">
                      {{
                        dashboardData?.workflow_stats.waiting_evaluation_count
                      }}
                    </span>
                    <div
                      v-if="
                        dashboardData?.workflow_stats.waiting_evaluation_count >
                        0
                      "
                      class="pending-badge"
                    >
                      <el-tag type="warning" effect="plain" size="small"
                        >待评价</el-tag
                      >
                    </div>
                  </div>
                </div>
              </div>
            </router-link>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="8">
          <el-card class="dashboard-card workflow-card" shadow="never">
            <router-link to="/workflow/my/order" class="card-link">
              <div class="card-header">
                <div class="icon-wrapper">
                  <iconify-icon-online icon="mdi:clipboard-text" />
                </div>
                <div class="header-content">
                  <span class="title">我的工单</span>
                  <span class="subtitle">查看您提交的工单</span>
                </div>
              </div>
              <div class="card-content">
                <div class="stat-row">
                  <div class="stat-item">
                    <span class="stat-label">审批中</span>
                    <span class="stat-number">
                      {{
                        dashboardData?.workflow_stats.my_order_approving_count
                      }}
                    </span>
                    <div
                      v-if="dashboardData?.workflow_stats.my_order_count"
                      class="stat-progress"
                    >
                      <el-progress
                        :percentage="
                          Math.round(
                            (dashboardData?.workflow_stats
                              .my_order_approving_count /
                              dashboardData?.workflow_stats.my_order_count) *
                              100
                          )
                        "
                        :stroke-width="6"
                        :format="percentage => '占比 ' + percentage + '%'"
                        status="warning"
                      />
                    </div>
                  </div>
                  <div class="stat-divider"></div>
                  <div class="stat-item">
                    <span class="stat-label">总工单</span>
                    <span class="stat-number">
                      {{ dashboardData?.workflow_stats.my_order_count }}
                    </span>
                  </div>
                </div>
              </div>
            </router-link>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <div v-if="isAdmin" class="dashboard-section asset-section">
      <div class="section-title">
        <h3>资产概览</h3>
        <p class="subtitle">查看系统资产情况</p>
      </div>

      <!-- 计算资源统计 -->
      <el-row :gutter="16" class="mt-16 mb-24">
        <el-col :xs="24">
          <el-card class="dashboard-card" shadow="never">
            <router-link to="/statistic/asset/daily-report" class="card-link">
              <div class="card-header">
                <div class="icon-wrapper">
                  <iconify-icon-online icon="mdi:desktop-classic" />
                </div>
                <div class="header-content">
                  <span class="title">计算资源统计</span>
                  <span class="subtitle">查看系统计算资源总量</span>
                </div>
              </div>
            </router-link>
            <div class="card-content">
              <el-row v-if="dashboardData?.computer_resource" :gutter="20">
                <el-col :xs="12" :sm="4" class="resource-stat-col">
                  <div class="resource-stat-card">
                    <div class="resource-stat-icon server-icon">
                      <iconify-icon-online icon="carbon:virtual-machine" />
                    </div>
                    <el-statistic
                      title="主机总数"
                      :value="dashboardData.computer_resource.host_total"
                    >
                      <template #suffix>
                        <span class="resource-unit">台</span>
                      </template>
                    </el-statistic>
                  </div>
                </el-col>
                <el-col :xs="12" :sm="4" class="resource-stat-col">
                  <div class="resource-stat-card">
                    <div class="resource-stat-icon cpu-icon">
                      <iconify-icon-online icon="ep:cpu" />
                    </div>
                    <el-statistic
                      title="CPU总量"
                      :value="dashboardData.computer_resource.cpu_total"
                    >
                      <template #suffix>
                        <span class="resource-unit">核</span>
                      </template>
                    </el-statistic>
                  </div>
                </el-col>
                <el-col :xs="12" :sm="4" class="resource-stat-col">
                  <div class="resource-stat-card">
                    <div class="resource-stat-icon memory-icon">
                      <iconify-icon-online icon="material-symbols:memory" />
                    </div>
                    <el-statistic
                      title="内存总量"
                      :value="
                        formatMemorySize(
                          dashboardData.computer_resource.memory_mb_total
                        ).value
                      "
                      :precision="2"
                    >
                      <template #suffix>
                        <span class="resource-unit">{{
                          formatMemorySize(
                            dashboardData.computer_resource.memory_mb_total
                          ).unit
                        }}</span>
                      </template>
                    </el-statistic>
                  </div>
                </el-col>
                <el-col :xs="12" :sm="4" class="resource-stat-col">
                  <div class="resource-stat-card">
                    <div class="resource-stat-icon gpu-icon">
                      <iconify-icon-online icon="mdi:gpu" />
                    </div>
                    <el-statistic
                      title="GPU总量"
                      :value="dashboardData.computer_resource.gpu_total"
                    >
                      <template #suffix>
                        <span class="resource-unit">个</span>
                      </template>
                    </el-statistic>
                  </div>
                </el-col>
                <el-col :xs="12" :sm="4" class="resource-stat-col">
                  <div class="resource-stat-card">
                    <div class="resource-stat-icon dli-icon">
                      <iconify-icon-online icon="mdi:server-network" />
                    </div>
                    <el-statistic
                      title="DLI计算单元"
                      :value="dashboardData.computer_resource.dli_cu_total"
                    >
                      <template #suffix>
                        <span class="resource-unit">CU</span>
                      </template>
                    </el-statistic>
                  </div>
                </el-col>
              </el-row>
              <el-row v-else :gutter="20">
                <el-col :xs="24">
                  <div class="no-data-message">
                    <iconify-icon-online
                      icon="mdi:information-outline"
                      style="margin-right: 8px; font-size: 24px"
                    />
                    暂无计算资源统计数据
                  </div>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 数据资源统计 -->
      <el-row :gutter="16" class="mt-16 mb-24">
        <el-col :xs="24">
          <el-card class="dashboard-card" shadow="never">
            <router-link to="/statistic/asset/daily-report" class="card-link">
              <div class="card-header">
                <div class="icon-wrapper">
                  <iconify-icon-online icon="mdi:database" />
                </div>
                <div class="header-content">
                  <span class="title">数据资源统计</span>
                  <span class="subtitle">查看系统数据存储资源总量</span>
                </div>
              </div>
            </router-link>
            <div class="card-content">
              <el-row v-if="dashboardData?.data_resource" :gutter="20">
                <el-col 
                  v-for="resource in sortedDataResources" 
                  :key="resource.key" 
                  :xs="12" 
                  :sm="4" 
                  :md="3"
                  class="resource-stat-col"
                >
                  <div class="resource-stat-card">
                    <div class="resource-stat-icon">
                      <iconify-icon-online :icon="resource.icon" />
                    </div>
                    <el-statistic
                      :title="resource.title"
                      :value="formatStorageSize(resource.value).value"
                      :precision="2"
                    >
                      <template #suffix>
                        <span class="resource-unit">{{ formatStorageSize(resource.value).unit }}</span>
                      </template>
                    </el-statistic>
                  </div>
                </el-col>
              </el-row>
              <el-row v-else :gutter="20">
                <el-col :xs="24">
                  <div class="no-data-message">
                    <iconify-icon-online
                      icon="mdi:information-outline"
                      style="margin-right: 8px; font-size: 24px"
                    />
                    暂无数据资源统计数据
                  </div>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="16" class="mt-16 mb-24">
        <el-col :xs="24">
          <el-card class="dashboard-card" shadow="never">
            <router-link to="/asset/host" class="card-link">
              <div class="card-header">
                <div class="icon-wrapper">
                  <iconify-icon-online icon="mdi:server" />
                </div>
                <div class="header-content">
                  <span class="title">主机类型统计</span>
                  <span class="subtitle">查看各类型主机数量</span>
                </div>
              </div>
            </router-link>
            <div class="card-content">
              <div class="host-stats-header">
                <div class="view-options">
                  <span class="view-options-label">视图：</span>
                  <el-radio-group v-model="hostViewType" size="small">
                    <el-radio-button :value="'pie'">
                      <el-tooltip
                        content="环形图"
                        placement="top"
                        :effect="'light'"
                      >
                        <div class="view-option-content">
                          <iconify-icon-online icon="mdi:chart-donut" />
                          <span class="view-option-text">环形图</span>
                        </div>
                      </el-tooltip>
                    </el-radio-button>
                    <el-radio-button :value="'bar'">
                      <el-tooltip
                        content="柱状图"
                        placement="top"
                        :effect="'light'"
                      >
                        <div class="view-option-content">
                          <iconify-icon-online icon="mdi:chart-bar" />
                          <span class="view-option-text">柱状图</span>
                        </div>
                      </el-tooltip>
                    </el-radio-button>
                    <el-radio-button :value="'table'">
                      <el-tooltip
                        content="表格"
                        placement="top"
                        :effect="'light'"
                      >
                        <div class="view-option-content">
                          <iconify-icon-online icon="mdi:table" />
                          <span class="view-option-text">表格</span>
                        </div>
                      </el-tooltip>
                    </el-radio-button>
                  </el-radio-group>
                </div>
              </div>

              <!-- 环形图视图 -->
              <el-row v-if="hostViewType === 'pie'" :gutter="20">
                <el-col :xs="24" :sm="12">
                  <div class="host-stats-chart-container">
                    <div ref="hostTypePieChart" class="host-type-chart"></div>
                  </div>
                </el-col>
                <el-col :xs="24" :sm="12">
                  <div class="host-stats-list">
                    <div
                      v-for="(host, index) in dashboardData?.host_type_stats"
                      :key="index"
                      class="host-stat-item"
                    >
                      <div
                        class="host-stat-icon"
                        :class="`host-type-${getHostTypeColor(host.name)}`"
                      >
                        <iconify-icon-online
                          :icon="getHostTypeIcon(host.name)"
                        />
                      </div>
                      <div class="host-stat-info">
                        <div class="host-stat-name">{{ host.name }}</div>
                        <div class="host-stat-count">
                          <span class="host-stat-number">{{ host.count }}</span>
                          <span class="host-stat-percentage"
                            >{{ calculatePercentage(host.count) }}%</span
                          >
                        </div>
                      </div>
                      <div class="host-stat-bar-container">
                        <div
                          class="host-stat-bar"
                          :class="`host-type-${getHostTypeColor(host.name)}-bg`"
                          :style="{
                            width: `${calculatePercentage(host.count)}%`
                          }"
                        ></div>
                      </div>
                    </div>
                  </div>
                </el-col>
              </el-row>

              <!-- 柱状图视图 -->
              <el-row v-else-if="hostViewType === 'bar'" :gutter="20">
                <el-col :xs="24">
                  <div class="host-stats-chart-container" style="height: 350px">
                    <div ref="hostTypeBarChart" class="host-type-chart"></div>
                  </div>
                </el-col>
              </el-row>

              <!-- 表格视图 -->
              <el-row v-else-if="hostViewType === 'table'" :gutter="20">
                <el-col :xs="24">
                  <el-table
                    :data="dashboardData?.host_type_stats"
                    style="width: 100%"
                    :header-cell-style="{
                      background: 'var(--el-fill-color-light)',
                      color: 'var(--el-text-color-primary)'
                    }"
                    border
                  >
                    <el-table-column label="主机类型" min-width="180">
                      <template #default="scope">
                        <div class="host-type-table-cell">
                          <div
                            class="host-stat-icon small"
                            :class="`host-type-${getHostTypeColor(scope.row.name)}`"
                          >
                            <iconify-icon-online
                              :icon="getHostTypeIcon(scope.row.name)"
                            />
                          </div>
                          <span>{{ scope.row.name }}</span>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      prop="count"
                      label="数量"
                      width="120"
                      align="center"
                    />
                    <el-table-column label="占比" width="180" align="center">
                      <template #default="scope">
                        <div class="percentage-cell">
                          <el-progress
                            :percentage="calculatePercentage(scope.row.count)"
                            :color="getChartColor(scope.row.name)"
                            :stroke-width="10"
                            :show-text="true"
                          />
                        </div>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- K8s集群统计 -->
      <el-row :gutter="16" class="mt-16 mb-24">
        <el-col :xs="24">
          <el-card class="dashboard-card" shadow="never">
            <router-link to="/k8s/cluster" class="card-link">
              <div class="card-header">
                <div class="icon-wrapper">
                  <iconify-icon-online icon="mdi:kubernetes" />
                </div>
                <div class="header-content">
                  <span class="title">K8s集群统计</span>
                  <span class="subtitle">查看K8s集群节点数量</span>
                </div>
              </div>
            </router-link>
            <div class="card-content">
              <div class="k8s-stats-header">
                <div class="k8s-stats-title">
                  <iconify-icon-online
                    icon="mdi:kubernetes"
                    style="margin-right: 8px; font-size: 20px; color: #326ce5"
                  />
                  <span>集群节点统计</span>
                </div>
              </div>
              <el-row :gutter="20" class="mt-16">
                <template
                  v-if="
                    dashboardData?.k8s_stats &&
                    dashboardData.k8s_stats.length > 0
                  "
                >
                  <el-col
                    v-for="cluster in sortedK8sClusters"
                    :key="cluster.cluster_id"
                    class="k8s-stat-col"
                    :xs="12"
                    :sm="8"
                    :md="6"
                  >
                    <div class="k8s-cluster-card">
                      <div class="k8s-cluster-header">
                        <div class="k8s-icon">
                          <iconify-icon-online icon="mdi:kubernetes" />
                        </div>
                        <div class="k8s-name">{{ cluster.name }}</div>
                      </div>
                      <div class="k8s-node-count">
                        <el-statistic
                          :value="cluster.node_count"
                          title="节点数量"
                        >
                          <template #suffix>
                            <span class="k8s-unit">个</span>
                          </template>
                        </el-statistic>
                      </div>
                      <div class="k8s-progress">
                        <el-progress
                          :percentage="Math.min(100, cluster.node_count * 5)"
                          :color="'#326CE5'"
                          :stroke-width="8"
                          :show-text="false"
                        />
                      </div>
                    </div>
                  </el-col>
                </template>
                <el-col v-else :xs="24">
                  <div class="no-data-message">
                    <iconify-icon-online
                      icon="mdi:information-outline"
                      style="margin-right: 8px; font-size: 24px"
                    />
                    暂无K8s集群数据
                  </div>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 账单管理模块 -->
      <div class="section-title mt-32">
        <h3>账单管理</h3>
        <p class="subtitle">查看系统账单情况</p>
      </div>

      <!-- 云账号数量/账单统计 -->
      <el-row :gutter="16" class="mt-16 mb-24">
        <el-col :xs="24">
          <el-card class="dashboard-card" shadow="never">
            <router-link to="/asset/cloud-account" class="card-link">
              <div class="card-header">
                <div class="icon-wrapper">
                  <iconify-icon-online icon="mdi:cloud-outline" />
                </div>
                <div class="header-content">
                  <span class="title">云账号数量/账单统计</span>
                  <span class="subtitle">查看不同类型账号的统计信息</span>
                </div>
              </div>
            </router-link>
            <div class="card-content">
              <el-row :gutter="20">
                <el-col
                  v-for="(
                    account, index
                  ) in dashboardData?.cloud_account_type_stats"
                  :key="index"
                  :xs="12"
                  :sm="4"
                  class="account-stat-col"
                >
                  <el-statistic
                    :title="account.name"
                    :value="account.count"
                    suffix="个"
                    :precision="0"
                    class="stat-item"
                  />
                  <div
                    v-if="account.cost > 0 || account.last_cost > 0"
                    class="bill-info"
                  >
                    <p v-if="account.cost > 0" class="current-month">
                      <el-text>本月账单： </el-text>
                      <el-text type="warning">{{
                        account.cost?.toLocaleString() + " 元"
                      }}</el-text>
                    </p>
                    <p v-if="account.last_cost > 0" class="last-month">
                      <el-text>上月账单： </el-text>
                      <el-text type="info">{{
                        account.last_cost?.toLocaleString() + " 元"
                      }}</el-text>
                    </p>

                    <el-progress
                      :percentage="
                        account.last_cost > 0
                          ? Math.round(
                              (100 * account.cost) / account.last_cost
                            ) > 100
                            ? 100
                            : Math.round(
                                (100 * account.cost) / account.last_cost
                              )
                          : 100
                      "
                      type="line"
                      :stroke-width="10"
                      :format="
                        () =>
                          '达到上月：' +
                          (account.last_cost > 0
                            ? Math.round(
                                (100 * account.cost) / account.last_cost
                              )
                            : 100) +
                          '%'
                      "
                      :color="
                        account.cost > account.last_cost
                          ? '#F56C6C'
                          : account.cost / account.last_cost > 0.9
                            ? '#67C23A'
                            : '#409EFF'
                      "
                    />
                  </div>
                </el-col>
                <el-col :xs="24">
                  <el-button
                    type="primary"
                    size="default"
                    plain
                    class="trend-button"
                    @click="showTrendChart"
                  >
                    <el-icon><TrendCharts /></el-icon>
                    <span>趋势图</span>
                  </el-button>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 云账号月度账单 -->
      <el-row :gutter="16" class="mt-16 mb-24">
        <el-col :xs="24">
          <el-card class="dashboard-card" shadow="never">
            <router-link to="/asset/bills" class="card-link">
              <div class="card-header">
                <div class="icon-wrapper">
                  <iconify-icon-online icon="mdi:chart-areaspline" />
                </div>
                <div class="header-content">
                  <div class="title-wrapper">
                    <span class="title">云账号月度账单</span>
                    <div class="month-progress-text">
                      <el-tag
                        size="small"
                        type="success"
                        effect="plain"
                        class="month-tag"
                      >
                        本月(第{{ dayOfMonth }}天)已经过去：{{
                          currentMonthProgress
                        }}%
                      </el-tag>
                    </div>
                  </div>
                  <span class="subtitle">查看账号账单情况</span>
                </div>
              </div>
            </router-link>
            <div class="card-content">
              <el-row :gutter="20">
                <el-col
                  v-for="bill in dashboardData?.cloud_account_monthly_bills"
                  :key="bill.account_name"
                  :xs="12"
                  :sm="6"
                  class="bill-stat-col"
                >
                  <el-statistic
                    :title="bill.account_name"
                    :value="bill.current_month"
                    :precision="2"
                    :prefix="bill.current_month > bill.last_month ? '↑' : '↓'"
                    :prefix-style="{
                      color:
                        bill.current_month > bill.last_month
                          ? '#F56C6C'
                          : '#67C23A'
                    }"
                    suffix="¥"
                    class="bill-statistic"
                  />
                  <p class="last-month">
                    上月账单: {{ bill.last_month?.toLocaleString() }}
                  </p>

                  <el-progress
                    :percentage="
                      bill.last_month > 0
                        ? Math.round(
                            (100 * bill.current_month) / bill.last_month
                          ) > 100
                          ? 100
                          : Math.round(
                              (100 * bill.current_month) / bill.last_month
                            )
                        : 100
                    "
                    type="line"
                    :stroke-width="10"
                    :format="
                      () =>
                        '达到上月：' +
                        (bill.last_month > 0
                          ? Math.round(
                              (100 * bill.current_month) / bill.last_month
                            )
                          : 100) +
                        '%'
                    "
                    :color="
                      bill.current_month > bill.last_month
                        ? '#F56C6C'
                        : bill.current_month / bill.last_month > 0.9
                          ? '#67C23A'
                          : '#409EFF'
                    "
                  />
                </el-col>
              </el-row>
              <el-row :gutter="16" class="mt-16 mb-24">
                <el-col :xs="24">
                  <el-card class="dashboard-card nested-card" shadow="never">
                    <div class="card-header">
                      <div class="icon-wrapper">
                        <iconify-icon-online icon="mdi:chart-areaspline" />
                      </div>
                      <div class="header-content">
                        <span class="title">账户账单图表</span>
                      </div>
                      <div class="view-options">
                        <span class="view-options-label">视图：</span>
                        <el-radio-group v-model="accountViewType" size="small">
                          <el-radio-button :value="'bar'">
                            <el-tooltip
                              content="柱状图"
                              placement="top"
                              :effect="'light'"
                            >
                              <div class="view-option-content">
                                <iconify-icon-online icon="mdi:chart-bar" />
                                <span class="view-option-text">柱状图</span>
                              </div>
                            </el-tooltip>
                          </el-radio-button>
                          <el-radio-button :value="'line'">
                            <el-tooltip
                              content="折线图"
                              placement="top"
                              :effect="'light'"
                            >
                              <div class="view-option-content">
                                <iconify-icon-online icon="mdi:chart-line" />
                                <span class="view-option-text">折线图</span>
                              </div>
                            </el-tooltip>
                          </el-radio-button>
                          <el-radio-button :value="'table'">
                            <el-tooltip
                              content="表格"
                              placement="top"
                              :effect="'light'"
                            >
                              <div class="view-option-content">
                                <iconify-icon-online icon="mdi:table" />
                                <span class="view-option-text">表格</span>
                              </div>
                            </el-tooltip>
                          </el-radio-button>
                        </el-radio-group>
                      </div>
                    </div>

                    <!-- 柱状图视图 -->
                    <div
                      v-if="accountViewType === 'bar'"
                      ref="accountBillBarChart"
                      style="width: 100%; height: 400px"
                    ></div>

                    <!-- 折线图视图 -->
                    <div
                      v-else-if="accountViewType === 'line'"
                      ref="accountBillLineChart"
                      style="width: 100%; height: 400px"
                    ></div>

                    <!-- 表格视图 -->
                    <div
                      v-else-if="accountViewType === 'table'"
                      class="account-bill-table"
                    >
                      <div
                        v-if="!accountBillData || accountBillData.length === 0"
                        class="no-data-message"
                      >
                        <iconify-icon-online
                          icon="mdi:information-outline"
                          style="margin-right: 8px; font-size: 24px"
                        />
                        暂无账单数据 ({{
                          isAdmin ? "管理员已登录" : "非管理员"
                        }})
                      </div>
                      <div v-else>
                        <div
                          class="debug-info"
                          style="
                            margin-bottom: 10px;
                            font-size: 12px;
                            color: #999;
                          "
                        >
                          数据条数: {{ accountBillData.length }}
                        </div>
                        <el-button
                          type="primary"
                          size="small"
                          style="margin-bottom: 10px"
                          @click="refreshAccountData"
                        >
                          <iconify-icon-online
                            icon="mdi:refresh"
                            style="margin-right: 4px"
                          />
                          刷新数据
                        </el-button>
                        <el-table
                          v-if="accountBillData && accountBillData.length > 0"
                          :data="accountBillData"
                          style="width: 100%"
                          :header-cell-style="{
                            background: 'var(--el-fill-color-light)',
                            color: 'var(--el-text-color-primary)'
                          }"
                          border
                        >
                          <el-table-column
                            prop="account_name"
                            label="账户名称"
                            min-width="180"
                          />
                          <el-table-column
                            label="上月账单"
                            width="150"
                            align="right"
                          >
                            <template #default="scope">
                              <span class="bill-amount"
                                >{{
                                  scope.row.last_month != null
                                    ? scope.row.last_month.toLocaleString()
                                    : "0"
                                }}
                                ¥</span
                              >
                            </template>
                          </el-table-column>
                          <el-table-column
                            label="本月账单"
                            width="150"
                            align="right"
                          >
                            <template #default="scope">
                              <span
                                class="bill-amount"
                                :class="
                                  (scope.row.current_month || 0) >
                                  (scope.row.last_month || 0)
                                    ? 'bill-increase'
                                    : 'bill-decrease'
                                "
                              >
                                {{
                                  scope.row.current_month != null
                                    ? scope.row.current_month.toLocaleString()
                                    : "0"
                                }}
                                ¥
                                <iconify-icon-online
                                  :icon="
                                    (scope.row.current_month || 0) >
                                    (scope.row.last_month || 0)
                                      ? 'mdi:arrow-up'
                                      : 'mdi:arrow-down'
                                  "
                                />
                              </span>
                            </template>
                          </el-table-column>
                          <el-table-column
                            label="变化率"
                            width="150"
                            align="center"
                          >
                            <template #default="scope">
                              <div
                                class="change-rate"
                                :class="
                                  (scope.row.current_month || 0) >
                                  (scope.row.last_month || 0)
                                    ? 'rate-increase'
                                    : 'rate-decrease'
                                "
                              >
                                {{
                                  calculateChangeRate(
                                    scope.row.last_month,
                                    scope.row.current_month
                                  )
                                }}%
                              </div>
                            </template>
                          </el-table-column>
                          <el-table-column label="占比" min-width="180">
                            <template #default="scope">
                              <el-progress
                                :percentage="
                                  calculateBillPercentage(
                                    scope.row.current_month
                                  )
                                "
                                :color="
                                  getBillColor(
                                    scope.row.current_month,
                                    scope.row.last_month
                                  )
                                "
                                :stroke-width="10"
                                :show-text="true"
                              />
                            </template>
                          </el-table-column>
                        </el-table>
                      </div>
                    </div>
                  </el-card>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  computed,
  h,
  ref,
  watch,
  onMounted,
  onBeforeUnmount,
  nextTick
} from "vue";
import * as echarts from "echarts";
import { TrendCharts } from "@element-plus/icons-vue";
import { useUserStore } from "@/store/modules/user";
import { useWelcome } from "./hook";
import { addDialog } from "@/components/ReDialog";
import CloudTypeMonthlyBills from "@/views/asset/bill/CloudTypeMonthlyBills.vue";
// 不需要导入这些枚举，因为我们使用自定义的映射函数
// import { HostTypes, HostTypeColors } from "@/config/enum";

defineOptions({
  name: "Welcome"
});

const { nickname } = useUserStore().$state;

const { dashboardData } = useWelcome();

// 当前时间
const currentTime = ref("");
const currentSeconds = ref(0);
let timerInterval: number | null = null;

// 更新当前时间函数
const updateCurrentTime = () => {
  const now = new Date();
  const hours = now.getHours().toString().padStart(2, "0");
  const minutes = now.getMinutes().toString().padStart(2, "0");
  const seconds = now.getSeconds().toString().padStart(2, "0");
  currentTime.value = `${hours}:${minutes}:${seconds}`;
  currentSeconds.value = parseInt(seconds);
};

// 组件挂载时开始时间更新
onMounted(() => {
  updateCurrentTime(); // 立即更新一次
  timerInterval = window.setInterval(updateCurrentTime, 1000);

  // 初始化图表
  nextTick(() => {
    initCharts();
  });
});

// 组件销毁前清除定时器
onBeforeUnmount(() => {
  if (timerInterval) {
    clearInterval(timerInterval);
    timerInterval = null;
  }
});

const timeGreeting = computed(() => {
  const hour = new Date().getHours();
  if (hour < 6) return "夜深了";
  if (hour < 9) return "早安";
  if (hour < 12) return "上午好";
  if (hour < 18) return "下午好";
  return "晚上好";
});

const currentDate = computed(() => {
  return new Date().toLocaleDateString("zh-CN", {
    year: "numeric",
    month: "long",
    day: "numeric"
  });
});

const currentWeek = computed(() => {
  const weeks = [
    "星期日",
    "星期一",
    "星期二",
    "星期三",
    "星期四",
    "星期五",
    "星期六"
  ];
  return weeks[new Date().getDay()];
});

const isAdmin = computed(() => {
  return useUserStore().IS_ADMIN();
});

const showTrendChart = () => {
  addDialog({
    title: "趋势图",
    width: "80%",
    hideFooter: true,
    contentRenderer: () => h(CloudTypeMonthlyBills)
  });
};

const accountBillData = ref<any>(null);
const accountBillBarChart = ref<any>(null);
const accountBillLineChart = ref<any>(null);
const accountViewType = ref<"bar" | "line" | "table">("bar");
const hostTypePieChart = ref<any>(null);
const hostTypeBarChart = ref<any>(null);
const hostViewType = ref<"pie" | "bar" | "table">("pie");

const currentMonthProgress = ref<number>(0); // 初始化当前月进度
const dayOfMonth = ref<number>(0);
const customColors = ref<"" | "success" | "warning" | "exception">(""); // 根据需要初始化

// 计算当前月的进度
const updateCurrentMonthProgress = () => {
  const today = new Date();
  const currentMonth = today.getMonth(); // 当前月份（0-11）
  const currentDate = today.getDate(); // 当前日期

  // 假设每个月的天数为30天，您可以根据实际情况调整
  const daysInMonth = new Date(
    today.getFullYear(),
    currentMonth + 1,
    0
  ).getDate();
  dayOfMonth.value = currentDate;

  // 计算进度百分比
  currentMonthProgress.value = Math.round((currentDate / daysInMonth) * 100);

  // 根据进度设置颜色
  customColors.value = currentMonthProgress.value > 50 ? "warning" : "success";
};

// 在组件挂载时更新进度
updateCurrentMonthProgress();

// 格式化内存大小，自动选择合适的单位（GB或TB）
const formatMemorySize = memoryMB => {
  const memoryGB = memoryMB / 1024;

  if (memoryGB >= 1024) {
    // 如果大于等于1024GB，转换为TB
    return {
      value: memoryGB / 1024,
      unit: "TB"
    };
  } else {
    // 否则保持GB
    return {
      value: memoryGB,
      unit: "GB"
    };
  }
};

// 格式化存储大小，自动选择合适的单位（KB、MB、GB、TB、PB）
const formatStorageSize = bytes => {
  if (bytes === 0) return { value: 0, unit: "B" };

  const units = ["B", "KB", "MB", "GB", "TB", "PB"];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));

  // 最大单位为PB
  const unit = units[Math.min(i, units.length - 1)];
  const value = bytes / Math.pow(1024, Math.min(i, units.length - 1));

  return {
    value: value,
    unit: unit
  };
};

// 获取主机类型图标
const getHostTypeIcon = (typeName: string) => {
  // 根据主机类型名称返回对应的图标
  switch (typeName) {
    case "阿里云":
    case "阿里云主机":
      return "simple-icons:alibabacloud";
    case "华为云":
    case "华为云主机":
      return "simple-icons:huawei";
    case "腾讯云":
    case "腾讯云主机":
      return "simple-icons:tencentqq";
    case "亚马逊云":
    case "亚马逊云主机":
      return "mdi:aws";
    case "虚拟机":
      return "mdi:server-network";
    case "物理机":
    case "物理主机":
      return "mdi:desktop-tower";
    default:
      return "mdi:server";
  }
};

// 获取主机类型颜色
const getHostTypeColor = (typeName: string) => {
  // 根据主机类型名称返回对应的颜色类名
  switch (typeName) {
    case "阿里云":
    case "阿里云主机":
      return "alibaba";
    case "华为云":
    case "华为云主机":
      return "huawei";
    case "腾讯云":
    case "腾讯云主机":
      return "tencent";
    case "亚马逊云":
    case "亚马逊云主机":
      return "amazon";
    case "虚拟机":
      return "vm";
    case "物理机":
    case "物理主机":
      return "physical";
    default:
      return "default";
  }
};

// 计算百分比
const calculatePercentage = (count: number) => {
  if (!dashboardData.value?.host_type_stats) return 0;

  const total = dashboardData.value.host_type_stats.reduce(
    (sum, host) => sum + host.count,
    0
  );

  return total > 0 ? Math.round((count / total) * 100) : 0;
};

// 删除未使用的函数

// 初始化主机类型饼图
const initHostTypePieChart = () => {
  if (!hostTypePieChart.value || !dashboardData.value?.host_type_stats) return;

  const chartInstance = echarts.init(hostTypePieChart.value, null, {
    useDirtyRect: true
  });

  const hostData = dashboardData.value.host_type_stats.map(host => ({
    name: host.name,
    value: host.count,
    itemStyle: {
      color: getChartColor(host.name)
    }
  }));

  chartInstance.setOption({
    tooltip: {
      trigger: "item",
      formatter: "{a} <br/>{b}: {c} ({d}%)",
      backgroundColor: "rgba(50, 50, 50, 0.8)",
      borderColor: "rgba(255, 255, 255, 0.2)",
      textStyle: {
        color: "#fff"
      }
    },
    legend: {
      show: false
    },
    series: [
      {
        name: "主机类型",
        type: "pie",
        radius: ["40%", "70%"],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 6,
          borderColor: "var(--el-bg-color)",
          borderWidth: 2
        },
        label: {
          show: false
        },
        labelLine: {
          show: false
        },
        data: hostData,
        emphasis: {
          scale: true,
          scaleSize: 10,
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: "rgba(0, 0, 0, 0.5)"
          },
          label: {
            show: true,
            fontSize: "14",
            fontWeight: "bold",
            formatter: "{b}: {c} ({d}%)"
          }
        },
        animationType: "scale",
        animationEasing: "elasticOut",
        animationDelay: function (_idx: number) {
          return Math.random() * 200;
        }
      }
    ]
  });

  // 窗口大小变化时重新调整图表大小
  window.addEventListener("resize", () => {
    chartInstance.resize();
  });
};

// 初始化主机类型柱状图
const initHostTypeBarChart = () => {
  if (!hostTypeBarChart.value || !dashboardData.value?.host_type_stats) return;

  const chartInstance = echarts.init(hostTypeBarChart.value, null, {
    useDirtyRect: true
  });

  const hostData = dashboardData.value.host_type_stats.map(host => ({
    name: host.name,
    value: host.count,
    itemStyle: {
      color: getChartColor(host.name)
    }
  }));

  chartInstance.setOption({
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow"
      },
      backgroundColor: "rgba(50, 50, 50, 0.8)",
      borderColor: "rgba(255, 255, 255, 0.2)",
      textStyle: {
        color: "#fff"
      }
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true
    },
    xAxis: {
      type: "category",
      data: hostData.map(item => item.name),
      axisLabel: {
        interval: 0,
        rotate: 30
      }
    },
    yAxis: {
      type: "value",
      name: "数量",
      nameTextStyle: {
        padding: [0, 0, 0, 40]
      }
    },
    series: [
      {
        name: "主机数量",
        type: "bar",
        barWidth: "60%",
        data: hostData.map(item => ({
          value: item.value,
          itemStyle: {
            color: item.itemStyle.color
          }
        })),
        label: {
          show: true,
          position: "top",
          formatter: "{c}"
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: "rgba(0, 0, 0, 0.5)"
          }
        },
        animationDelay: function (_idx: number) {
          return Math.random() * 200;
        }
      }
    ],
    animationEasing: "elasticOut"
  });

  // 窗口大小变化时重新调整图表大小
  window.addEventListener("resize", () => {
    chartInstance.resize();
  });
};

// 初始化主机类型图表
const initHostTypeChart = () => {
  if (hostViewType.value === "pie") {
    initHostTypePieChart();
  } else if (hostViewType.value === "bar") {
    initHostTypeBarChart();
  }
};

// 获取图表颜色
const getChartColor = (typeName: string) => {
  switch (typeName) {
    case "阿里云":
    case "阿里云主机":
      return "#FF9F43"; // 明亮的橙色
    case "华为云":
    case "华为云主机":
      return "#FF6B6B"; // 鲜艳的红色
    case "腾讯云":
    case "腾讯云主机":
      return "#54A0FF"; // 明亮的蓝色
    case "亚马逊云":
    case "亚马逊云主机":
      return "#FFA502"; // 金黄色
    case "虚拟机":
      return "#5F27CD"; // 紫色
    case "物理机":
    case "物理主机":
      return "#2ED573"; // 鲜艳的绿色
    default:
      return "#4B7BEC"; // 靛蓝色
  }
};

// 刷新账单数据
const refreshAccountData = () => {
  console.log("手动刷新账单数据");
  if (dashboardData.value?.cloud_account_monthly_bills) {
    // 创建一个新的数组，确保触发响应式更新
    const billsData = dashboardData.value.cloud_account_monthly_bills.map(
      bill => ({
        ...bill
      })
    );

    // 直接赋值一个新数组，确保Vue能检测到变化
    accountBillData.value = billsData;

    console.log("账单数据已刷新:", accountBillData.value);

    // 强制更新视图
    nextTick(() => {
      console.log("视图已更新，数据条数:", accountBillData.value?.length);
    });
  } else {
    console.log("无法刷新账单数据: dashboardData中没有账单数据");
  }
};

// 计算账单变化率
const calculateChangeRate = (
  lastMonth: number | null | undefined,
  currentMonth: number | null | undefined
) => {
  // 确保值不为 null 或 undefined
  const last = lastMonth || 0;
  const current = currentMonth || 0;

  if (last === 0) return current > 0 ? 100 : 0;
  const rate = ((current - last) / last) * 100;
  return rate.toFixed(2);
};

// 计算账单占比
const calculateBillPercentage = (amount: number | null | undefined) => {
  if (!accountBillData.value) return 0;

  // 确保值不为 null 或 undefined
  const safeAmount = amount || 0;

  const total = accountBillData.value.reduce(
    (sum: number, bill: any) => sum + (bill.current_month || 0),
    0
  );

  return total > 0 ? Math.round((safeAmount / total) * 100) : 0;
};

// 获取账单颜色
const getBillColor = (
  currentMonth: number | null | undefined,
  lastMonth: number | null | undefined
) => {
  // 确保值不为 null 或 undefined
  const current = currentMonth || 0;
  const last = lastMonth || 0;

  if (current > last * 1.1) {
    return "#F56C6C"; // 大幅增加 - 红色
  } else if (current > last) {
    return "#E6A23C"; // 小幅增加 - 橙色
  } else if (current < last * 0.9) {
    return "#67C23A"; // 大幅减少 - 绿色
  } else {
    return "#409EFF"; // 小幅减少或持平 - 蓝色
  }
};

// 初始化账单柱状图
const initAccountBillBarChart = () => {
  if (!accountBillBarChart.value || !accountBillData.value) return;

  const chartInstance = echarts.init(accountBillBarChart.value, null, {
    useDirtyRect: true
  });

  chartInstance.setOption({
    title: {
      text: "上月 vs 本月账单对比",
      left: "center",
      textStyle: {
        fontSize: 16,
        fontWeight: "normal"
      }
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow"
      },
      backgroundColor: "rgba(50, 50, 50, 0.8)",
      borderColor: "rgba(255, 255, 255, 0.2)",
      textStyle: {
        color: "#fff"
      },
      formatter: function (params: any) {
        // 确保我们有两个参数（上月和本月）
        let lastMonth: number | undefined;
        let currentMonth: number | undefined;
        let accountName: string | undefined;

        for (const param of params) {
          if (param.seriesName === "上月") {
            lastMonth = param.value;
            accountName = param.name;
          } else if (param.seriesName === "本月") {
            currentMonth = param.value;
          }
        }

        if (lastMonth !== undefined && currentMonth !== undefined) {
          const rate = calculateChangeRate(lastMonth, currentMonth);
          const isIncrease = currentMonth > lastMonth;

          return `<div style="font-weight:bold;margin-bottom:5px;">${accountName}</div>
                  <div>上月: <span style="color:#409EFF;font-weight:bold">${lastMonth.toLocaleString()} ¥</span></div>
                  <div>本月: <span style="color:${getBillColor(currentMonth, lastMonth)};font-weight:bold">${currentMonth.toLocaleString()} ¥</span></div>
                  <div style="margin-top:5px;color:${isIncrease ? "#F56C6C" : "#67C23A"}">
                    变化率: ${isIncrease ? "+" : ""}${rate}%
                  </div>`;
        }

        // 如果只有一个参数，则显示简单的提示
        return `${params[0].seriesName}: ${params[0].value.toLocaleString()} ¥`;
      }
    },
    legend: {
      data: ["上月", "本月"],
      top: 30
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true
    },
    xAxis: {
      type: "value",
      name: "金额 (¥)",
      nameLocation: "middle",
      nameGap: 30,
      axisLabel: {
        formatter: function (value: number) {
          if (value >= 1000) {
            return value / 1000 + "k";
          }
          return value;
        }
      }
    },
    yAxis: {
      type: "category",
      data: accountBillData.value.map((bill: any) => bill.account_name),
      axisLabel: {
        interval: 0,
        width: 100,
        overflow: "truncate"
      }
    },
    series: [
      {
        name: "上月",
        type: "bar",
        // 移除堆叠效果，使用并排显示
        // stack: "total",
        barGap: "30%",
        emphasis: {
          focus: "series"
        },
        data: accountBillData.value.map((bill: any) => bill.last_month),
        itemStyle: {
          color: "#409EFF"
        },
        label: {
          show: true,
          position: "top",
          formatter: function (params: any) {
            return params.value > 0 ? params.value.toLocaleString() : "";
          }
        }
      },
      {
        name: "本月",
        type: "bar",
        // 移除堆叠效果，使用并排显示
        // stack: "total",
        emphasis: {
          focus: "series"
        },
        data: accountBillData.value.map((bill: any) => bill.current_month),
        itemStyle: {
          color: function (params: any) {
            const lastMonth =
              accountBillData.value[params.dataIndex].last_month;
            const currentMonth = params.value;
            return getBillColor(currentMonth, lastMonth);
          }
        },
        label: {
          show: true,
          position: "top",
          formatter: function (params: any) {
            const index = params.dataIndex;
            const lastMonth = accountBillData.value[index].last_month;
            const currentMonth = params.value;
            const diff = currentMonth - lastMonth;
            const isIncrease = diff > 0;

            // 显示差异值
            return params.value > 0
              ? `${params.value.toLocaleString()} ${isIncrease ? "↑" : "↓"} ${Math.abs(diff).toLocaleString()}`
              : "";
          },
          color: function (params: any) {
            const index = params.dataIndex;
            const lastMonth = accountBillData.value[index].last_month;
            const currentMonth = params.value;
            return currentMonth > lastMonth ? "#F56C6C" : "#67C23A";
          }
        }
      }
    ],
    animationEasing: "elasticOut",
    animationDelay: function (_idx: number) {
      return Math.random() * 200;
    }
  });

  // 窗口大小变化时重新调整图表大小
  window.addEventListener("resize", () => {
    chartInstance.resize();
  });
};

// 初始化账单折线图
const initAccountBillLineChart = () => {
  if (!accountBillLineChart.value || !accountBillData.value) return;

  const chartInstance = echarts.init(accountBillLineChart.value, null, {
    useDirtyRect: true
  });

  chartInstance.setOption({
    title: {
      text: "上月 vs 本月账单趋势",
      left: "center",
      textStyle: {
        fontSize: 16,
        fontWeight: "normal"
      }
    },
    tooltip: {
      trigger: "axis",
      backgroundColor: "rgba(50, 50, 50, 0.8)",
      borderColor: "rgba(255, 255, 255, 0.2)",
      textStyle: {
        color: "#fff"
      },
      formatter: function (params: any) {
        if (params.length > 0) {
          const accountName = params[0].name;
          let html = `<div style="font-weight:bold;margin-bottom:5px;">${accountName}</div>`;

          // 按账户名称分组
          let lastMonth: number | undefined;
          let currentMonth: number | undefined;

          params.forEach((param: any) => {
            if (param.seriesName === "上月账单") {
              lastMonth = param.value;
              html += `<div>上月: <span style="color:#409EFF;font-weight:bold">${lastMonth.toLocaleString()} ¥</span></div>`;
            } else if (param.seriesName === "本月账单") {
              currentMonth = param.value;
              html += `<div>本月: <span style="color:${getBillColor(currentMonth, lastMonth)};font-weight:bold">${currentMonth.toLocaleString()} ¥</span></div>`;
            }
          });

          // 如果有上月和本月的数据，计算变化率
          if (lastMonth !== undefined && currentMonth !== undefined) {
            const rate = calculateChangeRate(lastMonth, currentMonth);
            const isIncrease = currentMonth > lastMonth;
            const diff = currentMonth - lastMonth;

            html += `<div style="margin-top:5px;color:${isIncrease ? "#F56C6C" : "#67C23A"}">
                      变化率: ${isIncrease ? "+" : ""}${rate}%
                    </div>`;
            html += `<div style="color:${isIncrease ? "#F56C6C" : "#67C23A"}">
                      变化额: ${isIncrease ? "+" : ""}${diff.toLocaleString()} ¥
                    </div>`;
          }

          return html;
        }
        return "";
      }
    },
    legend: {
      data: ["上月账单", "本月账单"],
      top: 30
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true
    },
    xAxis: {
      type: "category",
      data: accountBillData.value.map((bill: any) => bill.account_name),
      axisLabel: {
        interval: 0,
        rotate: 30,
        width: 100,
        overflow: "truncate"
      }
    },
    yAxis: {
      type: "value",
      name: "金额 (¥)",
      axisLabel: {
        formatter: function (value: number) {
          if (value >= 1000) {
            return value / 1000 + "k";
          }
          return value;
        }
      }
    },
    series: [
      {
        name: "上月账单",
        type: "line",
        data: accountBillData.value.map((bill: any) => bill.last_month),
        symbol: "circle",
        symbolSize: 8,
        itemStyle: {
          color: "#409EFF"
        },
        lineStyle: {
          width: 3
        },
        emphasis: {
          focus: "series",
          lineStyle: {
            width: 4
          },
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: "rgba(0, 0, 0, 0.5)"
          }
        },
        label: {
          show: true,
          position: "top",
          formatter: function (params: any) {
            return params.value > 0 ? params.value.toLocaleString() : "";
          }
        }
      },
      {
        name: "本月账单",
        type: "line",
        data: accountBillData.value.map((bill: any) => bill.current_month),
        symbol: "circle",
        symbolSize: 8,
        itemStyle: {
          color: function (params: any) {
            const lastMonth =
              accountBillData.value[params.dataIndex].last_month;
            const currentMonth = params.value;
            return getBillColor(currentMonth, lastMonth);
          }
        },
        lineStyle: {
          width: 3
        },
        emphasis: {
          focus: "series",
          lineStyle: {
            width: 4
          },
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: "rgba(0, 0, 0, 0.5)"
          }
        },
        label: {
          show: true,
          position: "top",
          formatter: function (params: any) {
            const index = params.dataIndex;
            const lastMonth = accountBillData.value[index].last_month;
            const currentMonth = params.value;
            const diff = currentMonth - lastMonth;
            const isIncrease = diff > 0;

            // 显示差异值
            return params.value > 0
              ? `${params.value.toLocaleString()} ${isIncrease ? "↑" : "↓"} ${Math.abs(diff).toLocaleString()}`
              : "";
          },
          color: function (params: any) {
            const index = params.dataIndex;
            const lastMonth = accountBillData.value[index].last_month;
            const currentMonth = params.value;
            return currentMonth > lastMonth ? "#F56C6C" : "#67C23A";
          }
        },
        markLine: {
          data: [
            {
              type: "average",
              name: "平均值",
              lineStyle: {
                color: "#E6A23C",
                type: "dashed"
              }
            }
          ]
        }
      }
    ],
    animationEasing: "elasticOut",
    animationDelay: function (_idx: number) {
      return Math.random() * 200;
    }
  });

  // 窗口大小变化时重新调整图表大小
  window.addEventListener("resize", () => {
    chartInstance.resize();
  });
};

// 初始化账单图表
const initAccountBillChart = () => {
  // 确保我们有账单数据
  if (
    !accountBillData.value &&
    dashboardData.value?.cloud_account_monthly_bills
  ) {
    accountBillData.value = dashboardData.value.cloud_account_monthly_bills;
  }

  if (accountViewType.value === "bar") {
    initAccountBillBarChart();
  } else if (accountViewType.value === "line") {
    initAccountBillLineChart();
  }
  // 表格视图不需要额外初始化，但需要确保数据已加载
};

// 初始化图表
const initCharts = () => {
  // 确保账单数据已加载（如果用户是管理员）
  if (isAdmin.value && dashboardData.value?.cloud_account_monthly_bills) {
    if (!accountBillData.value) {
      console.log("初始化账单数据");
      // 创建一个新的数组，确保触发响应式更新
      accountBillData.value =
        dashboardData.value.cloud_account_monthly_bills.map(bill => ({
          ...bill
        }));
      console.log(
        "初始化账单数据完成，数据条数:",
        accountBillData.value.length
      );
    }
  }

  nextTick(() => {
    initHostTypeChart();
    if (isAdmin.value) {
      if (accountViewType.value === "table") {
        // 如果当前是表格视图，确保数据已加载
        refreshAccountData();
      } else {
        initAccountBillChart();
      }
    }
  });
};

// 监听数据变化并更新图表
watch(dashboardData, initCharts);

// 监听视图类型变化
watch(hostViewType, () => {
  nextTick(() => {
    initHostTypeChart();
  });
});

// K8s集群按节点数量倒序排序
const sortedK8sClusters = computed(() => {
  if (!dashboardData.value?.k8s_stats) return [];
  return [...dashboardData.value.k8s_stats].sort((a, b) => b.node_count - a.node_count);
});

// 数据资源统计按照图片中的顺序排列
const sortedDataResources = computed(() => {
  if (!dashboardData.value?.data_resource) return [];
  
  // 根据截图中的排列顺序定义资源配置
  const resourceConfig = [
    // 第一行
    { key: "mysql_storage_total", icon: "simple-icons:mysql", title: "MySQL存储" },
    { key: "tidb_storage_total", icon: "vscode-icons:file-type-mysql", title: "TiDB存储" },
    { key: "mongodb_storage_total", icon: "simple-icons:mongodb", title: "MongoDB存储" },
    { key: "starrocks_storage_total", icon: "fluent:database-24-filled", title: "StarRocks存储" },
    { key: "clickhouse_storage_total", icon: "simple-icons:clickhouse", title: "ClickHouse存储" },
    { key: "dli_storage_total", icon: "ph:database-duotone", title: "DLI存储" },
    // 第二行
    { key: "oss_storage_total", icon: "bi:cloud-arrow-down", title: "OSS存储" },
    { key: "obs_storage_total", icon: "bi:cloud-arrow-down", title: "OBS存储" },
    { key: "sfs_storage_total", icon: "carbon:storage-pool", title: "SFS存储" },
    { key: "nas_storage_total", icon: "mdi:nas", title: "NAS存储" }
  ];
  
  // 生成排序后的资源数组
  return resourceConfig.map(config => ({
    key: config.key,
    icon: config.icon,
    title: config.title,
    value: dashboardData.value.data_resource[config.key],
    iconClass: ""
  }));
});

// 监听数据变化并更新账单图表
watch(dashboardData, () => {
  if (isAdmin.value && dashboardData.value) {
    // 确保我们有最新的账单数据
    if (dashboardData.value.cloud_account_monthly_bills) {
      accountBillData.value = [
        ...dashboardData.value.cloud_account_monthly_bills
      ]; // 使用展开运算符创建新数组，确保响应式更新
      console.log("账单数据已更新:", accountBillData.value); // 添加日志以便调试
      nextTick(() => {
        initAccountBillChart();
      });
    } else {
      console.log("dashboardData中没有账单数据");
    }
  } else {
    console.log("用户不是管理员或dashboardData为空");
  }
});

// 监听账单视图类型变化
watch(accountViewType, newType => {
  console.log("账单视图类型变化:", newType);
  console.log("当前账单数据:", accountBillData.value);

  // 确保在切换到表格视图时数据已加载
  if (newType === "table") {
    // 直接调用刷新数据函数，确保表格视图有数据显示
    console.log("切换到表格视图，刷新数据");
    refreshAccountData();

    // 添加一个延迟刷新，以防第一次刷新不成功
    setTimeout(() => {
      if (!accountBillData.value || accountBillData.value.length === 0) {
        console.log("延迟刷新账单数据");
        refreshAccountData();
      }
    }, 500);
  } else {
    // 对于图表视图，初始化相应的图表
    nextTick(() => {
      initAccountBillChart();
    });
  }
});
</script>

<style lang="scss" scoped>
@keyframes pulse {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.03);
  }

  100% {
    transform: scale(1);
  }
}

.dashboard-container {
  min-height: calc(100vh - 90px);
  padding: 24px;
  background-color: var(--el-bg-color-page);

  @media (width <= 768px) {
    padding: 16px 12px;
  }
}

.trend-button {
  padding: 6px 12px;
  margin-top: 16px;
  font-size: 13px;
  font-weight: 500;

  .el-icon {
    margin-right: 4px;
    font-size: 14px;
  }
}

.bill-info {
  margin-top: 10px;

  .current-month {
    margin-top: 6px;
    font-size: 13px;
    font-weight: 600;
  }

  .last-month {
    margin-top: 4px;
    font-size: 12px;
    color: #909399;
  }
}

.welcome-section {
  margin-bottom: 32px;

  @media (width <= 768px) {
    margin-bottom: 24px;
  }
}

.welcome-message {
  padding: 24px;
  background-color: var(--el-bg-color);
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 6px;
  box-shadow: var(--el-box-shadow-light);

  @media (width <= 768px) {
    padding: 20px 16px;
  }

  .welcome-content {
    .welcome-row {
      display: flex;
      gap: 32px;
      align-items: flex-start;

      @media (width <= 768px) {
        flex-direction: column;
        gap: 24px;
      }
    }
  }

  .greeting-section {
    flex: 1;
    min-width: 0;

    .greeting-header {
      display: flex;
      flex-direction: column;
      gap: 16px;

      .user-profile {
        display: flex;
        gap: 24px;
        align-items: flex-start;

        @media (width <= 768px) {
          gap: 16px;
        }

        .avatar-wrapper {
          width: 72px;
          height: 72px;
          overflow: hidden;
          border: 2px solid rgb(64 158 255 / 10%);
          border-radius: 16px;
          box-shadow: 0 2px 8px rgb(0 0 0 / 10%);

          @media (width <= 768px) {
            width: 56px;
            height: 56px;
            border-radius: 12px;
          }

          .user-avatar {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }

        .user-info {
          display: flex;
          flex: 1;
          flex-direction: column;
          gap: 12px;
          padding-top: 4px;
        }
      }

      .date-info {
        display: flex;
        gap: 12px;
        align-items: center;

        @media (width <= 768px) {
          gap: 8px;
        }

        .date {
          font-size: 24px;
          font-weight: 600;
          color: var(--el-color-primary);

          @media (width <= 768px) {
            font-size: 20px;
          }
        }

        .week {
          padding: 4px 10px;
          font-size: 14px;
          color: var(--el-text-color-regular);
          background-color: var(--el-fill-color-light);
          border: 1px solid var(--el-border-color-lighter);
          border-radius: 4px;

          @media (width <= 768px) {
            padding: 2px 8px;
            font-size: 13px;
          }
        }
      }

      .greeting-text {
        font-size: 22px;
        font-weight: 600;
        color: var(--el-text-color-primary);

        @media (width <= 768px) {
          font-size: 18px;
        }

        .nickname {
          position: relative;
          padding: 0 4px;
          font-weight: 700;
          color: var(--el-color-primary);

          &::after {
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 100%;
            height: 2px;
            content: "";
            background-color: var(--el-color-primary-light-8);
            border-radius: 1px;
          }
        }
      }
    }
  }

  .tao-quote {
    width: 320px;
    padding: 20px;
    background-color: var(--el-fill-color-light);
    border: 1px solid var(--el-border-color-lighter);
    border-radius: 6px;
    box-shadow: none;

    @media (width <= 768px) {
      width: 100%;
      padding: 16px;
      margin-top: 8px;
    }

    .quote-content {
      display: flex;
      gap: 12px;

      .quote-icon {
        font-size: 24px;
        color: var(--el-color-primary);
        opacity: 0.6;
      }

      .quote-text {
        flex: 1;
      }
    }

    .main-quote {
      margin-bottom: 8px;
      font-size: 15px;
      font-weight: 500;
      line-height: 1.6;
      color: var(--el-text-color-primary);

      @media (width <= 768px) {
        font-size: 14px;
      }
    }

    .sub-quote {
      margin-bottom: 12px;
      font-size: 14px;
      line-height: 1.6;
      color: var(--el-text-color-regular);

      @media (width <= 768px) {
        margin-bottom: 8px;
        font-size: 13px;
      }
    }

    .quote-source {
      font-size: 12px;
      font-style: italic;
      color: var(--el-text-color-secondary);
      text-align: right;
    }
  }
}

.dashboard-section {
  margin-bottom: 40px;

  @media (width <= 768px) {
    margin-bottom: 30px;
  }

  .section-title {
    margin-bottom: 20px;

    @media (width <= 768px) {
      margin-bottom: 16px;
    }

    .title-row {
      display: flex;
      align-items: center;
      margin-bottom: 6px;
    }

    .month-progress-wrapper {
      display: flex;
      align-items: center;
    }

    .month-progress-text {
      margin-left: 15px;

      @media (width <= 768px) {
        display: block;
        margin-top: 6px;
        margin-left: 0;
      }

      .month-tag {
        height: 24px;
        padding: 0 8px;
        font-size: 12px;
        font-weight: 500;
        line-height: 22px;

        @media (width <= 768px) {
          height: 22px;
          padding: 0 6px;
          font-size: 11px;
          line-height: 20px;
        }
      }
    }

    h3 {
      position: relative;
      padding-left: 10px;
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: var(--el-text-color-primary);
      white-space: nowrap;

      &::before {
        position: absolute;
        top: 50%;
        left: 0;
        width: 3px;
        height: 16px;
        content: "";
        background-color: var(--el-color-primary);
        border-radius: 1px;
        transform: translateY(-50%);
      }
    }

    .subtitle {
      padding-left: 10px;
      margin: 6px 0 0;
      font-size: 13px;
      color: var(--el-text-color-secondary);
    }
  }
}

.asset-section {
  position: relative;
  padding-top: 36px;
  margin-top: 10px;

  @media (width <= 768px) {
    padding-top: 24px;
  }

  &::before {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    height: 1px;
    content: "";
    background: linear-gradient(
      90deg,
      var(--el-color-primary-light-8),
      var(--el-color-primary-light-5) 50%,
      var(--el-color-primary-light-8)
    );
  }
}

.dashboard-card {
  height: 100%;
  background-color: var(--el-bg-color);
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 6px;
  box-shadow: var(--el-box-shadow-light);
  transition: all 0.2s ease;

  &:hover {
    box-shadow: var(--el-box-shadow);
    transform: translateY(-1px);
  }

  .card-link {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: 18px 20px 10px;
    text-decoration: none;

    @media (width <= 768px) {
      padding: 14px 14px 8px;
    }
  }

  .card-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;

    .icon-wrapper {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 36px;
      height: 36px;
      margin-right: 14px;
      background-color: var(--el-color-primary-light-9);
      border: 1px solid var(--el-color-primary-light-8);
      border-radius: 6px;
      transition: all 0.2s ease;

      @media (width <= 768px) {
        width: 32px;
        height: 32px;
        margin-right: 10px;
        border-radius: 4px;
      }

      iconify-icon-online {
        font-size: 20px;
        color: var(--el-color-primary);

        @media (width <= 768px) {
          font-size: 16px;
        }
      }
    }

    .header-content {
      display: flex;
      flex-direction: column;
      gap: 4px;

      .title-wrapper {
        display: flex;
        align-items: center;

        @media (width <= 768px) {
          flex-direction: column;
          align-items: flex-start;
        }
      }

      .month-progress-wrapper {
        display: flex;
        align-items: center;
      }

      .title {
        font-size: 15px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        white-space: nowrap;

        @media (width <= 768px) {
          font-size: 14px;
        }
      }

      .subtitle {
        font-size: 13px;
        color: var(--el-text-color-secondary);

        @media (width <= 768px) {
          font-size: 12px;
        }
      }
    }
  }

  .card-content {
    padding: 14px 0;
    margin-top: auto;

    @media (width <= 768px) {
      padding: 10px 0;
    }

    .account-stat-col,
    .bill-stat-col {
      padding: 16px;
      margin-bottom: 14px;
      border-radius: 6px;
      transition: all 0.2s ease;

      @media (width <= 768px) {
        padding: 12px;
        margin-bottom: 10px;
        border-radius: 4px;
      }

      &:hover {
        background-color: var(--el-color-primary-light-9);
        transform: translateY(-1px);
      }
    }

    .account-stat-col {
      border-right: 1px dashed var(--el-border-color-lighter);

      @media (width <= 768px) {
        &:nth-child(odd) {
          border-right: 1px dashed var(--el-border-color-lighter);
        }

        &:nth-child(even) {
          border-right: none;
        }
      }

      &:last-child,
      &:nth-child(3n) {
        border-right: none;
      }
    }

    .bill-stat-col {
      border-right: 1px dashed var(--el-border-color-lighter);

      &:last-child,
      &:nth-child(2n) {
        border-right: none;
      }
    }

    .stat-row {
      display: flex;
      gap: 20px;
      align-items: center;
      justify-content: space-between;

      @media (width <= 768px) {
        gap: 12px;
      }
    }

    .stat-divider {
      width: 1px;
      height: 36px;
      background: linear-gradient(
        to bottom,
        transparent,
        var(--el-color-primary-light-7),
        transparent
      );

      @media (width <= 768px) {
        height: 30px;
      }
    }

    .stat-item {
      display: flex;
      flex: 1;
      flex-direction: column;
      gap: 6px;
      align-items: center;
      text-align: center;
      transition: all 0.3s ease;

      @media (width <= 768px) {
        gap: 4px;
      }

      &:hover {
        transform: translateY(-2px);
      }
    }

    .stat-number {
      position: relative;
      padding: 0 8px;
      font-family: "SF Mono", Monaco, monospace;
      font-size: 26px;
      font-weight: 600;
      color: var(--el-color-primary);
      transition: all 0.2s ease;

      @media (width <= 768px) {
        padding: 0 4px;
        font-size: 20px;
      }

      &::after {
        position: absolute;
        bottom: -2px;
        left: 0;
        width: 100%;
        height: 1px;
        content: "";
        background-color: var(--el-color-primary-light-8);
        border-radius: 1px;
      }
    }

    .stat-label {
      margin-bottom: 2px;
      font-size: 13px;
      color: var(--el-text-color-secondary);

      @media (width <= 768px) {
        margin-bottom: 1px;
        font-size: 12px;
      }
    }

    .stat-progress {
      width: 100%;
      padding: 0 4px;
      margin-top: 8px;

      @media (width <= 768px) {
        padding: 0 2px;
        margin-top: 6px;
      }
    }
  }

  &.has-pending {
    position: relative;
    border: 1px solid var(--el-color-warning-light-5);
    box-shadow: var(--el-box-shadow-light);

    &::before {
      position: absolute;
      top: 0;
      right: 0;
      left: 0;
      height: 3px;
      content: "";
      background: var(--el-color-warning);
      border-radius: 3px 3px 0 0;
    }

    &:hover {
      box-shadow: var(--el-box-shadow);
    }

    .icon-wrapper {
      background-color: var(--el-color-warning-light-9);
      border-color: var(--el-color-warning-light-7);

      iconify-icon-online {
        color: var(--el-color-warning);
      }
    }

    .stat-number {
      font-weight: 700;
      color: var(--el-color-warning);
      animation: pulse 2s infinite;
    }

    .pending-badge {
      margin-top: 8px;

      @media (width <= 768px) {
        margin-top: 6px;

        :deep(.el-tag) {
          transform: scale(0.85);
          transform-origin: center;
        }
      }
    }
  }
}

.mt-16 {
  margin-top: 24px;

  @media (width <= 768px) {
    margin-top: 16px;
  }
}

.bill-statistic {
  :deep(.el-statistic__content) {
    .el-statistic__value {
      font-family: "SF Mono", Monaco, monospace;
      font-size: 22px;
      font-weight: 600;
      color: var(--el-color-primary);
    }
  }

  :deep(.el-statistic__title) {
    margin-bottom: 6px;
    font-size: 13px;
    color: var(--el-text-color-primary);
  }
}

.stat-item {
  :deep(.el-statistic__content) {
    .el-statistic__value {
      font-family: "SF Mono", Monaco, monospace;
      font-size: 26px;
      font-weight: 600;
      color: var(--el-color-primary);
    }
  }

  :deep(.el-statistic__title) {
    margin-bottom: 6px;
    font-size: 13px;
    color: var(--el-text-color-primary);
  }
}

.last-month {
  margin-top: 8px;
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

.current-month-progress {
  padding: 16px;
  margin-top: 24px;
  background-color: var(--el-fill-color-light);
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 6px;
}

.current-time {
  display: flex;
  gap: 12px;
  align-items: center;
  margin-top: 12px;

  @media (width <= 768px) {
    gap: 8px;
    margin-top: 8px;
  }

  .time-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background-color: var(--el-fill-color-light);
    border: 1px solid var(--el-border-color-lighter);
    border-radius: 4px;

    @media (width <= 768px) {
      width: 28px;
      height: 28px;
      border-radius: 3px;
    }

    iconify-icon-online {
      font-size: 20px;
      color: var(--el-color-primary);

      @media (width <= 768px) {
        font-size: 16px;
      }
    }
  }

  .digital-clock {
    padding: 4px 10px;
    font-family: "SF Mono", Monaco, monospace;
    font-size: 24px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    background-color: var(--el-fill-color-light);
    border: 1px solid var(--el-border-color-lighter);
    border-radius: 4px;

    @media (width <= 768px) {
      padding: 2px 8px;
      font-size: 18px;
      border-radius: 3px;
    }
  }
}

.evaluation-card.has-pending {
  border: 1px solid var(--el-color-danger-light-5);
  box-shadow: var(--el-box-shadow-light);

  &::before {
    background: var(--el-color-danger);
  }

  &:hover {
    box-shadow: var(--el-box-shadow);
  }

  .icon-wrapper {
    background-color: var(--el-color-danger-light-9);
    border-color: var(--el-color-danger-light-7);

    iconify-icon-online {
      color: var(--el-color-danger);
    }
  }

  .stat-number {
    color: var(--el-color-danger);
  }
}

.mb-24 {
  margin-bottom: 32px;
}

.host-stats-header {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 16px;

  .view-options {
    display: flex;
    align-items: center;

    .view-options-label {
      margin-right: 8px;
      font-size: 14px;
      color: var(--el-text-color-secondary);
    }

    .el-radio-group {
      overflow: hidden;
      border: 1px solid var(--el-border-color-lighter);
      border-radius: 4px;

      .el-radio-button__inner {
        padding: 6px 12px;
        font-size: 13px;
      }
    }

    .view-option-content {
      display: flex;
      align-items: center;
      justify-content: center;

      iconify-icon-online {
        margin-right: 4px;
        font-size: 16px;
      }

      .view-option-text {
        @media (width <= 768px) {
          display: none;
        }
      }
    }
  }
}

.host-stats-chart-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 280px;

  @media (width <= 768px) {
    height: 220px;
    margin-bottom: 20px;
  }
}

.host-type-table-cell {
  display: flex;
  gap: 10px;
  align-items: center;

  .host-stat-icon.small {
    width: 28px;
    height: 28px;
    margin-right: 10px;
    border-radius: 4px;

    iconify-icon-online {
      font-size: 16px;
    }
  }
}

.percentage-cell {
  padding: 0 10px;
}

/* 账单表格样式 */
.account-bill-table {
  padding: 10px 0;

  .bill-amount {
    font-family: "SF Mono", Monaco, monospace;
    font-size: 14px;
    font-weight: 600;
  }

  .bill-increase {
    color: #f56c6c;

    iconify-icon-online {
      margin-left: 4px;
      font-size: 14px;
      vertical-align: -2px;
    }
  }

  .bill-decrease {
    color: #67c23a;

    iconify-icon-online {
      margin-left: 4px;
      font-size: 14px;
      vertical-align: -2px;
    }
  }

  .change-rate {
    display: inline-block;
    padding: 4px 8px;
    font-size: 13px;
    font-weight: 600;
    border-radius: 4px;
  }

  .rate-increase {
    color: #f56c6c;
    background-color: rgb(245 108 108 / 10%);
  }

  .rate-decrease {
    color: #67c23a;
    background-color: rgb(103 194 58 / 10%);
  }
}

/* 账单图表卡片样式 */
.nested-card {
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;

    .view-options {
      display: flex;
      align-items: center;

      @media (width <= 768px) {
        margin-top: 10px;
      }
    }
  }
}

.host-type-chart {
  width: 100%;
  height: 100%;
}

.host-stats-list {
  padding: 10px 0;

  .host-stat-item {
    position: relative;
    display: flex;
    align-items: center;
    padding: 12px 10px;
    margin-bottom: 10px;
    border-radius: 6px;
    transition: all 0.3s ease;

    &:hover {
      background-color: var(--el-fill-color-light);
      box-shadow: 0 2px 12px rgb(0 0 0 / 5%);
      transform: translateY(-2px);
    }

    .host-stat-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      margin-right: 14px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgb(0 0 0 / 10%);

      iconify-icon-online {
        font-size: 22px;
        color: #fff;
      }
    }

    .host-stat-info {
      flex: 1;
      min-width: 0;

      .host-stat-name {
        margin-bottom: 6px;
        font-size: 15px;
        font-weight: 600;
        color: var(--el-text-color-primary);
      }

      .host-stat-count {
        display: flex;
        align-items: baseline;

        .host-stat-number {
          margin-right: 10px;
          font-family: "SF Mono", Monaco, monospace;
          font-size: 18px;
          font-weight: 700;
          color: var(--el-text-color-primary);
        }

        .host-stat-percentage {
          padding: 2px 6px;
          font-size: 13px;
          color: var(--el-text-color-secondary);
          background-color: var(--el-fill-color-light);
          border-radius: 10px;
        }
      }
    }

    .host-stat-bar-container {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 4px;
      overflow: hidden;
      background-color: var(--el-fill-color-light);
      border-radius: 0 0 6px 6px;

      .host-stat-bar {
        height: 100%;
        border-radius: 0 4px 4px 0;
        transition: width 0.8s cubic-bezier(0.22, 0.61, 0.36, 1);
      }
    }
  }
}

/* 主机类型颜色 */
.host-type-alibaba {
  background-color: #ff9f43;
}

.host-type-huawei {
  background-color: #ff6b6b;
}

.host-type-tencent {
  background-color: #54a0ff;
}

.host-type-amazon {
  background-color: #ffa502;
}

.host-type-vm {
  background-color: #5f27cd;
}

.host-type-physical {
  background-color: #2ed573;
}

.host-type-default {
  background-color: #4b7bec;
}

/* 主机类型背景颜色 */
.host-type-alibaba-bg {
  background-color: rgb(255 159 67 / 20%);
}

.host-type-huawei-bg {
  background-color: rgb(255 107 107 / 20%);
}

.host-type-tencent-bg {
  background-color: rgb(84 160 255 / 20%);
}

.host-type-amazon-bg {
  background-color: rgb(255 165 2 / 20%);
}

.host-type-vm-bg {
  background-color: rgb(95 39 205 / 20%);
}

.host-type-physical-bg {
  background-color: rgb(46 213 115 / 20%);
}

.host-type-default-bg {
  background-color: rgb(75 123 236 / 20%);
}

/* 账单表格样式 - 额外样式 */
.account-bill-table .no-data-message {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  font-size: 16px;
  color: var(--el-text-color-secondary);
  background-color: var(--el-fill-color-blank);
  border: 1px dashed var(--el-border-color);
  border-radius: 4px;
}

/* 计算资源统计样式 */
.resource-stat-col {
  margin-bottom: 16px;
}

.resource-stat-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;
  background-color: var(--el-bg-color-overlay);
  border-radius: 8px;
  box-shadow: var(--el-box-shadow-light);
  transition: all 0.3s ease;
}

.resource-stat-card:hover {
  box-shadow: var(--el-box-shadow);
  transform: translateY(-5px);
}

.resource-stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  margin-bottom: 12px;
  font-size: 24px;
  color: #409eff;
  background-color: rgb(64 158 255 / 10%);
  border-radius: 50%;
}

/* 所有图标使用统一的蓝色和浅色背景 */
.server-icon,
.cpu-icon,
.memory-icon,
.gpu-icon,
.dli-icon,
.mysql-icon,
.oss-icon,
.obs-icon,
.sfs-icon,
.nas-icon,
.mongodb-icon,
.starrocks-icon,
.clickhouse-icon,
.tidb-icon,
.dli-storage-icon {
  color: #409eff;
  background-color: rgb(64 158 255 / 10%);
}

.resource-unit {
  margin-left: 4px;
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

/* K8s集群统计样式 */
.k8s-stats-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.k8s-stats-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.k8s-stat-col {
  margin-bottom: 16px;
}

.k8s-cluster-card {
  padding: 16px;
  background-color: var(--el-bg-color-overlay);
  border-radius: 8px;
  box-shadow: var(--el-box-shadow-light);
  transition: all 0.3s ease;
}

.k8s-cluster-card:hover {
  box-shadow: var(--el-box-shadow);
  transform: translateY(-5px);
}

.k8s-cluster-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.k8s-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  margin-right: 12px;
  font-size: 20px;
  color: white;
  background-color: #326ce5;
  border-radius: 50%;
}

.k8s-name {
  font-size: 16px;
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.k8s-node-count {
  margin-bottom: 12px;
}

.k8s-unit {
  margin-left: 4px;
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.k8s-progress {
  margin-top: 8px;
}
</style>
