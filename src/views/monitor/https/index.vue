<script setup lang="ts">
import { ref } from "vue";
import { useRole } from "./hook";
import { PureTableBar } from "@/components/RePureTableBar";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";

import Refresh from "@iconify-icons/ep/refresh";
import Plus from "@iconify-icons/ep/plus";
import {
  autoCheckHttpsMonitorAPI,
  autoCreateHttpsMonitorAPI,
  batCheckHttpsMonitorAPI,
  batDeleteHttpsMonitorAPI,
  batSwitchMonitor,
  type DomainHttps
} from "@/api/monitor/https";
import { message } from "@/utils/message";

defineOptions({
  name: "MonitorHttps"
});

const {
  form,
  loading,
  columns,
  dataList,
  pagination,
  onSearch,
  resetForm,
  handleSizeChange,
  handleCurrentChange,
  addFunc
} = useRole();

const formRef = ref();
const tableRef = ref();
const multipleSelection = ref<DomainHttps[]>([]);

// 表格选择处理
const handleSelectionChange = val => {
  multipleSelection.value = val;
};

const batCheck = () => {
  if (multipleSelection.value.length === 0) {
    message("请选择域名", { type: "warning" });
  } else {
    const ids = multipleSelection.value.map(domain => domain.id);
    batCheckHttpsMonitorAPI({
      ids: ids,
      enable_monitor: undefined
    })
      .then(res => {
        if (res.success) {
          message(res.msg, { type: "success" });
          onSearch();
        } else {
          message(res.msg, { type: "error" });
        }
      })
      .catch(error => {
        message(error, { type: "error" });
      });
  }
};

const batDelete = () => {
  if (multipleSelection.value.length === 0) {
    message("请选择域名", { type: "warning" });
  } else {
    const ids = multipleSelection.value.map(domain => domain.id);
    batDeleteHttpsMonitorAPI({
      ids: ids,
      enable_monitor: undefined
    })
      .then(res => {
        if (res.success) {
          message(res.msg, { type: "success" });
          onSearch();
        } else {
          message(res.msg, { type: "error" });
        }
      })
      .catch(error => {
        message(error, { type: "error" });
      });
  }
};

const batSwitch = (enable: boolean) => {
  if (multipleSelection.value.length === 0) {
    message("请选择域名", { type: "warning" });
  } else {
    const ids = multipleSelection.value.map(domain => domain.id);
    batSwitchMonitor({
      ids: ids,
      enable_monitor: enable
    })
      .then(res => {
        if (res.success) {
          message(res.msg, { type: "success" });
          onSearch();
        } else {
          message(res.msg, { type: "error" });
        }
      })
      .catch(error => {
        message(error, { type: "error" });
      });
  }
};

const autoCheckHttpsMonitor = () => {
  autoCheckHttpsMonitorAPI()
    .then(res => {
      if (res.success) {
        message(res.msg, { type: "success" });
        onSearch();
      } else {
        message(res.msg, { type: "error" });
      }
    })
    .catch(error => {
      message(error, { type: "error" });
    });
};

const autoImport = () => {
  autoCreateHttpsMonitorAPI()
    .then(res => {
      if (res.success) {
        message(res.msg, { type: "success" });
        onSearch();
      } else {
        message(res.msg, { type: "error" });
      }
    })
    .catch(error => {
      message(error, { type: "error" });
    });
};
</script>

<template>
  <div class="main">
    <el-card class="search-container" shadow="never">
      <el-form
        ref="formRef"
        :inline="true"
        :model="form"
        class="search-form demo-form-inline"
        @submit.prevent
      >
        <el-form-item label="关键字" prop="keyword">
          <el-input
            v-model="form.keyword"
            placeholder="请输入关键字"
            clearable
            class="input-width"
            @keyup.enter="onSearch"
          />
        </el-form-item>
        <el-form-item label="安全状态" prop="is_secure">
          <el-select
            v-model="form.is_secure"
            placeholder="请选择安全状态"
            clearable
            class="input-width !w-[220px]"
            @change="onSearch"
          >
            <el-option label="安全" value="1" />
            <el-option label="危险" value="2" />
            <el-option label="无法连接" value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="是否监控" prop="enable_monitor">
          <el-select
            v-model="form.enable_monitor"
            placeholder="请选择是否监控"
            clearable
            class="input-width !w-[220px]"
            @change="onSearch"
          >
            <el-option label="是" value="true" />
            <el-option label="否" value="false" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            :icon="useRenderIcon('ri:search-line')"
            :loading="loading"
            @click="onSearch"
          >
            搜索
          </el-button>
          <el-button :icon="useRenderIcon(Refresh)" @click="resetForm(formRef)">
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card class="table-button-container" shadow="never">
      <el-space wrap>
        <el-button
          type="primary"
          :icon="useRenderIcon(Plus)"
          class="add-button"
          style="
            color: white;
            background: linear-gradient(135deg, #4fc3f7 0%, #29b6f6 100%);
            border: none;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgb(79 195 247 / 30%);
            transition: all 0.3s ease;
          "
          @click="addFunc"
        >
          添加
        </el-button>
        <el-button type="primary" plain @click="batCheck"> 批量检查 </el-button>
        <el-button type="danger" plain @click="batDelete"> 批量删除 </el-button>
        <el-button type="warning" plain @click="() => batSwitch(false)">
          批量关闭监控
        </el-button>
        <el-button type="success" plain @click="() => batSwitch(true)">
          批量开启监控
        </el-button>
        <el-button type="info" plain @click="autoCheckHttpsMonitor">
          全部检查并通知
        </el-button>
        <el-button type="warning" plain @click="autoImport">
          自动导入
        </el-button>
      </el-space>
    </el-card>

    <PureTableBar title="HTTPS监控列表" :columns="columns" @refresh="onSearch">
      <template #default="{ size, dynamicColumns }">
        <pure-table
          ref="tableRef"
          row-key="id"
          :loading="loading"
          :size="size"
          :data="dataList"
          :columns="dynamicColumns"
          :pagination="{ ...pagination, size }"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
          :row-style="{ cursor: 'pointer' }"
          border
          stripe
          highlight-current-row
          @page-size-change="handleSizeChange"
          @page-current-change="handleCurrentChange"
          @selection-change="handleSelectionChange"
        />
      </template>
    </PureTableBar>
  </div>
</template>

<style scoped lang="scss">
.search-container {
  margin-bottom: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 8%);
  transition: box-shadow 0.3s ease;

  &:hover {
    box-shadow: 0 6px 20px rgb(0 0 0 / 12%);
  }
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;

  :deep(.el-form-item) {
    margin-bottom: 0;
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: #606266;
  }

  .el-input {
    width: 220px;

    :deep(input) {
      border-color: #e4e7ed;
      border-radius: 8px;

      &:focus {
        border-color: #409eff;
        box-shadow: 0 0 0 1px rgb(64 158 255 / 20%);
      }
    }
  }
}

.table-button-container {
  margin-bottom: 16px;
  border-radius: 12px;
}

.el-button {
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
    transform: translateY(-2px);
  }
}

:deep(.pure-table) {
  overflow: hidden;
  border: 1px solid #e4e7ed;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 4%);
}

:deep(.el-card__body) {
  padding: 20px !important;
}

:deep(.el-space) {
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
}
</style>
