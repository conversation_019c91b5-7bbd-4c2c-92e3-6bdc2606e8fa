import dayjs from "dayjs";
import type { PaginationProps } from "@pureadmin/table";
import { reactive, ref, onMounted, h } from "vue";
import { message } from "@/utils/message";
import { addDrawer } from "@/components/ReDrawer";
import { addDialog } from "@/components/ReDialog";
import Uform from "./Uform.vue";
import {
  addDomainHttpsMonitorAPI,
  deleteHttpsMonitorAPI,
  type DomainHttps,
  updateDomainHttpsMonitorAPI,
  type DomainHttpsForm,
  getHttpsMonitorListAPI
} from "@/api/monitor/https";

export function useRole() {
  const form = reactive({
    keyword: undefined,
    enable_monitor: undefined,
    is_secure: undefined
  });
  const dataList = ref<DomainHttps[]>([]);
  const loading = ref<boolean>(true);

  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true,
    pageSizes: [10, 20, 30, 40, 50, 100]
  });
  const columns: TableColumnList = [
    {
      type: "selection",
      align: "center",
      width: 50
    },
    {
      label: "ID",
      prop: "id",
      minWidth: 80,
      sortable: true
    },
    {
      label: "域名",
      prop: "domain",
      minWidth: 120,
      showOverflowTooltip: true
    },
    {
      label: "端口",
      prop: "port",
      minWidth: 150,
      showOverflowTooltip: true
    },
    {
      label: "安全状态",
      prop: "is_secure",
      minWidth: 150,
      showOverflowTooltip: true,
      cellRenderer: ({ row }) => (
        <el-text
          type={
            row.is_secure === 1
              ? "success"
              : row.is_secure === 2
                ? "danger"
                : "info"
          }
        >
          {row.is_secure === 1 ? "安全" : row.is_secure === 2 ? "危险" : "未知"}
        </el-text>
      )
    },
    {
      label: "监控状态",
      prop: "monitor_status",
      minWidth: 150,
      showOverflowTooltip: true,
      cellRenderer: ({ row }) => (
        <el-text type={row.enable_monitor ? "success" : "danger"}>
          {row.enable_monitor ? "启用" : "禁用"}
        </el-text>
      )
    },
    {
      label: "提前告警天数",
      prop: "alarm_days_before_expired",
      minWidth: 150,
      showOverflowTooltip: true
    },
    {
      label: "证书有效期",
      prop: "not_after",
      minWidth: 250,
      cellRenderer: ({ row }) =>
        dayjs(row.not_before).isValid() && dayjs(row.not_after).isValid() ? (
          <span>{`${dayjs(row.not_before).format("YYYY-MM-DD")} - ${dayjs(row.not_after).format("YYYY-MM-DD")}`}</span>
        ) : (
          "-"
        )
    },
    {
      label: "备注",
      prop: "remark",
      minWidth: 150,
      showOverflowTooltip: true
    },
    {
      label: "更新时间",
      prop: "updated_at",
      minWidth: 160,
      sortable: true,
      cellRenderer: ({ row }) => (
        <el-tooltip
          content={dayjs(row.updated_at).format("YYYY-MM-DD HH:mm:ss")}
          placement="top"
        >
          <span>{dayjs(row.updated_at).format("YYYY-MM-DD HH:mm")}</span>
        </el-tooltip>
      )
    },
    {
      label: "操作",
      prop: "action",
      width: 120,
      cellRenderer: ({ row }) => (
        <div class="flex items-center gap-2">
          <el-button
            type="primary"
            link
            class="action-button"
            onClick={() => updateFunc(row)}
          >
            编辑
          </el-button>
          <el-button
            type="danger"
            link
            class="action-button"
            onClick={() => deleteBusiness(row)}
          >
            删除
          </el-button>
        </div>
      )
    }
  ];
  const editForm = ref<DomainHttpsForm>();
  const childrenRef = ref(null);
  function updateFunc(row: DomainHttps) {
    editForm.value = {
      domain: row.domain,
      port: row.port,
      remark: row.remark,
      enable_monitor: row.enable_monitor,
      alarm_days_before_expired: row.alarm_days_before_expired
    };
    addDrawer({
      headerRenderer: ({ titleId, titleClass }) => (
        // jsx 语法
        <div class="flex flex-row justify-between">
          <h4 id={titleId} class={titleClass}>
            编辑域名
            <b>{row.domain}</b>
          </h4>
        </div>
      ),
      contentRenderer: () =>
        h(Uform, {
          formInline: {
            form: editForm.value
          },
          ref: childrenRef
        }),
      beforeSure: done => {
        if (childrenRef.value?.ruleFormRef) {
          childrenRef.value.ruleFormRef
            .validate((valid: boolean) => {
              if (valid) {
                updateDomainHttpsMonitorAPI(row.id, editForm.value)
                  .then(res => {
                    if (res.success) {
                      message(res.msg, { type: "success" });
                      done();
                      editForm.value = undefined;
                      onSearch();
                    } else {
                      message(res.msg, { type: "error" });
                    }
                  })
                  .catch(error => {
                    message(error, { type: "error" });
                  });
              } else {
                message("请检查表单数据", { type: "error" });
              }
            })
            .catch(error => {
              message("请检查表单数据:" + error, { type: "error" });
            });
        }
      }
    });
  }

  function addFunc() {
    editForm.value = {
      domain: "",
      port: 443,
      remark: "",
      enable_monitor: true,
      alarm_days_before_expired: 30
    };
    addDrawer({
      headerRenderer: ({ titleId, titleClass }) => (
        <div class="flex flex-row justify-between">
          <h4 id={titleId} class={titleClass}>
            添加域名
          </h4>
        </div>
      ),
      contentRenderer: () =>
        h(Uform, {
          formInline: {
            form: editForm.value
          },
          ref: childrenRef
        }),
      beforeSure: done => {
        if (childrenRef.value?.ruleFormRef) {
          childrenRef.value.ruleFormRef.validate((valid: boolean) => {
            if (valid) {
              addDomainHttpsMonitorAPI(editForm.value)
                .then(res => {
                  if (res.success) {
                    message(res.msg, { type: "success" });
                    editForm.value = undefined;
                    onSearch();
                    done();
                  } else {
                    message(res.msg, { type: "error" });
                  }
                })
                .catch(error => {
                  message(error, { type: "error" });
                });
            } else {
              message("请检查表单数据", { type: "error" });
            }
          });
        } else {
          message("请检查表单", { type: "error" });
        }
      }
    });
  }

  function deleteBusiness(row: DomainHttps) {
    addDialog({
      title: "",
      width: 500,
      sureBtnLoading: true,
      contentRenderer: () => (
        <p>
          确认删除：
          <b style="color:red">{row.domain}</b>
        </p>
      ),
      beforeSure: done => {
        deleteHttpsMonitorAPI(row.id)
          .then(res => {
            if (res.success) {
              done();
              message(res.msg, { type: "success" });
              onSearch();
            } else {
              message(res.msg, { type: "error" });
            }
          })
          .catch(error => {
            message(error, { type: "error" });
          });
      }
    });
  }

  function handleSizeChange(val: number) {
    pagination.pageSize = val;
    onSearch();
  }

  function handleCurrentChange(val: number) {
    pagination.currentPage = val;
    onSearch();
  }

  async function onSearch() {
    loading.value = true;
    getHttpsMonitorListAPI({
      page: pagination.currentPage,
      limit: pagination.pageSize,
      keyword: form.keyword,
      enable_monitor: form.enable_monitor,
      is_secure: form.is_secure
    })
      .then(res => {
        if (res.success) {
          dataList.value = res.data;
          pagination.total = res.count;
          pagination.pageSize = pagination.pageSize;
          pagination.currentPage = pagination.currentPage;
        } else {
          dataList.value = [];
          pagination.total = 0;
          pagination.pageSize = pagination.pageSize;
          pagination.currentPage = pagination.currentPage;
        }
      })
      .catch(() => {
        message("请求失败", { type: "error" });
      })
      .finally(() => {
        loading.value = false;
      });
  }

  const resetForm = formEl => {
    if (!formEl) return;
    formEl.resetFields();
    onSearch();
  };

  onMounted(() => {
    onSearch();
  });

  return {
    form,
    loading,
    columns,
    dataList,
    pagination,
    onSearch,
    resetForm,
    handleSizeChange,
    handleCurrentChange,
    addFunc
  };
}
