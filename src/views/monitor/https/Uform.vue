<template>
  <div>
    <el-card shadow="never" class="form-card">
      <el-form
        ref="ruleFormRef"
        label-width="100px"
        style="max-width: 600px; margin: auto"
        :model="newFormInline.form"
        size="large"
        status-icon
        label-position="top"
      >
        <el-form-item
          label="名称"
          prop="domain"
          :rules="[
            { required: true, message: '请输入名称', trigger: 'blur' },
            { max: 255, message: '请输入最大255', trigger: 'blur' }
          ]"
        >
          <el-input
            v-model="newFormInline.form.domain"
            placeholder="请输入名称"
            maxlength="255"
            show-word-limit
            class="input-field"
          />
        </el-form-item>
        <el-form-item
          label="备注"
          prop="remark"
          :rules="[{ max: 255, message: '请输入最大255', trigger: 'blur' }]"
        >
          <el-input
            v-model="newFormInline.form.remark"
            type="textarea"
            placeholder="请输入备注"
            :rows="4"
            maxlength="255"
            show-word-limit
            class="input-field"
          />
        </el-form-item>
        <el-form-item
          label="端口"
          prop="port"
          :rules="[
            { required: true, message: '请输入端口', trigger: 'blur' },
            { type: 'number', message: '端口必须为数字', trigger: 'blur' }
          ]"
        >
          <el-input
            v-model="newFormInline.form.port"
            placeholder="请输入端口"
            type="number"
            class="input-field"
          />
        </el-form-item>
        <el-form-item
          label="监控状态"
          prop="enable_monitor"
          :rules="[
            { required: true, message: '请选择监控状态', trigger: 'change' }
          ]"
        >
          <el-switch
            v-model="newFormInline.form.enable_monitor"
            active-text="启用"
            inactive-text="禁用"
          />
        </el-form-item>
        <el-form-item
          label="提前告警天数"
          prop="alarm_days_before_expired"
          :rules="[
            { required: true, message: '请输入提前告警天数', trigger: 'blur' },
            { type: 'number', message: '天数必须为数字', trigger: 'blur' }
          ]"
        >
          <el-input
            v-model="newFormInline.form.alarm_days_before_expired"
            placeholder="请输入提前告警天数"
            type="number"
            class="input-field"
          />
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { defineProps, ref } from "vue";
import type { FormInstance } from "element-plus";
import type { DomainHttpsForm } from "@/api/monitor/https";
export interface FormProps {
  formInline: {
    form: DomainHttpsForm;
  };
}
const props = withDefaults(defineProps<FormProps>(), {
  formInline: () => ({ row: undefined, form: undefined })
});

const newFormInline = ref(props.formInline);
const ruleFormRef = ref<FormInstance>();
defineExpose({ ruleFormRef });
</script>

<style scoped lang="scss">
.input-field {
  border-radius: 4px; // 自定义输入框圆角
  transition: border-color 0.3s; // 添加过渡效果
}

.input-field:focus {
  border-color: #409eff; // 聚焦时边框颜色
}

.form-card {
  padding: 20px; // 添加内边距
  border-radius: 8px; // 自定义卡片圆角
  box-shadow: 0 2px 8px rgb(0 0 0 / 10%); // 添加阴影效果
}
</style>
