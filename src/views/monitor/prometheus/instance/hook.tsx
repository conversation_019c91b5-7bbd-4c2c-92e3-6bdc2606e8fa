import dayjs from "dayjs";
import {
  getInstanceListAPI,
  createInstanceAPI,
  updateInstanceAPI,
  deleteInstanceAPI,
  type Instance,
  type InstanceForm,
  testInstanceConnectAPI
} from "@/api/monitor/prometheus/instance";
import type { PaginationProps } from "@pureadmin/table";
import { reactive, ref, onMounted, h } from "vue";
import { message } from "@/utils/message";
import { addDrawer } from "@/components/ReDrawer";
import { addDialog } from "@/components/ReDialog";
import Uform from "./Uform.vue";
import { PrometheusInstanceTypes } from "@/config/enum";

export function useRole() {
  const form = reactive({
    keyword: undefined,
    instance_type: undefined
  });
  const dataList = ref<Instance[]>([]);
  const loading = ref(true);

  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true,
    pageSizes: [10, 20, 30, 40, 50, 100]
  });

  const columns: TableColumnList = [
    {
      label: "ID",
      prop: "id",
      minWidth: 50
    },
    {
      label: "名称",
      prop: "name",
      minWidth: 100
    },
    {
      label: "类型",
      prop: "instance_type",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <el-text type="primary">
          {PrometheusInstanceTypes.get(row.instance_type)}
        </el-text>
      )
    },
    {
      label: "地址",
      prop: "datasource",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <el-text type="primary">{row.datasource}</el-text>
      )
    },
    {
      label: "创建时间",
      prop: "created_at",
      minWidth: 120,
      cellRenderer: ({ row }) => (
        <p>{dayjs(row.created_at).format("YYYY-MM-DD HH:mm:ss")}</p>
      )
    },
    {
      label: "更新时间",
      prop: "updated_at",
      minWidth: 120,
      cellRenderer: ({ row }) => (
        <p>{dayjs(row.updated_at).format("YYYY-MM-DD HH:mm:ss")}</p>
      )
    },
    {
      label: "操作",
      prop: "action",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <div style="display: flex; align-items: center">
          <el-button type="primary" link onClick={() => testConnectFunc(row)}>
            <iconify-icon-online icon="ep:refresh" style="margin:3px;" />
            测试连接
          </el-button>
          <el-button type="primary" link onClick={() => updateFunc(row)}>
            <iconify-icon-online icon="ep:edit" style="margin:3px;" />
            编辑
          </el-button>
          <el-button type="danger" link onClick={() => deleteFunc(row)}>
            <iconify-icon-online icon="ep:delete" style="margin:3px;" />
            删除
          </el-button>
        </div>
      )
    }
  ];

  const editForm = ref<InstanceForm>();
  const childrenRef = ref(null);

  function testConnectFunc(row: Instance) {
    testInstanceConnectAPI(row.id)
      .then(res => {
        if (res.success) {
          addDialog({
            title: "测试连接: " + row.name + " (" + row.datasource + ")",
            width: 500,
            hideFooter: true,
            contentRenderer: () => (
              <div>
                <p>
                  <b style="color:green">状态码：{res.data?.responseCode}</b>
                </p>
                <p>
                  <b>消息：{res.data?.result}</b>
                </p>
              </div>
            ),
            beforeSure: done => {
              done();
            }
          });
        } else {
          addDialog({
            title: "测试连接: " + row.name + " (" + row.datasource + ")",
            width: 500,
            hideFooter: true,
            contentRenderer: () => (
              <div>
                <p>
                  <b style="color:red">状态码：{res.data?.responseCode}</b>
                </p>
                <p>
                  <b>
                    消息：{res.msg}
                    {res.data?.result}
                  </b>
                </p>
              </div>
            ),
            beforeSure: done => {
              done();
            }
          });
        }
      })
      .catch(error => {
        message("连接失败:" + error, { type: "error" });
      });
  }
  function updateFunc(row: Instance) {
    editForm.value = {
      name: row.name,
      instance_type: row.instance_type,
      datasource: row.datasource
    };
    addDrawer({
      headerRenderer: ({ titleId, titleClass }) => (
        <div class="flex flex-row justify-between">
          <h4 id={titleId} class={titleClass}>
            编辑 Prometheus 实例
            <b>{row.name}</b>
          </h4>
        </div>
      ),
      contentRenderer: () =>
        h(Uform, {
          formInline: {
            form: editForm.value
          },
          ref: childrenRef
        }),
      beforeSure: done => {
        if (childrenRef.value?.ruleFormRef) {
          childrenRef.value.ruleFormRef
            .validate((valid: boolean) => {
              if (valid) {
                updateInstanceAPI(row.id, editForm.value)
                  .then(res => {
                    if (res.success) {
                      message(res.msg, { type: "success" });
                      done();
                      editForm.value = undefined;
                      onSearch();
                    } else {
                      message(res.msg, { type: "error" });
                    }
                  })
                  .catch(error => {
                    message(error, { type: "error" });
                  });
              } else {
                message("请检查表单数据", { type: "error" });
              }
            })
            .catch(error => {
              message("请检查表单数据:" + error, { type: "error" });
            });
        }
      }
    });
  }

  function addFunc() {
    editForm.value = {
      name: "",
      instance_type: "kubernetes",
      datasource: ""
    };
    addDrawer({
      headerRenderer: ({ titleId, titleClass }) => (
        <div class="flex flex-row justify-between">
          <h4 id={titleId} class={titleClass}>
            添加 Prometheus 实例
          </h4>
        </div>
      ),
      contentRenderer: () =>
        h(Uform, {
          formInline: {
            form: editForm.value
          },
          ref: childrenRef
        }),
      beforeSure: done => {
        if (childrenRef.value?.ruleFormRef) {
          childrenRef.value.ruleFormRef.validate((valid: boolean) => {
            if (valid) {
              createInstanceAPI(editForm.value)
                .then(res => {
                  if (res.success) {
                    message(res.msg, { type: "success" });
                    editForm.value = undefined;
                    onSearch();
                  } else {
                    message(res.msg, { type: "error" });
                  }
                  done();
                })
                .catch(error => {
                  message(error, { type: "error" });
                });
            } else {
              message("请检查表单数据", { type: "error" });
            }
          });
        }
      }
    });
  }

  function deleteFunc(row: Instance) {
    addDialog({
      title: "删除确认",
      contentRenderer: () => (
        <p>
          确认删除:
          <b style="color:red">
            {row.name} ( {row.datasource})
          </b>
        </p>
      ),
      beforeSure: done => {
        deleteInstanceAPI(row.id)
          .then(res => {
            if (res.success) {
              message(res.msg, { type: "success" });
              onSearch();
            } else {
              message(res.msg, { type: "error" });
            }
            done();
          })
          .catch(error => {
            message(error, { type: "error" });
          });
      }
    });
  }

  function onSearch() {
    loading.value = true;
    getInstanceListAPI({
      page: pagination.currentPage,
      limit: pagination.pageSize,
      keyword: form.keyword,
      instance_type: form.instance_type
    })
      .then(res => {
        if (res.success) {
          dataList.value = res.data;
          pagination.total = res.count;
        } else {
          message(res.msg, { type: "error" });
        }
      })
      .catch(error => {
        message(error, { type: "error" });
      })
      .finally(() => {
        loading.value = false;
      });
  }

  function resetForm(formEl) {
    if (!formEl) return;
    formEl.resetFields();
    onSearch();
  }

  function handleSizeChange(val: number) {
    pagination.pageSize = val;
    onSearch();
  }

  function handleCurrentChange(val: number) {
    pagination.currentPage = val;
    onSearch();
  }

  onMounted(() => {
    onSearch();
  });

  return {
    form,
    loading,
    columns,
    dataList,
    pagination,
    onSearch,
    resetForm,
    handleSizeChange,
    handleCurrentChange,
    addFunc
  };
}
