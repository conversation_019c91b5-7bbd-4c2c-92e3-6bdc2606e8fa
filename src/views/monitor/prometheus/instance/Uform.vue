<template>
  <el-form
    ref="ruleFormRef"
    :model="formInline.form"
    :rules="rules"
    label-width="100px"
    class="form"
  >
    <el-form-item label="名称" prop="name">
      <el-input
        v-model="newFormInline.form.name"
        placeholder="请输入名称"
        clearable
      />
    </el-form-item>
    <el-form-item label="地址" prop="datasource">
      <el-input
        v-model="newFormInline.form.datasource"
        placeholder="请输入地址"
        clearable
      />
    </el-form-item>
    <el-form-item label="类型" prop="instance_type">
      <el-select
        v-model="newFormInline.form.instance_type"
        placeholder="请选择类型"
      >
        <el-option
          v-for="(item, key) in PrometheusInstanceTypes"
          :key="key"
          :label="item[1]"
          :value="item[0]"
        />
      </el-select>
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import type { FormInstance } from "element-plus";
import { ref } from "vue";
import type { InstanceForm } from "@/api/monitor/prometheus/instance";
import { PrometheusInstanceTypes } from "@/config/enum";

defineOptions({
  name: "PrometheusInstanceForm"
});

export interface FormProps {
  formInline: {
    form: InstanceForm;
  };
}
const props = withDefaults(defineProps<FormProps>(), {
  formInline: () => ({ row: undefined, form: undefined })
});
const newFormInline = ref(props.formInline);

const ruleFormRef = ref<FormInstance>();

const rules = {
  name: [{ required: true, message: "请输入名称", trigger: "blur" }],
  datasource: [{ required: true, message: "请输入地址", trigger: "blur" }],
  instance_type: [{ required: true, message: "请选择类型", trigger: "change" }]
};

defineExpose({ ruleFormRef });
</script>

<style scoped lang="scss">
.form {
  padding: 20px;

  :deep(.el-form-item) {
    margin-bottom: 20px;
  }

  :deep(.el-input),
  :deep(.el-select) {
    width: 100%;
  }

  :deep(.el-textarea__inner) {
    min-height: 80px;
  }
}
</style>
