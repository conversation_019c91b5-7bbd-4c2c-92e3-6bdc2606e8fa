<template>
  <div class="main">
    <el-card class="search-card">
      <el-form
        ref="formRef"
        :inline="true"
        :model="form"
        class="search-form"
        @submit.prevent
      >
        <el-form-item label="阀值（%）" prop="threshold">
          <el-input-number
            v-model="form.threshold"
            placeholder="请输入阀值"
            clearable
            :min="0"
            :max="100"
            :step="5"
            class="threshold-input"
            @keyup.enter="onSearch"
          />
        </el-form-item>
        <el-form-item label="主机状态">
          <el-select
            v-model="form.status"
            placeholder="选择主机状态"
            clearable
            class="status-select"
            @change="onSearch"
          >
            <el-option
              v-for="[value, label] in Array.from(HostStatus.entries())"
              :key="value"
              :label="label"
              :value="value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            unlink-panels
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :shortcuts="shortcuts"
            value-format="YYYY-MM-DD"
            class="date-range-picker"
            @change="handleDateChange"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            :icon="useRenderIcon('ri:search-line')"
            :loading="loading"
            class="search-button"
            style="
              background: linear-gradient(135deg, #409eff 0%, #4facff 100%);
              border: none;
              box-shadow: 0 4px 12px rgb(64 158 255 / 30%);
              transition: all 0.3s ease;
            "
            @click="onSearch"
          >
            搜索
          </el-button>
          <el-button
            :icon="useRenderIcon(Refresh)"
            class="reset-button"
            style="
              color: #606266;
              border-color: #dcdfe6;
              transition: all 0.3s ease;
            "
            @click="resetForm(formRef)"
          >
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <PureTableBar title="低使用率主机" :columns="columns" @refresh="onSearch">
      <template #title>
        <div class="custom-title">
          <i class="title-icon ri:computer-line"></i>
          <span>低使用率主机</span>
          <el-tag size="large" effect="plain" type="danger" class="title-tag">
            阀值: {{ form.threshold }}%
          </el-tag>
          <el-tag size="large" effect="plain" type="danger" class="title-tag">
            {{ dateRange[0] }} - {{ dateRange[1] }}
          </el-tag>
        </div>
      </template>
      <template v-slot="{ size, dynamicColumns }">
        <pure-table
          ref="tableRef"
          row-key="id"
          align-whole="left"
          table-layout="auto"
          :loading="loading"
          :size="size"
          :minHeight="500"
          :data="dataList"
          :columns="dynamicColumns"
          :pagination="{ ...pagination, size }"
          :header-cell-style="{
            fontWeight: '600',
            backgroundColor: '#f5f7fa',
            color: '#606266',
            borderBottom: '1px solid #EBEEF5',
            padding: '12px 10px',
            textAlign: 'left'
          }"
          :row-style="{
            cursor: 'pointer',
            transition: 'background-color 0.2s ease',
            borderBottom: '1px solid #EBEEF5'
          }"
          :cell-style="{
            padding: '12px 10px'
          }"
          border
          stripe
          highlight-current-row
          @page-size-change="handleSizeChange"
          @page-current-change="handleCurrentChange"
        >
          <template #empty>
            <el-empty
              description="暂无数据"
              :image-size="120"
              style="padding: 40px 0"
            />
          </template>
        </pure-table>
      </template>
    </PureTableBar>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useRole } from "./hook";
import { PureTableBar } from "@/components/RePureTableBar";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import Refresh from "@iconify-icons/ep/refresh";
import dayjs from "dayjs";
import { HostStatus } from "@/config/enum";

defineOptions({
  name: "PrometheusInstance"
});

const formRef = ref();
const tableRef = ref();
const dateRange = ref([]);

// 快速选择选项
const shortcuts = [
  {
    text: "最近一周",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    }
  },
  {
    text: "最近一个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    }
  },
  {
    text: "最近三个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      return [start, end];
    }
  },
  {
    text: "最近六个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 180);
      return [start, end];
    }
  },
  {
    text: "最近一年",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 365);
      return [start, end];
    }
  }
];

const {
  form,
  loading,
  columns,
  dataList,
  pagination,
  onSearch,
  resetForm,
  handleSizeChange,
  handleCurrentChange
} = useRole();

// 处理日期变化
function handleDateChange(val) {
  if (val) {
    form.start_time = val[0];
    form.end_time = val[1];
  } else {
    form.start_time = "";
    form.end_time = "";
  }
  onSearch(); // 自动搜索
}

// 组件挂载时设置默认日期范围
onMounted(() => {
  // 设置默认日期范围为最近7天
  const start = dayjs().subtract(7, "days").format("YYYY-MM-DD");
  const end = dayjs().format("YYYY-MM-DD");
  dateRange.value = [start, end];
  form.start_time = start;
  form.end_time = end;
  onSearch();
});
</script>

<style lang="scss" scoped>


/* 响应式调整 */
@media (width <= 1200px) {
  .search-form {
    gap: 10px;
  }

  .date-range-picker {
    width: 320px;
  }
}

@media (width <= 992px) {
  .date-range-picker {
    width: 280px;
  }

  .status-select {
    width: 130px;
  }
}

.main {
  margin: 10px;
}

.search-card {
  margin-bottom: 10px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 5%);
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
  padding: 5px 0;
}

:deep(.el-form-item) {
  margin-right: 0;
  margin-bottom: 0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-input__wrapper),
:deep(.el-input-number__wrapper),
:deep(.el-select__wrapper),
:deep(.el-date-editor) {
  border-radius: 4px;
  box-shadow: 0 0 0 1px #dcdfe6 inset;
  transition: all 0.2s;
}

:deep(.el-input__wrapper:hover),
:deep(.el-input-number__wrapper:hover),
:deep(.el-select__wrapper:hover),
:deep(.el-date-editor:hover) {
  box-shadow: 0 0 0 1px #c0c4cc inset;
}

:deep(.el-input__wrapper:focus-within),
:deep(.el-input-number__wrapper:focus-within),
:deep(.el-select__wrapper:focus-within),
:deep(.el-date-editor:focus-within) {
  box-shadow: 0 0 0 1px #409eff inset !important;
}

.search-button {
  margin-right: 10px;
  border-radius: 6px;
  transition: all 0.3s;
}

.threshold-input {
  width: 120px;
}

.status-select {
  width: 150px;
}

.date-range-picker {
  width: 360px;
}

/* 主机信息美化 */
:deep(.host-info) {
  display: flex;
  flex-direction: column;
  gap: 6px;
  padding: 8px;
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.2s ease;
}

:deep(.host-info:hover) {
  background-color: #f0f7ff;
  box-shadow: 0 2px 12px rgb(0 0 0 / 10%);
  transform: translateY(-2px);
}

:deep(.host-name) {
  display: flex;
  gap: 6px;
  align-items: center;
  padding: 0 2px;
  overflow: hidden;
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  text-overflow: ellipsis;
  white-space: nowrap;
}

:deep(.status-tag) {
  height: 20px;
  padding: 0 6px;
  margin-left: 6px;
  font-size: 10px;
  line-height: 18px;
  border-radius: 10px;
}

:deep(.chart-icon) {
  font-size: 16px;
  color: #409eff;
}

:deep(.host-ip-info) {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 6px 10px;
  font-size: 12px;
  background-color: #f8f8f8;
  border-radius: 4px;
}

:deep(.ip-item) {
  display: flex;
  align-items: center;
  font-weight: bolder;
}

:deep(.ip-label) {
  flex-shrink: 0;
  width: 40px;
  color: #909399;
}

:deep(.ip-value) {
  font-weight: bolder;
  color: #606266;
}

/* 表格样式优化 */
:deep(.el-table) {
  overflow: hidden;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 5%);
  transition: all 0.3s ease;
}

:deep(.el-table:hover) {
  box-shadow: 0 4px 16px 0 rgb(0 0 0 / 8%);
}

:deep(.el-table__row:hover) {
  background-color: #f0f7ff !important;
}

:deep(.el-table__row.current-row) {
  background-color: #ecf5ff !important;
}

:deep(.el-table__header) {
  font-size: 14px;
}

:deep(.el-table__body) {
  font-size: 13px;
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background-color: #fafafa;
}

:deep(.el-table__fixed-right-patch) {
  background-color: #f5f7fa;
}

:deep(.el-pagination) {
  justify-content: flex-end;
  padding: 10px 15px;
  margin-top: 16px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 5%);
  transition: all 0.3s ease;
}

:deep(.el-pagination:hover) {
  box-shadow: 0 4px 16px 0 rgb(0 0 0 / 8%);
}

:deep(.el-pagination .el-pagination__sizes .el-input .el-input__inner) {
  border-radius: 16px;
}

:deep(.el-pagination .btn-prev),
:deep(.el-pagination .btn-next) {
  background-color: #f4f4f5;
  border-radius: 50%;
  transition: all 0.2s ease;
}

:deep(.el-pagination .btn-prev:hover),
:deep(.el-pagination .btn-next:hover) {
  color: #409eff;
  background-color: #e6f1fc;
}

:deep(.el-pagination .el-pager li) {
  min-width: 30px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

:deep(.el-pagination .el-pager li:hover) {
  background-color: #e6f1fc;
}

:deep(.el-pagination .el-pager li.active) {
  color: #fff;
  background-color: #409eff;
}

/* 使用率详情样式 */
:deep(.usage-container) {
  display: flex;
  flex-direction: column;
  gap: 8px;
  font-weight: bolder;
}

:deep(.usage-details) {
  display: flex;
  justify-content: flex-end;
}

:deep(.usage-values) {
  display: inline-block;
  padding: 2px 8px;
  font-size: 12px;
  font-weight: bolder;
  color: #606266;
  background-color: #f8f8f8;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgb(0 0 0 / 5%);
  transition: all 0.2s ease;
}

:deep(.usage-values:hover) {
  background-color: #eef5fe;
  box-shadow: 0 2px 4px rgb(0 0 0 / 10%);
}

:deep(.el-progress) {
  margin-bottom: 0;
}

:deep(.el-progress-bar__outer) {
  overflow: hidden;
  background-color: #ebeef5;
  border-radius: 4px;
}

:deep(.el-progress-bar__inner) {
  position: relative;
  overflow: hidden;
  border-radius: 4px;
  transition: width 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
}

:deep(.el-progress-bar__inner)::after {
  position: absolute;
  inset: 0;
  content: "";
  background: linear-gradient(
    90deg,
    rgb(255 255 255 / 0%) 0%,
    rgb(255 255 255 / 20%) 50%,
    rgb(255 255 255 / 0%) 100%
  );
}

:deep(.el-progress--line .el-progress-bar__innerText) {
  font-size: 12px;
  font-weight: 500;
}

/* 自定义标题栏样式 */
.custom-title {
  display: flex;
  gap: 8px;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
}

.title-icon {
  margin-right: 4px;
  font-size: 18px;
}

.title-tag {
  padding: 2px 8px;
  margin-left: 8px;
  font-size: 12px;
  font-weight: 500;
  color: #606266;
  background-color: #f8f8f8;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}
</style>
