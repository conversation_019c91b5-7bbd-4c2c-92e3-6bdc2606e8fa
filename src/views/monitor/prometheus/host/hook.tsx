import {
  getLowUsageHostsAPI,
  type HostMetric
} from "@/api/monitor/prometheus/host";
import type { PaginationProps } from "@pureadmin/table";
import { reactive, ref, onMounted, h } from "vue";
import { message } from "@/utils/message";
import dayjs from "dayjs";
import { addDrawer } from "@/components/ReDrawer";
import HostMetrics from "./Metrics.vue";
import { HostStatus, HostStatusColors } from "@/config/enum";

export function useRole() {
  // 设置默认时间范围为最近7天
  const defaultStartTime = dayjs()
    .subtract(7, "day")
    .startOf("day")
    .format("YYYY-MM-DD");
  const defaultEndTime = dayjs().endOf("day").format("YYYY-MM-DD");

  const form = reactive({
    start_time: defaultStartTime,
    end_time: defaultEndTime,
    threshold: 10,
    status: 1
  });
  const dataList = ref<HostMetric[]>([]);
  const loading = ref(true);

  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true,
    pageSizes: [10, 20, 30, 40, 50, 100, 200, 300, 500, 1000]
  });

  const columns: TableColumnList = [
    {
      label: "主机",
      prop: "host",
      minWidth: 150,
      filters: Array.from(HostStatus.entries()).map(([value, label]) => ({
        text: label,
        value: String(value)
      })),
      filterMethod: (value, row) => {
        return row.host?.status === Number(value);
      },
      cellRenderer: ({ row }) => (
        <div class="host-info" onClick={() => openHostMetricsDrawer(row)}>
          <div class="host-name">
            <i class="ri:line-chart-line chart-icon"></i>
            {row.host?.name || "未命名主机"}
            {row.host?.status && (
              <el-tag
                size="small"
                type={HostStatusColors.get(row.host.status) || "info"}
                class="status-tag"
              >
                {HostStatus.get(row.host.status) || "未知状态"}
              </el-tag>
            )}
          </div>
          <div class="host-ip-info">
            <div class="ip-item">
              <span class="ip-label">内网:</span>
              <span class="ip-value">{row.host?.ip || row.ip || "-"}</span>
            </div>
            {row.host?.public_ip && (
              <div class="ip-item">
                <span class="ip-label">公网:</span>
                <span class="ip-value">{row.host?.public_ip}</span>
              </div>
            )}
          </div>
        </div>
      )
    },
    {
      label: "CPU使用率",
      prop: "max_cpu_usage",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <div class="usage-container">
          <el-progress
            type="line"
            percentage={Number(row.max_cpu_usage).toFixed(2)}
            stroke-width={10}
          />
          <div class="usage-details">
            <div class="usage-values">
              {Number(row.max_cpu_usage).toFixed(2)}% /{" "}
              {Number(row.avg_cpu_usage).toFixed(2)}% /{" "}
              {Number(row.min_cpu_usage).toFixed(2)}%
            </div>
          </div>
        </div>
      )
    },
    {
      label: "内存使用率",
      prop: "max_memory_usage",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <div class="usage-container">
          <el-progress
            type="line"
            percentage={Number(row.max_memory_usage).toFixed(2)}
            stroke-width={10}
          />
          <div class="usage-details">
            <div class="usage-values">
              {Number(row.max_memory_usage).toFixed(2)}% /{" "}
              {Number(row.avg_memory_usage).toFixed(2)}% /{" "}
              {Number(row.min_memory_usage).toFixed(2)}%
            </div>
          </div>
        </div>
      )
    },
    {
      label: "IO使用率",
      prop: "max_io_usage",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <div class="usage-container">
          <el-progress
            type="line"
            percentage={Number(row.max_io_usage).toFixed(2)}
            stroke-width={10}
          />
          <div class="usage-details">
            <div class="usage-values">
              {Number(row.max_io_usage).toFixed(2)}% /{" "}
              {Number(row.avg_io_usage).toFixed(2)}% /{" "}
              {Number(row.min_io_usage).toFixed(2)}%
            </div>
          </div>
        </div>
      )
    },
    {
      label: "负载使用率",
      prop: "max_load_usage",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <div class="usage-container">
          <el-progress
            type="line"
            percentage={Number(row.max_load_usage).toFixed(2)}
            stroke-width={10}
          />
          <div class="usage-details">
            <div class="usage-values">
              {Number(row.max_load_usage).toFixed(2)}% /{" "}
              {Number(row.avg_load_usage).toFixed(2)}% /{" "}
              {Number(row.min_load_usage).toFixed(2)}%
            </div>
          </div>
        </div>
      )
    }
  ];

  function onSearch() {
    loading.value = true;
    getLowUsageHostsAPI({
      start_time: form.start_time,
      end_time: form.end_time,
      threshold: form.threshold,
      status: form.status,
      page: pagination.currentPage,
      limit: pagination.pageSize
    })
      .then(res => {
        if (res.success) {
          dataList.value = res.data;
          pagination.total = res.count;
        } else {
          message(res.msg, { type: "error" });
        }
      })
      .catch(error => {
        message(error, { type: "error" });
      })
      .finally(() => {
        loading.value = false;
      });
  }

  function resetForm(formEl) {
    if (!formEl) return;
    formEl.resetFields();
    onSearch();
  }

  function handleSizeChange(val: number) {
    pagination.pageSize = val;
    onSearch();
  }

  function handleCurrentChange(val: number) {
    pagination.currentPage = val;
    onSearch();
  }

  // 打开主机指标图表抽屉
  function openHostMetricsDrawer(host: HostMetric) {
    addDrawer({
      title: `${host.host?.name || host.ip} 的使用率指标`,
      size: "90%",
      contentRenderer: () =>
        h(HostMetrics, {
          ip: host.host?.ip || host.ip
        })
    });
  }

  onMounted(() => {
    onSearch();
  });

  return {
    form,
    loading,
    columns,
    dataList,
    pagination,
    onSearch,
    resetForm,
    handleSizeChange,
    handleCurrentChange,
    openHostMetricsDrawer
  };
}
