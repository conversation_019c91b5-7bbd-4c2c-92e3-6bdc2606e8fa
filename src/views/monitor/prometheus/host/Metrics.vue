<template>
  <div class="metrics-container">
    <div class="search-bar">
      <el-form ref="searchFormRef" :inline="true" :model="searchForm">
        <div class="flex justify-between items-center">
          <div class="flex items-center gap-2">
            <el-form-item label="IP地址">
              <el-input
                v-model="searchForm.ip"
                placeholder="请输入IP地址"
                clearable
                @clear="handleSearch"
              />
            </el-form-item>
            <el-form-item label="时间范围">
              <el-date-picker
                v-model="dateRange"
                type="daterange"
                unlink-panels
                value-format="YYYY-MM-DD"
                format="YYYY-MM-DD"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :shortcuts="dateShortcuts"
                :disabled-date="disabledDate"
                @change="handleDateChange"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" :icon="Search" @click="handleSearch"
                >搜索</el-button
              >
              <el-button :icon="Delete" @click="handleReset">重置</el-button>
            </el-form-item>
          </div>
          <div>
            <el-button :icon="Refresh" @click="handleRefresh">刷新</el-button>
          </div>
        </div>
      </el-form>
    </div>

    <div v-if="!searchForm.ip" class="empty-tip">
      <el-empty description="请输入IP地址" />
    </div>

    <div v-else-if="!hasData" class="empty-tip">
      <el-empty description="暂无数据" />
    </div>

    <div v-else class="charts-container">
      <!-- CPU使用率图表 -->
      <div class="chart-item">
        <div class="chart-header">
          <div class="chart-title">CPU使用率趋势</div>
          <div class="chart-tools">
            <el-radio-group
              v-model="chartTypes.cpu"
              size="small"
              @change="handleChartTypeChange('cpu')"
            >
              <el-radio-button label="line">折线图</el-radio-button>
              <el-radio-button label="area">面积图</el-radio-button>
            </el-radio-group>
            <el-divider direction="vertical" />
            <el-tooltip content="切换平滑曲线" placement="top">
              <el-switch
                v-model="chartStyles.cpu.smooth"
                size="small"
                @change="handleStyleChange('cpu')"
              />
            </el-tooltip>
          </div>
        </div>
        <div ref="cpuChart" class="chart"></div>
      </div>

      <!-- 负载使用率图表 -->
      <div class="chart-item">
        <div class="chart-header">
          <div class="chart-title">负载使用率趋势</div>
          <div class="chart-tools">
            <el-radio-group
              v-model="chartTypes.load"
              size="small"
              @change="handleChartTypeChange('load')"
            >
              <el-radio-button label="line">折线图</el-radio-button>
              <el-radio-button label="area">面积图</el-radio-button>
            </el-radio-group>
            <el-divider direction="vertical" />
            <el-tooltip content="切换平滑曲线" placement="top">
              <el-switch
                v-model="chartStyles.load.smooth"
                size="small"
                @change="handleStyleChange('load')"
              />
            </el-tooltip>
          </div>
        </div>
        <div ref="loadChart" class="chart"></div>
      </div>

      <!-- 内存使用率图表 -->
      <div class="chart-item">
        <div class="chart-header">
          <div class="chart-title">内存使用率趋势</div>
          <div class="chart-tools">
            <el-radio-group
              v-model="chartTypes.memory"
              size="small"
              @change="handleChartTypeChange('memory')"
            >
              <el-radio-button label="line">折线图</el-radio-button>
              <el-radio-button label="area">面积图</el-radio-button>
            </el-radio-group>
            <el-divider direction="vertical" />
            <el-tooltip content="切换平滑曲线" placement="top">
              <el-switch
                v-model="chartStyles.memory.smooth"
                size="small"
                @change="handleStyleChange('memory')"
              />
            </el-tooltip>
          </div>
        </div>
        <div ref="memoryChart" class="chart"></div>
      </div>

      <!-- IO使用率图表 -->
      <div class="chart-item">
        <div class="chart-header">
          <div class="chart-title">IO使用率趋势</div>
          <div class="chart-tools">
            <el-radio-group
              v-model="chartTypes.io"
              size="small"
              @change="handleChartTypeChange('io')"
            >
              <el-radio-button label="line">折线图</el-radio-button>
              <el-radio-button label="area">面积图</el-radio-button>
            </el-radio-group>
            <el-divider direction="vertical" />
            <el-tooltip content="切换平滑曲线" placement="top">
              <el-switch
                v-model="chartStyles.io.smooth"
                size="small"
                @change="handleStyleChange('io')"
              />
            </el-tooltip>
          </div>
        </div>
        <div ref="ioChart" class="chart"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from "vue";
import { ElMessage } from "element-plus";
import * as echarts from "echarts";
import { Search, Refresh, Delete } from "@element-plus/icons-vue";
import { getHostMetricsAPI } from "@/api/monitor/prometheus/host";
import type { HostMetric } from "@/api/monitor/prometheus/host";
import type { FormInstance } from "element-plus";

const props = defineProps<{
  ip?: string;
}>();

// 搜索表单引用
const searchFormRef = ref<FormInstance>();

// 是否有数据标志
const hasData = ref(false);

// 搜索表单
const searchForm = ref({
  ip: props.ip || "",
  start_time: "",
  end_time: ""
});

// 日期范围
const dateRange = ref<[string, string]>(["", ""]);
const dateShortcuts = [
  {
    text: "最近7天",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    }
  },
  {
    text: "最近30天",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    }
  },
  {
    text: "最近90天",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      return [start, end];
    }
  },
  {
    text: "最近120天",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 120);
      return [start, end];
    }
  },
  {
    text: "最近半年",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 180);
      return [start, end];
    }
  },
  {
    text: "最近一年",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 365);
      return [start, end];
    }
  }
];

// 图表实例
const cpuChart = ref<HTMLElement>();
const loadChart = ref<HTMLElement>();
const memoryChart = ref<HTMLElement>();
const ioChart = ref<HTMLElement>();
let cpuChartInstance: echarts.ECharts | null = null;
let loadChartInstance: echarts.ECharts | null = null;
let memoryChartInstance: echarts.ECharts | null = null;
let ioChartInstance: echarts.ECharts | null = null;

// 图表基础配置
const getBaseChartOption = () => ({
  tooltip: {
    trigger: "axis",
    formatter: (params: any) => {
      let result = `<div class="font-medium text-gray-600 mb-1">${params[0].axisValue}</div>`;
      params.forEach((item: any) => {
        const color = item.color;
        result += `
          <div class="flex items-center gap-2 mb-1">
            <span style="display:inline-block;width:10px;height:10px;border-radius:50%;background-color:${color}"></span>
            <span class="text-gray-600">${item.seriesName}:</span>
            <span class="font-medium">${item.value}%</span>
          </div>`;
      });
      return result;
    },
    backgroundColor: "rgba(255, 255, 255, 0.95)",
    borderColor: "#eee",
    borderWidth: 1,
    padding: [10, 15],
    textStyle: {
      color: "#333"
    },
    axisPointer: {
      type: "line",
      lineStyle: {
        color: "rgba(0, 0, 0, 0.1)",
        width: 1,
        type: "dashed"
      }
    }
  },
  legend: {
    data: ["最大使用率", "平均使用率", "最小使用率"],
    bottom: 0,
    icon: "circle",
    itemWidth: 8,
    itemHeight: 8,
    itemGap: 15,
    textStyle: {
      color: "#666",
      fontSize: 12
    }
  },
  grid: {
    left: "8%",
    right: "4%",
    bottom: "60px",
    top: "20px",
    containLabel: true
  },
  xAxis: {
    type: "category",
    boundaryGap: false,
    axisLine: {
      lineStyle: {
        color: "#eee"
      }
    },
    axisTick: {
      show: false
    },
    axisLabel: {
      rotate: 45,
      color: "#666",
      fontSize: 12,
      margin: 12
    }
  },
  yAxis: {
    type: "value",
    name: "使用率(%)",
    nameLocation: "middle",
    nameGap: 50,
    nameRotate: 90,
    nameTextStyle: {
      color: "#666",
      fontSize: 14,
      fontWeight: "bold",
      padding: [0, 0, 0, 0],
      align: "center",
      verticalAlign: "middle"
    },
    min: 0,
    max: 100,
    splitNumber: 5,
    splitLine: {
      lineStyle: {
        type: "dashed",
        color: "#eee"
      }
    },
    axisLine: {
      show: true,
      lineStyle: {
        color: "#ddd"
      }
    },
    axisTick: {
      show: true,
      length: 6,
      lineStyle: {
        color: "#ddd"
      }
    },
    axisLabel: {
      color: "#666",
      fontSize: 12,
      margin: 12,
      formatter: "{value}%"
    }
  },
  series: [
    {
      symbolSize: 6,
      symbol: "circle",
      showSymbol: false,
      sampling: "average",
      animation: true,
      lineStyle: {
        width: 2
      },
      emphasis: {
        focus: "series",
        itemStyle: {
          borderWidth: 3
        }
      },
      markPoint: {
        symbol: "arrow",
        symbolSize: [10, 15],
        symbolOffset: [0, 0],
        itemStyle: {
          color: "auto"
        },
        label: {
          show: false
        }
      }
    }
  ]
});

// 禁用未来日期
const disabledDate = (time: Date) => {
  return time.getTime() > Date.now();
};

// 图表类型
const chartTypes = ref({
  cpu: "area",
  load: "area",
  memory: "area",
  io: "area"
});

// 图表样式
const chartStyles = ref({
  cpu: { smooth: true },
  load: { smooth: true },
  memory: { smooth: true },
  io: { smooth: true }
});

// 获取数据
const fetchData = async () => {
  if (!searchForm.value.ip) {
    ElMessage.warning("请输入IP地址");
    hasData.value = false;
    return;
  }

  try {
    console.log("开始获取数据，参数:", {
      ip: searchForm.value.ip,
      start_time: searchForm.value.start_time,
      end_time: searchForm.value.end_time
    });

    const res = await getHostMetricsAPI({
      ip: searchForm.value.ip,
      start_time: searchForm.value.start_time,
      end_time: searchForm.value.end_time
    });

    console.log("获取到的原始数据:", res);

    if (res.success && res.data && res.data.length > 0) {
      hasData.value = true;
      await nextTick();

      // 确保图表实例存在
      if (
        !cpuChartInstance ||
        !loadChartInstance ||
        !memoryChartInstance ||
        !ioChartInstance
      ) {
        console.log("重新初始化图表");
        await initCharts();
      }

      // 隐藏所有图表的加载状态
      cpuChartInstance?.hideLoading();
      loadChartInstance?.hideLoading();
      memoryChartInstance?.hideLoading();
      ioChartInstance?.hideLoading();

      // 更新图表数据
      await nextTick();
      updateCharts(res.data);
    } else {
      hasData.value = false;
      ElMessage.warning(res.msg || "暂无数据");
    }
  } catch (error) {
    console.error("获取数据失败:", error);
    hasData.value = false;
    ElMessage.error("获取数据失败");
  }
};

// 更新图表数据
const updateCharts = (data: HostMetric[]) => {
  console.log("开始更新图表，数据长度:", data.length);

  const dates = data.map(item => {
    const date = new Date(item.stat_day);
    return `${date.getMonth() + 1}-${date.getDate()}`;
  });

  console.log("处理后的日期数据:", dates);

  // 图表配色方案
  const colors = {
    max: {
      line: "#409EFF",
      area: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
        { offset: 0, color: "rgba(64, 158, 255, 0.2)" },
        { offset: 1, color: "rgba(64, 158, 255, 0)" }
      ])
    },
    avg: {
      line: "#67C23A",
      area: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
        { offset: 0, color: "rgba(103, 194, 58, 0.2)" },
        { offset: 1, color: "rgba(103, 194, 58, 0)" }
      ])
    },
    min: {
      line: "#E6A23C",
      area: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
        { offset: 0, color: "rgba(230, 162, 60, 0.2)" },
        { offset: 1, color: "rgba(230, 162, 60, 0)" }
      ])
    }
  };

  // 修改 getSeriesConfig 函数
  const getSeriesConfig = (
    name: string,
    data: number[],
    colorType: "max" | "avg" | "min",
    chartType: string,
    isSmooth: boolean
  ) => {
    const baseConfig: any = {
      name,
      type: chartType === "area" ? "line" : chartType,
      data: data.map(val => Number(val).toFixed(2)),
      smooth: isSmooth,
      symbol: "circle",
      symbolSize: 6,
      showSymbol: false,
      sampling: "average",
      animation: true,
      lineStyle: {
        width: 2,
        color: colors[colorType].line
      },
      itemStyle: {
        color: colors[colorType].line,
        borderWidth: 2,
        borderColor: "#fff"
      },
      emphasis: {
        focus: "series",
        scale: true,
        itemStyle: {
          borderWidth: 3
        }
      }
    };

    // 面积图样式
    if (chartType === "area") {
      baseConfig.areaStyle = {
        color: colors[colorType].area,
        opacity: colorType === "max" ? 1 : 0.3
      };
    }

    return baseConfig;
  };

  // CPU使用率图表
  if (cpuChartInstance) {
    console.log("更新CPU图表");
    const cpuOption = {
      ...getBaseChartOption(),
      xAxis: {
        ...getBaseChartOption().xAxis,
        data: dates
      },
      series: [
        getSeriesConfig(
          "最大使用率",
          data.map(item => item.max_cpu_usage),
          "max",
          chartTypes.value.cpu,
          chartStyles.value.cpu.smooth
        ),
        getSeriesConfig(
          "平均使用率",
          data.map(item => item.avg_cpu_usage),
          "avg",
          chartTypes.value.cpu,
          chartStyles.value.cpu.smooth
        ),
        getSeriesConfig(
          "最小使用率",
          data.map(item => item.min_cpu_usage),
          "min",
          chartTypes.value.cpu,
          chartStyles.value.cpu.smooth
        )
      ]
    };
    console.log("CPU图表配置:", cpuOption);
    cpuChartInstance.setOption(cpuOption, true);
  }

  // 负载使用率图表
  if (loadChartInstance) {
    console.log("更新负载图表");
    loadChartInstance.setOption(
      {
        ...getBaseChartOption(),
        xAxis: {
          ...getBaseChartOption().xAxis,
          data: dates
        },
        yAxis: {
          ...getBaseChartOption().yAxis,
          name: "负载(%)",
          min: 0,
          max: null,
          axisLabel: {
            ...getBaseChartOption().yAxis.axisLabel,
            formatter: "{value}%"
          }
        },
        tooltip: {
          ...getBaseChartOption().tooltip,
          formatter: (params: any) => {
            let result = `<div class="font-medium text-gray-600 mb-1">${params[0].axisValue}</div>`;
            params.forEach((item: any) => {
              const color = item.color;
              result += `
                <div class="flex items-center gap-2 mb-1">
                  <span style="display:inline-block;width:10px;height:10px;border-radius:50%;background-color:${color}"></span>
                  <span class="text-gray-600">${item.seriesName}:</span>
                  <span class="font-medium">${item.value}%</span>
                </div>`;
            });
            return result;
          }
        },
        series: [
          getSeriesConfig(
            "最大负载",
            data.map(item => item.max_load_usage),
            "max",
            chartTypes.value.load,
            chartStyles.value.load.smooth
          ),
          getSeriesConfig(
            "平均负载",
            data.map(item => item.avg_load_usage),
            "avg",
            chartTypes.value.load,
            chartStyles.value.load.smooth
          ),
          getSeriesConfig(
            "最小负载",
            data.map(item => item.min_load_usage),
            "min",
            chartTypes.value.load,
            chartStyles.value.load.smooth
          )
        ]
      },
      true
    );
  }

  // 内存使用率图表
  if (memoryChartInstance) {
    console.log("更新内存图表");
    memoryChartInstance.setOption(
      {
        ...getBaseChartOption(),
        xAxis: {
          ...getBaseChartOption().xAxis,
          data: dates
        },
        series: [
          getSeriesConfig(
            "最大使用率",
            data.map(item => item.max_memory_usage),
            "max",
            chartTypes.value.memory,
            chartStyles.value.memory.smooth
          ),
          getSeriesConfig(
            "平均使用率",
            data.map(item => item.avg_memory_usage),
            "avg",
            chartTypes.value.memory,
            chartStyles.value.memory.smooth
          ),
          getSeriesConfig(
            "最小使用率",
            data.map(item => item.min_memory_usage),
            "min",
            chartTypes.value.memory,
            chartStyles.value.memory.smooth
          )
        ]
      },
      true
    );
  }

  // IO使用率图表
  if (ioChartInstance) {
    console.log("更新IO图表");
    ioChartInstance.setOption(
      {
        ...getBaseChartOption(),
        xAxis: {
          ...getBaseChartOption().xAxis,
          data: dates
        },
        series: [
          getSeriesConfig(
            "最大使用率",
            data.map(item => item.max_io_usage),
            "max",
            chartTypes.value.io,
            chartStyles.value.io.smooth
          ),
          getSeriesConfig(
            "平均使用率",
            data.map(item => item.avg_io_usage),
            "avg",
            chartTypes.value.io,
            chartStyles.value.io.smooth
          ),
          getSeriesConfig(
            "最小使用率",
            data.map(item => item.min_io_usage),
            "min",
            chartTypes.value.io,
            chartStyles.value.io.smooth
          )
        ]
      },
      true
    );
  }
};

// 处理日期变化
const handleDateChange = (val: [string, string]) => {
  if (val) {
    searchForm.value.start_time = val[0];
    searchForm.value.end_time = val[1];
    fetchData(); // 日期变化时自动触发搜索
  }
};

// 处理搜索
const handleSearch = () => {
  fetchData();
};

// 处理刷新
const handleRefresh = () => {
  if (searchForm.value.ip) {
    fetchData();
  } else {
    ElMessage.warning("请先输入IP地址");
  }
};

// 处理重置
const handleReset = async () => {
  // 重置表单数据
  searchForm.value.ip = "";

  // 重置日期范围为最近7天
  const end = new Date();
  const start = new Date();
  start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
  const formatDate = (date: Date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    return `${year}-${month}-${day}`;
  };
  dateRange.value = [formatDate(start), formatDate(end)];
  searchForm.value.start_time = dateRange.value[0];
  searchForm.value.end_time = dateRange.value[1];

  // 清空图表数据
  hasData.value = false;

  // 销毁现有图表实例
  cpuChartInstance?.dispose();
  loadChartInstance?.dispose();
  memoryChartInstance?.dispose();
  ioChartInstance?.dispose();

  cpuChartInstance = null;
  loadChartInstance = null;
  memoryChartInstance = null;
  ioChartInstance = null;

  // 重新初始化图表
  await nextTick();
  await initCharts();

  // 重置后自动触发搜索
  if (props.ip) {
    searchForm.value.ip = props.ip;
    await fetchData();
  }
};

// 初始化图表
const initCharts = async () => {
  await nextTick();
  console.log("初始化图表");
  try {
    if (cpuChart.value && !cpuChartInstance) {
      console.log("初始化CPU图表");
      cpuChartInstance = echarts.init(cpuChart.value);
    }
    if (loadChart.value && !loadChartInstance) {
      console.log("初始化负载图表");
      loadChartInstance = echarts.init(loadChart.value);
    }
    if (memoryChart.value && !memoryChartInstance) {
      console.log("初始化内存图表");
      memoryChartInstance = echarts.init(memoryChart.value);
    }
    if (ioChart.value && !ioChartInstance) {
      console.log("初始化IO图表");
      ioChartInstance = echarts.init(ioChart.value);
    }

    // 为所有图表设置加载状态
    const loadingOption = {
      text: "加载中...",
      color: "#409EFF",
      textColor: "#409EFF",
      maskColor: "rgba(255, 255, 255, 0.8)",
      zlevel: 0
    };

    cpuChartInstance?.showLoading(loadingOption);
    loadChartInstance?.showLoading(loadingOption);
    memoryChartInstance?.showLoading(loadingOption);
    ioChartInstance?.showLoading(loadingOption);
  } catch (error) {
    console.error("图表初始化失败:", error);
  }
};

// 监听IP变化
watch(
  () => props.ip,
  newVal => {
    if (newVal) {
      searchForm.value.ip = newVal;
      fetchData();
    } else {
      hasData.value = false;
    }
  }
);

// 监听窗口大小变化
const handleResize = () => {
  cpuChartInstance?.resize();
  loadChartInstance?.resize();
  memoryChartInstance?.resize();
  ioChartInstance?.resize();
};

// 处理图表类型变化
const handleChartTypeChange = (
  _chartName: "cpu" | "load" | "memory" | "io"
) => {
  fetchData();
};

// 处理样式变化
const handleStyleChange = (_chartName: "cpu" | "load" | "memory" | "io") => {
  fetchData();
};

onMounted(async () => {
  // 设置默认时间范围为最近7天
  const end = new Date();
  const start = new Date();
  start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);

  // 格式化日期
  const formatDate = (date: Date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    return `${year}-${month}-${day}`;
  };

  dateRange.value = [formatDate(start), formatDate(end)];
  searchForm.value.start_time = dateRange.value[0];
  searchForm.value.end_time = dateRange.value[1];

  await initCharts();

  if (searchForm.value.ip) {
    await fetchData();
  }
});

onUnmounted(() => {
  window.removeEventListener("resize", handleResize);
  // 确保清理所有图表实例
  if (cpuChartInstance) {
    cpuChartInstance.dispose();
    cpuChartInstance = null;
  }
  if (loadChartInstance) {
    loadChartInstance.dispose();
    loadChartInstance = null;
  }
  if (memoryChartInstance) {
    memoryChartInstance.dispose();
    memoryChartInstance = null;
  }
  if (ioChartInstance) {
    ioChartInstance.dispose();
    ioChartInstance = null;
  }
});
</script>

<style scoped>
.metrics-container {
  min-height: 100vh;
  padding: 20px;
  background-color: #f5f7fa;
}

.search-bar {
  padding: 20px;
  margin-bottom: 20px;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 5%);
}

.empty-tip {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400px;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 5%);
}

.charts-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
}

.chart-item {
  padding: 20px;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 5%);
}

.chart-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 12px;
  margin-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.chart-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.chart-tools {
  display: flex;
  gap: 12px;
  align-items: center;
}

.chart {
  height: 350px;
}

:deep(.el-form-item__label) {
  font-weight: bold;
  color: #606266;
}

:deep(.el-radio-button__inner) {
  padding: 4px 12px;
  font-size: 12px;
}

:deep(.el-divider--vertical) {
  height: 20px;
  margin: 0 12px;
}

:deep(.el-switch) {
  --el-switch-on-color: #409eff;
}
</style>
