<template>
  <div>
    <el-card shadow="hover" class="form-card">
      <el-form
        ref="ruleFormRef"
        label-width="auto"
        style="max-width: 600px"
        :model="newFormInline.form"
        size="large"
        status-icon
        label-position="top"
        class="form-layout"
      >
        <el-form-item
          label="名称"
          prop="name"
          :rules="[
            { required: true, message: '请输入名称', trigger: 'blur' },
            { max: 255, message: '请输入最大255', trigger: 'blur' }
          ]"
        >
          <el-input
            v-model="newFormInline.form.name"
            maxlength="255"
            show-word-limit
            placeholder="请输入名称"
            class="input-field"
          />
        </el-form-item>
        <el-form-item
          label="版本"
          prop="version"
          :rules="[
            { required: true, message: '请选择版本', trigger: 'change' }
          ]"
        >
          <el-select v-model="newFormInline.form.version" class="!w-full">
            <el-option label="v2" value="v2" />
            <el-option label="v1" value="v1" />
          </el-select>
        </el-form-item>
        <el-form-item
          label="地址"
          prop="address"
          :rules="[
            { required: true, message: '请输入地址', trigger: 'blur' },
            { max: 255, message: '请输入最大255', trigger: 'blur' }
          ]"
        >
          <el-input
            v-model="newFormInline.form.address"
            maxlength="255"
            show-word-limit
            placeholder="请输入地址"
            class="input-field"
          />
        </el-form-item>
        <el-form-item
          label="用户名"
          prop="username"
          :rules="[
            { max: 255, message: '请输入最大255', trigger: 'blur' },
            { required: true, message: '请输入用户名', trigger: 'blur' }
          ]"
        >
          <el-input
            v-model="newFormInline.form.username"
            maxlength="255"
            show-word-limit
            placeholder="请输入用户名"
            class="input-field"
          />
        </el-form-item>
        <el-form-item
          label="密码"
          prop="password"
          :rules="[{ max: 255, message: '请输入最大255', trigger: 'blur' }]"
        >
          <el-input
            v-model="newFormInline.form.password"
            maxlength="255"
            show-word-limit
            placeholder="请输入密码,留空表示不修改"
            autocomplete="off"
            type="password"
            class="input-field"
          />
        </el-form-item>
        <el-form-item
          label="备注"
          prop="remark"
          :rules="[{ max: 255, message: '请输入最大255', trigger: 'blur' }]"
        >
          <el-input
            v-model="newFormInline.form.remark"
            type="textarea"
            placeholder="请输入备注"
            maxlength="255"
            :rows="5"
            show-word-limit
            class="textarea-field"
          />
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { defineProps, ref } from "vue";
import type { FormInstance } from "element-plus";
import type { HarborForm } from "@/api/appops/harbor";

export interface FormProps {
  formInline: {
    form: HarborForm;
  };
}

const props = withDefaults(defineProps<FormProps>(), {
  formInline: () => ({ row: undefined, form: undefined })
});

const newFormInline = ref(props.formInline);
const ruleFormRef = ref<FormInstance>();
defineExpose({ ruleFormRef });
</script>

<style scoped lang="scss">
.form-layout {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.input-field {
  border-radius: 8px; /* 输入框圆角 */
  transition: border-color 0.3s ease;

  &:focus-within {
    border-color: var(--el-color-primary);
    box-shadow: 0 0 0 1px rgb(64 158 255 / 20%);
  }
}

.textarea-field {
  border-radius: 8px; /* 输入框圆角 */
  transition: border-color 0.3s ease;

  &:focus-within {
    border-color: var(--el-color-primary);
    box-shadow: 0 0 0 1px rgb(64 158 255 / 20%);
  }
}

.el-form-item {
  margin-bottom: 20px; /* 表单项底部间距 */
}

.el-input:hover {
  border-color: var(--el-color-primary); /* 悬停时边框颜色 */
}

.el-input.is-focus {
  border-color: var(--el-color-primary); /* 聚焦时边框颜色 */
}
</style>
