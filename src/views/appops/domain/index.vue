<template>
  <div class="main domain-list-page">
    <el-card class="search-card domain-search-card" shadow="hover">
      <el-form
        ref="formRef"
        :inline="true"
        :model="form"
        class="search-form domain-search-form"
        @submit.prevent
      >
        <div class="search-row">
          <el-form-item label="关键字" prop="keyword" class="search-form-item">
            <el-input
              v-model="form.keyword"
              placeholder="请输入关键字"
              clearable
              class="search-input"
              @keyup.enter="onSearch"
            />
          </el-form-item>
          <el-form-item
            label="业务"
            prop="business_id"
            class="search-form-item business-form-item"
          >
            <div class="business-select-refresh">
              <el-select
                v-model="form.business_id"
                class="!w-full business-select"
                placeholder="请选择业务"
                filterable
                clearable
                @change="onSearch"
              >
                <el-option
                  v-for="item in businessList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
              <el-button
                :icon="useRenderIcon(Refresh)"
                class="refresh-btn"
                circle
                @click="getAllBusinesses"
              />
            </div>
          </el-form-item>
          <el-form-item class="search-form-item search-btns-item">
            <el-button
              type="primary"
              :icon="useRenderIcon('ri:search-line')"
              :loading="loading"
              class="search-button"
              style="
                background: linear-gradient(135deg, #409eff 0%, #4facff 100%);
                border: none;
                box-shadow: 0 4px 12px rgb(64 158 255 / 30%);
                transition: all 0.3s ease;
              "
              @click="onSearch"
            >
              搜索
            </el-button>
            <el-button
              :icon="useRenderIcon(Refresh)"
              class="reset-button"
              style="
                color: #606266;
                border-color: #dcdfe6;
                transition: all 0.3s ease;
              "
              @click="resetForm(formRef)"
            >
              重置
            </el-button>
            <el-button
              type="primary"
              link
              :icon="useRenderIcon(Plus)"
              class="add-button"
              style="color: #409eff; transition: all 0.3s ease"
              @click="addFunc"
            >
              添加
            </el-button>
          </el-form-item>
        </div>
      </el-form>
    </el-card>

    <PureTableBar
      title="业务域名"
      :columns="columns"
      class="table-bar-wrap"
      @refresh="onSearch"
    >
      <template v-slot="{ size, dynamicColumns }">
        <pure-table
          ref="tableRef"
          row-key="id"
          align-whole="left"
          table-layout="auto"
          :loading="loading"
          :size="size"
          :minHeight="500"
          :data="dataList"
          :columns="dynamicColumns"
          :pagination="{ ...pagination, size }"
          :header-cell-style="{
            fontWeight: '600',
            background: '#f6f8fa',
            color: '#222',
            fontSize: '14px',
            borderBottom: '1.5px solid #e4e7ed'
          }"
          :row-style="{
            cursor: 'pointer',
            transition: 'background-color 0.2s ease',
            fontSize: '13px',
            background: '#fff'
          }"
          :cell-style="{
            padding: '10px 8px',
            borderBottom: '1px dashed #f0f0f0'
          }"
          border
          stripe
          highlight-current-row
          class="domain-table"
          @page-size-change="handleSizeChange"
          @page-current-change="handleCurrentChange"
        >
          <template #empty>
            <el-empty
              description="暂无数据"
              :image-size="120"
              style="padding: 40px 0"
            />
          </template>
        </pure-table>
      </template>
    </PureTableBar>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useRole } from "./hook";
import { PureTableBar } from "@/components/RePureTableBar";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";

import Refresh from "@iconify-icons/ep/refresh";
import Plus from "@iconify-icons/ep/plus";

defineOptions({
  name: "Domain"
});

const formRef = ref();
const tableRef = ref();
const businessList = ref<Business[]>([]);
import type { Business } from "@/api/asset/bussiness";
import { getAllBusinessesAPI } from "@/api/asset/bussiness";
import { message } from "@/utils/message";
const getAllBusinesses = () => {
  getAllBusinessesAPI()
    .then(res => {
      if (res.success) {
        businessList.value = res.data;
      } else {
        message(res.msg, { type: "error" });
      }
    })
    .catch(error => {
      message(error, { type: "error" });
    });
};
onMounted(() => {
  getAllBusinesses();
});
const {
  form,
  loading,
  columns,
  dataList,
  pagination,
  onSearch,
  resetForm,
  handleSizeChange,
  handleCurrentChange,
  addFunc
} = useRole();
</script>

<style scoped>
.search-row {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
}

.search-form-item {
  display: flex;
  align-items: center;
  margin-bottom: 0 !important;
}

.business-select-refresh {
  display: flex;
  gap: 6px;
  align-items: center;
}

.business-select {
  min-width: 160px;
}

.refresh-btn {
  min-width: 32px;
  height: 32px;
  padding: 4px;
  margin-left: 0;
}

.search-input {
  min-width: 180px;
}

.search-btns-item .el-button {
  height: 32px;
  padding: 0 14px;
  margin-right: 8px;
  font-size: 13px;
}

.search-btns-item .add-button {
  margin-right: 0;
}

@media (width <= 600px) {
  .search-row {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }

  .search-form-item {
    width: 100%;
  }

  .business-select {
    min-width: 100px;
  }

  .search-input {
    min-width: 100px;
  }
}
</style>
