<template>
  <div>
    <el-card shadow="hover" class="form-card domain-form-card">
      <el-form
        ref="ruleFormRef"
        label-width="auto"
        style="max-width: 600px"
        :model="newFormInline.form"
        size="large"
        status-icon
        label-position="top"
        class="form-layout domain-form-layout"
      >
        <el-form-item
          label="名称"
          prop="name"
          :rules="[
            { required: true, message: '请输入名称', trigger: 'blur' },
            { max: 255, message: '请输入最大255', trigger: 'blur' }
          ]"
          class="domain-form-item"
        >
          <el-input
            v-model="newFormInline.form.name"
            maxlength="255"
            show-word-limit
            placeholder="请输入名称"
            class="input-field"
          />
        </el-form-item>
        <el-form-item
          label="类型"
          prop="domain_type"
          :rules="[
            { required: true, message: '请选择类型', trigger: 'change' }
          ]"
          class="domain-form-item"
        >
          <el-select v-model="newFormInline.form.domain_type" class="!w-full">
            <el-option label="公网" value="public" />
            <el-option label="私网" value="private" />
          </el-select>
        </el-form-item>
        <el-form-item label="业务" prop="business_id" class="domain-form-item">
          <el-select
            v-model="newFormInline.form.business_id"
            class="!w-full"
            placeholder="请选择业务"
            filterable
            clearable
          >
            <el-option
              v-for="item in businessList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="备注"
          prop="remark"
          :rules="[{ max: 255, message: '请输入最大255', trigger: 'blur' }]"
          class="domain-form-item"
        >
          <el-input
            v-model="newFormInline.form.remark"
            type="textarea"
            placeholder="请输入备注"
            maxlength="255"
            :rows="5"
            show-word-limit
            class="textarea-field domain-textarea-field"
          />
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { defineProps, onMounted, ref } from "vue";
import type { FormInstance } from "element-plus";
import type { DomainForm } from "@/api/appops/domain";
import { getAllBusinessesAPI, type Business } from "@/api/asset/bussiness";
import { message } from "@/utils/message";

export interface FormProps {
  formInline: {
    form: DomainForm;
  };
}

const props = withDefaults(defineProps<FormProps>(), {
  formInline: () => ({ row: undefined, form: undefined })
});

const newFormInline = ref(props.formInline);
const ruleFormRef = ref<FormInstance>();
defineExpose({ ruleFormRef });
const businessList = ref<Business[]>([]);

const onSearch = () => {
  getAllBusinessesAPI()
    .then(res => {
      if (res.success) {
        businessList.value = res.data;
      } else {
        message(res.msg, { type: "error" });
      }
    })
    .catch(error => {
      message(error, { type: "error" });
    });
};

onMounted(() => {
  onSearch();
});
</script>

<style scoped>
.domain-form-card {
  padding: 20px 26px 10px;
  margin: 0 auto;
  background: #fff;
  border-radius: 13px;
  box-shadow: 0 4px 18px 0 rgb(64 158 255 / 9%);
}

.domain-form-layout {
  padding: 8px 0 0;
}

.domain-form-item {
  margin-bottom: 16px !important;
}

.domain-form-layout .el-form-item__label {
  margin-bottom: 3px;
  font-size: 13px;
  font-weight: 500;
  color: #333;
}

.domain-form-layout .el-input__wrapper,
.domain-form-layout .el-textarea__inner {
  font-size: 13px;
  border: 1px solid #e4e7ed;
  border-radius: 7px;
  box-shadow: none;
}

.domain-form-layout .el-input__wrapper {
  height: 36px;
}

.domain-form-layout .el-select {
  width: 100%;
}

.domain-form-layout .el-select .el-input__wrapper {
  height: 36px;
}

.domain-form-layout .el-input__inner,
.domain-form-layout .el-select__selected-value {
  font-size: 13px;
}

.domain-textarea-field .el-textarea__inner {
  min-height: 70px !important;
  font-size: 13px;
  border-radius: 7px;
}

.domain-form-layout .el-form-item__error {
  margin-top: 2px;
  font-size: 12px;
}

.form-layout {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.input-field {
  border-radius: 8px; /* 输入框圆角 */
  transition: border-color 0.3s ease;

  &:focus-within {
    border-color: var(--el-color-primary);
    box-shadow: 0 0 0 1px rgb(64 158 255 / 20%);
  }
}

.textarea-field {
  border-radius: 8px; /* 输入框圆角 */
  transition: border-color 0.3s ease;

  &:focus-within {
    border-color: var(--el-color-primary);
    box-shadow: 0 0 0 1px rgb(64 158 255 / 20%);
  }
}

.el-form-item {
  margin-bottom: 20px; /* 表单项底部间距 */
}

.el-input:hover {
  border-color: var(--el-color-primary); /* 悬停时边框颜色 */
}

.el-input.is-focus {
  border-color: var(--el-color-primary); /* 聚焦时边框颜色 */
}
</style>
