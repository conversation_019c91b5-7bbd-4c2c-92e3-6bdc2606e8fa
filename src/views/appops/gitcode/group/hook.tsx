import dayjs from "dayjs";
import { getGroupListAPI, type Group } from "@/api/appops/gitcode/group";
import type { PaginationProps } from "@pureadmin/table";
import { reactive, ref, onMounted } from "vue";
import { message } from "@/utils/message";
import { getallGitlabAPI, type Gitlab } from "@/api/appops/gitcode/gitlab";

export function useRole() {
  const form = reactive({
    keyword: undefined,
    gitlab_id: undefined
  });
  const dataList = ref<Group[]>([]);
  const loading = ref(true);
  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true,
    pageSizes: [10, 20, 30, 40, 50, 100]
  });

  const columns: TableColumnList = [
    {
      label: "ID",
      prop: "id",
      minWidth: 50
    },
    {
      label: "GitLab",
      prop: "gitlab_name",
      minWidth: 80
    },
    {
      label: "名称",
      prop: "name",
      minWidth: 100
    },
    {
      label: "地址",
      prop: "http_url",
      minWidth: 100
    },
    {
      label: "描述",
      prop: "description",
      minWidth: 100
    },
    {
      label: "同步时间",
      prop: "updated_at",
      minWidth: 120,
      cellRenderer: ({ row }) => (
        <p>{dayjs(row.updated_at).format("YYYY-MM-DD HH:mm:ss")}</p>
      )
    }
  ];

  const gitlabs = ref<Gitlab[]>([]);
  function getAllGitlab() {
    getallGitlabAPI()
      .then(res => {
        if (res.success) {
          gitlabs.value = res.data;
        } else {
          gitlabs.value = [];
        }
      })
      .catch(() => {
        message("请求失败", { type: "error" });
      });
  }

  function handleSizeChange(val: number) {
    pagination.pageSize = val;
    onSearch();
  }

  function handleCurrentChange(val: number) {
    pagination.currentPage = val;
    onSearch();
  }

  async function onSearch() {
    loading.value = true;
    getGroupListAPI({
      page: pagination.currentPage,
      limit: pagination.pageSize,
      keyword: form.keyword,
      gitlab_id: form.gitlab_id
    })
      .then(res => {
        if (res.success) {
          dataList.value = res.data;
          pagination.total = res.count;
        } else {
          dataList.value = [];
          pagination.total = 0;
        }
      })
      .catch(() => {
        message("请求失败", { type: "error" });
      })
      .finally(() => {
        loading.value = false;
      });
  }

  const resetForm = formEl => {
    if (!formEl) return;
    formEl.resetFields();
    onSearch();
  };

  onMounted(() => {
    onSearch();
    getAllGitlab();
  });

  return {
    form,
    loading,
    columns,
    dataList,
    pagination,
    onSearch,
    resetForm,
    handleSizeChange,
    handleCurrentChange,
    getAllGitlab,
    gitlabs
  };
}
