<template>
  <div class="main">
    <el-card class="search-card" shadow="hover">
      <el-form
        ref="formRef"
        :inline="true"
        :model="form"
        class="search-form"
        @submit.prevent
      >
        <el-form-item label="Gitlab" prop="gitlab_id">
          <div class="gitlab-select-wrapper">
            <el-select
              v-model="form.gitlab_id"
              class="search-select"
              filterable
              clearable
              placeholder="请选择 Gitlab"
              @change="onSearch"
            >
              <el-option
                v-for="(item, index) in gitlabs"
                :key="index"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
            <el-button
              class="sync-button"
              type="primary"
              :icon="useRenderIcon(Refresh)"
              @click="getAllGitlab"
            />
          </div>
        </el-form-item>
        <el-form-item label="关键字" prop="keyword">
          <el-input
            v-model="form.keyword"
            placeholder="请输入关键字搜索"
            clearable
            class="search-input"
            @keyup.enter="onSearch"
          >
            <template #prefix>
              <el-icon class="search-icon"><Search /></el-icon>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            :icon="useRenderIcon('ri:search-line')"
            :loading="loading"
            class="search-button"
            @click="onSearch"
          >
            搜索
          </el-button>
          <el-button
            :icon="useRenderIcon('ri:refresh-line')"
            class="reset-button"
            @click="resetForm(formRef)"
          >
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <PureTableBar title="Git 组" :columns="columns" @refresh="onSearch">
      <template v-slot="{ size, dynamicColumns }">
        <pure-table
          ref="tableRef"
          row-key="id"
          align-whole="left"
          table-layout="auto"
          :loading="loading"
          :size="size"
          :minHeight="500"
          :data="dataList"
          :columns="dynamicColumns"
          :pagination="{ ...pagination, size }"
          :header-cell-style="{
            fontWeight: '600'
          }"
          :row-style="{
            cursor: 'pointer',
            transition: 'background-color 0.2s ease'
          }"
          :cell-style="{
            padding: '12px 10px'
          }"
          border
          stripe
          highlight-current-row
          @page-size-change="handleSizeChange"
          @page-current-change="handleCurrentChange"
        >
          <template #empty>
            <el-empty
              description="暂无数据"
              :image-size="120"
              style="padding: 40px 0"
            />
          </template>
        </pure-table>
      </template>
    </PureTableBar>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useRole } from "./hook";
import { PureTableBar } from "@/components/RePureTableBar";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";

import Refresh from "@iconify-icons/ep/refresh";
import Search from "@iconify-icons/ep/search";

defineOptions({
  name: "GitcodeGroup"
});

const formRef = ref();
const tableRef = ref();

const {
  form,
  loading,
  columns,
  dataList,
  pagination,
  onSearch,
  resetForm,
  handleSizeChange,
  handleCurrentChange,
  getAllGitlab,
  gitlabs
} = useRole();
</script>

<style lang="scss" scoped>
.main {
  margin: 10px;

  .search-card {
    margin-bottom: 20px;
    border-radius: 8px;
    transition: all 0.3s ease;

    :deep(.el-card__body) {
      padding: 20px;
    }
  }

  .search-form {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    align-items: center;

    .el-form-item {
      margin-right: 16px;
      margin-bottom: 0;
    }
  }

  .gitlab-select-wrapper {
    display: flex;
    gap: 8px;
    align-items: center;

    .search-select {
      width: 200px;
    }

    .sync-button {
      padding: 8px;
      border-radius: 4px;
      transition: all 0.3s ease;

      &:hover {
        transform: rotate(180deg);
      }
    }
  }

  .search-input {
    width: 240px;

    :deep(.el-input__wrapper) {
      border-radius: 6px;
    }

    .search-icon {
      color: #909399;
    }
  }

  .search-button {
    padding: 8px 20px;
    background: linear-gradient(135deg, #409eff 0%, #4facff 100%);
    border: none;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgb(64 158 255 / 30%);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 6px 16px rgb(64 158 255 / 40%);
      transform: translateY(-2px);
    }
  }

  .reset-button {
    padding: 8px 20px;
    margin-left: 10px;
    border-radius: 6px;
    transition: all 0.3s ease;

    &:hover {
      background-color: #f5f7fa;
      transform: translateY(-2px);
    }
  }
}
</style>
