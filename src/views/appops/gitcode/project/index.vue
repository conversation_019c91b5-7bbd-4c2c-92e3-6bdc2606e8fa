<template>
  <div class="main">
    <el-card class="search-card">
      <el-form
        ref="formRef"
        :inline="true"
        :model="form"
        class="search-form"
        @submit.prevent
      >
        <el-form-item label="Gitlab" prop="gitlab_id">
          <div class="gitlab-select-wrapper">
            <el-select
              v-model="form.gitlab_id"
              class="search-select"
              filterable
              clearable
              placeholder="请选择 Gitlab"
              @change="onSearch"
            >
              <el-option
                v-for="(item, index) in gitlabs"
                :key="index"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
            <el-button
              class="sync-button"
              type="primary"
              :icon="useRenderIcon(Refresh)"
              @click="getAllGitlab"
            />
          </div>
        </el-form-item>
        <el-form-item label="关键字" prop="keyword">
          <el-input
            v-model="form.keyword"
            placeholder="请输入关键字"
            clearable
            @keyup.enter="onSearch"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            :icon="useRenderIcon('ri:search-line')"
            :loading="loading"
            class="search-button"
            style="
              background: linear-gradient(135deg, #409eff 0%, #4facff 100%);
              border: none;
              box-shadow: 0 4px 12px rgb(64 158 255 / 30%);
              transition: all 0.3s ease;
            "
            @click="onSearch"
          >
            搜索
          </el-button>
          <el-button
            :icon="useRenderIcon(Refresh)"
            class="reset-button"
            style="
              color: #606266;
              border-color: #dcdfe6;
              transition: all 0.3s ease;
            "
            @click="resetForm(formRef)"
          >
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <PureTableBar title="Git 项目" :columns="columns" @refresh="onSearch">
      <template v-slot="{ size, dynamicColumns }">
        <pure-table
          ref="tableRef"
          row-key="id"
          align-whole="left"
          table-layout="auto"
          :loading="loading"
          :size="size"
          :minHeight="500"
          :data="dataList"
          :columns="dynamicColumns"
          :pagination="{ ...pagination, size }"
          :header-cell-style="{
            fontWeight: '600'
          }"
          :row-style="{
            cursor: 'pointer',
            transition: 'background-color 0.2s ease'
          }"
          :cell-style="{
            padding: '12px 10px'
          }"
          border
          stripe
          highlight-current-row
          @page-size-change="handleSizeChange"
          @page-current-change="handleCurrentChange"
        >
          <template #empty>
            <el-empty
              description="暂无数据"
              :image-size="120"
              style="padding: 40px 0"
            />
          </template>
        </pure-table>
      </template>
    </PureTableBar>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useRole } from "./hook";
import { PureTableBar } from "@/components/RePureTableBar";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";

import Refresh from "@iconify-icons/ep/refresh";

defineOptions({
  name: "GitcodeProject"
});

const formRef = ref();
const tableRef = ref();

const {
  form,
  loading,
  columns,
  dataList,
  pagination,
  onSearch,
  resetForm,
  handleSizeChange,
  handleCurrentChange,
  getAllGitlab,
  gitlabs
} = useRole();
</script>

<style scoped lang="scss">
.main {
  margin: 10px;

  .search-card {
    margin-bottom: 20px;
    border-radius: 8px;
    transition: all 0.3s ease;

    :deep(.el-card__body) {
      padding: 20px;
    }
  }

  .search-form {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    align-items: center;

    .el-form-item {
      margin-right: 16px;
      margin-bottom: 0;
    }
  }

  .gitlab-select-wrapper {
    display: flex;
    gap: 8px;
    align-items: center;

    .search-select {
      width: 200px;
    }

    .sync-button {
      padding: 8px;
      border-radius: 4px;
      transition: all 0.3s ease;

      &:hover {
        transform: rotate(180deg);
      }
    }
  }

  .search-input {
    width: 240px;

    :deep(.el-input__wrapper) {
      border-radius: 6px;
    }

    .search-icon {
      color: #909399;
    }
  }

  .search-button {
    padding: 8px 20px;
    background: linear-gradient(135deg, #409eff 0%, #4facff 100%);
    border: none;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgb(64 158 255 / 30%);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 6px 16px rgb(64 158 255 / 40%);
      transform: translateY(-2px);
    }
  }

  .reset-button {
    padding: 8px 20px;
    margin-left: 10px;
    border-radius: 6px;
    transition: all 0.3s ease;

    &:hover {
      background-color: #f5f7fa;
      transform: translateY(-2px);
    }
  }

  :deep(.url-column) {
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding: 4px 0;

    .url-item {
      display: flex;
      gap: 12px;
      align-items: flex-start;

      .url-type {
        flex-shrink: 0;
        width: 52px;
      }

      .url-content {
        display: flex;
        flex: 1;
        gap: 8px;
        align-items: center;
        padding: 6px 8px;
        background-color: var(--el-fill-color-light);
        border-radius: 4px;
        transition: all 0.3s ease;

        &:hover {
          background-color: var(--el-fill-color);
        }

        .url-text {
          flex: 1;
          overflow: hidden;
          font-family: monospace;
          font-size: 13px;
          color: var(--el-text-color-regular);
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .copy-button {
          flex-shrink: 0;
          padding: 4px 8px;
          font-size: 12px;
          opacity: 0;
          transition: opacity 0.3s ease;
        }
      }

      &:hover {
        .url-content {
          .copy-button {
            opacity: 1;
          }
        }
      }
    }
  }
}
</style>
