import dayjs from "dayjs";
import { getProjectListAPI, type Project } from "@/api/appops/gitcode/project";
import { getallGitlabAPI, type Gitlab } from "@/api/appops/gitcode/gitlab";
import type { PaginationProps } from "@pureadmin/table";
import { reactive, ref, onMounted } from "vue";
import { message } from "@/utils/message";
import { useClipboard } from "@vueuse/core";
import { ElMessage, ElTag, ElButton } from "element-plus";

export function useRole() {
  const form = reactive({
    keyword: undefined,
    gitlab_id: undefined
  });
  const dataList = ref<Project[]>([]);
  const loading = ref(true);
  const { copy: copyToClipboard } = useClipboard();

  const handleCopy = async (text: string, type: string) => {
    try {
      await copyToClipboard(text);
      ElMessage.success(`${type} URL 已复制到剪贴板`);
    } catch (error) {
      ElMessage.error("复制失败");
    }
  };

  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true,
    pageSizes: [10, 20, 30, 40, 50, 100]
  });

  const columns: TableColumnList = [
    // {
    //   label: "ID",
    //   prop: "id",
    //   minWidth: 50
    // },
    {
      label: "GitLab",
      prop: "gitlab_name",
      minWidth: 80
    },
    {
      label: "项目 ID",
      prop: "project_id",
      minWidth: 80
    },
    {
      label: "名称",
      prop: "name",
      minWidth: 120
    },
    {
      label: "描述",
      prop: "description",
      minWidth: 150
    },
    {
      label: "仓库地址",
      prop: "urls",
      minWidth: 320,
      cellRenderer: ({ row }) => (
        <div class="url-column">
          <div class="url-item">
            <div class="url-type">
              <ElTag size="small" type="success" effect="plain">
                HTTP
              </ElTag>
            </div>
            <div class="url-content">
              <span class="url-text">{row.http_url}</span>
              <ElButton
                class="copy-button"
                type="primary"
                link
                onClick={() => handleCopy(row.http_url, "HTTP")}
              >
                复制
              </ElButton>
            </div>
          </div>
          <div class="url-item">
            <div class="url-type">
              <ElTag size="small" type="warning" effect="plain">
                SSH
              </ElTag>
            </div>
            <div class="url-content">
              <span class="url-text">{row.ssh_url}</span>
              <ElButton
                class="copy-button"
                type="primary"
                link
                onClick={() => handleCopy(row.ssh_url, "SSH")}
              >
                复制
              </ElButton>
            </div>
          </div>
        </div>
      )
    },
    {
      label: "时间",
      prop: "created_at",
      width: 260,
      cellRenderer: ({ row }) => (
        <div>
          <p>创建：{dayjs(row.created_at).format("YYYY-MM-DD HH:mm:ss")}</p>
          <p>同步：{dayjs(row.sync_time).format("YYYY-MM-DD HH:mm:ss")}</p>
        </div>
      )
    }
  ];

  const gitlabs = ref<Gitlab[]>([]);
  function getAllGitlab() {
    getallGitlabAPI()
      .then(res => {
        if (res.success) {
          gitlabs.value = res.data;
        } else {
          gitlabs.value = [];
        }
      })
      .catch(() => {
        message("请求失败", { type: "error" });
      });
  }

  function handleSizeChange(val: number) {
    pagination.pageSize = val;
    onSearch();
  }

  function handleCurrentChange(val: number) {
    pagination.currentPage = val;
    onSearch();
  }

  async function onSearch() {
    loading.value = true;
    getProjectListAPI({
      page: pagination.currentPage,
      limit: pagination.pageSize,
      keyword: form.keyword,
      gitlab_id: form.gitlab_id
    })
      .then(res => {
        if (res.success) {
          dataList.value = res.data;
          pagination.total = res.count;
        } else {
          dataList.value = [];
          pagination.total = 0;
        }
      })
      .catch(() => {
        message("请求失败", { type: "error" });
      })
      .finally(() => {
        loading.value = false;
      });
  }

  const resetForm = formEl => {
    if (!formEl) return;
    formEl.resetFields();
    onSearch();
  };

  onMounted(() => {
    onSearch();
    getAllGitlab();
  });

  return {
    form,
    loading,
    columns,
    dataList,
    pagination,
    onSearch,
    resetForm,
    handleSizeChange,
    handleCurrentChange,
    gitlabs,
    getAllGitlab
  };
}
