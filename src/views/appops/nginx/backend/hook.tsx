import dayjs from "dayjs";
import {
  getNginxBackendListAPI,
  type NginxBackend
} from "@/api/appops/nginx/backend";
import type { PaginationProps } from "@pureadmin/table";
import { reactive, ref, onMounted } from "vue";
import { message } from "@/utils/message";

export function useRole() {
  const form = reactive({
    keyword: undefined,
    server_name: undefined,
    ip: undefined
  });
  const dataList = ref<NginxBackend[]>([]);
  const loading = ref(true);

  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true,
    pageSizes: [10, 20, 30, 40, 50, 100]
  });

  const columns: TableColumnList = [
    {
      label: "主机",
      prop: "ip",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <div style="white-space: nowrap">
          <p>
            <el-tag type="primary" effect="dark">
              IP：
            </el-tag>
            <el-text type="primary" style="margin-left: 10px">
              {row.ip}
            </el-text>
          </p>
          <p style="margin-top: 10px">
            <el-tag type="info" effect="dark">
              备注：
            </el-tag>
            <el-text type="info" style="margin-left: 10px">
              {row.remark}
            </el-text>
          </p>
        </div>
      )
    },
    {
      label: "后端",
      prop: "backends",
      minWidth: 100,
      cellRenderer: ({ row }) => {
        if (typeof row._backendExpanded === 'undefined') {
          row._backendExpanded = false;
        }
        const maxShow = 3;
        const toggleExpand = () => {
          row._backendExpanded = !row._backendExpanded;
        };
        const remain = row.backends && row.backends.length > maxShow ? row.backends.length - maxShow : 0;
        return (
          <div class="pretty-list-wrap backend-list-wrap">
            {row.backends && row.backends.length > 0 && (
              <div>
                <ul class="pretty-list backend-list">
                  {(row._backendExpanded ? row.backends : row.backends.slice(0, maxShow)).map((item, idx) => (
                    <li key={idx} class="pretty-list-item backend-item">
                      <el-icon style="vertical-align: middle; margin-right: 6px; color: #67c23a; font-size: 15px;">
                        <i class="el-icon-connection" />
                      </el-icon>
                      <span class="pretty-list-text">{item}</span>
                    </li>
                  ))}
                </ul>
                {row.backends.length > maxShow && (
                  <span
                    class="show-more-btn"
                    onClick={toggleExpand}
                  >
                    {row._backendExpanded ? "收起" : `展示更多(${remain})`}
                  </span>
                )}
              </div>
            )}
          </div>
        );
      }
    },
    {
      label: "域名",
      prop: "server_names",
      minWidth: 100,
      cellRenderer: ({ row }) => {
        if (typeof row._serverExpanded === 'undefined') {
          row._serverExpanded = false;
        }
        const maxShow = 3;
        const toggleExpand = () => {
          row._serverExpanded = !row._serverExpanded;
        };
        const remain = row.server_names && row.server_names.length > maxShow ? row.server_names.length - maxShow : 0;
        return (
          <div class="pretty-list-wrap domain-list-wrap">
            {row.server_names && row.server_names.length > 0 && (
              <div>
                <ul class="pretty-list domain-list">
                  {(row._serverExpanded ? row.server_names : row.server_names.slice(0, maxShow)).map((name, idx) => (
                    <li key={idx} class="pretty-list-item domain-item">
                      <el-icon style="vertical-align: middle; margin-right: 6px; color: #409eff; font-size: 15px;">
                        <i class="el-icon-link" />
                      </el-icon>
                      <span class="pretty-list-text">{name}</span>
                    </li>
                  ))}
                </ul>
                {row.server_names.length > maxShow && (
                  <span
                    class="show-more-btn"
                    onClick={toggleExpand}
                  >
                    {row._serverExpanded ? "收起" : `展示更多(${remain})`}
                  </span>
                )}
              </div>
            )}
          </div>
        );
      }
    },
    {
      label: "信息汇总",
      prop: "summary_info",
      minWidth: 170,
      cellRenderer: ({ row }) => {
        // 统计
        const backendNum = row.backends ? row.backends.length : 0;
        const domainNum = row.server_names ? row.server_names.length : 0;
        // 时间
        const syncTime = row.sync_time ? dayjs(row.sync_time).format("YYYY-MM-DD HH:mm:ss") : "-";
        return (
          <div class="summary-info-col">
            <div class="summary-info-counts">
              <span class="summary-info-chip domain-chip">
                <el-icon style="vertical-align: middle; margin-right: 2px; font-size: 14px;"><i class="el-icon-link" /></el-icon>
                域名 <b>{domainNum}</b>
              </span>
              <span class="summary-info-chip backend-chip">
                <el-icon style="vertical-align: middle; margin-right: 2px; font-size: 14px;"><i class="el-icon-connection" /></el-icon>
                后端 <b>{backendNum}</b>
              </span>
            </div>
            <div class="summary-info-time">
              <el-icon style="vertical-align: middle; margin-right: 2px; color: #909399; font-size: 14px;"><i class="el-icon-time" /></el-icon>
              <span style="color: #666; font-size: 12px;">{syncTime}</span>
            </div>
          </div>
        );
      }
    },
  ];

  function handleSizeChange(val: number) {
    pagination.pageSize = val;
    onSearch();
  }

  function handleCurrentChange(val: number) {
    pagination.currentPage = val;
    onSearch();
  }

  async function onSearch() {
    loading.value = true;
    getNginxBackendListAPI({
      page: pagination.currentPage,
      limit: pagination.pageSize,
      keyword: form.keyword,
      server_name: form.server_name,
      ip: form.ip
    })
      .then(res => {
        if (res.success) {
          dataList.value = res.data;
          pagination.total = res.count;
        } else {
          dataList.value = [];
          pagination.total = 0;
        }
      })
      .catch(() => {
        message("请求失败", { type: "error" });
      })
      .finally(() => {
        loading.value = false;
      });
  }

  const resetForm = formEl => {
    if (!formEl) return;
    formEl.resetFields();
    onSearch();
  };

  onMounted(() => {
    onSearch();
  });

  return {
    form,
    loading,
    columns,
    dataList,
    pagination,
    onSearch,
    resetForm,
    handleSizeChange,
    handleCurrentChange
  };
}
