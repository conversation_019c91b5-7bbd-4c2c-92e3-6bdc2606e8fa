<template>
  <div class="main">
    <el-card class="search-card">
      <el-form
        ref="formRef"
        :inline="true"
        :model="form"
        class="search-form"
        @submit.prevent
      >
        <el-form-item label="IP" prop="ip">
          <el-input
            v-model="form.ip"
            placeholder="请输入IP"
            clearable
            @keyup.enter="onSearch"
          />
        </el-form-item>
        <el-form-item label="域名" prop="server_name">
          <el-input
            v-model="form.server_name"
            placeholder="请输入域名"
            clearable
            @keyup.enter="onSearch"
          />
        </el-form-item>
        <el-form-item label="关键字" prop="keyword">
          <el-input
            v-model="form.keyword"
            placeholder="请输入关键字"
            clearable
            @keyup.enter="onSearch"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            :icon="useRenderIcon('ri:search-line')"
            :loading="loading"
            class="search-button"
            style="
              background: linear-gradient(135deg, #409eff 0%, #4facff 100%);
              border: none;
              box-shadow: 0 4px 12px rgb(64 158 255 / 30%);
              transition: all 0.3s ease;
            "
            @click="onSearch"
          >
            搜索
          </el-button>
          <el-button
            :icon="useRenderIcon(Refresh)"
            class="reset-button"
            style="
              color: #606266;
              border-color: #dcdfe6;
              transition: all 0.3s ease;
            "
            @click="resetForm(formRef)"
          >
            重置
          </el-button>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            link
            :icon="useRenderIcon(Refresh)"
            @click="syncConfig"
          >
            同步配置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <PureTableBar title="nginx后端" :columns="columns" @refresh="onSearch">
      <template v-slot="{ size, dynamicColumns }">
        <pure-table
          ref="tableRef"
          row-key="id"
          align-whole="left"
          table-layout="auto"
          :loading="loading"
          :size="size"
          :minHeight="500"
          :data="dataList"
          :columns="dynamicColumns"
          :pagination="{ ...pagination, size }"
          :header-cell-style="{
            fontWeight: '600'
          }"
          :row-style="{
            cursor: 'pointer',
            transition: 'background-color 0.2s ease'
          }"
          :cell-style="{
            padding: '12px 10px'
          }"
          border
          stripe
          highlight-current-row
          @page-size-change="handleSizeChange"
          @page-current-change="handleCurrentChange"
        >
          <template #empty>
            <el-empty
              description="暂无数据"
              :image-size="120"
              style="padding: 40px 0"
            />
          </template>
        </pure-table>
      </template>
    </PureTableBar>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useRole } from "./hook";
import { PureTableBar } from "@/components/RePureTableBar";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";

import Refresh from "@iconify-icons/ep/refresh";
import { message } from "@/utils/message";
import { syncNginxBackendsAPI } from "@/api/appops/nginx/backend";

defineOptions({
  name: "NginxBackend"
});

const syncConfig = () => {
  syncNginxBackendsAPI()
    .then(res => {
      if (res.success) {
        message(res.msg, { type: "success" });
      } else {
        message(res.msg, { type: "error" });
      }
    })
    .catch(() => {
      message("同步失败", { type: "error" });
    });
};

const formRef = ref();
const tableRef = ref();

const {
  form,
  loading,
  columns,
  dataList,
  pagination,
  onSearch,
  resetForm,
  handleSizeChange,
  handleCurrentChange
} = useRole();
</script>

<style scoped lang="scss">
.main {
  padding: 20px;
  border-radius: 12px;
}

.search-card {
  margin-bottom: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 8%);
  transition: box-shadow 0.3s ease;

  &:hover {
    box-shadow: 0 6px 20px rgb(0 0 0 / 12%);
  }
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;

  :deep(.el-form-item) {
    margin-bottom: 0;
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: #606266;
  }

  .el-input {
    width: 220px;

    :deep(.el-input__wrapper) {
      border-radius: 8px;

      &:focus {
        border-color: #409eff;
        box-shadow: 0 0 0 1px rgb(64 158 255 / 20%);
      }
    }
  }
}

.search-button,
.reset-button,
.add-button {
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.search-button {
  background-color: #409eff;
  border-color: #409eff;
}

:deep(.pure-table) {
  overflow: hidden;
  border: 1px solid #e4e7ed;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 4%);
}

.summary-info-col {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: flex-start;
  padding: 6px 0 6px 2px;
}

.summary-info-counts {
  display: flex;
  gap: 10px;
  margin-bottom: 2px;
}

.summary-info-chip {
  display: flex;
  align-items: center;
  min-width: 60px;
  padding: 2px 8px 2px 6px;
  font-size: 13px;
  font-weight: 500;
  color: #222;
  background: #f6f8fa;
  border-radius: 8px;
  box-shadow: 0 1px 2px 0 rgb(64 158 255 / 4%);
}

.summary-info-chip.domain-chip {
  color: #409eff;
  background: #eaf3ff;
}

.summary-info-chip.backend-chip {
  color: #67c23a;
  background: #f3fae5;
}

.summary-info-time {
  display: flex;
  align-items: center;
  padding: 2px 8px 2px 6px;
  margin-top: 2px;
  font-size: 12px;
  color: #888;
  background: #f9f9fb;
  border-radius: 6px;
}

.summary-info-time .el-icon {
  color: #909399 !important;
}
</style>
