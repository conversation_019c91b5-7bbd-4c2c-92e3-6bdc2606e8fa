import dayjs from "dayjs";
import type { PaginationProps } from "@pureadmin/table";
import { reactive, ref, onMounted } from "vue";
import { getOperateLogsList } from "@/api/audit/operate";
import { message } from "@/utils/message";

export function useRole() {
  const form = reactive({
    user: undefined,
    ip: undefined,
    module: undefined,
    op_time: undefined
  });
  const dataList = ref([]);
  const loading = ref(true);

  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true,
    pageSizes: [10, 20, 30, 40, 50, 100]
  });
  const columns: TableColumnList = [
    {
      label: "操作时间",
      prop: "created_at",
      minWidth: 180,
      cellRenderer: ({ row }) => (
        <div class="log-cell time-cell">
          <div class="icon-wrapper primary">
            <iconify-icon-online icon="ep:timer" />
          </div>
          <span class="time-text">
            {dayjs(row.created_at).format("YYYY-MM-DD HH:mm:ss")}
          </span>
        </div>
      )
    },
    {
      label: "用户名",
      prop: "user",
      minWidth: 160,
      cellRenderer: ({ row }) => (
        <div class="log-cell user-cell">
          <div class="icon-wrapper success">
            <iconify-icon-online icon="ep:user" />
          </div>
          <div class="user-info">
            <span class="user-name">{row.user}</span>
            <span class="user-account">({row.username})</span>
          </div>
        </div>
      )
    },
    {
      label: "操作 IP",
      prop: "ip",
      minWidth: 160,
      cellRenderer: ({ row }) => (
        <div class="log-cell ip-cell">
          <div class="icon-wrapper">
            <iconify-icon-online icon="ep:location" />
          </div>
          <span class="ip-text">{row.ip}</span>
        </div>
      )
    },
    {
      label: "操作模块",
      prop: "module",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <div class="log-cell module-cell">
          <div class="icon-wrapper">
            <iconify-icon-online icon="ep:menu" />
          </div>
          <span class="module-text">{row.module}</span>
        </div>
      )
    },
    {
      label: "操作内容",
      prop: "content",
      minWidth: 200,
      cellRenderer: ({ row }) => (
        <div class="log-cell content-cell">
          <div class="icon-wrapper">
            <iconify-icon-online icon="ep:document" />
          </div>
          <div class="content-wrapper">
            <span class="content-text" style="white-space: pre-wrap;">
              {row.content}
            </span>
          </div>
        </div>
      )
    }
  ];

  function handleSizeChange(val: number) {
    pagination.pageSize = val;
    onSearch();
  }

  function handleCurrentChange(val: number) {
    pagination.currentPage = val;
    onSearch();
  }

  async function onSearch() {
    loading.value = true;
    getOperateLogsList({
      page: pagination.currentPage,
      limit: pagination.pageSize,
      ip: form.ip,
      user: form.user,
      module: form.module,
      op_time: form.op_time
    })
      .then(res => {
        if (res.success) {
          dataList.value = res.data;
          pagination.total = res.count;
          pagination.pageSize = pagination.pageSize;
          pagination.currentPage = pagination.currentPage;
        } else {
          dataList.value = [];
          pagination.total = 0;
          pagination.pageSize = pagination.pageSize;
          pagination.currentPage = pagination.currentPage;
        }
      })
      .catch(() => {
        message("获取数据失败", { type: "error" });
      })
      .finally(() => {
        loading.value = false;
      });
  }

  const resetForm = formEl => {
    if (!formEl) return;
    formEl.resetFields();
    onSearch();
  };

  onMounted(() => {
    onSearch();
  });

  return {
    form,
    loading,
    columns,
    dataList,
    pagination,
    onSearch,
    resetForm,
    handleSizeChange,
    handleCurrentChange
  };
}
