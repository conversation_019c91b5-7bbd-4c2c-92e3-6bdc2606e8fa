import dayjs from "dayjs";
import { getLoginLogsList } from "@/api/audit/login";
import type { PaginationProps } from "@pureadmin/table";
import { reactive, ref, onMounted } from "vue";
import { message } from "@/utils/message";

export function useRole() {
  const form = reactive({
    user: undefined,
    ip: undefined,
    login_time: undefined
  });
  const dataList = ref([]);
  const loading = ref(true);

  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true,
    pageSizes: [10, 20, 30, 40, 50, 100]
  });
  const columns: TableColumnList = [
    {
      label: "登录时间",
      prop: "created_at",
      minWidth: 180,
      cellRenderer: ({ row }) => (
        <div class="log-cell time-cell">
          <div class="icon-wrapper primary">
            <iconify-icon-online icon="ep:timer" />
          </div>
          <span class="time-text">
            {dayjs(row.created_at).format("YYYY-MM-DD HH:mm:ss")}
          </span>
        </div>
      )
    },
    {
      label: "用户名",
      prop: "user",
      minWidth: 160,
      cellRenderer: ({ row }) => (
        <div class="log-cell user-cell">
          <div class="icon-wrapper success">
            <iconify-icon-online icon="ep:user" />
          </div>
          <div class="user-info">
            <span class="user-name">{row.user}</span>
            <span class="user-account">({row.username})</span>
          </div>
        </div>
      )
    },
    {
      label: "登录结果",
      prop: "result",
      minWidth: 180,
      cellRenderer: ({ row }) => (
        <div class="log-cell result-cell">
          <div class="icon-wrapper">
            <iconify-icon-online
              icon="ep:circle-check"
              class={row.result.includes("成功") ? "success" : "danger"}
            />
          </div>
          <div class="result-info">
            <el-text
              type={row.result.includes("成功") ? "success" : "danger"}
              effect="light"
              class="result-tag"
            >
              {row.result}
            </el-text>
            <span class="mode-text">({row.mode})</span>
          </div>
        </div>
      )
    },
    {
      label: "登录 IP",
      prop: "ip",
      minWidth: 140,
      cellRenderer: ({ row }) => (
        <div class="log-cell ip-cell">
          <div class="icon-wrapper">
            <iconify-icon-online icon="ep:add-location" />
          </div>
          <span class="ip-text">{row.ip}</span>
        </div>
      )
    },
    {
      label: "浏览器类型",
      prop: "agent",
      minWidth: 60
    }
  ];

  function handleSizeChange(val: number) {
    pagination.pageSize = val;
    onSearch();
  }

  function handleCurrentChange(val: number) {
    pagination.currentPage = val;
    onSearch();
  }

  async function onSearch() {
    loading.value = true;
    getLoginLogsList({
      page: pagination.currentPage,
      limit: pagination.pageSize,
      ip: form.ip,
      user: form.user,
      login_time: form.login_time
    })
      .then(res => {
        if (res.success) {
          dataList.value = res.data;
          pagination.total = res.count;
          pagination.pageSize = pagination.pageSize;
          pagination.currentPage = pagination.currentPage;
        } else {
          dataList.value = [];
          pagination.total = 0;
          pagination.pageSize = pagination.pageSize;
          pagination.currentPage = pagination.currentPage;
        }
      })
      .catch(() => {
        message("请求失败", { type: "error" });
      })
      .finally(() => {
        loading.value = false;
      });
  }

  const resetForm = formEl => {
    if (!formEl) return;
    formEl.resetFields();
    onSearch();
  };

  onMounted(() => {
    onSearch();
  });

  return {
    form,
    loading,
    columns,
    dataList,
    pagination,
    onSearch,
    resetForm,
    handleSizeChange,
    handleCurrentChange
  };
}
