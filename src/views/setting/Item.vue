<template>
  <div class="item-container">
    <div class="item-header">
      <h3>{{ sectionName }}</h3>
    </div>
    <div class="item-content">
      <el-form ref="formRef" v-model="formData" label-width="100px">
        <el-form-item
          v-for="item in formData"
          :key="item.id"
          :label="item.alias"
          :prop="item.name"
        >
          <template v-if="item.setting_type === 1">
            <el-switch
              v-model="item.content"
              active-value="true"
              inactive-value="false"
              active-text="开启"
              inactive-text="关闭"
            />
          </template>
          <template v-else-if="item.setting_type === 2">
            <el-input
              v-model="item.content"
              type="password"
              clearable
              placeholder="请输入密码"
            />
          </template>
          <template v-else-if="item.setting_type === 3">
            <el-select
              v-model="item.content"
              placeholder="请选择联系人"
              multiple
              clearable
              filterable
            >
              <el-option
                v-for="contact in contacts"
                :key="contact.id"
                :label="contact.name"
                :value="contact.id.toString()"
              />
            </el-select>
          </template>
          <template v-else>
            <el-input
              v-model="item.content"
              clearable
              placeholder="请输入内容"
            />
          </template>
        </el-form-item>
        <el-button type="primary" class="save-button" @click="handleAction"
          >保存</el-button
        >
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getAllContactAPI, type Contact } from "@/api/notice/contact";
import {
  getSettingItemAPI,
  saveSettingAPI,
  type Item,
  type SettingForm
} from "@/api/setting/setting";
import { message } from "@/utils/message";
import type { FormInstance } from "element-plus";
import { defineProps, onBeforeMount, ref } from "vue";

const props = defineProps<{
  sectionName: string;
}>();

const contacts = ref<Contact[]>([]);
const items = ref<Item[]>([]);
const formRef = ref<FormInstance>();
const formData = ref<SettingForm[]>([]);
const handleAction = () => {
  // 正确获取表单所有内容
  const submittedData = formData.value.map(item => {
    if (item.setting_type === 3) {
      return {
        section: props.sectionName,
        name: item.name,
        content: Array.isArray(item.content)
          ? item.content.join(",")
          : item.content
      };
    } else {
      return {
        section: props.sectionName,
        name: item.name,
        content: item.content
      };
    }
  });
  saveSettingAPI(submittedData)
    .then(res => {
      if (res.success) {
        message("保存成功", { type: "success" });
      } else {
        message(res.msg || "保存失败", { type: "error" });
      }
    })
    .catch(err => {
      message("保存失败" + err, { type: "error" });
    });
};
const getItems = () => {
  getSettingItemAPI(props.sectionName)
    .then(res => {
      if (res.success) {
        items.value = res.data as Item[];
        formData.value = items.value.map(item => {
          if (item.setting_type === 3) {
            return {
              section: item.section,
              setting_type: item.setting_type,
              name: item.name,
              alias: item.alias,
              content: item.content.split(",")
            };
          } else {
            return {
              section: item.section,
              setting_type: item.setting_type,
              name: item.name,
              alias: item.alias,
              content: item.content
            };
          }
        });
      } else {
        message(res.msg || "获取配置项失败", { type: "error" });
      }
    })
    .catch(err => {
      message("获取配置项失败" + err, { type: "error" });
    });
};
const getAllContacts = () => {
  getAllContactAPI()
    .then(res => {
      if (res.success) {
        contacts.value = res.data as Contact[];
      } else {
        message(res.msg || "获取联系人失败", { type: "error" });
      }
    })
    .catch(err => {
      message("获取联系人失败" + err, { type: "error" });
    });
};

onBeforeMount(() => {
  getItems();
  getAllContacts();
});
</script>

<style scoped>
.item-container {
  padding: 20px;
  margin: 20px 0;
  border-radius: 4px;
  box-shadow: 0 4px 20px rgb(0 0 0 / 10%);
  transition: box-shadow 0.3s;
}

.item-header {
  margin-bottom: 20px;
}

.item-header h3 {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.item-content {
  padding: 20px;
  border-radius: 4px;
}

.save-button {
  margin-top: 20px;
}
</style>
