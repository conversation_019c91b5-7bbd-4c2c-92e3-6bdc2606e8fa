<template>
  <el-card class="setting-card">
    <el-tabs
      v-model="activeName"
      class="tabs"
      tab-position="left"
      @tab-click="handleClick"
    >
      <el-tab-pane
        v-for="section in sections"
        :key="section.id"
        :label="section.alias"
        :name="section.name"
        class="tab-pane"
        lazy
      >
        <Item :section-name="section.name" />
      </el-tab-pane>
    </el-tabs>
  </el-card>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { getSettingAPI, type Section } from "@/api/setting/setting";
import { message } from "@/utils/message";
import type { TabsPaneContext } from "element-plus";
import Item from "./Item.vue";

const sections = ref<Section[]>([]);
const activeName = ref("");
const getSections = async () => {
  getSettingAPI()
    .then(res => {
      if (res.success) {
        sections.value = res.data || [];
        if (sections.value.length > 0) {
          activeName.value = sections.value[0].name;
        }
      } else {
        message(res.msg, { type: "error" });
      }
    })
    .catch(error => {
      message(error, { type: "error" });
    });
};
const handleClick = (tab: TabsPaneContext, event: Event) => {
  console.log(tab.paneName, event);
};

onMounted(() => {
  getSections();
});
</script>
<style scoped lang="scss">
.setting-card {
  margin: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 8%);
  transition: box-shadow 0.3s ease;

  &:hover {
    box-shadow: 0 6px 20px rgb(0 0 0 / 12%);
  }
}

.tabs > .el-tabs__content {
  padding: 32px;
  font-size: 18px;
  font-weight: 500;
  color: #303133;
}

.tab-pane {
  padding: 16px;
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 5%);
}
</style>
