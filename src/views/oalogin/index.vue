<script setup>
import { initRouter, getTopMenu } from "@/router/utils";
import { message } from "@/utils/message";
import { useRouter } from "vue-router";
import { onMounted, ref } from "vue";
import { useUserStoreHook } from "@/store/modules/user";

const loading = ref(false);
const router = useRouter();
const fail = ref(false);

const getQueryParam = code =>
  new URLSearchParams(window.location.search).get(code);

const onOALogin = async () => {
  loading.value = true;
  const userStore = useUserStoreHook();
  try {
    const res = await userStore.loginByOA({ code: getQueryParam("code") });
    if (res.success) {
      await initRouter();
      await router.push(getTopMenu(true).path);
      message("登录成功", { type: "success" });
    } else {
      message(res.msg, { type: "error" });
      fail.value = true;
    }
  } catch (error) {
    message(error, { type: "error" });
    fail.value = true;
  } finally {
    loading.value = false;
  }
};

onMounted(onOALogin);
</script>
<template>
  <el-card v-if="fail" class="oa-login-card">
    <a href="/" class="retry-link">点击重新登录</a>
  </el-card>
  <el-card v-if="loading" class="oa-login-card">
    <span class="loading-text">loading...</span>
  </el-card>
</template>

<style scoped>
.oa-login-card {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 8%);
  transition: box-shadow 0.3s ease;

  &:hover {
    box-shadow: 0 6px 20px rgb(0 0 0 / 12%);
  }
}

.retry-link {
  font-size: 18px;
  color: #409eff;
  text-decoration: none;
  transition: color 0.3s ease;

  &:hover {
    color: #66b1ff;
  }
}

.loading-text {
  font-size: 18px;
  color: #606266;
}
</style>
