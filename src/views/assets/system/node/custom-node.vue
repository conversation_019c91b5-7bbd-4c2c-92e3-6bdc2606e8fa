<template>
  <div
    class="custom-node"
    :class="[
      `type-${data.data.asset_type || 'unknown'}`,
      { selected: data.selected }
    ]"
  >
    <div class="node-header" :style="{ backgroundColor: getColor() }">
      <div class="node-type">{{ getTypeText() }}</div>
      <div class="node-actions">
        <el-tooltip content="编辑节点" placement="top" effect="light">
          <el-button
            type="primary"
            size="small"
            link
            @click.stop.prevent="e => editNode(e)"
          >
            <el-icon><Edit /></el-icon>
          </el-button>
        </el-tooltip>
        <el-tooltip content="删除节点" placement="top" effect="light">
          <el-button
            type="danger"
            size="small"
            link
            @click.stop.prevent="e => deleteNode(e)"
          >
            <el-icon><Delete /></el-icon>
          </el-button>
        </el-tooltip>
        <el-tooltip content="查看资产" placement="top" effect="light">
          <el-button
            type="info"
            size="small"
            link
            @click.stop.prevent="() => emit('show-assets', props.data.data.id)"
          >
            <el-icon><Document /></el-icon>
          </el-button>
        </el-tooltip>
      </div>
    </div>
    <div class="node-content">
      <div class="node-name">{{ data.data.name }}</div>
      <div v-if="data.data.tags && data.data.tags.length > 0" class="node-tags">
        <el-tag
          v-for="(tag, index) in data.data.tags"
          :key="index"
          size="small"
          effect="light"
          class="tag-item"
        >
          {{ tag.key }}:{{ tag.value }}
        </el-tag>
      </div>
    </div>
    <div
      class="vue-flow__handle vue-flow__handle-top"
      data-type="target"
      data-handleid="target"
      data-nodeid="data.id"
    />
    <div
      class="vue-flow__handle vue-flow__handle-bottom"
      data-type="source"
      data-handleid="source"
      data-nodeid="data.id"
    />
  </div>
</template>

<script setup lang="ts">
import type { AssetNode } from "@/api/assets/asset-node";
import { AssetTypeColors, AssetTypeMap } from "@/config/asset-type";
import { Edit, Delete, Document } from "@element-plus/icons-vue";

const props = defineProps<{
  data: {
    id: string;
    data: AssetNode & {
      typeText: string;
      typeColor: string;
    };
    selected: boolean;
  };
}>();

const emit = defineEmits<{
  (e: "edit" | "delete", node: AssetNode): void;
  (e: "show-assets", nodeId: number): void;
}>();

// 获取节点颜色
const getColor = () => {
  const assetType = props.data.data.asset_type;

  // 尝试将assetType转换为数字，作为AssetType枚举使用
  if (assetType) {
    const assetTypeNum = parseInt(assetType, 10);
    if (!isNaN(assetTypeNum)) {
      // 将数字映射到El颜色变量
      const colorMap = {
        primary: "var(--el-color-primary)",
        success: "var(--el-color-success)",
        warning: "var(--el-color-warning)",
        danger: "var(--el-color-danger)",
        info: "var(--el-color-info)"
      };

      const colorName = AssetTypeColors[assetTypeNum];
      if (colorName && colorMap[colorName]) {
        return colorMap[colorName];
      }
    }

    // 使用预定义的类型映射
    const typeColorMap: Record<string, string> = {
      server: "#409eff",
      database: "#67c23a",
      app: "#e6a23c",
      middleware: "#f56c6c",
      network: "#909399",
      storage: "#9c27b0"
    };

    return typeColorMap[assetType] || "#909399";
  }

  // 默认使用灰色
  return "#909399";
};

// 获取类型文本
const getTypeText = () => {
  // 优先使用传入的typeText
  if (props.data.data.typeText) {
    return props.data.data.typeText;
  }

  // 尝试从资产类型映射中获取
  const assetType = props.data.data.asset_type;
  if (assetType) {
    // 尝试将字符串转换为数字，因为AssetTypeMap使用数字作为键
    const assetTypeNum = parseInt(assetType, 10);
    if (!isNaN(assetTypeNum) && AssetTypeMap[assetTypeNum]) {
      return AssetTypeMap[assetTypeNum];
    }

    // 使用自定义映射
    const typeNameMap: Record<string, string> = {
      server: "服务器",
      database: "数据库",
      app: "应用",
      middleware: "中间件",
      network: "网络设备",
      storage: "存储设备"
    };
    return typeNameMap[assetType] || assetType;
  }

  return "未知类型";
};

// 编辑节点
const editNode = (event: MouseEvent) => {
  // 阻止事件冒泡和默认行为
  event.stopPropagation();
  event.preventDefault();

  console.log("编辑节点被调用", props.data.data);
  emit("edit", props.data.data);
};

// 删除节点
const deleteNode = (event: MouseEvent) => {
  // 阻止事件冒泡和默认行为
  event.stopPropagation();
  event.preventDefault();

  console.log("删除节点被调用", props.data.data);
  emit("delete", props.data.data);
};
</script>

<style lang="scss" scoped>
.custom-node {
  position: relative;
  width: fit-content;
  min-width: 250px;
  padding: 0;
  overflow: visible;
  background: #fff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgb(0 0 0 / 5%);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  .node-header {
    position: relative;
    display: flex;
    gap: 8px;
    align-items: center;
    width: 100%;
    min-width: 200px;
    padding: 12px 16px;
    color: #fff;
    border-radius: 12px 12px 0 0;

    &::after {
      position: absolute;
      inset: 0;
      content: "";
      background: linear-gradient(
        rgb(255 255 255 / 10%),
        rgb(255 255 255 / 0%)
      );
      border-radius: 12px 12px 0 0;
      opacity: 0;
      transition: opacity 0.2s;
    }

    &:hover::after {
      opacity: 1;
    }

    .node-type {
      flex-shrink: 0;
      max-width: none;
      padding: 3px 8px;
      font-size: 12px;
      font-weight: 500;
      text-shadow: 0 1px 2px rgb(0 0 0 / 10%);
      white-space: nowrap;
      background: rgb(255 255 255 / 20%);
      backdrop-filter: blur(4px);
      border-radius: 6px;
    }
  }

  .node-content {
    width: 100%;
    min-width: 200px;
    padding: 12px 16px;
    font-size: 16px;
    line-height: 1.6;
    background: linear-gradient(to bottom, #f8fafc, #fff);
    border-radius: 0 0 12px 12px;

    .node-name {
      margin-bottom: 8px;
      font-size: 18px;
      font-weight: bold;
      color: #1a202c;
      word-break: break-word;
      white-space: normal;
    }
  }

  .node-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    width: 100%;
    margin-top: 12px;

    .tag-item {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      height: 22px;
      padding: 0 8px;
      font-size: 11px;
      color: #475569;
      white-space: nowrap;
      background: #f1f5f9;
      border: none;
      border-radius: 6px;
      box-shadow: inset 0 0 0 1px rgb(0 0 0 / 5%);
    }
  }

  .node-actions {
    position: absolute;
    top: 8px;
    right: 8px;
    z-index: 10;
    display: none;
    gap: 4px;

    .el-button {
      padding: 4px;
      background: rgb(255 255 255 / 90%);
      backdrop-filter: blur(4px);
      border: none;
      border-radius: 6px;
      box-shadow: 0 1px 2px rgb(0 0 0 / 10%);
      transition: all 0.2s;

      &:hover {
        background: #fff;
        box-shadow: 0 4px 8px rgb(0 0 0 / 10%);
        transform: translateY(-1px);
      }

      .el-icon {
        font-size: 16px;
      }
    }
  }

  &:hover {
    border-color: transparent;
    box-shadow: 0 4px 20px rgb(0 0 0 / 8%);
    transform: translateY(-1px);

    .node-actions {
      display: flex;
    }
  }

  &.selected {
    box-shadow:
      0 0 0 2px var(--el-color-primary),
      0 4px 20px rgb(0 0 0 / 8%);
  }

  // 连接点样式优化
  .vue-flow__handle {
    z-index: 5;
    width: 12px;
    height: 12px;
    background: #fff;
    border: 2px solid var(--el-color-primary);
    border-radius: 6px;
    box-shadow: 0 2px 4px rgb(0 0 0 / 10%);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

    &:hover {
      box-shadow: 0 2px 8px rgb(0 0 0 / 15%);
      transform: scale(1.2);
    }

    &-top {
      top: -6px;

      &:hover {
        transform: translateX(-50%) scale(1.2);
      }
    }

    &-bottom {
      bottom: -6px;

      &:hover {
        transform: translateX(-50%) scale(1.2);
      }
    }
  }
}

// 优化渐变色样式
.type-server .node-header,
.type-host .node-header,
.type-4 .node-header {
  background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
}

.type-database .node-header,
.type-mysql .node-header,
.type-mongodb .node-header,
.type-7 .node-header,
.type-8 .node-header {
  background: linear-gradient(135deg, #059669 0%, #10b981 100%);
}

.type-app .node-header,
.type-5 .node-header,
.type-6 .node-header {
  background: linear-gradient(135deg, #d97706 0%, #f59e0b 100%);
}

.type-middleware .node-header,
.type-redis .node-header,
.type-mq .node-header,
.type-9 .node-header,
.type-10 .node-header {
  background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
}

.type-network .node-header,
.type-3 .node-header,
.type-loadbalancer .node-header {
  background: linear-gradient(135deg, #7c3aed 0%, #8b5cf6 100%);
}

.type-storage .node-header {
  background: linear-gradient(135deg, #db2777 0%, #ec4899 100%);
}

.type-domain .node-header,
.type-2 .node-header,
.type-14 .node-header,
.type-13 .node-header {
  background: linear-gradient(135deg, #0d9488 0%, #14b8a6 100%);
}

.type-system .node-header,
.type-1 .node-header,
.type-k8s .node-header,
.type-public .node-header {
  background: linear-gradient(135deg, #4f46e5 0%, #6366f1 100%);
}

.type-unknown .node-header {
  background: linear-gradient(135deg, #4b5563 0%, #6b7280 100%);
}
</style>
