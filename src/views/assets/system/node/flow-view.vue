<template>
  <div class="flow-view-container">
    <div class="flow-header">
      <h3>系统节点关系图 - {{ systemName }}</h3>
      <div class="flow-actions">
        <el-tooltip content="添加新节点" effect="light" placement="top">
          <el-button
            type="primary"
            size="default"
            class="custom-btn add-btn"
            @click="handleAddNode"
          >
            <el-icon><Plus /></el-icon> 添加节点
          </el-button>
        </el-tooltip>
        <el-tooltip content="刷新节点" effect="light" placement="top">
          <el-button
            type="success"
            size="default"
            plain
            class="custom-btn refresh-btn"
            :loading="loading"
            @click="fetchNodes"
          >
            <el-icon><Refresh /></el-icon>
          </el-button>
        </el-tooltip>
        <el-tooltip content="自适应视图" effect="light" placement="top">
          <el-button
            type="info"
            size="default"
            plain
            class="custom-btn fit-btn"
            @click="fitView"
          >
            <el-icon><FullScreen /></el-icon>
          </el-button>
        </el-tooltip>
        <el-tooltip content="全屏显示" effect="light" placement="top">
          <el-button
            type="warning"
            size="default"
            plain
            class="custom-btn fullscreen-btn"
            @click="toggleFullscreen"
          >
            <el-icon><FullScreen /></el-icon>
          </el-button>
        </el-tooltip>
      </div>
    </div>
    <div class="flow-tips">
      <el-alert
        title="提示：可拖拽节点调整位置，按住Ctrl+滚轮缩放视图，按住空格拖动画布"
        type="info"
        :closable="false"
        effect="light"
        show-icon
      >
        <template #title>
          <div class="custom-alert-title">
            <span class="alert-icon">💡</span>
            <span
              >提示：可拖拽节点调整位置，按住Ctrl+滚轮缩放视图，按住空格拖动画布</span
            >
          </div>
        </template>
      </el-alert>
    </div>
    <div
      ref="flowWrapper"
      class="flow-wrapper"
      style="width: 100%; height: calc(100vh - 180px)"
    >
      <vue-flow
        v-model="elements"
        :nodes="nodes"
        :edges="edges"
        :default-zoom="1"
        :min-zoom="0.2"
        :max-zoom="3"
        :node-types="nodeTypes"
        :snapToGrid="true"
        :fit-view-on-init="true"
        :selectable="true"
        :node-draggable="true"
        :zoomOnScroll="true"
        :panOnScroll="true"
        :preventScrolling="true"
        :zoomOnDoubleClick="false"
        :connection-mode="ConnectionMode.Loose"
        :default-viewport="{ x: 0, y: 0, zoom: 1 }"
        class="custom-flow vertical-flow"
        style="width: 100%; height: 100%"
        @node-click="onNodeClick"
        @edge-click="onEdgeClick"
        @connect="onConnect"
      >
        <template #node-customNode="nodeProps">
          <custom-node
            :data="nodeProps"
            @edit="editNode"
            @delete="showDeleteDialog"
            @show-assets="onShowNodeAssets"
          />
        </template>
        <controls />
        <background
          :pattern-color="'#e2e8f0'"
          :gap="24"
          :size="1.5"
          variant="dots"
        />
      </vue-flow>
    </div>

    <!-- 节点表单对话框 -->
    <node-dialog
      v-model:visible="nodeDialogVisible"
      :is-edit="isEditNode"
      :node-data="currentNodeData"
      :system-id="systemIdValue"
      @success="handleDialogSuccess"
    />

    <!-- 删除确认对话框 -->
    <el-dialog v-model="deleteDialogVisible" title="删除节点" width="400px">
      <p>确定要删除节点"{{ currentNodeData?.name }}"吗？</p>
      <template #footer>
        <el-button @click="deleteDialogVisible = false">取消</el-button>
        <el-button type="danger" :loading="loading" @click="confirmDeleteNode">
          确定
        </el-button>
      </template>
    </el-dialog>
    <el-dialog
      v-if="showNodeAssetsDialog"
      :model-value="showNodeAssetsDialog"
      title="节点资产列表"
      width="650px"
      append-to-body
      @close="showNodeAssetsDialog = false"
    >
      <NodeAssets :node_id="selectedNodeId" />
      <template #footer>
        <el-button @click="showNodeAssetsDialog = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import {
  ref,
  onMounted,
  defineProps,
  computed,
  defineExpose,
  nextTick,
  onBeforeUnmount
} from "vue";
import {
  VueFlow,
  type NodeMouseEvent,
  type EdgeMouseEvent,
  type Connection,
  useVueFlow,
  ConnectionMode,
  PanOnScrollMode
} from "@vue-flow/core";
import "@vue-flow/core/dist/style.css";
import "@vue-flow/core/dist/theme-default.css";

// 从独立包中导入组件而不是从Core包
import { Background } from "@vue-flow/background";
import { Controls } from "@vue-flow/controls";

// 核心样式表已经导入，不需要再导入单独组件的样式表
import type { AssetNode } from "@/api/assets/asset-node";
import { getAssetNodesAPI, deleteAssetNodeAPI } from "@/api/assets/asset-node";
import { message } from "@/utils/message";
import CustomNode from "./custom-node.vue";
import NodeDialog from "./node-dialog.vue";
import NodeAssets from "./node-assets.vue";

// 引入element-plus图标
import { FullScreen, Plus, Refresh } from "@element-plus/icons-vue";

// 引入资产类型配置
import { AssetTypeMap, AssetType } from "@/config/asset-type";

// 获取资产类型图标的辅助函数
const getIcon = (assetType: string) => {
  const typeMap: Record<string, string> = {
    server: "icon-fuwuqi",
    database: "icon-shujuku",
    app: "icon-yingyong",
    middleware: "icon-zhongjianjian",
    network: "icon-wangluo",
    storage: "icon-cunchu"
  };
  return typeMap[assetType] || "icon-default";
};

// 获取资产类型名称的辅助函数
const getAssetTypeName = (assetType: string) => {
  // 尝试将字符串转换为数字，因为AssetTypeMap使用数字作为键
  const assetTypeNum = parseInt(assetType, 10);
  if (!isNaN(assetTypeNum) && AssetTypeMap[assetTypeNum]) {
    return AssetTypeMap[assetTypeNum];
  }

  // 如果不是有效的枚举数字，使用名称映射
  const typeNameMap: Record<string, string> = {
    server: "服务器",
    database: "数据库",
    app: "应用",
    middleware: "中间件",
    network: "网络设备",
    storage: "存储设备"
  };
  return typeNameMap[assetType] || assetType || "未知类型";
};

// 获取连接线颜色
// 注意：此函数已被替换为基于层级的颜色方案
const _getConnectionColor = (sourceNode: any, _targetId: number) => {
  // 根据源节点类型决定连线颜色
  const assetType = sourceNode.asset_type;
  const colorMap: Record<string, string> = {
    // 系统、负载均衡等紫色系
    "1": "#8B5CF6",
    system: "#8B5CF6",
    k8s: "#8B5CF6",
    // 服务器蓝色系
    "4": "#3B82F6",
    server: "#3B82F6",
    host: "#3B82F6",
    // 数据库绿色系
    "7": "#10B981",
    "8": "#10B981",
    database: "#10B981",
    mysql: "#10B981",
    // 应用橙色系
    "5": "#F59E0B",
    "6": "#F59E0B",
    app: "#F59E0B",
    // 中间件红色系
    "9": "#EF4444",
    "10": "#EF4444",
    middleware: "#EF4444",
    redis: "#EF4444",
    // 网络、域名青色系
    "2": "#14B8A6",
    "3": "#14B8A6",
    "13": "#14B8A6",
    "14": "#14B8A6",
    network: "#14B8A6",
    domain: "#14B8A6",
    // 负载均衡
    slb: "#8B5CF6",
    负载均衡: "#8B5CF6"
  };

  // 如果找到匹配的颜色则使用，否则使用默认颜色
  return colorMap[assetType] || "#64748B";
};

// 检测是否为循环连接
const isCircularConnection = (
  sourceId: number,
  targetId: number,
  nodes: any[]
) => {
  // 检查目标节点是否也指向源节点（形成循环）
  const targetNode = nodes.find(n => n.id === targetId);
  if (
    targetNode &&
    targetNode.next_nodes &&
    Array.isArray(targetNode.next_nodes)
  ) {
    return targetNode.next_nodes.includes(sourceId);
  }
  return false;
};

// 资产弹窗相关状态
const showNodeAssetsDialog = ref(false);
const selectedNodeId = ref<number | null>(null);

function onShowNodeAssets(nodeId: number) {
  selectedNodeId.value = nodeId;
  showNodeAssetsDialog.value = true;
}

// 单独通过defineProps接收属性
const props = defineProps({
  systemId: {
    type: [Number, String],
    required: true
  },
  systemName: {
    type: String,
    default: ""
  }
});

// 使用computed获取props值，防止响应式丢失
const systemIdValue = computed(() => {
  console.log(
    "获取systemId计算属性:",
    props.systemId,
    "类型:",
    typeof props.systemId
  );
  // 确保返回数字类型
  return typeof props.systemId === "string"
    ? parseInt(props.systemId, 10)
    : props.systemId;
});
const systemName = computed(() => props.systemName);

// 获取VueFlow实例和组件方法
const {
  nodes,
  edges,
  addNodes,
  addEdges,
  setNodes,
  onNodesChange,
  fitView: fitViewFunc
} = useVueFlow({
  defaultEdgeOptions: {
    animated: true,
    type: "step",
    style: { strokeWidth: 2, stroke: "#cbd5e1" }
  },
  defaultViewport: { x: 0, y: 0, zoom: 1 },
  minZoom: 0.2,
  maxZoom: 3,
  panOnScroll: true,
  zoomOnScroll: true,
  preventScrolling: true,
  zoomOnDoubleClick: false,
  panOnDrag: true,
  selectNodesOnDrag: false,
  snapToGrid: true,
  panOnScrollMode: PanOnScrollMode.Free,
  fitViewOnInit: false
});

// 自定义节点类型
const nodeTypes = {
  customNode: CustomNode
};

// 状态管理
const flowWrapper = ref<HTMLElement | null>(null);
const loading = ref(false);
const isEditNode = ref(false);
const nodeDialogVisible = ref(false);
const deleteDialogVisible = ref(false);
const currentNodeData = ref<AssetNode | null>(null);
const elements = ref([]); // 添加elements响应式数据

// 添加全屏相关的状态和方法
const isFullscreen = ref(false);

const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value;

  // 获取页面主要元素
  const flowContainer = document.querySelector(
    ".flow-view-container"
  ) as HTMLElement;
  const mainContent = document.querySelector(".main-content") as HTMLElement;

  if (isFullscreen.value) {
    // 进入全屏模式

    // 暂存样式，用于恢复
    if (flowContainer) {
      flowContainer.setAttribute(
        "data-original-styles",
        flowContainer.getAttribute("style") || ""
      );
    }
    if (mainContent) {
      mainContent.setAttribute(
        "data-original-styles",
        mainContent.getAttribute("style") || ""
      );
    }

    // 添加全局全屏样式
    const styleEl = document.createElement("style");
    styleEl.id = "flow-fullscreen-styles";
    styleEl.textContent = `
      /* 调整下拉菜单层级 */
      .el-select-dropdown, .el-dropdown-menu, .el-popper {
        z-index: 9999 !important;
      }

      /* 全屏状态下的主内容区域 */
      body.flow-fullscreen-active .main-content {
        padding: 0 !important;
        margin: 0 !important;
        height: 100vh !important;
        max-height: 100vh !important;
        overflow: hidden !important;
        transition: none !important;
      }

      /* 全屏状态下的流程图容器 */
      body.flow-fullscreen-active .flow-view-container {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
        width: 100vw !important;
        height: 100vh !important;
        z-index: 2000 !important;
        background-color: #fff !important;
        overflow: auto !important;
        padding: 0 !important;
        margin: 0 !important;
        border-radius: 0 !important;
        transition: none !important;
        will-change: transform !important;
      }

      /* 全屏状态下的流程图头部 */
      body.flow-fullscreen-active .flow-header {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        width: 100% !important;
        z-index: 2001 !important;
        background-color: #fff !important;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
        transition: none !important;
      }

      /* 全屏状态下的流程图内容区 */
      body.flow-fullscreen-active .flow-wrapper {
        position: absolute !important;
        top: 85px !important; /* 头部高度 + 提示信息高度 */
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
        height: calc(100vh - 85px) !important;
        width: 100% !important;
        transition: none !important;
        will-change: transform !important;
      }

      /* 隐藏过渡动画，避免闪烁 */
      body.flow-fullscreen-active .vue-flow__node {
        transition: none !important;
      }

      body.flow-fullscreen-active .vue-flow__edge {
        transition: none !important;
      }

      body.flow-fullscreen-active .vue-flow__viewport {
        transition: none !important;
      }

      /* 全局视图优化 - 这里保留CSS样式，虽然移除了smoothViewport选项 */
      .vue-flow__viewport {
        transform-origin: 0 0;
        will-change: transform;
      }

      .vue-flow__node {
        transform-origin: center center;
        will-change: transform;
      }

      .vue-flow {
        cursor: grab;
      }

      .vue-flow.dragging {
        cursor: grabbing;
      }

      .vue-flow__edge-path {
        stroke-width: 2px;
        pointer-events: all;
      }

      /* 平滑化节点和边的变换 */
      .vue-flow__node-position-animate {
        transition: transform 0ms !important;
      }

      /* 禁用动画过渡，避免闪烁 */
      .vue-flow .vue-flow__viewport {
        transition-duration: 0ms !important;
        transition-timing-function: linear !important;
        transform-style: preserve-3d;
        backface-visibility: hidden;
      }
    `;
    document.head.appendChild(styleEl);
    document.body.classList.add("flow-fullscreen-active");

    // 设置其他内容区域样式，立即完成过渡
    if (flowWrapper.value) {
      flowWrapper.value.style.transition = "none";
    }

    // 延迟执行适应视图，避免立即触发导致闪烁
    setTimeout(() => {
      // 全屏模式下不自动适应视图，减少闪烁
      // fitView();
    }, 500);
  } else {
    // 退出全屏模式

    // 恢复各元素原始样式
    if (flowContainer) {
      const originalStyles =
        flowContainer.getAttribute("data-original-styles") || "";
      flowContainer.removeAttribute("data-original-styles");
      flowContainer.setAttribute("style", originalStyles);
    }

    if (mainContent) {
      const originalStyles =
        mainContent.getAttribute("data-original-styles") || "";
      mainContent.removeAttribute("data-original-styles");
      mainContent.setAttribute("style", originalStyles);
    }

    // 移除全局全屏样式
    const styleEl = document.getElementById("flow-fullscreen-styles");
    if (styleEl) {
      document.head.removeChild(styleEl);
    }
    document.body.classList.remove("flow-fullscreen-active");

    // 恢复流程图容器大小
    if (flowWrapper.value) {
      flowWrapper.value.style.transition = "none"; // 先移除过渡动画
      flowWrapper.value.style.height = "calc(100vh - 180px)";

      // 稍后恢复过渡动画
      setTimeout(() => {
        flowWrapper.value.style.transition = "";
      }, 300);
    }

    // 延迟执行适应视图，避免触发闪烁
    setTimeout(() => {
      // 退出全屏后不自动适应视图，减少闪烁
      // fitView();
    }, 500);
  }
};

// 获取节点数据
const fetchNodes = async () => {
  if (systemIdValue.value === undefined || systemIdValue.value === null) {
    console.log("无效的systemId，无法获取节点数据");
    return;
  }

  try {
    loading.value = true;

    // 清空现有节点和边
    setNodes([]);
    edges.value = [];
    elements.value = [];

    const sysId = Number(systemIdValue.value);
    const res = await getAssetNodesAPI(sysId);

    if (res.success && res.data) {
      console.log("获取节点数据成功, 节点数量:", res.data.length);

      if (res.data.length === 0) {
        loading.value = false;
        return;
      }

      try {
        // 如果找不到系统节点，就使用所有没有父节点的节点作为根节点
        let rootNodes = [];
        if (rootNodes.length === 0) {
          // 找出没有被其他节点指向的节点作为根节点
          const hasParentSet = new Set();
          res.data.forEach(node => {
            if (node.next_nodes && Array.isArray(node.next_nodes)) {
              node.next_nodes.forEach(targetId => {
                hasParentSet.add(targetId);
              });
            }
          });

          rootNodes = res.data.filter(node => !hasParentSet.has(node.id));
        }

        // 如果还是找不到根节点，就使用第一个节点
        if (rootNodes.length === 0 && res.data.length > 0) {
          rootNodes = [res.data[0]];
        }

        console.log("找到根节点:", rootNodes.length, "个");

        let newNodes: any[] = [];
        let newEdges: any[] = [];
        const processedNodeIds = new Set();

        // 计算根节点的起始X坐标，使整体居中
        const totalRootWidth = rootNodes.length * 400; // 增加根节点间距
        const startX = Math.max(
          500,
          window.innerWidth / 2 - totalRootWidth / 2
        );

        // 对每个根节点，使用层次布局
        rootNodes.forEach((rootNode, rootIndex) => {
          // 计算这个根节点的位置 - 水平排列在顶部并居中
          const rootX = startX + rootIndex * 400; // 增加根节点间距
          const rootY = 150;

          // 创建根节点
          const rootVueNode = {
            id: `${rootNode.id}`,
            type: "customNode",
            data: {
              name: rootNode.name,
              label: rootNode.name,
              icon: getIcon(
                typeof rootNode.asset_type === "string"
                  ? rootNode.asset_type
                  : ""
              ),
              id: rootNode.id,
              node: rootNode,
              tags: rootNode.tags || [],
              asset_type: rootNode.asset_type || "",
              typeText: rootNode.asset_type
                ? getAssetTypeName(String(rootNode.asset_type))
                : "未知类型"
            },
            position: { x: rootX, y: rootY }
          };

          newNodes.push(rootVueNode);
          processedNodeIds.add(rootNode.id);

          // 用BFS算法逐层处理节点
          const queue = [
            { node: rootNode, level: 0, position: { x: rootX, y: rootY } }
          ];
          const visited = new Set([rootNode.id]);

          while (queue.length > 0) {
            const current = queue.shift()!;
            const { node, level, position } = current;

            // 处理当前节点的子节点
            if (node.next_nodes && Array.isArray(node.next_nodes)) {
              const children = node.next_nodes
                .map(id => res.data.find(n => n.id === id))
                .filter(Boolean);

              // 计算子节点的垂直分布位置
              const childCount = children.length;
              const horizontalWidth = Math.max(400, childCount * 200); // 水平宽度

              children.forEach((childNode, index) => {
                const childNodeId = childNode.id;

                // 计算子节点位置 - 向下扩展，同级节点水平分布
                // 增加水平间距，确保节点不会重叠
                const horizontalGap = Math.max(
                  350,
                  horizontalWidth / (childCount + 1)
                ); // 增加最小间距
                const childX =
                  position.x -
                  (horizontalGap * (childCount - 1)) / 2 +
                  horizontalGap * index;
                const childY = position.y + 200; // 增加垂直间距

                // 创建边连接
                const edge = {
                  id: `e${node.id}-${childNodeId}`,
                  source: `${node.id}`,
                  target: `${childNodeId}`,
                  data: {
                    label: "",
                    parent: node.id,
                    child: childNodeId,
                    level: level
                  },
                  type: "smoothstep", // 使用平滑的阶梯线条
                  animated: true,
                  style: {
                    strokeWidth: 2,
                    stroke:
                      level === 0
                        ? "#3b82f6"
                        : level === 1
                          ? "#10b981"
                          : level === 2
                            ? "#f59e0b"
                            : "#64748b",
                    strokeDasharray: isCircularConnection(
                      node.id,
                      childNodeId,
                      res.data
                    )
                      ? "5,5"
                      : "none"
                  },
                  markerEnd: {
                    type: "arrowclosed",
                    width: 30,
                    height: 30,
                    color:
                      level === 0
                        ? "#3b82f6"
                        : level === 1
                          ? "#10b981"
                          : level === 2
                            ? "#f59e0b"
                            : "#64748b"
                  }
                };

                newEdges.push(edge);

                // 如果节点未访问过，创建新节点
                if (!visited.has(childNodeId)) {
                  const childVueNode = {
                    id: `${childNodeId}`,
                    type: "customNode",
                    data: {
                      name: childNode.name,
                      label: childNode.name,
                      icon: getIcon(
                        typeof childNode.asset_type === "string"
                          ? childNode.asset_type
                          : ""
                      ),
                      id: childNodeId,
                      node: childNode,
                      tags: childNode.tags || [],
                      asset_type: childNode.asset_type || "",
                      typeText: childNode.asset_type
                        ? getAssetTypeName(String(childNode.asset_type))
                        : "未知类型"
                    },
                    position: { x: childX, y: childY }
                  };

                  newNodes.push(childVueNode);
                  processedNodeIds.add(childNodeId);
                  visited.add(childNodeId);

                  queue.push({
                    node: childNode,
                    level: level + 1,
                    position: { x: childX, y: childY }
                  });
                }
              });
            }
          }
        });

        // 最后添加未处理的节点（不在根节点子图中的孤立节点）
        res.data.forEach((node, index) => {
          if (!processedNodeIds.has(node.id)) {
            const isolatedNode = {
              id: `${node.id}`,
              type: "customNode",
              data: {
                name: node.name,
                label: node.name,
                icon: getIcon(
                  typeof node.asset_type === "string" ? node.asset_type : ""
                ),
                id: node.id,
                node: node,
                tags: node.tags || [],
                asset_type: node.asset_type || "",
                typeText: node.asset_type
                  ? getAssetTypeName(String(node.asset_type))
                  : "未知类型"
              },
              // 将孤立节点放在右侧
              position: {
                x: 1200, // 进一步增大距离
                y: 150 + (index % 6) * 180 // 增加孤立节点间距
              }
            };

            newNodes.push(isolatedNode);
            processedNodeIds.add(node.id);
          }
        });

        console.log("节点准备完成:", newNodes.length, "个节点");
        console.log("连接线准备完成:", newEdges.length, "条线");

        // 添加节点和边
        if (newNodes.length > 0) {
          addNodes(newNodes);
        }

        if (newEdges.length > 0) {
          addEdges(newEdges);
        }

        // 更新elements
        elements.value = [...newNodes, ...newEdges];

        // 确保视图更新
        await nextTick();

        // 尝试自适应视图
        setTimeout(() => {
          tryToFitView();
        }, 300);
      } catch (err) {
        console.error("创建节点布局出错:", err);
      }
    } else {
      console.error("获取节点数据失败:", res);
      message(res.msg || "获取节点数据失败", { type: "error" });
    }
  } catch (error) {
    console.error("获取节点数据失败:", error);
    message("获取节点数据失败", { type: "error" });
  } finally {
    loading.value = false;
  }
};

// 尝试自适应视图，如果失败则使用默认展示
const tryToFitView = () => {
  // 检查节点数量
  const nodeCount = nodes.value.length || 0;
  console.log(`尝试自适应视图，当前节点数量: ${nodeCount}`);

  if (nodeCount === 0) {
    console.log("没有节点，不进行自适应");
    return;
  }

  // 大于30个节点时可能会导致性能问题，设置最大尝试次数为1
  const maxAttempts = nodeCount > 30 ? 1 : 2;
  let didFitSuccessfully = false;

  try {
    console.log(`开始尝试自适应视图，最多尝试${maxAttempts}次`);

    // 先尝试进行自适应
    if (fitViewFunc) {
      // 避免超时错误，使用setTimeout
      const fitViewTimeout = setTimeout(() => {
        console.log("自适应视图超时，使用默认展示");
        setDefaultView();
      }, 2000);

      try {
        console.log("执行自适应视图");
        // 添加过渡效果
        if (flowWrapper.value) {
          flowWrapper.value.style.transition = "all 0.5s ease";
        }

        fitViewFunc({
          padding: 0.2,
          includeHiddenNodes: false,
          duration: 300
        });

        // 自适应成功
        didFitSuccessfully = true;
        console.log("自适应视图成功");
        clearTimeout(fitViewTimeout);
      } catch (error) {
        console.error("自适应视图失败:", error);
        clearTimeout(fitViewTimeout);
      }
    }

    // 如果自适应失败，使用默认视图
    if (!didFitSuccessfully) {
      console.log("自适应视图失败或不可用，使用默认展示");
      setDefaultView();
    }
  } catch (error) {
    console.error("尝试自适应视图时出错:", error);
    // 出错时使用默认视图
    setDefaultView();
  }
};

// 设置默认视图
const setDefaultView = () => {
  console.log("设置默认视图");

  try {
    // 设置一个合理的默认视图，考虑到根节点位置
    const viewport = { x: 0, y: 0, zoom: 0.8 };

    if (nodes.value.length > 0) {
      // 获取所有节点的平均位置作为中心点
      const positions = nodes.value.map(
        node => node.position || { x: 0, y: 0 }
      );
      const avgX =
        positions.reduce((sum, pos) => sum + pos.x, 0) / positions.length;
      const avgY =
        positions.reduce((sum, pos) => sum + pos.y, 0) / positions.length;

      // 设置默认视图位置
      viewport.x = window.innerWidth / 2 - avgX * 0.8;
      viewport.y = window.innerHeight / 4 - avgY * 0.8;
    }

    // 使用Vue Flow的setViewport方法设置视口
    const { setViewport } = useVueFlow();

    // 添加过渡效果
    if (flowWrapper.value) {
      flowWrapper.value.style.transition = "all 0.5s ease";
    }

    setViewport(viewport);
    console.log("已设置默认视图:", viewport);
  } catch (error) {
    console.error("设置默认视图失败:", error);
    // 如果设置视口失败，尝试使用更简单的方式
    try {
      const { setViewport } = useVueFlow();
      setViewport({ x: 0, y: 0, zoom: 0.8 });
    } catch (e) {
      console.error("设置简单默认视图也失败:", e);
    }
  }
};

// 添加节点
const handleAddNode = () => {
  console.log("打开添加节点对话框");
  isEditNode.value = false;
  currentNodeData.value = {
    id: 0, // 临时ID
    name: "新节点", // 默认名称
    system_id: 0, // 临时系统ID
    asset_type: AssetType.Host, // 使用枚举类型
    next_nodes: JSON.parse("{}"), // 空的JSON对象
    tags: [], // 默认标签
    created_at: new Date(), // 当前时间
    updated_at: new Date() // 当前时间
  };

  // 确保状态更新后再显示对话框
  nextTick(() => {
    console.log("设置nodeDialogVisible为true");
    nodeDialogVisible.value = true;
    console.log("nodeDialogVisible设置后的值:", nodeDialogVisible.value);
  });
};

// 节点点击事件
const onNodeClick = (event: NodeMouseEvent) => {
  console.log("节点点击", event);
};

// 边点击事件
const onEdgeClick = (_: EdgeMouseEvent) => {
  // 可以添加边的交互逻辑
};

// 连接节点事件
const onConnect = (connection: Connection) => {
  console.log("连接节点:", connection);
  // 正确调用Vue Flow的onConnect方法
  if (connection.source && connection.target) {
    const newEdge = {
      id: `e-${connection.source}-${connection.target}`,
      source: connection.source,
      target: connection.target,
      animated: true,
      type: "step", // 使用直角连接线
      style: { stroke: "#3b82f6", strokeWidth: 2 }
    };
    addEdges([newEdge]);

    // 这里还可以调用API更新节点关系
    console.log("添加了新的连接:", newEdge);
  }
};

// 编辑节点
const editNode = (node: any) => {
  console.log("编辑节点", node);
  // 检查和适配节点数据，确保得到符合AssetNode类型的数据
  const nodeData = node.node || node;
  currentNodeData.value = { ...nodeData }; // 创建深拷贝，避免直接修改原对象
  isEditNode.value = true;

  // 使用nextTick确保状态更新后再显示对话框
  nextTick(() => {
    nodeDialogVisible.value = true;
    console.log("打开编辑对话框，数据:", currentNodeData.value);
  });
};

// 显示删除节点对话框
const showDeleteDialog = (node: any) => {
  console.log("显示删除对话框", node);
  // 检查和适配节点数据
  const nodeData = node.node || node;
  currentNodeData.value = nodeData;
  deleteDialogVisible.value = true;
};

// 确认删除节点
const confirmDeleteNode = async () => {
  if (!currentNodeData.value) return;

  try {
    loading.value = true;
    const res = await deleteAssetNodeAPI(currentNodeData.value.id);
    if (res.success) {
      message("删除成功", { type: "success" });
      deleteDialogVisible.value = false;
      fetchNodes(); // 刷新节点列表
    } else {
      message(res.msg || "删除失败", { type: "error" });
    }
  } catch (err) {
    message("删除失败", { type: "error" });
  } finally {
    loading.value = false;
  }
};

// 表单提交成功后的回调
const handleDialogSuccess = () => {
  console.log("表单提交成功，重新获取节点数据");

  // 延迟一下确保后端数据已更新
  setTimeout(() => {
    fetchNodes(); // 刷新节点列表

    // 在节点加载完成后，自动适应视图
    nextTick(() => {
      try {
        const { fitView } = useVueFlow();
        fitView({ padding: 0.2, includeHiddenNodes: false });
        console.log("已重新调整视图以适应所有节点");
      } catch (e) {
        console.error("调整视图失败:", e);
      }

      console.log(
        "重新加载节点后的状态:",
        "节点数量:",
        nodes.value.length,
        "连接线数量:",
        edges.value.length
      );
    });
  }, 300);
};

// 监听节点变化，处理自适应
let nodeChangeTimer: ReturnType<typeof setTimeout> | null = null;
onNodesChange(changes => {
  console.log("节点变化:", changes);

  // 全屏模式下不触发自动适应，避免闪烁
  if (isFullscreen.value) {
    return;
  }

  // 节点变化时自动适应视图，但需要防抖处理
  if (nodeChangeTimer) clearTimeout(nodeChangeTimer);

  // 只有在大量变化时才适应视图
  if (changes.length > 5) {
    nodeChangeTimer = setTimeout(() => {
      if (nodes.value.length <= 30) {
        // 只有节点数量在合理范围内才自适应
        fitView();
      }
    }, 800); // 延长防抖时间
  }
});

// 初始化
onMounted(async () => {
  console.log(
    "FlowView组件已挂载，systemId:",
    systemIdValue.value,
    "类型:",
    typeof systemIdValue.value
  );

  // 确保DOM已完全渲染
  await nextTick();

  // 确保容器尺寸正确
  if (flowWrapper.value) {
    flowWrapper.value.style.height = "calc(100vh - 180px)";
    flowWrapper.value.style.width = "100%";
  }

  // 确保systemId有效后再获取节点数据
  if (systemIdValue.value !== undefined && systemIdValue.value !== null) {
    console.log("systemId有效，将开始获取节点数据");

    try {
      await fetchNodes();

      // 确保节点数据加载完成后初始化视图
      await nextTick();

      // 设置初始视图
      const { setViewport, fitView } = useVueFlow();

      // 先设置一个基础视口
      setViewport({ x: 0, y: 0, zoom: 1 });

      // 等待一小段时间后执行自适应
      setTimeout(() => {
        if (nodes.value.length > 0) {
          fitView({
            padding: 0.2,
            includeHiddenNodes: false,
            duration: 200
          });
        }
      }, 300);
    } catch (error) {
      console.error("初始化视图失败:", error);
    }
  } else {
    console.log("systemId无效，不获取节点数据:", systemIdValue.value);
  }
});

// 刷新节点数据
const refresh = () => {
  console.log("刷新节点数据被调用，当前systemId:", systemIdValue.value);
  if (systemIdValue.value === 0 || systemIdValue.value) {
    console.log("systemId有效，将获取节点数据");
    // 清空现有节点和边，确保重新加载
    try {
      console.log("清空现有节点和边");
      setNodes([]);
      edges.value = []; // 直接清空边数组
    } catch (err) {
      console.error("清空节点或边时出错:", err);
    }
    return fetchNodes();
  } else {
    console.log("systemId无效，不获取节点数据", systemIdValue.value);
    return Promise.resolve();
  }
};

// 自适应视图
const fitView = () => {
  console.log("开始适应视图");

  // 获取节点数量
  const nodeCount = nodes.value.length || 0;
  console.log(`当前节点数量: ${nodeCount}`);

  // 如果节点数量超过阈值，可能会导致抖动，不进行自适应
  if (nodeCount > 30) {
    console.log("节点数量过多，不进行自适应以避免抖动");
    return;
  }

  try {
    // 添加防抖动处理
    let attemptCount = 0;
    const maxAttempts = 1; // 减少尝试次数，避免多次触发造成闪烁
    const fitViewWithFallback = () => {
      try {
        if (attemptCount >= maxAttempts) {
          console.log("自适应尝试次数过多，放弃自适应");
          return;
        }
        attemptCount++;

        // 使用之前已获取的fitViewFunc方法
        if (fitViewFunc) {
          console.log(`正在调用fitView (尝试 ${attemptCount}/${maxAttempts})`);

          // 先设置过渡样式，减少闪烁
          if (flowWrapper.value) {
            flowWrapper.value.style.transition = "all 0.5s ease";
          }

          // 减少动画时间，减轻闪烁感
          fitViewFunc({
            padding: 0.2, // 减小边距
            includeHiddenNodes: false,
            duration: 300 // 减少动画时间
          });
          console.log("fitView调用完成");
        } else {
          console.error("fitViewFunc方法不可用");
        }
      } catch (e) {
        console.error("自适应视图失败:", e);
        console.log("自适应失败，放弃进一步尝试");
      }
    };

    // 延迟执行以确保DOM已更新，延迟时间稍长一些
    setTimeout(fitViewWithFallback, 300);
  } catch (e) {
    console.error("适应视图失败:", e);
  }
};

// 使用CSS和样式实现滚动优化效果
const applyScrollOptimizations = () => {
  // 添加全局样式以优化滚动和缩放体验
  const styleEl = document.createElement("style");
  styleEl.id = "vue-flow-optimizations";
  styleEl.textContent = `
    /* 禁用Vue Flow的默认过渡动画 */
    .vue-flow__viewport {
      transition: none !important;
      transform-style: preserve-3d;
      backface-visibility: hidden;
      will-change: transform;
    }

    .vue-flow__node {
      transition: none !important;
      transform-style: preserve-3d;
      backface-visibility: hidden;
      will-change: transform;
    }

    .vue-flow__edge {
      transition: none !important;
    }

    .vue-flow__edge-path {
      transition: none !important;
    }
  `;
  document.head.appendChild(styleEl);

  return () => {
    // 清理函数
    const el = document.getElementById("vue-flow-optimizations");
    if (el) document.head.removeChild(el);
  };
};

// 在组件挂载时应用优化，在卸载时清理
onMounted(() => {
  const cleanup = applyScrollOptimizations();

  // 在组件卸载时清理
  onBeforeUnmount(() => {
    cleanup();
  });
});

// 对外暴露的方法
defineExpose({
  refresh: refresh,
  addNode: handleAddNode,
  editNode,
  deleteNode: showDeleteDialog
});
</script>

<style scoped lang="scss">
.flow-view-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  background-color: #f8fafc;

  .flow-wrapper {
    position: relative;
    flex: 1;
    min-height: 500px;
    overflow: hidden;
    background-color: #f8fafc;
    border: none;
    border-radius: 0;

    :deep(.vue-flow) {
      width: 100%;
      height: 100%;

      .vue-flow__container {
        width: 100%;
        height: 100%;
      }

      .vue-flow__viewport {
        width: 100%;
        height: 100%;
      }

      .vue-flow__nodes {
        width: 100%;
        height: 100%;
      }
    }
  }

  .flow-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    margin-bottom: 0;
    background-color: #fff;
    border-bottom: 1px solid #e6e8eb;
    box-shadow: 0 1px 3px rgb(0 0 0 / 5%);

    h3 {
      position: relative;
      padding-left: 12px;
      margin: 0;
      font-size: 20px;
      font-weight: 600;
      color: #1f2937;

      &::before {
        position: absolute;
        top: 50%;
        left: 0;
        width: 4px;
        height: 18px;
        content: "";
        background: #409eff;
        border-radius: 2px;
        transform: translateY(-50%);
      }
    }

    .flow-actions {
      display: flex;
      gap: 12px;
    }
  }

  .flow-tips {
    padding: 0 20px 16px;

    :deep(.el-alert) {
      background-color: rgb(219 234 254 / 50%);
      border: 1px solid rgb(96 165 250 / 20%);
      border-radius: 8px;

      .el-alert__content {
        padding: 4px 0;
      }

      .el-alert__icon {
        display: none;
      }

      .custom-alert-title {
        display: flex;
        align-items: center;
        font-size: 14px;
        font-weight: 500;
        color: #3b82f6;

        .alert-icon {
          margin-right: 8px;
          font-size: 16px;
        }
      }
    }
  }

  .custom-btn {
    border-radius: 8px;
    transition: all 0.3s;

    &:hover {
      transform: translateY(-2px);
    }

    &.add-btn {
      background: linear-gradient(135deg, #3b82f6 0%, #60a5fa 100%);
      border: none;
      box-shadow: 0 4px 10px rgb(59 130 246 / 30%);

      &:hover {
        box-shadow: 0 6px 15px rgb(59 130 246 / 40%);
      }
    }

    &.fit-btn {
      border-color: #e2e8f0;

      &:hover {
        background-color: #f8fafc;
        border-color: #cbd5e1;
      }
    }

    &.refresh-btn {
      color: #10b981;
      border-color: #34d399;

      &:hover {
        background-color: rgb(16 185 129 / 5%);
        border-color: #10b981;
      }
    }

    &.fullscreen-btn {
      color: #f59e0b;
      border-color: #fbbf24;

      &:hover {
        background-color: rgb(245 158 11 / 5%);
        border-color: #f59e0b;
      }
    }
  }
}
</style>
