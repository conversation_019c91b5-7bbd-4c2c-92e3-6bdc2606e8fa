<template>
  <div class="node-manager">
    <el-card class="node-card">
      <div class="node-content">
        <flow-view
          ref="flowViewRef"
          :system-id="systemId"
          :system-name="systemName"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, defineExpose, computed, nextTick } from "vue";
import FlowView from "./flow-view.vue";

const props = defineProps<{
  systemId: number;
  systemName: string;
}>();

// 使用计算属性获取props值
const systemId = computed(() => props.systemId);
const systemName = computed(() => props.systemName);

const flowViewRef = ref<InstanceType<typeof FlowView> | null>(null);

// 刷新节点数据
const refreshNodes = () => {
  flowViewRef.value?.refresh();
};

// 添加节点
const addNode = () => {
  console.log("父组件调用添加节点");
  if (flowViewRef.value) {
    flowViewRef.value.addNode();
  } else {
    console.error("flowViewRef未找到");
  }
};

// 初始化
onMounted(() => {
  console.log(
    "节点管理页面已挂载，systemId:",
    systemId.value,
    "类型:",
    typeof systemId.value
  );
  // 确保在组件挂载后立即获取节点数据
  if (systemId.value !== undefined && systemId.value !== null) {
    console.log("systemId有效，将在nextTick后刷新节点");
    // 使用nextTick确保引用已经初始化
    nextTick(() => {
      refreshNodes();
    });
  } else {
    console.log("systemId无效，不刷新节点:", systemId.value);
  }
});

// 暴露方法给父组件
defineExpose({
  refresh: refreshNodes,
  addNode
});
</script>

<style scoped lang="scss">
.node-manager {
  height: 100%;

  .node-card {
    display: flex;
    flex-direction: column;
    height: 100%;

    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .title {
        font-size: 16px;
        font-weight: bold;
      }

      .actions {
        display: flex;
        gap: 8px;
      }
    }

    .node-content {
      position: relative;
      flex: 1;
      height: calc(100% - 60px);
      overflow: hidden;
    }
  }
}

:deep(.el-card__body) {
  display: flex;
  flex: 1;
  flex-direction: column;
  padding: 0;
  overflow: hidden;
}
</style>
