<template>
  <div>
    <el-table
      v-if="!loading && assets.length"
      :data="assets"
      style="width: 100%"
      size="small"
      :empty-text="error ? error : '暂无资产'"
    >
      <el-table-column prop="name" label="资产名称" min-width="120" />
      <el-table-column prop="host" label="主机地址" min-width="120" />
    </el-table>
    <div v-else-if="loading" style=" padding: 24px 0;text-align: center">
      <el-icon><i class="el-icon-loading"></i></el-icon> 加载中...
    </div>
    <div
      v-else-if="!assets.length && !loading && !error"
      style=" padding: 24px 0; color: #999;text-align: center"
    >
      暂无资产
    </div>
    <div
      v-else-if="error"
      style=" padding: 24px 0; color: red;text-align: center"
    >
      {{ error }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import {
  getAssetNodeAssetsAPI,
  type AssetNodeAsset
} from "@/api/assets/asset-node";

const props = defineProps<{ node_id: number }>();

const assets = ref<AssetNodeAsset[]>([]);
const loading = ref(false);
const error = ref("");

const fetchAssets = async () => {
  if (!props.node_id) {
    assets.value = [];
    return;
  }
  loading.value = true;
  error.value = "";
  try {
    const res = await getAssetNodeAssetsAPI(props.node_id);
    if (!res.success) {
      error.value = res.msg || "资产获取失败";
      return;
    }
    assets.value = res.data || [];
  } catch (e: any) {
    error.value = e?.message || "资产获取失败";
    assets.value = [];
  } finally {
    loading.value = false;
  }
};

watch(() => props.node_id, fetchAssets, { immediate: true });
</script>

<style scoped>
.el-table {
  margin-top: 8px;
}
</style>
