<template>
  <el-dialog
    :model-value="visible"
    :title="isEdit ? '编辑节点' : '添加节点'"
    width="650px"
    :close-on-click-modal="false"
    destroy-on-close
    @update:model-value="updateVisible"
    @open="onDialogOpen"
    @close="onDialogClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      class="node-form"
    >
      <div class="form-section">
        <h3 class="section-title">基本信息</h3>
        <el-form-item label="节点名称" prop="name">
          <el-input
            v-model="form.name"
            placeholder="请输入节点名称"
            clearable
          />
        </el-form-item>

        <el-form-item label="资产类型" prop="asset_type">
          <el-select
            v-model="form.asset_type"
            placeholder="请选择资产类型"
            style="width: 100%"
            filterable
            clearable
          >
            <el-option
              v-for="(name, value) in AssetTypeMap"
              :key="Number(value)"
              :label="name"
              :value="Number(value)"
            >
              <div class="asset-type-option">
                <span
                  class="asset-type-dot"
                  :style="{ backgroundColor: getTypeColor(Number(value)) }"
                ></span>
                {{ name }}
              </div>
            </el-option>
          </el-select>
        </el-form-item>
      </div>

      <div class="form-section">
        <h3 class="section-title">关联信息</h3>
        <el-form-item label="下级节点" prop="next_nodes">
          <el-select
            v-model="form.next_nodes"
            multiple
            filterable
            placeholder="请选择下级节点"
            style="width: 100%"
            :disabled="!nodeOptions.length"
            size="large"
            clearable
            tag-size="large"
            tag-type="primary"
          >
            <template #empty>
              <div class="empty-data">
                <el-icon><CircleCloseFilled /></el-icon>
                <span>暂无其他节点可选</span>
              </div>
            </template>
            <el-option
              v-for="node in nodeOptions"
              :key="node.id"
              :label="node.name"
              :value="node.id"
            >
              <div class="node-option">
                <span class="node-name">{{ node.name }}</span>
                <span class="node-type">{{
                  getAssetTypeName(node.asset_type)
                }}</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="标签" prop="tag_ids">
          <el-select
            v-model="form.tag_ids"
            multiple
            filterable
            placeholder="请选择标签"
            style="width: 100%"
            :disabled="!tagOptions.length"
            size="large"
            tag-size="large"
            tag-type="primary"
            clearable
          >
            <template #empty>
              <div class="empty-data">
                <el-icon><CircleCloseFilled /></el-icon>
                <span>暂无标签数据</span>
              </div>
            </template>
            <el-option
              v-for="tag in tagOptions"
              :key="tag.id"
              :label="`${tag.key}: ${tag.value}`"
              :value="tag.id"
            >
              <div class="tag-option">
                <span class="tag-value">{{ tag.key + ":" + tag.value }}</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
      </div>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" :loading="loading" @click="submitForm">
          {{ isEdit ? "保存修改" : "创建节点" }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, reactive, onMounted, toRefs } from "vue";
import type { FormInstance, FormRules } from "element-plus";
import { AssetType, AssetTypeMap, AssetTypeColors } from "@/config/asset-type";
import type { AssetNode, AssetNodeForm } from "@/api/assets/asset-node";
import {
  createAssetNodeAPI,
  updateAssetNodeAPI,
  getAssetNodesAPI
} from "@/api/assets/asset-node";
import { getAllTagsAPI } from "@/api/asset/tag";
import { message } from "@/utils/message";
import { CircleCloseFilled } from "@element-plus/icons-vue";

const props = defineProps<{
  visible: boolean;
  isEdit: boolean;
  nodeData: AssetNode | null;
  systemId: number;
}>();

const emit = defineEmits(["update:visible", "success"]);

// 简化对话框状态管理，避免使用computed
const { visible } = toRefs(props);

// 更新visible状态
const updateVisible = (value: boolean) => {
  emit("update:visible", value);
};

const formRef = ref<FormInstance | null>(null);
const loading = ref(false);
const nodeOptions = ref<AssetNode[]>([]);
const tagOptions = ref<any[]>([]);

// 表单数据
const form = reactive<AssetNodeForm>({
  name: "",
  system_id: props.systemId || 0,
  asset_type: AssetType.System,
  next_nodes: [],
  tag_ids: []
});

// 表单验证规则
const rules: FormRules = {
  name: [{ required: true, message: "请输入节点名称", trigger: "blur" }],
  asset_type: [
    { required: true, message: "请选择资产类型", trigger: "change" }
  ],
  system_id: [{ required: true, message: "系统ID不能为空", trigger: "blur" }]
};

// 获取资产类型颜色
const getTypeColor = (typeValue: number) => {
  const colorName = AssetTypeColors[typeValue];
  const colorMap = {
    primary: "#409eff",
    success: "#67c23a",
    warning: "#e6a23c",
    danger: "#f56c6c",
    info: "#909399"
  };
  return colorMap[colorName] || "#909399";
};

// 获取资产类型名称
const getAssetTypeName = (typeValue: any) => {
  return AssetTypeMap[typeValue] || "未知类型";
};

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields();
  }
  form.name = "";
  form.asset_type = AssetType.System;
  form.next_nodes = [];
  form.tag_ids = [];
  form.system_id = props.systemId || 0;
};

// 对话框打开事件
const onDialogOpen = () => {
  console.log("对话框打开");
  initFormData();
  fetchNodes();
  fetchTags();
};

// 对话框关闭事件
const onDialogClose = () => {
  console.log("对话框关闭");
  resetForm();
};

// 关闭对话框
const closeDialog = () => {
  updateVisible(false);
};

// 初始化表单数据
const initFormData = () => {
  if (props.isEdit && props.nodeData) {
    console.log("编辑模式，初始化表单数据:", props.nodeData);
    form.name = props.nodeData.name || "";
    form.system_id = props.systemId || 0;
    form.asset_type = props.nodeData.asset_type || AssetType.System;
    // 处理next_nodes，确保它是数组
    if (props.nodeData.next_nodes) {
      if (Array.isArray(props.nodeData.next_nodes)) {
        form.next_nodes = [...props.nodeData.next_nodes];
      } else if (typeof props.nodeData.next_nodes === "object") {
        // 如果是JSON对象，尝试转换为数组
        try {
          const nextNodes = JSON.parse(
            JSON.stringify(props.nodeData.next_nodes)
          );
          form.next_nodes = Array.isArray(nextNodes) ? nextNodes : [];
        } catch (e) {
          form.next_nodes = [];
          console.error("解析next_nodes失败:", e);
        }
      } else {
        form.next_nodes = [];
      }
    } else {
      form.next_nodes = [];
    }

    // 处理标签
    form.tag_ids = props.nodeData.tags?.map(tag => tag.id) || [];

    console.log("表单数据初始化完成:", form);
  } else {
    resetForm();
    form.system_id = props.systemId || 0;
  }
};

// 监听系统ID变化
watch(
  () => props.systemId,
  newVal => {
    if (newVal) {
      form.system_id = newVal;
      fetchNodes();
      fetchTags();
    }
  }
);

// 获取系统下的所有节点
const fetchNodes = async () => {
  if (!props.systemId) return;

  try {
    const res = await getAssetNodesAPI(props.systemId);
    if (res.success && res.data) {
      // 过滤掉当前正在编辑的节点
      nodeOptions.value = res.data.filter(node => {
        return !props.isEdit || node.id !== props.nodeData?.id;
      });
    }
  } catch (error) {
    console.error("获取节点失败:", error);
  }
};

// 获取所有标签
const fetchTags = async () => {
  try {
    const res = await getAllTagsAPI();
    if (res.success && res.data) {
      tagOptions.value = res.data;
    }
  } catch (error) {
    console.error("获取标签失败:", error);
  }
};

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return;

  try {
    const valid = await formRef.value.validate();
    if (!valid) return;

    loading.value = true;
    const formData = { ...form };

    let res;
    if (props.isEdit && props.nodeData) {
      res = await updateAssetNodeAPI(props.nodeData.id, formData);
    } else {
      res = await createAssetNodeAPI(formData);
    }

    if (res.success) {
      message("操作成功", { type: "success" });
      updateVisible(false);
      emit("success");
    } else {
      message(res.msg || "操作失败", { type: "error" });
    }
  } catch (error) {
    message("操作失败", { type: "error" });
  } finally {
    loading.value = false;
  }
};

// 初始获取数据
onMounted(() => {
  if (props.systemId) {
    fetchNodes();
    fetchTags();
  }
});
</script>

<style scoped lang="scss">
.node-form {
  max-height: 65vh;
  padding: 0 10px;
  overflow-y: auto;
}

.form-section {
  padding: 16px;
  margin-bottom: 20px;
  background-color: #f8fafc;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgb(0 0 0 / 3%);

  &:hover {
    background-color: #f1f5f9;
  }
}

.section-title {
  position: relative;
  padding-left: 12px;
  margin: 0 0 16px;
  font-size: 16px;
  font-weight: 600;
  color: #334155;

  &::before {
    position: absolute;
    top: 50%;
    left: 0;
    width: 3px;
    height: 16px;
    content: "";
    background: #409eff;
    border-radius: 3px;
    transform: translateY(-50%);
  }
}

.asset-type-option {
  display: flex;
  gap: 8px;
  align-items: center;

  .asset-type-dot {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
  }
}

.node-option {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .node-name {
    font-weight: 500;
  }

  .node-type {
    padding: 2px 6px;
    font-size: 12px;
    color: #64748b;
    background-color: #f1f5f9;
    border-radius: 4px;
  }
}

.tag-option {
  display: flex;
  gap: 8px;
  align-items: center;

  .tag-value {
    font-weight: 500;
    color: #334155;
  }
}

.empty-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  color: #94a3b8;

  .el-icon {
    margin-bottom: 8px;
    font-size: 20px;
  }
}

.dialog-footer {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding-top: 8px;
}

:deep(.el-select) {
  width: 100%;
}

:deep(.el-select-dropdown__item) {
  padding: 8px 12px;
}

:deep(.el-dialog__body) {
  padding: 10px 20px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin-right: 0;
  border-bottom: 1px solid #e2e8f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 600;
}

:deep(.el-dialog__footer) {
  padding: 12px 20px 20px;
  border-top: 1px solid #e2e8f0;
}

:deep(.el-select__tags-text) {
  display: inline-block;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
