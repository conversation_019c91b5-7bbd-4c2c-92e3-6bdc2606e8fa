import { h, ref } from "vue";
import type { PaginationProps } from "@pureadmin/table";
import { message } from "@/utils/message";
import {
  getAssetSystemListAPI,
  deleteAssetSystemAPI,
  type AssetSystem,
  createAssetSystemAPI,
  updateAssetSystemAPI
} from "@/api/assets/asset-system";
import { dayjs, ElMessageBox } from "element-plus";
import { addDrawer } from "@/components/ReDrawer";
import Form from "./form.vue";
import { useRouter } from "vue-router";

export function useAssetSystem() {
  const router = useRouter();
  const loading = ref(false);
  const dataList = ref<AssetSystem[]>([]);
  const childrenRef = ref<any>(null);

  const form = ref({
    keyword: undefined,
    business_id: undefined
  });

  const pagination = ref<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true
  });

  const columns: TableColumnList = [
    {
      label: "系统名称",
      prop: "name",
      minWidth: 180
    },
    {
      label: "业务线",
      prop: "business",
      minWidth: 180
    },
    {
      label: "备注",
      prop: "remark",
      minWidth: 180
    },
    {
      label: "时间信息",
      prop: "time",
      minWidth: 280,
      cellRenderer: ({ row }) => (
        <div class="time-info">
          <div class="time-item">
            <span class="label">创建时间:</span>
            <span class="value">
              {dayjs(row.created_at).format("YYYY-MM-DD HH:mm:ss")}
            </span>
          </div>
          <div class="time-item">
            <span class="label">更新时间:</span>
            <span class="value">
              {dayjs(row.updated_at).format("YYYY-MM-DD HH:mm:ss")}
            </span>
          </div>
        </div>
      )
    },
    {
      label: "操作",
      minWidth: 180,
      slot: "operation"
    }
  ];

  async function onSearch() {
    loading.value = true;
    try {
      const { data, success, count } = await getAssetSystemListAPI({
        page: pagination.value.currentPage,
        limit: pagination.value.pageSize,
        keyword: form.value.keyword,
        business_id: form.value.business_id
      } as any);
      if (success) {
        dataList.value = data;
        pagination.value.total = count;
      }
    } catch (error) {
      message("获取系统列表失败", { type: "error" });
    } finally {
      loading.value = false;
    }
  }

  function resetForm(formEl) {
    if (!formEl) return;
    formEl.resetFields();
    onSearch();
  }

  function handleSizeChange(val: number) {
    pagination.value.pageSize = val;
    onSearch();
  }

  function handleCurrentChange(val: number) {
    pagination.value.currentPage = val;
    onSearch();
  }
  function addFunc() {
    addDrawer({
      title: "新增系统",
      contentRenderer: () =>
        h(Form, {
          isEdit: false,
          id: 0,
          ref: childrenRef
        }),
      beforeSure: async done => {
        if (childrenRef.value) {
          try {
            const valid = await childrenRef.value.validate();
            if (valid) {
              loading.value = true;
              const formData = childrenRef.value.getFormData();
              const res = await createAssetSystemAPI(formData);
              if (res.success) {
                message("创建成功", { type: "success" });
                done();
                onSearch();
              } else {
                message(res.msg || "创建失败", { type: "error" });
              }
            }
          } catch (error) {
            message("创建失败", { type: "error" });
          } finally {
            loading.value = false;
          }
        } else {
          done();
        }
      }
    });
  }

  function editFunc(row: AssetSystem) {
    addDrawer({
      title: "编辑系统",
      contentRenderer: () =>
        h(Form, {
          isEdit: true,
          id: row.id,
          ref: childrenRef
        }),
      beforeSure: async done => {
        if (childrenRef.value) {
          try {
            const valid = await childrenRef.value.validate();
            if (valid) {
              loading.value = true;
              const formData = childrenRef.value.getFormData();
              const res = await updateAssetSystemAPI(row.id, formData);
              if (res.success) {
                message("更新成功", { type: "success" });
                done();
                onSearch();
              } else {
                message(res.msg || "更新失败", { type: "error" });
              }
            }
          } catch (error) {
            message("更新失败", { type: "error" });
          } finally {
            loading.value = false;
          }
        } else {
          done();
        }
      }
    });
  }

  function viewDetails(row: AssetSystem) {
    router.push(`/assets/system/detail/${row.id}`);
  }

  async function handleDelete(row: AssetSystem) {
    try {
      await ElMessageBox.confirm("确认删除该系统吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      });

      const res = await deleteAssetSystemAPI(row.id);
      if (res.success) {
        message("删除成功", { type: "success" });
        onSearch();
      } else {
        message(res.msg || "删除失败", { type: "error" });
      }
    } catch (error) {
      if (error !== "cancel") {
        message("删除失败", { type: "error" });
      }
    }
  }

  onSearch();

  return {
    form,
    loading,
    columns,
    dataList,
    pagination,
    onSearch,
    resetForm,
    handleSizeChange,
    handleCurrentChange,
    addFunc,
    editFunc,
    handleDelete,
    viewDetails
  };
}
