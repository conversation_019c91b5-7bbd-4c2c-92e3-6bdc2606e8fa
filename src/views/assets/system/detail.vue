<template>
  <div class="system-detail-container">
    <el-card class="header-card">
      <div class="system-header">
        <div class="system-info">
          <h2>{{ systemData?.name || "加载中..." }}</h2>
          <div class="system-meta">
            <span class="meta-item">
              <label>业务线:</label>
              <span>{{ systemData?.business || "-" }}</span>
            </span>
            <span class="meta-item">
              <label>创建时间:</label>
              <span>{{ formatTime(systemData?.created_at) }}</span>
            </span>
            <span class="meta-item">
              <label>备注:</label>
              <span>{{ systemData?.remark || "-" }}</span>
            </span>
          </div>
        </div>
        <div class="system-actions">
          <el-button type="primary" @click="goBack">返回列表</el-button>
          <el-button type="primary" @click="refreshData">刷新</el-button>
        </div>
      </div>
    </el-card>

    <el-tabs v-model="activeTab" class="content-tabs">
      <div class="tab-content">
        <node-manager
          ref="nodeManagerRef"
          :system-id="systemId"
          :system-name="systemData?.name || ''"
        />
      </div>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import dayjs from "dayjs";
import { message } from "@/utils/message";
import {
  getAssetSystemDetailAPI,
  type AssetSystem
} from "@/api/assets/asset-system";
import NodeManager from "./node/index.vue";

const route = useRoute();
const router = useRouter();
const systemId = ref<number>(0);
const systemData = ref<AssetSystem | null>(null);
const activeTab = ref("nodes");
const nodeManagerRef = ref<InstanceType<typeof NodeManager> | null>(null);
const loading = ref(false);

// 获取系统详情
const fetchSystemDetail = async () => {
  if (!systemId.value) return;

  try {
    loading.value = true;
    const res = await getAssetSystemDetailAPI(systemId.value);
    if (res.success && res.data) {
      systemData.value = res.data;
    } else {
      message("获取系统详情失败", { type: "error" });
    }
  } catch (error) {
    message("获取系统详情失败", { type: "error" });
  } finally {
    loading.value = false;
  }
};

// 格式化时间
const formatTime = (time: string | undefined) => {
  if (!time) return "-";
  return dayjs(time).format("YYYY-MM-DD HH:mm:ss");
};

// 返回列表页
const goBack = () => {
  router.push("/assets/system");
};

// 刷新数据
const refreshData = async () => {
  await fetchSystemDetail();
  if (activeTab.value === "nodes") {
    nodeManagerRef.value?.refresh();
  }
};

onMounted(() => {
  const id = route.params.id;
  if (id && !isNaN(Number(id))) {
    systemId.value = Number(id);
    fetchSystemDetail();
  } else {
    message("无效的系统ID", { type: "error" });
    goBack();
  }
});
</script>

<style scoped lang="scss">
.system-detail-container {
  padding: 20px;

  .header-card {
    margin-bottom: 20px;

    .system-header {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .system-info {
        h2 {
          margin: 0 0 10px;
        }

        .system-meta {
          display: flex;
          flex-wrap: wrap;
          gap: 20px;

          .meta-item {
            label {
              margin-right: 8px;
              font-weight: bold;
            }
          }
        }
      }

      .system-actions {
        display: flex;
        gap: 10px;
      }
    }
  }

  .content-tabs {
    padding: 20px;
    background-color: white;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);

    .tab-content {
      height: calc(100vh - 260px);
      min-height: 400px;
    }
  }
}
</style>
