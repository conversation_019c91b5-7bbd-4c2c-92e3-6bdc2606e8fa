<template>
  <div class="main">
    <el-card class="form-card">
      <template #header>
        <div class="card-header">
          <span>{{ isEditMode ? "编辑系统" : "新增系统" }}</span>
        </div>
      </template>
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        class="system-form"
      >
        <el-form-item label="系统名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入系统名称" />
        </el-form-item>
        <el-form-item label="业务线" prop="business_id">
          <el-select
            v-model="form.business_id"
            placeholder="请选择业务线"
            clearable
            style="width: 100%"
            filterable
          >
            <el-option
              v-for="item in businessOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            placeholder="请输入备注"
            :rows="4"
          />
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, defineExpose, watch } from "vue";
import type { FormInstance, FormRules } from "element-plus";
import { message } from "@/utils/message";
import {
  getAssetSystemDetailAPI,
  type AssetSystemForm
} from "@/api/assets/asset-system";
import { getAllBusinessesAPI } from "@/api/asset/bussiness";

const formRef = ref<FormInstance>();
const loading = ref(false);
const isEditMode = ref(false);

const form = ref<AssetSystemForm>({
  name: "",
  remark: "",
  business_id: null
});

const businessOptions = ref([]);

const rules: FormRules = {
  name: [{ required: true, message: "请输入系统名称", trigger: "blur" }],
  business_id: [{ required: true, message: "请选择业务线", trigger: "change" }]
};

const props = defineProps<{
  isEdit: boolean;
  id: number;
}>();

watch(
  () => props.id,
  async newId => {
    if (props.isEdit && newId) {
      isEditMode.value = true;
      await loadSystemDetail(newId);
    } else {
      isEditMode.value = false;
      form.value = {
        name: "",
        remark: "",
        business_id: null
      };
    }
  },
  { immediate: true }
);

async function loadSystemDetail(id: number) {
  loading.value = true;
  try {
    const res = await getAssetSystemDetailAPI(id);
    if (res.success && res.data) {
      form.value = res.data;
    } else {
      message("获取系统详情失败", { type: "error" });
    }
  } catch (error) {
    message("获取系统详情失败", { type: "error" });
  } finally {
    loading.value = false;
  }
}

onMounted(async () => {
  // 获取业务线列表
  try {
    const businessRes = await getAllBusinessesAPI();
    if (businessRes.success && businessRes.data) {
      businessOptions.value = businessRes.data.map(item => ({
        id: item.id,
        name: item.name
      }));
    }
  } catch (error) {
    message("获取业务线列表失败", { type: "error" });
  }
});

// 暴露表单引用和验证方法给父组件
defineExpose({
  formRef,
  validate: async () => {
    if (!formRef.value) return false;
    try {
      await formRef.value.validate();
      return true;
    } catch (error) {
      return false;
    }
  },
  getFormData: () => form.value
});
</script>

<style scoped lang="scss">
.main {
  padding: 20px;
}

.form-card {
  max-width: 800px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.system-form {
  padding: 20px;
}
</style>
