<template>
  <div class="main">
    <el-card class="filter-card">
      <div class="page-header">
        <el-form :inline="true" class="form-inline">
          <el-form-item label="选择月份" class="date-form-item">
            <el-date-picker
              v-model="selectedDate"
              type="month"
              format="YYYY-MM"
              value-format="YYYY-MM"
              placeholder="选择月份"
              :disabled-date="disabledDate"
              class="date-picker"
              size="small"
              @change="handleDateChange"
            />
          </el-form-item>
          <div class="page-title-container">
            <h1 class="page-title">
              <iconify-icon-online icon="carbon:report" class="title-icon" />
              {{ selectedDate || dayjs().format("YYYY-MM") }} 资产月报
            </h1>
          </div>
          <el-form-item class="refresh-form-item">
            <el-button
              type="primary"
              :icon="Refresh"
              class="refresh-btn"
              :loading="loading"
              round
              size="small"
              @click="fetchData"
            >
              刷新
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <!-- 没有数据时的空状态提示 -->
    <div v-if="!reportData && !loading" class="no-data-container">
      <el-empty description="暂无月报数据" :image-size="200">
        <template #description>
          <div>
            <p>暂无月报数据</p>
            <p class="no-data-tips">可能是该日期没有数据或者数据加载失败</p>
          </div>
        </template>
        <el-button type="primary" @click="fetchData">重新加载</el-button>
      </el-empty>
    </div>

    <!-- 有数据时显示的内容 -->
    <template v-if="reportData">
      <!-- 计算资源统计 -->
      <el-card v-loading="loading" class="stat-card">
        <template #header>
          <div class="card-header">
            <h3>
              <iconify-icon-online
                icon="carbon:cloud-service-management"
                style="font-size: 22px"
              />计算资源统计
            </h3>
            <div class="resource-tabs">
              <el-tabs v-model="activeResourceTab">
                <el-tab-pane label="总体资源" name="total"></el-tab-pane>
                <el-tab-pane label="云资源" name="cloud"></el-tab-pane>
                <el-tab-pane label="区域资源" name="region"></el-tab-pane>
              </el-tabs>
            </div>
          </div>
        </template>

        <!-- 总体资源统计 -->
        <div v-if="activeResourceTab === 'total'">
          <el-empty
            v-if="!reportData"
            description="暂无数据"
            :image-size="200"
          ></el-empty>
          <el-row :gutter="24">
            <el-col
              v-for="(item, index) in computeResourceItems"
              :key="index"
              :span="4"
            >
              <div
                class="stat-item"
                :class="{ clickable: item.metricType }"
                @click="showMetricHistory(item)"
              >
                <div class="stat-icon-wrapper">
                  <iconify-icon-online
                    :icon="item.icon"
                    style="font-size: 36px"
                  />
                </div>
                <div class="stat-title">{{ item.title }}</div>
                <div class="stat-value">
                  {{ item.value }}
                  <span
                    v-if="item.change !== undefined"
                    class="change-indicator"
                    :class="{
                      increase: item.change > 0,
                      decrease: item.change < 0,
                      'no-change': item.change === 0
                    }"
                  >
                    <iconify-icon-online
                      v-if="item.change !== 0"
                      :icon="item.change > 0 ? 'ep:top' : 'ep:bottom'"
                      style="margin-right: 2px"
                    />
                    <span v-else style="margin-right: 2px">-</span>
                    {{
                      item.change === 0
                        ? "-"
                        : item.isMemory
                          ? formatStorage(Math.abs(item.change * 1024 * 1024))
                          : formatThousands(Math.abs(item.change)) +
                            (item.unit ? " " + item.unit : "")
                    }}
                  </span>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 云资源统计 -->
        <div v-if="activeResourceTab === 'cloud'" class="cloud-resources">
          <el-empty
            v-if="cloudResources.length === 0"
            description="暂无云资源数据"
            :image-size="200"
          ></el-empty>
          <template v-else>
            <el-row :gutter="24">
              <el-col
                v-for="(item, index) in cloudResources"
                :key="index"
                :span="12"
              >
                <el-card shadow="hover" class="cloud-card">
                  <div class="cloud-header">
                    <div class="cloud-title">
                      <iconify-icon-online
                        :icon="getCloudIcon(item.cloud_type)"
                        style="margin-right: 8px; font-size: 24px"
                      />
                      {{ item.name }}
                      <el-tag
                        size="small"
                        effect="plain"
                        class="cloud-type-tag"
                        >{{ item.cloud_type }}</el-tag
                      >
                    </div>
                  </div>
                  <div class="cloud-content">
                    <!-- 资源统计 -->
                    <el-row :gutter="16">
                      <el-col :span="12">
                        <div class="resource-stat-item">
                          <div class="resource-stat-icon">
                            <iconify-icon-online icon="grommet-icons:host" />
                          </div>
                          <div class="resource-stat-info">
                            <div class="resource-stat-title">主机数</div>
                            <div
                              class="resource-stat-value clickable"
                              @click="
                                showCloudMetricHistory(
                                  item,
                                  MetricType.HostTotal,
                                  '主机数',
                                  '台'
                                )
                              "
                            >
                              {{ formatThousands(item.host_total || 0) }} 台
                            </div>
                            <div
                              class="resource-stat-change"
                              :class="{
                                increase: (item.host_total_change || 0) > 0,
                                decrease: (item.host_total_change || 0) < 0,
                                'no-change': (item.host_total_change || 0) === 0
                              }"
                            >
                              <iconify-icon-online
                                v-if="(item.host_total_change || 0) !== 0"
                                :icon="
                                  (item.host_total_change || 0) > 0
                                    ? 'ep:top'
                                    : 'ep:bottom'
                                "
                                style="margin-right: 2px"
                              />
                              {{
                                (item.host_total_change || 0) === 0
                                  ? "-"
                                  : formatThousands(
                                      Math.abs(item.host_total_change || 0)
                                    ) + " 台"
                              }}
                            </div>
                          </div>
                        </div>
                      </el-col>
                      <el-col :span="12">
                        <div class="resource-stat-item">
                          <div class="resource-stat-icon">
                            <iconify-icon-online icon="ep:cpu" />
                          </div>
                          <div class="resource-stat-info">
                            <div class="resource-stat-title">CPU核数</div>
                            <div
                              class="resource-stat-value clickable"
                              @click="
                                showCloudMetricHistory(
                                  item,
                                  MetricType.CPUTotal,
                                  'CPU核数',
                                  '核'
                                )
                              "
                            >
                              {{ formatThousands(item.cpu_total || 0) }} 核
                            </div>
                            <div
                              class="resource-stat-change"
                              :class="{
                                increase: (item.cpu_total_change || 0) > 0,
                                decrease: (item.cpu_total_change || 0) < 0,
                                'no-change': (item.cpu_total_change || 0) === 0
                              }"
                            >
                              <iconify-icon-online
                                v-if="(item.cpu_total_change || 0) !== 0"
                                :icon="
                                  (item.cpu_total_change || 0) > 0
                                    ? 'ep:top'
                                    : 'ep:bottom'
                                "
                                style="margin-right: 2px"
                              />
                              {{
                                (item.cpu_total_change || 0) === 0
                                  ? "-"
                                  : formatThousands(
                                      Math.abs(item.cpu_total_change || 0)
                                    ) + " 核"
                              }}
                            </div>
                          </div>
                        </div>
                      </el-col>
                    </el-row>
                    <el-row :gutter="16" style="margin-top: 16px">
                      <el-col :span="12">
                        <div class="resource-stat-item">
                          <div class="resource-stat-icon">
                            <iconify-icon-online
                              icon="material-symbols:memory"
                            />
                          </div>
                          <div class="resource-stat-info">
                            <div class="resource-stat-title">内存容量</div>
                            <div
                              class="resource-stat-value clickable"
                              @click="
                                showCloudMetricHistory(
                                  item,
                                  MetricType.MemoryTotal,
                                  '内存容量',
                                  '',
                                  true
                                )
                              "
                            >
                              {{
                                formatStorage(
                                  (item.memory_mb_total || 0) * 1024 * 1024
                                )
                              }}
                            </div>
                            <div
                              class="resource-stat-change"
                              :class="{
                                increase:
                                  (item.memory_mb_total_change || 0) > 0,
                                decrease:
                                  (item.memory_mb_total_change || 0) < 0,
                                'no-change':
                                  (item.memory_mb_total_change || 0) === 0
                              }"
                            >
                              <iconify-icon-online
                                v-if="(item.memory_mb_total_change || 0) !== 0"
                                :icon="
                                  (item.memory_mb_total_change || 0) > 0
                                    ? 'ep:top'
                                    : 'ep:bottom'
                                "
                                style="margin-right: 2px"
                              />
                              {{
                                (item.memory_mb_total_change || 0) === 0
                                  ? "-"
                                  : formatStorage(
                                      Math.abs(
                                        item.memory_mb_total_change || 0
                                      ) *
                                        1024 *
                                        1024
                                    )
                              }}
                            </div>
                          </div>
                        </div>
                      </el-col>
                      <el-col :span="12">
                        <div class="resource-stat-item">
                          <div class="resource-stat-icon">
                            <iconify-icon-online icon="mdi:gpu" />
                          </div>
                          <div class="resource-stat-info">
                            <div class="resource-stat-title">GPU数量</div>
                            <div
                              class="resource-stat-value clickable"
                              @click="
                                showCloudMetricHistory(
                                  item,
                                  MetricType.GPUTotal,
                                  'GPU数量',
                                  '个'
                                )
                              "
                            >
                              {{ formatThousands(item.gpu_total || 0) }} 个
                            </div>
                            <div
                              class="resource-stat-change"
                              :class="{
                                increase: (item.gpu_total_change || 0) > 0,
                                decrease: (item.gpu_total_change || 0) < 0,
                                'no-change': (item.gpu_total_change || 0) === 0
                              }"
                            >
                              <iconify-icon-online
                                v-if="(item.gpu_total_change || 0) !== 0"
                                :icon="
                                  (item.gpu_total_change || 0) > 0
                                    ? 'ep:top'
                                    : 'ep:bottom'
                                "
                                style="margin-right: 2px"
                              />
                              {{
                                (item.gpu_total_change || 0) === 0
                                  ? "-"
                                  : formatThousands(
                                      Math.abs(item.gpu_total_change || 0)
                                    ) + " 个"
                              }}
                            </div>
                          </div>
                        </div>
                      </el-col>
                    </el-row>
                    <!-- 账单图表 -->
                    <div class="monthly-bills">
                      <div class="bills-chart">
                        <div
                          v-for="(bill, billIndex) in item.monthly_bills"
                          :key="billIndex"
                          class="bill-item"
                        >
                          <div class="bill-month">{{ bill.month }}</div>
                          <div class="bill-bar-container">
                            <div
                              class="bill-bar"
                              :style="{
                                width: calculateBillWidth(
                                  bill.cost,
                                  item.monthly_bills
                                ),
                                background: getBillColor(billIndex)
                              }"
                            ></div>
                          </div>
                          <div class="bill-cost">
                            ¥{{ formatThousands(bill.cost) }}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-card>
              </el-col>
            </el-row>
          </template>
        </div>

        <!-- 区域资源统计 -->
        <div v-if="activeResourceTab === 'region'" class="region-resources">
          <el-empty
            v-if="regionResources.length === 0"
            description="暂无区域资源数据"
            :image-size="200"
          ></el-empty>
          <template v-else>
            <el-row :gutter="24">
              <el-col
                v-for="(item, index) in regionResources"
                :key="index"
                :span="12"
              >
                <el-card shadow="hover" class="region-card">
                  <div class="region-header">
                    <div class="region-title">
                      <iconify-icon-online
                        icon="carbon:location-current"
                        style="margin-right: 8px; font-size: 24px"
                      />
                      {{ item.region_name || item.name }}
                    </div>
                  </div>
                  <div class="region-content">
                    <el-row :gutter="16">
                      <el-col :span="12">
                        <div class="resource-stat-item">
                          <div class="resource-stat-icon">
                            <iconify-icon-online icon="grommet-icons:host" />
                          </div>
                          <div class="resource-stat-info">
                            <div class="resource-stat-title">主机数</div>
                            <div
                              class="resource-stat-value clickable"
                              @click="
                                showRegionMetricHistory(
                                  item,
                                  MetricType.HostTotal,
                                  '主机数',
                                  '台'
                                )
                              "
                            >
                              {{ formatThousands(item.host_total) }} 台
                            </div>
                            <div
                              class="resource-stat-change"
                              :class="{
                                increase: item.host_total_change > 0,
                                decrease: item.host_total_change < 0,
                                'no-change': item.host_total_change === 0
                              }"
                            >
                              <iconify-icon-online
                                v-if="item.host_total_change !== 0"
                                :icon="
                                  item.host_total_change > 0
                                    ? 'ep:top'
                                    : 'ep:bottom'
                                "
                                style="margin-right: 2px"
                              />
                              {{
                                item.host_total_change === 0
                                  ? "-"
                                  : formatThousands(
                                      Math.abs(item.host_total_change)
                                    ) + " 台"
                              }}
                            </div>
                          </div>
                        </div>
                      </el-col>
                      <el-col :span="12">
                        <div class="resource-stat-item">
                          <div class="resource-stat-icon">
                            <iconify-icon-online icon="ep:cpu" />
                          </div>
                          <div class="resource-stat-info">
                            <div class="resource-stat-title">CPU核数</div>
                            <div
                              class="resource-stat-value clickable"
                              @click="
                                showRegionMetricHistory(
                                  item,
                                  MetricType.CPUTotal,
                                  'CPU核数',
                                  '核'
                                )
                              "
                            >
                              {{ formatThousands(item.cpu_total) }} 核
                            </div>
                            <div
                              class="resource-stat-change"
                              :class="{
                                increase: item.cpu_total_change > 0,
                                decrease: item.cpu_total_change < 0,
                                'no-change': item.cpu_total_change === 0
                              }"
                            >
                              <iconify-icon-online
                                v-if="item.cpu_total_change !== 0"
                                :icon="
                                  item.cpu_total_change > 0
                                    ? 'ep:top'
                                    : 'ep:bottom'
                                "
                                style="margin-right: 2px"
                              />
                              {{
                                item.cpu_total_change === 0
                                  ? "-"
                                  : formatThousands(
                                      Math.abs(item.cpu_total_change)
                                    ) + " 核"
                              }}
                            </div>
                          </div>
                        </div>
                      </el-col>
                    </el-row>
                    <el-row :gutter="16" style="margin-top: 16px">
                      <el-col :span="12">
                        <div class="resource-stat-item">
                          <div class="resource-stat-icon">
                            <iconify-icon-online
                              icon="material-symbols:memory"
                            />
                          </div>
                          <div class="resource-stat-info">
                            <div class="resource-stat-title">内存容量</div>
                            <div
                              class="resource-stat-value clickable"
                              @click="
                                showRegionMetricHistory(
                                  item,
                                  MetricType.MemoryTotal,
                                  '内存容量',
                                  '',
                                  true
                                )
                              "
                            >
                              {{
                                formatStorage(
                                  item.memory_mb_total * 1024 * 1024
                                )
                              }}
                            </div>
                            <div
                              class="resource-stat-change"
                              :class="{
                                increase: item.memory_mb_total_change > 0,
                                decrease: item.memory_mb_total_change < 0,
                                'no-change': item.memory_mb_total_change === 0
                              }"
                            >
                              <iconify-icon-online
                                v-if="item.memory_mb_total_change !== 0"
                                :icon="
                                  item.memory_mb_total_change > 0
                                    ? 'ep:top'
                                    : 'ep:bottom'
                                "
                                style="margin-right: 2px"
                              />
                              {{
                                item.memory_mb_total_change === 0
                                  ? "-"
                                  : formatStorage(
                                      Math.abs(item.memory_mb_total_change) *
                                        1024 *
                                        1024
                                    )
                              }}
                            </div>
                          </div>
                        </div>
                      </el-col>
                      <el-col :span="12">
                        <div class="resource-stat-item">
                          <div class="resource-stat-icon">
                            <iconify-icon-online icon="mdi:gpu" />
                          </div>
                          <div class="resource-stat-info">
                            <div class="resource-stat-title">GPU数量</div>
                            <div
                              class="resource-stat-value clickable"
                              @click="
                                showRegionMetricHistory(
                                  item,
                                  MetricType.GPUTotal,
                                  'GPU数量',
                                  '个'
                                )
                              "
                            >
                              {{ formatThousands(item.gpu_total) }} 个
                            </div>
                            <div
                              class="resource-stat-change"
                              :class="{
                                increase: item.gpu_total_change > 0,
                                decrease: item.gpu_total_change < 0,
                                'no-change': item.gpu_total_change === 0
                              }"
                            >
                              <iconify-icon-online
                                v-if="item.gpu_total_change !== 0"
                                :icon="
                                  item.gpu_total_change > 0
                                    ? 'ep:top'
                                    : 'ep:bottom'
                                "
                                style="margin-right: 2px"
                              />
                              {{
                                item.gpu_total_change === 0
                                  ? "-"
                                  : formatThousands(
                                      Math.abs(item.gpu_total_change)
                                    ) + " 个"
                              }}
                            </div>
                          </div>
                        </div>
                      </el-col>
                    </el-row>
                  </div>
                </el-card>
              </el-col>
            </el-row>
          </template>
        </div>
      </el-card>

      <!-- 数据资源统计 -->
      <el-card v-loading="loading" class="stat-card">
        <template #header>
          <div class="card-header">
            <h3>
              <iconify-icon-online
                icon="carbon:data-base"
                style="font-size: 22px"
              />数据资源统计
            </h3>
          </div>
        </template>
        <el-empty
          v-if="!reportData"
          description="暂无数据"
          :image-size="200"
        ></el-empty>
        <el-row v-else :gutter="24">
          <el-col
            v-for="(item, index) in dataResourceItems"
            :key="index"
            :span="4"
          >
            <div
              class="stat-item"
              :class="{ clickable: item.metricType }"
              @click="showMetricHistory(item)"
            >
              <div class="stat-icon-wrapper">
                <iconify-icon-online
                  :icon="item.icon"
                  style="font-size: 36px"
                />
              </div>
              <div class="stat-title">{{ item.title }}</div>
              <div class="stat-value">
                {{ item.value }}
                <span
                  v-if="item.change !== undefined"
                  class="change-indicator"
                  :class="{
                    increase: item.change > 0,
                    decrease: item.change < 0,
                    'no-change': item.change === 0
                  }"
                >
                  <iconify-icon-online
                    v-if="item.change !== 0"
                    :icon="item.change > 0 ? 'ep:top' : 'ep:bottom'"
                    style="margin-right: 2px"
                  />
                  <span v-else style="margin-right: 2px">-</span>
                  {{
                    item.change === 0
                      ? "-"
                      : formatStorage(Math.abs(item.change))
                  }}
                </span>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 可优化资源 -->
      <el-card v-loading="loading" class="stat-card">
        <template #header>
          <div class="card-header">
            <h3>
              <iconify-icon-online
                icon="grommet-icons:optimize"
                style="font-size: 22px"
              />可优化资源
            </h3>
          </div>
        </template>
        <el-empty
          v-if="!reportData"
          description="暂无数据"
          :image-size="200"
        ></el-empty>
        <el-row v-else :gutter="24">
          <el-col
            v-for="(item, index) in optimizableItems"
            :key="index"
            :span="8"
          >
            <div
              class="stat-item optimize-item"
              :class="{ clickable: item.metricType }"
              style="
                display: flex;
                flex-direction: column;
                align-items: center;
                height: auto;
                padding: 24px;
                margin-bottom: 20px;
                background-color: #fff;
                border-radius: 10px;
                box-shadow: 0 4px 15px 0 rgb(0 0 0 / 8%);
                transition: all 0.3s;
              "
              @click="showMetricHistory(item)"
            >
              <div class="stat-title">{{ item.title }}</div>
              <div class="progress-container">
                <el-progress
                  type="dashboard"
                  :percentage="item.value"
                  :color="item.color"
                  :stroke-width="10"
                  :width="120"
                  :show-text="true"
                  :format="() => item.value.toFixed(1) + '%'"
                />
              </div>
              <div class="stat-value">
                {{ item.value.toFixed(2) }}%
                <span
                  v-if="item.change !== undefined"
                  class="change-indicator"
                  :class="{
                    increase: item.change > 0,
                    decrease: item.change < 0,
                    'no-change': item.change === 0
                  }"
                >
                  <iconify-icon-online
                    v-if="item.change !== 0"
                    :icon="item.change > 0 ? 'ep:top' : 'ep:bottom'"
                    style="margin-right: 2px"
                  />
                  <span v-else style="margin-right: 2px">-</span>
                  {{
                    item.change === 0
                      ? "-"
                      : Math.abs(item.change).toFixed(2) + "%"
                  }}
                </span>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 域名带宽统计 -->
      <el-card v-loading="loading" class="stat-card">
        <template #header>
          <div class="card-header">
            <h3>
              <iconify-icon-online
                icon="carbon:network-2"
                style="font-size: 22px"
              />域名带宽统计
            </h3>
            <div v-if="domainBpsStats.length > 0" class="bandwidth-summary">
              <div class="summary-item">
                <div class="summary-label">
                  <iconify-icon-online
                    icon="carbon:arrow-down"
                    style="color: #409eff"
                  />
                  总入口带宽
                </div>
                <div class="summary-value">
                  {{ formatBandwidth(totalInBps) }}
                </div>
              </div>
              <div class="summary-item">
                <div class="summary-label">
                  <iconify-icon-online
                    icon="carbon:arrow-up"
                    style="color: #67c23a"
                  />
                  总出口带宽
                </div>
                <div class="summary-value">
                  {{ formatBandwidth(totalOutBps) }}
                </div>
              </div>
              <div class="bandwidth-sort">
                <span class="sort-label">排序: </span>
                <el-radio-group
                  v-model="bandwidthSortType"
                  size="small"
                  @change="sortDomainBpsStats"
                >
                  <el-radio-button label="in">入口带宽</el-radio-button>
                  <el-radio-button label="out">出口带宽</el-radio-button>
                  <el-radio-button label="domain">域名</el-radio-button>
                </el-radio-group>
                <el-switch
                  v-model="bandwidthSortDesc"
                  class="sort-direction"
                  size="small"
                  active-text="降序"
                  inactive-text="升序"
                  @change="sortDomainBpsStats"
                />
              </div>
            </div>
          </div>
        </template>
        <div class="domain-bps-container">
          <el-empty
            v-if="domainBpsStats.length === 0"
            description="暂无数据"
            :image-size="200"
          ></el-empty>
          <template v-else>
            <el-row :gutter="16">
              <el-col
                v-for="(item, index) in domainBpsStats"
                :key="index"
                :xs="24"
                :sm="12"
                :md="8"
                :lg="6"
                :xl="6"
                class="domain-bps-col"
              >
                <div
                  class="domain-bps-card clickable"
                  @click="showDomainBpsHistory(item)"
                >
                  <div class="domain-header">
                    <div class="domain-name">
                      <iconify-icon-online
                        icon="carbon:earth"
                        style="margin-right: 8px; font-size: 18px"
                      />
                      {{ item.domain }}
                    </div>
                  </div>
                  <div class="bandwidth-stats">
                    <div class="bandwidth-row">
                      <div class="bandwidth-label">
                        <iconify-icon-online
                          icon="carbon:arrow-down"
                          style="color: #409eff"
                        />
                        入口带宽
                      </div>
                      <div class="bandwidth-value">
                        {{ formatBandwidth(item.in_bps) }}
                        <span
                          v-if="totalInBps > 0"
                          class="bandwidth-percentage"
                        >
                          ({{
                            calculateBandwidthPercentage(
                              item.in_bps,
                              totalInBps
                            ).toFixed(1)
                          }}%)
                        </span>
                      </div>
                    </div>
                    <div class="bandwidth-bar-container">
                      <div
                        class="bandwidth-bar in-bandwidth"
                        :style="{
                          width: calculateBandwidthWidth(
                            item.in_bps,
                            domainBpsStats,
                            'in'
                          )
                        }"
                      ></div>
                    </div>
                    <div class="bandwidth-row">
                      <div class="bandwidth-label">
                        <iconify-icon-online
                          icon="carbon:arrow-up"
                          style="color: #67c23a"
                        />
                        出口带宽
                      </div>
                      <div class="bandwidth-value">
                        {{ formatBandwidth(item.out_bps) }}
                        <span
                          v-if="totalOutBps > 0"
                          class="bandwidth-percentage"
                        >
                          ({{
                            calculateBandwidthPercentage(
                              item.out_bps,
                              totalOutBps
                            ).toFixed(1)
                          }}%)
                        </span>
                      </div>
                    </div>
                    <div class="bandwidth-bar-container">
                      <div
                        class="bandwidth-bar out-bandwidth"
                        :style="{
                          width: calculateBandwidthWidth(
                            item.out_bps,
                            domainBpsStats,
                            'out'
                          )
                        }"
                      ></div>
                    </div>
                  </div>
                </div>
              </el-col>
            </el-row>
          </template>
        </div>
      </el-card>

      <!-- 指标历史记录对话框 -->
      <MetricHistory
        v-model:visible="metricHistoryVisible"
        :title="currentMetric.title"
        :resource-type="currentMetric.resourceType"
        :metric-type="currentMetric.metricType"
        :resource-name="currentMetric.resourceName"
        :unit="currentMetric.unit"
        :is-memory="currentMetric.isMemory"
        :is-network="currentMetric.isNetwork"
      />
    </template>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import { getMonthlyReportAPI } from "@/api/statistic/asset/monthly-report";
import {
  formatStorage,
  formatBandwidth,
  formatThousands
} from "@/utils/format";
import dayjs from "dayjs";
import { Refresh } from "@element-plus/icons-vue";
import { ResourceType, MetricType } from "@/api/statistic/asset/metric";
import MetricHistory from "./components/MetricHistory.vue";

// 域名带宽统计接口定义
interface DomainBpsStat {
  domain: string;
  in_bps: number;
  out_bps: number;
}

// 响应式数据
const loading = ref(false);
const reportData = ref<any>(null);
const selectedDate = ref(dayjs().subtract(1, "month").format("YYYY-MM"));
const domainBpsStats = ref<DomainBpsStat[]>([]);
const bandwidthSortType = ref<"in" | "out" | "domain">("in");
const bandwidthSortDesc = ref(true);
const cloudResources = ref<any[]>([]);
const regionResources = ref<any[]>([]);
const activeResourceTab = ref<"total" | "cloud" | "region">("total");

// 指标历史记录对话框
const metricHistoryVisible = ref(false);
const currentMetric = ref<{
  title: string;
  resourceType: ResourceType;
  metricType: MetricType;
  resourceName: string;
  unit: string;
  isMemory: boolean;
  isNetwork: boolean;
}>({
  title: "",
  resourceType: ResourceType.Computer,
  metricType: MetricType.HostTotal,
  resourceName: "",
  unit: "",
  isMemory: false,
  isNetwork: false
});

// 禁用本月及之后的月份
const disabledDate = (time: Date) => {
  const now = new Date();
  // 当前月的第一天
  const currentMonth = new Date(now.getFullYear(), now.getMonth(), 1);
  return time.getTime() >= currentMonth.getTime();
};

// 处理日期变化
const handleDateChange = () => {
  fetchData();
};

// 获取报告数据
const fetchData = async () => {
  if (!selectedDate.value) {
    return;
  }

  loading.value = true;
  try {
    const response = await getMonthlyReportAPI({
      monthly: selectedDate.value
    });

    if (response.success && response.data) {
      reportData.value = response.data;
      domainBpsStats.value = response.data.domain_bps_stats || [];
      // 初始排序
      sortDomainBpsStats();
      cloudResources.value =
        response.data.computer_resource?.cloud_resource || [];
      regionResources.value =
        response.data.computer_resource?.region_resource || [];
      // 不显示成功消息提示
    } else {
      // 请求失败但有返回，设置为空数据
      reportData.value = null;
      domainBpsStats.value = [];
      cloudResources.value = [];
      regionResources.value = [];
      console.warn("获取月报数据失败:", response.msg || "未知错误");
    }
  } catch (error: any) {
    // 请求异常，设置为空数据
    reportData.value = null;
    domainBpsStats.value = [];
    cloudResources.value = [];
    regionResources.value = [];
    console.error("获取月报数据异常:", error);
  } finally {
    loading.value = false;
  }
};

// 计算资源统计项
const computeResourceItems = computed(() => {
  if (!reportData.value || !reportData.value.computer_resource) return [];

  const { computer_resource } = reportData.value;
  return [
    {
      title: "主机数",
      icon: "carbon:virtual-machine",
      value: formatThousands(computer_resource.host_total) + " 台",
      change: computer_resource.host_total_change,
      unit: "台",
      resourceType: ResourceType.Computer,
      metricType: MetricType.HostTotal
    },
    {
      title: "CPU核数",
      icon: "ep:cpu",
      value: formatThousands(computer_resource.cpu_total) + " 核",
      change: computer_resource.cpu_total_change,
      unit: "核",
      resourceType: ResourceType.Computer,
      metricType: MetricType.CPUTotal
    },
    {
      title: "内存容量",
      icon: "material-symbols:memory",
      value: formatStorage(computer_resource.memory_mb_total * 1024 * 1024),
      change: computer_resource.memory_mb_total_change,
      isMemory: true,
      resourceType: ResourceType.Computer,
      metricType: MetricType.MemoryTotal
    },
    {
      title: "GPU数量",
      icon: "mdi:gpu",
      value: formatThousands(computer_resource.gpu_total) + " 个",
      change: computer_resource.gpu_total_change,
      unit: "个",
      resourceType: ResourceType.Computer,
      metricType: MetricType.GPUTotal
    },
    {
      title: "DLI CU数",
      icon: "mdi:server-network",
      value: formatThousands(computer_resource.dli_cu_total) + " 个",
      change: computer_resource.dli_cu_total_change,
      unit: "个",
      resourceType: ResourceType.Computer,
      metricType: MetricType.DLICUTotal
    }
  ];
});

// 数据资源统计项
const dataResourceItems = computed(() => {
  if (!reportData.value || !reportData.value.data_resource) return [];

  const { data_resource } = reportData.value;
  return [
    {
      title: "MySQL存储",
      icon: "simple-icons:mysql",
      value: formatStorage(data_resource.mysql_storage_total),
      change: data_resource.mysql_storage_total_change,
      isMemory: true,
      resourceType: ResourceType.Data,
      metricType: MetricType.MySQLStorage,
      resourceName: "MySQL存储"
    },
    {
      title: "TiDB存储",
      icon: "vscode-icons:file-type-mysql",
      value: formatStorage(data_resource.tidb_storage_total),
      change: data_resource.tidb_storage_total_change,
      isMemory: true,
      resourceType: ResourceType.Data,
      metricType: MetricType.TiDBStorage,
      resourceName: "TiDB存储"
    },
    {
      title: "MongoDB存储",
      icon: "simple-icons:mongodb",
      value: formatStorage(data_resource.mongodb_storage_total),
      change: data_resource.mongodb_storage_total_change,
      isMemory: true,
      resourceType: ResourceType.Data,
      metricType: MetricType.MongoDBStorage,
      resourceName: "MongoDB存储"
    },
    {
      title: "StarRocks存储",
      icon: "fluent:database-24-filled",
      value: formatStorage(data_resource.starrocks_storage_total),
      change: data_resource.starrocks_storage_total_change,
      isMemory: true,
      resourceType: ResourceType.Data,
      metricType: MetricType.StarrocksStorage,
      resourceName: "StarRocks存储"
    },
    {
      title: "ClickHouse存储",
      icon: "simple-icons:clickhouse",
      value: formatStorage(data_resource.clickhouse_storage_total),
      change: data_resource.clickhouse_storage_total_change,
      isMemory: true,
      resourceType: ResourceType.Data,
      metricType: MetricType.ClickhouseStorage,
      resourceName: "ClickHouse存储"
    },
    {
      title: "DLI存储",
      icon: "ph:database-duotone",
      value: formatStorage(data_resource.dli_storage_total),
      change: data_resource.dli_storage_total_change,
      isMemory: true,
      resourceType: ResourceType.Data,
      metricType: MetricType.DLIStorage,
      resourceName: "DLI存储"
    },
    {
      title: "OSS存储",
      icon: "bi:cloud-arrow-down",
      value: formatStorage(data_resource.oss_storage_total),
      change: data_resource.oss_storage_total_change,
      isMemory: true,
      resourceType: ResourceType.Data,
      metricType: MetricType.OSSStorage,
      resourceName: "OSS存储"
    },
    {
      title: "OBS存储",
      icon: "bi:cloud-arrow-down",
      value: formatStorage(data_resource.obs_storage_total),
      change: data_resource.obs_storage_total_change,
      isMemory: true,
      resourceType: ResourceType.Data,
      metricType: MetricType.OBSStorage,
      resourceName: "OBS存储"
    },
    {
      title: "SFS存储",
      icon: "carbon:storage-pool",
      value: formatStorage(data_resource.sfs_storage_total),
      change: data_resource.sfs_storage_total_change,
      isMemory: true,
      resourceType: ResourceType.Data,
      metricType: MetricType.SFSStorage,
      resourceName: "SFS存储"
    },
    {
      title: "NAS存储",
      icon: "mdi:nas",
      value: formatStorage(data_resource.nas_storage_total),
      change: data_resource.nas_storage_total_change,
      isMemory: true,
      resourceType: ResourceType.Data,
      metricType: MetricType.NASStorage,
      resourceName: "NAS存储"
    }
  ];
});

// 可优化资源统计项
const optimizableItems = computed(() => {
  if (!reportData.value || !reportData.value.optimizable_asset) return [];

  const { optimizable_asset } = reportData.value;
  return [
    {
      title: "AMD服务器占比",
      value: optimizable_asset.amd_percent || 0,
      change: optimizable_asset.amd_percent_change,
      color: getProgressColor(optimizable_asset.amd_percent || 0, true),
      resourceType: ResourceType.Optimizable,
      metricType: MetricType.AMDPercent,
      resourceName: "AMD服务器占比",
      unit: "%"
    },
    {
      title: "CPU负载使用率",
      value: optimizable_asset.cpu_usage || 0,
      change: optimizable_asset.cpu_usage_change,
      color: getProgressColor(optimizable_asset.cpu_usage || 0),
      resourceType: ResourceType.Optimizable,
      metricType: MetricType.CPUUsage,
      resourceName: "CPU负载使用率",
      unit: "%"
    },
    {
      title: "内存使用率",
      value: optimizable_asset.memory_usage || 0,
      change: optimizable_asset.memory_usage_change,
      color: getProgressColor(optimizable_asset.memory_usage || 0),
      resourceType: ResourceType.Optimizable,
      metricType: MetricType.MemoryUsage,
      resourceName: "内存使用率",
      unit: "%"
    }
  ];
});

// 格式化带宽
// 使用导入的formatBandwidth函数替代自定义函数

// 获取进度条颜色
const getProgressColor = (percent: number, isPositive = false) => {
  if (isPositive) {
    // 对于正向指标（如AMD占比），值越高越好
    if (percent >= 80) return "#67C23A";
    if (percent >= 60) return "#85ce61";
    if (percent >= 40) return "#E6A23C";
    return "#F56C6C";
  } else {
    // 对于负向指标（如资源使用率），值越低越好
    if (percent >= 90) return "#F56C6C";
    if (percent >= 70) return "#E6A23C";
    if (percent >= 50) return "#85ce61";
    return "#67C23A";
  }
};

// 获取云服务商图标
const getCloudIcon = (cloudType: string) => {
  const iconMap: Record<string, string> = {
    aliyun: "simple-icons:alibabacloud",
    aws: "logos:aws",
    azure: "logos:microsoft-azure",
    gcp: "logos:google-cloud",
    huawei: "simple-icons:huawei",
    tencent: "simple-icons:tencentqq",
    baidu: "simple-icons:baidu"
  };

  return iconMap[cloudType.toLowerCase()] || "carbon:cloud";
};

// 计算账单条形图宽度
const calculateBillWidth = (cost: number, bills: any[]) => {
  if (!bills || bills.length === 0) return "0%";

  const maxCost = Math.max(...bills.map(b => b.cost));
  if (maxCost === 0) return "0%";

  const percentage = (cost / maxCost) * 100;
  return `${Math.max(percentage, 5)}%`; // 最小宽度5%，保证可见性
};

// 计算账单条形图颜色
const getBillColor = (index: number) => {
  const colors = [
    "linear-gradient(90deg, #409eff, #53a8ff)",
    "linear-gradient(90deg, #67c23a, #85ce61)",
    "linear-gradient(90deg, #e6a23c, #f3d19e)",
    "linear-gradient(90deg, #f56c6c, #f89898)",
    "linear-gradient(90deg, #909399, #c0c4cc)",
    "linear-gradient(90deg, #9c27b0, #d559ea)"
  ];

  return colors[index % colors.length];
};

// 计算域名总入口带宽
const totalInBps = computed(() => {
  return domainBpsStats.value.reduce((sum, item) => sum + item.in_bps, 0);
});

// 计算域名总出口带宽
const totalOutBps = computed(() => {
  return domainBpsStats.value.reduce((sum, item) => sum + item.out_bps, 0);
});

// 计算带宽百分比
const calculateBandwidthPercentage = (bps: number, total: number) => {
  if (total === 0) return 0;
  return (bps / total) * 100;
};

// 计算带宽条形图宽度
const calculateBandwidthWidth = (
  bps: number,
  stats: any[],
  type: "in" | "out"
) => {
  if (!stats || stats.length === 0) return "0%";

  let maxBps = 0;
  if (type === "in") {
    maxBps = Math.max(...stats.map(s => s.in_bps));
  } else {
    maxBps = Math.max(...stats.map(s => s.out_bps));
  }

  if (maxBps === 0) return "0%";

  const percentage = (bps / maxBps) * 100;
  return `${Math.max(percentage, 5)}%`; // 最小宽度5%，保证可见性
};

// 域名带宽排序
const sortDomainBpsStats = () => {
  if (!domainBpsStats.value || domainBpsStats.value.length === 0) return;

  const sortedStats = [...domainBpsStats.value];

  // 根据排序类型和方向进行排序
  sortedStats.sort((a, b) => {
    let compareResult = 0;

    if (bandwidthSortType.value === "in") {
      compareResult = a.in_bps - b.in_bps;
    } else if (bandwidthSortType.value === "out") {
      compareResult = a.out_bps - b.out_bps;
    } else {
      // domain
      compareResult = a.domain.localeCompare(b.domain);
    }

    // 根据排序方向调整结果
    return bandwidthSortDesc.value ? -compareResult : compareResult;
  });

  domainBpsStats.value = sortedStats;
};

// 显示指标历史记录
const showMetricHistory = (item: any) => {
  if (!item.metricType) return;

  currentMetric.value = {
    title: item.title,
    resourceType: item.resourceType,
    metricType: item.metricType,
    resourceName: item.resourceName || "",
    unit: item.unit || "",
    isMemory: !!item.isMemory,
    isNetwork: !!item.isNetwork
  };

  metricHistoryVisible.value = true;
};

// 显示域名带宽历史记录
const showDomainBpsHistory = (item: any) => {
  // 根据用户排序偏好或当前排序选择显示入口或出口带宽
  const metricType =
    bandwidthSortType.value === "out" ? MetricType.OutBps : MetricType.InBps;

  currentMetric.value = {
    title: `${item.domain} 带宽历史`,
    resourceType: ResourceType.Network,
    metricType: metricType,
    resourceName: item.domain,
    unit: "bps",
    isMemory: false,
    isNetwork: true
  };

  metricHistoryVisible.value = true;
};

// 显示云资源指标历史记录
const showCloudMetricHistory = (
  item: any,
  metricType: MetricType,
  title: string,
  unit: string = "",
  isMemory: boolean = false
) => {
  metricHistoryVisible.value = true;
  currentMetric.value = {
    resourceType: ResourceType.Cloud,
    metricType: metricType,
    title: `${item.cloud_type} ${title}`,
    unit: unit,
    resourceName: item.name,
    isMemory: isMemory,
    isNetwork: false
  };
};

// 显示区域资源指标历史记录
const showRegionMetricHistory = (
  item: any,
  metricType: MetricType,
  title: string,
  unit: string = "",
  isMemory: boolean = false
) => {
  metricHistoryVisible.value = true;
  currentMetric.value = {
    resourceType: ResourceType.Region,
    metricType: metricType,
    title: `${item.region_name || item.name} ${title}`,
    unit: unit,
    resourceName: item.region_name || item.name,
    isMemory: isMemory,
    isNetwork: false
  };
};

// 生命周期钩子
onMounted(() => {
  fetchData();
});
</script>

<style scoped>
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgb(64 158 255 / 40%);
    transform: scale(0.95);
  }

  70% {
    box-shadow: 0 0 0 6px rgb(64 158 255 / 0%);
    transform: scale(1);
  }

  100% {
    box-shadow: 0 0 0 0 rgb(64 158 255 / 0%);
    transform: scale(0.95);
  }
}

/* 添加响应式布局 */
@media screen and (width <= 1200px) {
  .stat-item {
    padding: 12px;
  }

  .stat-icon-wrapper {
    width: 50px;
    height: 50px;
  }

  .stat-value {
    font-size: 16px;
  }

  .resource-stat-icon {
    width: 36px;
    height: 36px;
  }

  .resource-stat-icon > :deep(iconify-icon-online) {
    font-size: 18px;
  }

  .bill-month {
    width: 50px;
  }

  .bill-cost {
    width: 70px;
  }
}

@media screen and (width <= 768px) {
  .stat-item {
    padding: 8px;
  }

  .stat-icon-wrapper {
    width: 40px;
    height: 40px;
  }

  .stat-title {
    font-size: 12px;
  }

  .stat-value {
    font-size: 14px;
  }

  .cloud-title,
  .region-title {
    font-size: 14px;
  }

  .resource-stat-icon {
    width: 32px;
    height: 32px;
    margin-right: 8px;
  }

  .resource-stat-icon > :deep(iconify-icon-online) {
    font-size: 16px;
  }

  .resource-stat-title {
    font-size: 12px;
  }

  .resource-stat-value {
    font-size: 14px;
  }

  .bill-month {
    width: 40px;
    font-size: 12px;
  }

  .bill-bar-container {
    height: 12px;
  }

  .bill-cost {
    width: 60px;
    font-size: 12px;
  }
}

.main {
  padding: 12px;
}

.page-header {
  margin-bottom: 12px;
  text-align: center;
}

.form-inline {
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  justify-content: space-between;
}

.date-form-item {
  margin-right: 0;
  margin-bottom: 0;
}

.refresh-form-item {
  margin-bottom: 0;
  margin-left: 0;
}

.page-title-container {
  display: flex;
  flex: 1;
  align-items: center;
  justify-content: center;
  margin: 0 8px;
}

.page-title {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
  white-space: nowrap;
  background: linear-gradient(90deg, #409eff, #79bbff);
  background-clip: text;
  animation: fade-in 0.8s ease-in-out;
  -webkit-text-fill-color: transparent;
}

.title-icon {
  margin-right: 6px;
  font-size: 18px;
  color: #409eff;
}

.page-date-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.page-date {
  display: inline-block;
  padding: 4px 12px;
  font-size: 16px;
  font-weight: 600;
  color: white;
  background-color: #409eff;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgb(0 0 0 / 10%);
  transition: all 0.3s ease;
}

.page-subtitle {
  font-size: 16px;
  color: #909399;
  animation: fade-in 1s ease-in-out;
}

.filter-card {
  padding: 8px;
  margin-bottom: 8px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
}

.filter-card:hover {
  box-shadow: 0 4px 12px 0 rgb(0 0 0 / 10%);
}

.filter-container {
  padding: 10px 12px;
}

.filter-form {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.date-form-item :deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

.date-picker {
  width: 180px;
}

/* Additional page title container styles */
.page-title-container-alt {
  flex: 1;
  margin: 0 20px;
  text-align: center;
}

.refresh-btn {
  padding: 8px 16px;
  font-weight: 500;
  transition: all 0.3s;
}

.refresh-btn:hover {
  box-shadow: 0 2px 8px rgb(0 0 0 / 15%);
  transform: scale(1.05);
}

.stat-card {
  margin-bottom: 16px;
  overflow: hidden;
  border: none;
  border-radius: 8px;
  box-shadow: 0 2px 8px 0 rgb(0 0 0 / 6%);
  opacity: 0;
  transition: all 0.3s ease;
  animation: slide-up 0.5s ease-out forwards;
}

.stat-card:nth-child(2) {
  animation-delay: 0.1s;
}

.stat-card:nth-child(3) {
  animation-delay: 0.2s;
}

.stat-card:nth-child(4) {
  animation-delay: 0.3s;
}

.stat-card:nth-child(5) {
  animation-delay: 0.4s;
}

.stat-card:hover {
  box-shadow: 0 4px 16px 0 rgb(0 0 0 / 10%);
  transform: translateY(-2px);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 4px 0;
}

.resource-tabs {
  margin-left: 20px;
}

.resource-tabs :deep(.el-tabs__header) {
  margin-bottom: 0;
}

.resource-tabs :deep(.el-tabs__nav-wrap::after) {
  display: none;
}

.resource-tabs :deep(.el-tabs__item) {
  height: 40px;
  font-size: 14px;
  font-weight: 500;
  line-height: 40px;
  transition: all 0.3s;
}

.resource-tabs :deep(.el-tabs__item.is-active) {
  font-weight: 600;
  color: #409eff;
}

.resource-tabs :deep(.el-tabs__active-bar) {
  height: 2px;
  background-color: #409eff;
  border-radius: 2px;
}

.card-header h3 {
  position: relative;
  display: flex;
  align-items: center;
  padding: 4px 0;
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.card-header h3::after {
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 30px;
  height: 2px;
  content: "";
  background-color: #409eff;
  border-radius: 2px;
}

.card-header h3 > :deep(iconify-icon-online) {
  margin-right: 8px;
  color: #409eff;
}

.stat-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;
  margin-bottom: 16px;
  overflow: hidden;
  text-align: center;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 5%);
  transition: all 0.3s ease;
}

.stat-item::before {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  content: "";
  background: linear-gradient(90deg, #409eff, #67c23a);
  transition: transform 0.5s ease;
  transform: translateX(-100%);
}

.stat-item:hover::before {
  transform: translateX(0);
}

.stat-item:hover {
  box-shadow: 0 4px 12px rgb(0 0 0 / 10%);
  transform: translateY(-3px);
}

.stat-title {
  margin: 12px 0 8px;
  font-size: 14px;
  font-weight: 500;
  color: #606266;
}

.stat-value {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.stat-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  color: #409eff;
  background: linear-gradient(135deg, #f0f5ff, #e6f7ff);
  border-radius: 50%;
  box-shadow: 0 4px 12px rgb(0 149 255 / 15%);
  transition: all 0.3s ease;
}

.stat-item:hover .stat-icon-wrapper {
  box-shadow: 0 6px 16px rgb(0 149 255 / 25%);
  transform: scale(1.1);
}

.change-indicator {
  display: inline-flex;
  align-items: center;
  padding: 2px 6px;
  margin-left: 8px;
  font-size: 12px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.increase {
  color: #f56c6c;
  background-color: #fef0f0;
}

.decrease {
  color: #67c23a;
  background-color: #f0f9eb;
}

.no-change {
  color: #909399;
  background-color: #f4f4f5;
}

.optimize-item .progress-container {
  position: relative;
  margin: 16px auto;
}

.optimize-item .progress-container::after {
  position: absolute;
  inset: 0;
  content: "";
  border-radius: 50%;
  box-shadow: 0 0 0 2px rgb(64 158 255 / 10%);
  animation: pulse 2s infinite;
}

.table-container {
  width: 100%;
  padding: 0 16px;
  margin-top: 16px;
}

.bandwidth-cell {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* 表格样式优化 */
:deep(.el-table) {
  overflow: hidden;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 5%);
}

:deep(.el-table__row) {
  transition: all 0.3s;
}

:deep(.el-table__row:hover) {
  background-color: #f0f7ff !important;
}

:deep(.el-progress-bar__inner) {
  transition: all 0.6s ease-in-out;
}

:deep(.el-progress-bar__outer) {
  background-color: #ebeef5;
  border-radius: 4px;
}

:deep(.el-progress__text) {
  font-weight: 600;
  color: #606266;
}

/* 自定义日期选择器样式 */
:deep(.el-date-editor) {
  border-radius: 8px;
}

:deep(.el-button) {
  border-radius: 8px;
  transition: all 0.3s ease;
}

:deep(.el-button:hover) {
  box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
  transform: translateY(-2px);
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

/* 云资源和区域资源样式 */
.cloud-resources,
.region-resources {
  margin-top: 16px;
}

.cloud-card,
.region-card {
  height: 100%;
  overflow: hidden;
  border: none;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 6%);
  transition: all 0.3s ease;
}

.cloud-card:hover,
.region-card:hover {
  box-shadow: 0 4px 12px rgb(0 0 0 / 10%);
}

.cloud-card :deep(.el-card__header),
.region-card :deep(.el-card__header) {
  padding: 12px 16px;
  background: linear-gradient(to right, #f8f9fa, #fff);
  border-bottom: 1px solid rgb(0 0 0 / 5%);
}

.cloud-header,
.region-header {
  padding-bottom: 12px;
  margin-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.cloud-title,
.region-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.cloud-type-tag {
  margin-left: 8px;
}

.cloud-content,
.region-content {
  padding: 8px 0;
}

.monthly-bills {
  margin-top: 12px;
}

.bills-title {
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: 500;
  color: #606266;
}

.bills-chart {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.bill-item {
  display: flex;
  gap: 10px;
  align-items: center;
}

.bill-month {
  width: 60px;
  font-size: 13px;
  color: #606266;
}

.bill-bar-container {
  flex: 1;
  height: 16px;
  overflow: hidden;
  background-color: #f5f7fa;
  border-radius: 8px;
}

.bill-bar {
  height: 100%;
  border-radius: 8px;
  transition: width 0.6s ease-in-out;
}

.bill-cost {
  width: 80px;
  font-size: 13px;
  font-weight: 500;
  color: #303133;
  text-align: right;
}

/* 域名带宽统计样式 */
.domain-bps-container {
  padding: 16px;
}

.no-data-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  margin-bottom: 20px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
}

.no-data-tips {
  margin-top: 10px;
  font-size: 14px;
  color: #909399;
}

.domain-bps-col {
  margin-bottom: 16px;
}

.domain-bps-card {
  height: 100%;
  padding: 16px;
  background-color: #fff;
  border: 1px solid #e6e6e6;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 5%);
  transition: all 0.3s ease;
}

.domain-bps-card:hover {
  background-color: #f9fafc;
  border-color: #d9ecff;
  box-shadow: 0 4px 12px rgb(0 0 0 / 10%);
}

.domain-bp.resource-stat-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  padding: 0 4px;
  margin-top: 20px;
}

.resource-stat-item {
  position: relative;
  display: flex;
  align-items: center;
  padding: 20px;
  overflow: hidden;
  background-color: #fff;
  border: 1px solid rgb(0 0 0 / 3%);
  border-radius: 12px;
  box-shadow: 0 4px 12px rgb(0 0 0 / 5%);
  transition: all 0.3s ease;
}

.resource-stat-item::before {
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  content: "";
  background-color: #409eff;
  opacity: 0.8;
}

.resource-stat-item:hover {
  box-shadow: 0 8px 16px rgb(0 0 0 / 10%);
  transform: translateY(-3px);
}

.domain-header {
  padding-bottom: 8px;
  margin-bottom: 12px;
  border-bottom: 1px dashed #ebeef5;
}

.domain-name {
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.bandwidth-stats {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.bandwidth-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 4px;
}

.bandwidth-label {
  display: flex;
  gap: 6px;
  align-items: center;
  font-size: 13px;
  color: #606266;
}

.bandwidth-value {
  display: flex;
  align-items: center;
  font-size: 13px;
  font-weight: 500;
  color: #303133;
  white-space: nowrap;
}

.bandwidth-percentage {
  margin-left: 4px;
  font-size: 12px;
  font-weight: normal;
  color: #909399;
}

.bandwidth-bar-container {
  height: 8px;
  overflow: hidden;
  background-color: #ebeef5;
  border-radius: 4px;
}

.bandwidth-bar {
  height: 100%;
  border-radius: 4px;
  transition: width 0.6s ease-in-out;
}

.in-bandwidth {
  background: linear-gradient(90deg, #409eff, #53a8ff);
}

.out-bandwidth {
  background: linear-gradient(90deg, #67c23a, #85ce61);
}

.bandwidth-summary {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
}

.bandwidth-sort {
  display: flex;
  gap: 8px;
  align-items: center;
  margin-left: auto;
}

.sort-label {
  font-size: 13px;
  color: #606266;
}

.sort-direction {
  margin-left: 8px;
}

.summary-item {
  display: flex;
  gap: 8px;
  align-items: center;
}

.summary-label {
  display: flex;
  gap: 4px;
  align-items: center;
  font-size: 13px;
  color: #606266;
}

.summary-value {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

/* 区域资源统计项目样式 */
.resource-stat-item-alt {
  display: flex;
  align-items: center;
  padding: 12px;
  background-color: #f9fafc;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.resource-stat-item-alt:hover {
  background-color: #f0f7ff;
}

.resource-stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  margin-right: 12px;
  color: #409eff;
  background: linear-gradient(135deg, #f0f5ff, #e6f7ff);
  border-radius: 50%;
  box-shadow: 0 2px 8px rgb(0 149 255 / 15%);
}

.resource-stat-icon > :deep(iconify-icon-online) {
  font-size: 20px;
}

.resource-stat-info {
  flex: 1;
}

.resource-stat-title {
  margin-bottom: 4px;
  font-size: 13px;
  color: #606266;
}

.resource-stat-value {
  margin-bottom: 4px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.resource-stat-change {
  display: inline-flex;
  align-items: center;
  padding: 2px 6px;
  font-size: 12px;
  border-radius: 4px;
}
</style>
