<template>
  <el-dialog
    :modelValue="dialogVisible"
    :title="title"
    :close-on-click-modal="true"
    width="70%"
    destroy-on-close
    custom-class="metric-history-dialog"
    @update:modelValue="updateDialogVisible"
  >
    <div v-loading="loading" class="metric-history-container">
      <div class="date-picker-container">
        <div class="filter-panel">
          <div class="filter-item">
            <span class="filter-label"
              ><i class="el-icon-time" style="margin-right: 4px"></i
              >时间范围</span
            >
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :shortcuts="dateShortcuts"
              :disabled-date="disabledDate"
              value-format="YYYY-MM-DD"
              class="date-range"
              size="small"
              @change="fetchMetricHistory"
            />
          </div>
        </div>
      </div>

      <div v-if="error" class="error-message">
        <el-alert
          type="error"
          :title="error"
          show-icon
          :closable="false"
          effect="dark"
        />
      </div>
      <div v-else-if="metrics.length === 0 && !loading" class="no-data">
        <el-empty description="暂无历史数据" />
      </div>
      <div v-else-if="metrics.length > 0" class="metric-data">
        <div class="chart-container">
          <div class="chart-header">
            <div class="chart-title">{{ title }}趋势图</div>
            <div class="chart-legend">
              <div class="legend-item">
                <div class="color-dot low"></div>
                <div class="legend-text">最小值</div>
              </div>
              <div class="legend-item">
                <div class="color-dot medium"></div>
                <div class="legend-text">指标值</div>
              </div>
              <div class="legend-item">
                <div class="color-dot high"></div>
                <div class="legend-text">最大值</div>
              </div>
            </div>
          </div>
          <div ref="chartRef" class="chart"></div>
          <div v-if="metrics.length > 0" class="chart-stats">
            <div class="stat-item">
              <div class="stat-label">最新值</div>
              <div
                class="stat-value"
                :class="{
                  positive:
                    metrics[metrics.length - 1].value >
                    metrics.reduce((sum, item) => sum + item.value, 0) /
                      metrics.length,
                  negative:
                    metrics[metrics.length - 1].value <
                    metrics.reduce((sum, item) => sum + item.value, 0) /
                      metrics.length
                }"
              >
                {{ formatValue(metrics[metrics.length - 1].value) }}
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-label">平均值</div>
              <div class="stat-value neutral">
                {{
                  formatValue(
                    metrics.reduce((sum, item) => sum + item.value, 0) /
                      metrics.length
                  )
                }}
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-label">最大值</div>
              <div class="stat-value positive">
                {{ formatValue(Math.max(...metrics.map(item => item.value))) }}
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-label">最小值</div>
              <div class="stat-value negative">
                {{ formatValue(Math.min(...metrics.map(item => item.value))) }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import {
  ref,
  onMounted,
  watch,
  nextTick,
  defineProps,
  defineEmits,
  onBeforeUnmount,
  computed
} from "vue";
import * as echarts from "echarts";
import dayjs from "dayjs";
import { getResourceMetricsAPI } from "@/api/statistic/asset/metric";
import type {
  ResourceType,
  MetricType,
  MetricItem
} from "@/api/statistic/asset/metric";
import {
  formatStorage,
  formatThousands,
  formatBandwidth
} from "@/utils/format";

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: "指标历史"
  },
  resourceType: {
    type: String,
    required: true
  },
  metricType: {
    type: String,
    required: true
  },
  resourceName: {
    type: String,
    default: ""
  },
  unit: {
    type: String,
    default: ""
  },
  isMemory: {
    type: Boolean,
    default: false
  },
  isNetwork: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits<{
  "update:visible": [value: boolean];
  close: [];
}>();

const dialogVisible = ref(props.visible);
const loading = ref(false);
const error = ref("");
const metrics = ref<MetricItem[]>([]);
const chartRef = ref<HTMLElement | null>(null);
let chart: echarts.ECharts | null = null;

// 日期范围
const dateRange = ref<[string, string]>([
  dayjs().subtract(93, "days").format("YYYY-MM-DD"),
  dayjs().format("YYYY-MM-DD")
]);
const startDate = computed(() => dateRange.value[0]);
const endDate = computed(() => dateRange.value[1]);

// 格式化函数 - 移到根作用域，使其可在模板中访问
const formatValue = (value: number) => {
  let formattedValue = "";

  if (props.isMemory) {
    // 检查是否为 memory_mb_total，只有它使用 MB 单位
    if (props.metricType === "memory_mb_total") {
      // 对于 memory_mb_total，单位已经是 MB，直接格式化
      const storageStr = formatStorage(value * 1024 * 1024); // 转换为字节以便使用 formatStorage
      // 提取数字部分
      const numMatch = storageStr.match(/([\d\.]+)([A-Za-z]+)/);
      if (numMatch) {
        const num = parseFloat(numMatch[1]);
        const unit = numMatch[2];
        formattedValue = num.toFixed(2) + unit;
      } else {
        formattedValue = storageStr;
      }
    } else {
      // 其他内存指标已经是字节单位，直接格式化
      const storageStr = formatStorage(value);
      // 提取数字部分
      const numMatch = storageStr.match(/([\d\.]+)([A-Za-z]+)/);
      if (numMatch) {
        const num = parseFloat(numMatch[1]);
        const unit = numMatch[2];
        formattedValue = num.toFixed(2) + unit;
      } else {
        formattedValue = storageStr;
      }
    }
  } else if (props.isNetwork) {
    // 网络带宽格式化（保留单位和两位小数）
    const bwStr = formatBandwidth(value);
    // 提取数字部分
    const numMatch = bwStr.match(/([\d\.]+)([A-Za-z\/]+)/);
    if (numMatch) {
      const num = parseFloat(numMatch[1]);
      const unit = numMatch[2];
      formattedValue = num.toFixed(2) + unit;
    } else {
      formattedValue = bwStr;
    }
  } else {
    // 其他数字格式化（保留两位小数）
    const numStr = formatThousands(parseFloat(value.toFixed(2)));
    formattedValue = numStr + (props.unit ? ` ${props.unit}` : "");
  }

  return formattedValue;
};

// 禁用未来日期
const disabledDate = (time: Date) => {
  return time.getTime() > Date.now();
};

// 日期快捷选项
const dateShortcuts = [
  {
    text: "最近一周",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    }
  },
  {
    text: "最近一个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    }
  },
  {
    text: "最近三个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      return [start, end];
    }
  },
  {
    text: "最近半年",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 180); // 半年大约180天
      return [start, end];
    }
  },
  {
    text: "最近一年",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 365); // 一年365天
      return [start, end];
    }
  }
];

// 监听visible属性变化
watch(
  () => props.visible,
  newVal => {
    dialogVisible.value = newVal;
    if (newVal) {
      fetchMetricHistory();
    }
  }
);

// 更新对话框可见状态
const updateDialogVisible = (val: boolean) => {
  dialogVisible.value = val;
  if (!val) {
    emit("update:visible", false);
    emit("close");
  }
};

// 获取指标历史数据
const fetchMetricHistory = async () => {
  loading.value = true;
  error.value = "";
  metrics.value = [];

  try {
    const response = await getResourceMetricsAPI(
      props.resourceType as ResourceType,
      props.metricType as MetricType,
      props.resourceName,
      startDate.value,
      endDate.value
    );

    if (response && response.success) {
      // 获取数据成功
      let metricsData = [];

      if (Array.isArray(response.data)) {
        // 如果直接返回数组
        metricsData = response.data;
      } else if (response.data && response.data.metrics) {
        // 如果返回对象中包含metrics数组
        metricsData = response.data.metrics;
      }

      // 设置图表数据
      if (metricsData && metricsData.length > 0) {
        metrics.value = metricsData;
        nextTick(() => {
          initChart();
        });
      } else {
        // 没有数据
        error.value = "暂无历史数据";
      }
    } else {
      // 失败时显示错误信息
      error.value = response.msg || "获取历史数据失败";
    }
  } catch (err) {
    error.value = "获取历史数据失败，请稍后重试";
  } finally {
    loading.value = false;
  }
};

// 初始化图表
const initChart = () => {
  // 确保Chart DOM已经加载
  if (!chartRef.value) return;

  // 销毁旧图表
  if (chart) {
    chart.dispose();
    chart = null;
  }

  // 创建新图表
  chart = echarts.init(chartRef.value, null, { renderer: "canvas" });

  // 准备数据
  const dates = metrics.value.map(item => item.date);
  const values = metrics.value.map(item => item.value);

  // 图表配置
  const option = {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "cross",
        label: {
          backgroundColor: "#6a7985"
        },
        lineStyle: {
          color: "#409EFF",
          type: "dashed"
        }
      },
      backgroundColor: "rgba(255, 255, 255, 0.98)",
      borderWidth: 1,
      borderColor: "#ddd",
      padding: 12,
      textStyle: {
        color: "#333"
      },
      extraCssText:
        "box-shadow: 0 0 10px rgba(0, 0, 0, 0.1); border-radius: 4px;",
      formatter: function (params: any) {
        if (!params || !params.length || !metrics.value.length) return "";

        const date = params[0].axisValue;
        const value = params[0].value;
        const avgValue = metrics.value.length
          ? metrics.value.reduce((sum, item) => sum + item.value, 0) /
            metrics.value.length
          : 0;
        // 计算与平均值的偏差百分比
        const diffPercent =
          avgValue !== 0
            ? Math.round(((value - avgValue) / avgValue) * 100)
            : 0;
        const diffSymbol = diffPercent >= 0 ? "+" : "";
        const diffColor =
          diffPercent > 0 ? "#f56c6c" : diffPercent < 0 ? "#67c23a" : "#909399";

        return `<div style="padding: 2px 0; font-weight: 600; border-bottom: 1px solid #eee; margin-bottom: 6px;">${date}</div>
                <div style="display: flex; align-items: center; margin-bottom: 6px;">
                  <span style="display:inline-block;margin-right:6px;border-radius:50%;width:8px;height:8px;background-color:#409EFF;"></span>
                  <span style="flex: 1;">${props.title}</span>
                  <span style="font-weight: 600; margin-left: 16px;">${formatValue(
                    value
                  )}</span>
                </div>
                <div style="font-size: 12px; color: #909399;">
                  <span>与平均值偏差: </span>
                  <span style="color: ${diffColor}; font-weight: 500;">${diffSymbol}${diffPercent}%</span>
                </div>`;
      }
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "15%",
      top: "10%",
      containLabel: true
    },
    toolbox: {
      feature: {
        dataZoom: {
          yAxisIndex: "none",
          title: {
            zoom: "区域缩放",
            back: "还原"
          }
        },
        saveAsImage: {
          title: "保存图片"
        },
        restore: {
          title: "重置"
        }
      },
      right: 20,
      top: 0,
      itemSize: 15,
      itemGap: 8
    },
    dataZoom: [
      {
        type: "slider",
        show: true,
        start: 0,
        end: 100,
        bottom: 0,
        height: 20,
        borderColor: "transparent",
        backgroundColor: "#f5f7fa",
        fillerColor: "rgba(64, 158, 255, 0.15)",
        handleStyle: {
          color: "#409EFF",
          borderColor: "#409EFF"
        },
        textStyle: {
          color: "#909399"
        },
        brushSelect: false
      },
      {
        type: "inside",
        xAxisIndex: 0
      }
    ],
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: dates,
      axisLine: {
        lineStyle: {
          color: "#ddd"
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: "#666",
        fontSize: 12
      },
      splitLine: {
        show: false
      }
    },
    yAxis: {
      type: "value",
      axisLabel: {
        formatter: function (value: number) {
          return formatValue(value);
        },
        color: "#666",
        fontSize: 12
      },
      splitLine: {
        lineStyle: {
          type: "dashed",
          color: "#eee"
        }
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      }
    },
    series: [
      {
        name: props.title,
        type: "line",
        data: values,
        smooth: true,
        symbolSize: 6,
        showSymbol: false,
        emphasis: {
          focus: "series",
          itemStyle: {
            shadowBlur: 10,
            shadowColor: "rgba(0, 0, 0, 0.2)"
          }
        },
        lineStyle: {
          width: 3,
          join: "bevel",
          cap: "round",
          shadowColor: "rgba(0, 0, 0, 0.1)",
          shadowBlur: 10,
          shadowOffsetY: 5
        },
        itemStyle: {
          borderWidth: 2,
          borderColor: "#fff",
          color: "#409EFF" // 使用固定颜色避免与gradient冲突
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: "rgba(64, 158, 255, 0.2)"
            },
            {
              offset: 1,
              color: "rgba(64, 158, 255, 0.02)"
            }
          ])
        },
        markPoint: {
          symbol: "pin",
          symbolSize: 35,
          data: [
            { type: "max", name: "最大值", itemStyle: { color: "#F56C6C" } },
            { type: "min", name: "最小值", itemStyle: { color: "#67C23A" } }
          ],
          label: {
            formatter: params => formatValue(params.value),
            color: "#fff",
            fontSize: 10,
            fontWeight: "bold"
          }
        },
        markLine: {
          silent: true,
          symbol: "none",
          lineStyle: {
            type: "dashed",
            width: 1,
            color: "#909399"
          },
          data: [
            {
              type: "average",
              name: "平均值",
              label: {
                formatter: "平均值",
                position: "end",
                show: true,
                color: "#909399",
                fontSize: 12,
                backgroundColor: "rgba(255,255,255,0.8)",
                padding: [4, 5],
                borderRadius: 3
              }
            }
          ]
        },
        animation: true,
        animationDuration: 1500,
        animationEasing: "cubicOut"
      }
    ]
  };

  chart.setOption(option);

  // 监听窗口大小变化，自适应调整
  const resizeHandler = () => {
    chart && chart.resize();
  };
  window.addEventListener("resize", resizeHandler);

  // 销毁时移除事件监听
  onBeforeUnmount(() => {
    window.removeEventListener("resize", resizeHandler);
    chart && chart.dispose();
  });

  return chart;
};

// 初始加载
onMounted(() => {
  if (props.visible) {
    fetchMetricHistory();
  }
});
</script>

<style scoped>
/* Add responsive design */
@media screen and (width <= 768px) {
  .filter-panel {
    flex-direction: column;
    align-items: flex-start;
  }

  .chart {
    height: 300px;
  }

  .chart-header {
    flex-direction: column;
    gap: 10px;
  }

  .chart-stats {
    flex-wrap: wrap;
  }

  .stat-item {
    min-width: 40%;
  }
}

/* Dialog styles */
:deep(.metric-history-dialog) {
  border-radius: 8px;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

:deep(.el-dialog__headerbtn) {
  top: 16px;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

.metric-history-container {
  min-height: 400px;
}

.date-picker-container {
  margin-bottom: 20px;
}

.filter-panel {
  display: flex;
  gap: 16px;
  align-items: center;
  justify-content: space-between;
}

.filter-label {
  display: flex;
  align-items: center;
  margin-right: 12px;
  font-size: 14px;
  font-weight: 500;
  color: #606266;
}

.error-message {
  padding: 20px 0;
}

.no-data {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 300px;
}

.metric-data {
  margin-top: 10px;
}

.chart-container {
  padding: 16px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 5%);
}

.chart-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.chart-title {
  font-size: 15px;
  font-weight: 600;
  color: #303133;
}

.chart-legend {
  display: flex;
  gap: 15px;
}

.legend-item {
  display: flex;
  gap: 5px;
  align-items: center;
}

.legend-text {
  font-size: 12px;
  color: #909399;
}

.color-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.color-dot.low {
  background-color: #67c23a;
}

.color-dot.medium {
  background-color: #409eff;
}

.color-dot.high {
  background-color: #e6a23c;
}

.filter-item {
  display: flex;
  align-items: center;
}

.chart {
  height: 350px;
  margin-bottom: 20px;
}

.chart-stats {
  display: flex;
  gap: 20px;
  justify-content: space-between;
  padding: 16px;
  margin-top: 20px;
  background-color: #f9fafc;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.stat-item {
  flex: 1;
  min-width: 20%;
}

.stat-label {
  margin-bottom: 8px;
  font-size: 13px;
  color: #909399;
}

.stat-value {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.stat-value.positive {
  color: #67c23a;
}

.stat-value.negative {
  color: #f56c6c;
}

.stat-value.neutral {
  color: #409eff;
}

.date-range {
  width: 320px;
}
</style>
