<template>
  <div class="statistic-alert-container">
    <el-card class="dashboard-header mb-6" shadow="hover">
      <template #header>
        <div class="flex justify-between items-center">
          <div class="flex items-center">
            <el-icon class="mr-3 text-primary text-xl"><AlarmClock /></el-icon>
            <span class="text-xl font-bold text-primary">夜莺告警统计</span>
          </div>
        </div>
      </template>
      <div
        class="bg-gradient-to-r from-blue-50 to-indigo-50 p-5 rounded-xl transition-all duration-300 hover:shadow-lg"
      >
        <DateRangeSelector
          @day-change="handleDayChange"
          @range-change="handleDateRangeChange"
        />
      </div>
    </el-card>

    <!-- 总体告警统计卡片 -->
    <el-card
      shadow="hover"
      class="mb-6 overflow-hidden transform transition-all duration-300 hover:shadow-xl border-t-4 border-primary"
    >
      <template #header>
        <div class="flex items-center justify-between py-2">
          <div class="flex items-center">
            <el-icon class="mr-3 text-primary text-xl"
              ><DataAnalysis
            /></el-icon>
            <span class="text-xl font-bold text-primary">总体告警统计</span>
            <el-tag size="small" type="info" effect="plain" class="ml-3">
              {{
                currentDateRange?.start_time
                  ? currentDateRange.start_time.substring(0, 10)
                  : ""
              }}
              至
              {{
                currentDateRange?.end_time
                  ? currentDateRange.end_time.substring(0, 10)
                  : ""
              }}
            </el-tag>
          </div>
          <div class="flex items-center">
            <el-button
              type="primary"
              :icon="Refresh"
              size="small"
              class="refresh-btn transition-all duration-300 hover:scale-105"
              :loading="overviewLoading"
              @click="loadOverviewData"
            >
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <div
        class="total-section p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg backdrop-blur-sm"
      >
        <!-- 告警指标卡片 -->
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <!-- 总告警数 -->
          <el-card shadow="hover" class="stat-card border-l-4 border-primary">
            <div class="flex items-center">
              <el-icon class="text-2xl text-primary mr-3"><Bell /></el-icon>
              <div class="flex-1">
                <div class="text-sm text-gray-500 mb-1">总告警数</div>
                <div class="text-2xl font-bold text-primary">
                  {{ overview?.total_count || 0 }}
                </div>
              </div>
            </div>
          </el-card>

          <!-- 紧急告警 -->
          <el-card shadow="hover" class="stat-card border-l-4 border-red-500">
            <div class="flex items-center">
              <el-icon class="text-2xl text-red-500 mr-3"
                ><WarningFilled
              /></el-icon>
              <div class="flex-1">
                <div class="text-sm text-gray-500 mb-1">紧急告警</div>
                <div class="text-2xl font-bold text-red-500">
                  {{ overview?.emergency_count || 0 }}
                </div>
              </div>
            </div>
          </el-card>

          <!-- 警告告警 -->
          <el-card
            shadow="hover"
            class="stat-card border-l-4 border-orange-500"
          >
            <div class="flex items-center">
              <el-icon class="text-2xl text-orange-500 mr-3"
                ><Warning
              /></el-icon>
              <div class="flex-1">
                <div class="text-sm text-gray-500 mb-1">警告告警</div>
                <div class="text-2xl font-bold text-orange-500">
                  {{ overview?.warning_count || 0 }}
                </div>
              </div>
            </div>
          </el-card>

          <!-- 通知告警 -->
          <el-card shadow="hover" class="stat-card border-l-4 border-blue-500">
            <div class="flex items-center">
              <el-icon class="text-2xl text-blue-500 mr-3"
                ><InfoFilled
              /></el-icon>
              <div class="flex-1">
                <div class="text-sm text-gray-500 mb-1">通知告警</div>
                <div class="text-2xl font-bold text-blue-500">
                  {{ overview?.notice_count || 0 }}
                </div>
              </div>
            </div>
          </el-card>
        </div>

        <!-- 恢复率和持续时间指标 -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <!-- 恢复率 -->
          <el-card shadow="hover" class="border-l-4 border-green-500">
            <div class="flex items-center">
              <el-icon class="text-2xl text-green-500 mr-3"
                ><SuccessFilled
              /></el-icon>
              <div class="flex-1">
                <div class="flex justify-between items-center mb-2">
                  <span class="text-sm text-gray-500">告警恢复率</span>
                  <span class="font-medium text-green-500"
                    >{{
                      overview?.recovery_rate
                        ? overview.recovery_rate.toFixed(2)
                        : 0
                    }}%</span
                  >
                </div>
                <el-progress
                  :percentage="overview?.recovery_rate || 0"
                  :format="() => ''"
                  :color="getProgressColor(overview?.recovery_rate || 0)"
                  :stroke-width="10"
                />
              </div>
            </div>
          </el-card>

          <!-- 平均持续时间 -->
          <el-card shadow="hover" class="border-l-4 border-purple-500">
            <div class="flex items-center">
              <el-icon class="text-2xl text-purple-500 mr-3"><Timer /></el-icon>
              <div class="flex-1">
                <div class="text-sm text-gray-500 mb-1">平均持续时间</div>
                <div class="text-2xl font-bold text-purple-500">
                  <!-- 添加调试打印原始值 -->
                  <span v-if="overview?.avg_duration">
                    {{ formatDuration(Number(overview.avg_duration)) }}
                  </span>
                  <span v-else>0秒</span>
                </div>
              </div>
            </div>
          </el-card>
        </div>

        <!-- 告警分布饼图 -->
        <el-card shadow="hover" class="mb-6">
          <template #header>
            <div class="flex items-center justify-between py-1">
              <div class="flex items-center">
                <el-icon class="mr-2 text-primary"><PieChart /></el-icon>
                <span class="font-bold text-gray-700">告警类型分布</span>
              </div>
            </div>
          </template>
          <div class="h-80">
            <AlertChart
              v-if="
                overviewPieChartData.pieData &&
                overviewPieChartData.pieData.length > 0
              "
              chart-type="pie"
              :chart-data="overviewPieChartData"
              class="w-full h-full"
            />
            <el-empty v-else description="暂无告警分布数据" :image-size="80" />
          </div>
        </el-card>
      </div>
    </el-card>
    <!-- 实例维度告警统计 -->
    <el-card
      shadow="hover"
      class="mb-6 overflow-hidden transform transition-all duration-300 hover:shadow-xl border-t-4 border-orange-500"
    >
      <template #header>
        <div class="flex items-center justify-between py-2">
          <div class="flex items-center">
            <el-icon class="mr-3 text-orange-500 text-xl"><Monitor /></el-icon>
            <span class="text-xl font-bold text-orange-500"
              >实例维度告警统计</span
            >
            <el-tag size="small" type="info" effect="plain" class="ml-3">
              {{
                currentDateRange?.start_time
                  ? currentDateRange.start_time.substring(0, 10)
                  : ""
              }}
              至
              {{
                currentDateRange?.end_time
                  ? currentDateRange.end_time.substring(0, 10)
                  : ""
              }}
            </el-tag>
          </div>
        </div>
      </template>

      <div
        class="instance-section p-4 bg-gradient-to-r from-orange-50 to-amber-50 rounded-lg backdrop-blur-sm"
      >


        <!-- 实例告警表格 -->
        <el-card shadow="hover" :body-style="{ padding: '0' }">
          <template #header>
            <div class="flex justify-between items-center p-1">
              <div class="flex items-center">
                <el-icon class="mr-2 text-orange-500 text-xl"
                  ><Document
                /></el-icon>
                <span class="text-lg font-bold text-gray-700"
                  >实例告警明细</span
                >
                <el-tag size="small" type="info" effect="plain" class="ml-2"
                  >共 {{ instanceTableTotal }} 条记录</el-tag
                >
              </div>
              <div class="flex items-center">
                <el-button
                  type="primary"
                  :icon="Download"
                  size="small"
                  class="export-btn transition-all duration-300 hover:scale-105 mr-2"
                  @click="handleInstanceExport"
                >
                  导出
                </el-button>
                <el-button
                  type="primary"
                  :icon="Refresh"
                  size="small"
                  class="refresh-btn transition-all duration-300 hover:scale-105"
                  :loading="instanceLoading"
                  @click="loadInstanceData"
                >
                  刷新
                </el-button>
              </div>
            </div>
          </template>
          <div class="alert-table-wrapper p-2">
            <el-table
              :data="instanceTableData"
              border
              stripe
              height="400"
              :loading="instanceLoading"
              class="alert-table rounded-md overflow-hidden"
              :header-cell-style="{
                backgroundColor: '#fff7ed',
                color: '#f97316',
                fontWeight: 'bold',
                padding: '12px 8px',
                borderBottom: '2px solid #ffedd5'
              }"
              :row-class-name="tableRowClassName"
              :empty-text="null"
              @sort-change="handleInstanceSortChange"
            >
              <template #empty>
                <div class="table-empty-wrapper py-6">
                  <el-empty :image-size="0">
                    <template #image>
                      <el-icon class="empty-icon text-5xl text-gray-300"
                        ><Monitor
                      /></el-icon>
                    </template>
                    <template #description>
                      <p class="empty-text font-medium text-gray-500 mt-4">
                        暂无实例告警数据
                      </p>
                      <p class="empty-tip text-sm text-gray-400 mt-2">
                        请尝试选择其他时间范围
                      </p>
                    </template>
                  </el-empty>
                </div>
              </template>
              <el-table-column
                prop="target_ident"
                label="实例标识"
                min-width="180"
                sortable="custom"
                show-overflow-tooltip
              />
              <el-table-column
                prop="target_note"
                label="实例备注"
                min-width="150"
                show-overflow-tooltip
              />
              <el-table-column
                prop="total_count"
                label="告警总数"
                min-width="100"
                sortable="custom"
              >
                <template #default="scope">
                  <span class="font-medium text-gray-700">{{
                    scope.row.total_count
                  }}</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="emergency_count"
                label="紧急告警"
                min-width="100"
                sortable="custom"
              >
                <template #default="scope">
                  <span class="font-medium text-red-500">{{
                    scope.row.emergency_count
                  }}</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="warning_count"
                label="警告告警"
                min-width="100"
                sortable="custom"
              >
                <template #default="scope">
                  <span class="font-medium text-orange-500">{{
                    scope.row.warning_count
                  }}</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="notice_count"
                label="通知告警"
                min-width="100"
                sortable="custom"
              >
                <template #default="scope">
                  <span class="font-medium text-blue-500">{{
                    scope.row.notice_count
                  }}</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="recovery_rate"
                label="恢复率"
                min-width="100"
                sortable="custom"
              >
                <template #default="scope">
                  <el-progress
                    :percentage="scope.row.recovery_rate"
                    :format="val => val.toFixed(2) + '%'"
                    :color="getProgressColor(scope.row.recovery_rate)"
                    :stroke-width="10"
                  />
                </template>
              </el-table-column>
              <el-table-column
                prop="avg_duration"
                label="平均持续时间"
                min-width="120"
                sortable="custom"
              >
                <template #default="scope">
                  <span class="font-medium text-purple-500">{{
                    formatDuration(scope.row.avg_duration)
                  }}</span>
                </template>
              </el-table-column>
            </el-table>
            <div class="flex justify-end mt-4">
              <el-pagination
                v-model:current-page="instanceCurrentPage"
                v-model:page-size="instancePageSize"
                :page-sizes="[10, 20, 50, 100]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="instanceTableTotal"
                @size-change="handleInstanceSizeChange"
                @current-change="handleInstancePageChange"
              />
            </div>
          </div>
        </el-card>
      </div>
    </el-card>
    <!-- 规则维度告警统计 -->
    <el-card
      shadow="hover"
      class="mb-6 overflow-hidden transform transition-all duration-300 hover:shadow-xl border-t-4 border-blue-500"
    >
      <template #header>
        <div class="flex items-center justify-between py-2">
          <div class="flex items-center">
            <el-icon class="mr-3 text-blue-500 text-xl"><SetUp /></el-icon>
            <span class="text-xl font-bold text-blue-500"
              >规则维度告警统计</span
            >
            <el-tag size="small" type="info" effect="plain" class="ml-3">
              {{
                currentDateRange?.start_time
                  ? currentDateRange.start_time.substring(0, 10)
                  : ""
              }}
              至
              {{
                currentDateRange?.end_time
                  ? currentDateRange.end_time.substring(0, 10)
                  : ""
              }}
            </el-tag>
          </div>
        </div>
      </template>

      <div
        class="rule-section p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg backdrop-blur-sm"
      >


        <!-- 规则告警表格 -->
        <el-card shadow="hover" :body-style="{ padding: '0' }">
          <template #header>
            <div class="flex justify-between items-center p-1">
              <div class="flex items-center">
                <el-icon class="mr-2 text-blue-500 text-xl"
                  ><Document
                /></el-icon>
                <span class="text-lg font-bold text-gray-700"
                  >规则告警明细</span
                >
                <el-tag size="small" type="info" effect="plain" class="ml-2"
                  >共 {{ ruleTableTotal }} 条记录</el-tag
                >
              </div>
              <div class="flex items-center">
                <el-button
                  type="primary"
                  :icon="Download"
                  size="small"
                  class="export-btn transition-all duration-300 hover:scale-105 mr-2"
                  @click="handleRuleExport"
                >
                  导出
                </el-button>
                <el-button
                  type="primary"
                  :icon="Refresh"
                  size="small"
                  class="refresh-btn transition-all duration-300 hover:scale-105"
                  :loading="ruleLoading"
                  @click="loadRuleData"
                >
                  刷新
                </el-button>
              </div>
            </div>
          </template>
          <div class="alert-table-wrapper p-2">
            <el-table
              :data="ruleTableData"
              border
              stripe
              height="400"
              :loading="ruleLoading"
              class="alert-table rounded-md overflow-hidden"
              :header-cell-style="{
                backgroundColor: '#eff6ff',
                color: '#3b82f6',
                fontWeight: 'bold',
                padding: '12px 8px',
                borderBottom: '2px solid #dbeafe'
              }"
              :row-class-name="tableRowClassName"
              :empty-text="null"
              @sort-change="handleRuleSortChange"
            >
              <template #empty>
                <div class="table-empty-wrapper py-6">
                  <el-empty :image-size="0">
                    <template #image>
                      <el-icon class="empty-icon text-5xl text-gray-300"
                        ><SetUp
                      /></el-icon>
                    </template>
                    <template #description>
                      <p class="empty-text font-medium text-gray-500 mt-4">
                        暂无规则告警数据
                      </p>
                      <p class="empty-tip text-sm text-gray-400 mt-2">
                        请尝试选择其他时间范围
                      </p>
                    </template>
                  </el-empty>
                </div>
              </template>
              <el-table-column
                prop="rule_name"
                label="规则名称"
                min-width="180"
                sortable="custom"
                show-overflow-tooltip
              />
              <el-table-column
                prop="rule_note"
                label="规则备注"
                min-width="150"
                show-overflow-tooltip
              />
              <el-table-column
                prop="trigger_count"
                label="触发次数"
                min-width="100"
                sortable="custom"
              >
                <template #default="scope">
                  <span class="font-medium text-gray-700">{{
                    scope.row.trigger_count
                  }}</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="affected_instances"
                label="影响实例数"
                min-width="100"
                sortable="custom"
              >
                <template #default="scope">
                  <span class="font-medium text-indigo-500">{{
                    scope.row.affected_instances
                  }}</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="emergency_count"
                label="紧急告警"
                min-width="100"
                sortable="custom"
              >
                <template #default="scope">
                  <span class="font-medium text-red-500">{{
                    scope.row.emergency_count
                  }}</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="warning_count"
                label="警告告警"
                min-width="100"
                sortable="custom"
              >
                <template #default="scope">
                  <span class="font-medium text-orange-500">{{
                    scope.row.warning_count
                  }}</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="recovery_rate"
                label="恢复率"
                min-width="100"
                sortable="custom"
              >
                <template #default="scope">
                  <el-progress
                    :percentage="scope.row.recovery_rate"
                    :format="val => val.toFixed(2) + '%'"
                    :color="getProgressColor(scope.row.recovery_rate)"
                    :stroke-width="10"
                  />
                </template>
              </el-table-column>
              <el-table-column
                prop="avg_duration"
                label="平均持续时间"
                min-width="120"
                sortable="custom"
              >
                <template #default="scope">
                  <span class="font-medium text-purple-500">{{
                    formatDuration(scope.row.avg_duration)
                  }}</span>
                </template>
              </el-table-column>
            </el-table>
            <div class="flex justify-end mt-4">
              <el-pagination
                v-model:current-page="ruleCurrentPage"
                v-model:page-size="rulePageSize"
                :page-sizes="[10, 20, 50, 100]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="ruleTableTotal"
                @size-change="handleRuleSizeChange"
                @current-change="handleRulePageChange"
              />
            </div>
          </div>
        </el-card>
      </div>
    </el-card>
    <!-- 集群维度告警统计 -->
    <el-card
      shadow="hover"
      class="mb-6 overflow-hidden transform transition-all duration-300 hover:shadow-xl border-t-4 border-green-500"
    >
      <template #header>
        <div class="flex items-center justify-between py-2">
          <div class="flex items-center">
            <el-icon class="mr-3 text-green-500 text-xl"><Grid /></el-icon>
            <span class="text-xl font-bold text-green-500"
              >集群维度告警统计</span
            >
            <el-tag size="small" type="info" effect="plain" class="ml-3">
              {{
                currentDateRange?.start_time
                  ? currentDateRange.start_time.substring(0, 10)
                  : ""
              }}
              至
              {{
                currentDateRange?.end_time
                  ? currentDateRange.end_time.substring(0, 10)
                  : ""
              }}
            </el-tag>
          </div>
        </div>
      </template>

      <div
        class="cluster-section p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg backdrop-blur-sm"
      >


        <!-- 集群告警表格 -->
        <el-card shadow="hover" :body-style="{ padding: '0' }">
          <template #header>
            <div class="flex justify-between items-center p-1">
              <div class="flex items-center">
                <el-icon class="mr-2 text-green-500 text-xl"
                  ><Document
                /></el-icon>
                <span class="text-lg font-bold text-gray-700"
                  >集群告警明细</span
                >
                <el-tag size="small" type="info" effect="plain" class="ml-2"
                  >共 {{ clusterTableTotal }} 条记录</el-tag
                >
              </div>
              <div class="flex items-center">
                <el-button
                  type="primary"
                  :icon="Download"
                  size="small"
                  class="export-btn transition-all duration-300 hover:scale-105 mr-2"
                  @click="handleClusterExport"
                >
                  导出
                </el-button>
                <el-button
                  type="primary"
                  :icon="Refresh"
                  size="small"
                  class="refresh-btn transition-all duration-300 hover:scale-105"
                  :loading="clusterLoading"
                  @click="loadClusterData"
                >
                  刷新
                </el-button>
              </div>
            </div>
          </template>
          <div class="alert-table-wrapper p-2">
            <el-table
              :data="clusterTableData"
              border
              stripe
              height="400"
              :loading="clusterLoading"
              class="alert-table rounded-md overflow-hidden"
              :header-cell-style="{
                backgroundColor: '#f0fdf4',
                color: '#10b981',
                fontWeight: 'bold',
                padding: '12px 8px',
                borderBottom: '2px solid #dcfce7'
              }"
              :row-class-name="tableRowClassName"
              :empty-text="null"
              @sort-change="handleClusterSortChange"
            >
              <template #empty>
                <div class="table-empty-wrapper py-6">
                  <el-empty :image-size="0">
                    <template #image>
                      <el-icon class="empty-icon text-5xl text-gray-300"
                        ><Grid
                      /></el-icon>
                    </template>
                    <template #description>
                      <p class="empty-text font-medium text-gray-500 mt-4">
                        暂无集群告警数据
                      </p>
                      <p class="empty-tip text-sm text-gray-400 mt-2">
                        请尝试选择其他时间范围
                      </p>
                    </template>
                  </el-empty>
                </div>
              </template>
              <el-table-column
                prop="cluster"
                label="集群名称"
                min-width="180"
                sortable="custom"
                show-overflow-tooltip
              />
              <el-table-column
                prop="total_count"
                label="告警总数"
                min-width="100"
                sortable="custom"
              >
                <template #default="scope">
                  <span class="font-medium text-gray-700">{{
                    scope.row.total_count
                  }}</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="emergency_count"
                label="紧急告警"
                min-width="100"
                sortable="custom"
              >
                <template #default="scope">
                  <span class="font-medium text-red-500">{{
                    scope.row.emergency_count
                  }}</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="warning_count"
                label="警告告警"
                min-width="100"
                sortable="custom"
              >
                <template #default="scope">
                  <span class="font-medium text-orange-500">{{
                    scope.row.warning_count
                  }}</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="notice_count"
                label="通知告警"
                min-width="100"
                sortable="custom"
              >
                <template #default="scope">
                  <span class="font-medium text-blue-500">{{
                    scope.row.notice_count
                  }}</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="affected_instances"
                label="影响实例数"
                min-width="100"
                sortable="custom"
              >
                <template #default="scope">
                  <span class="font-medium text-indigo-500">{{
                    scope.row.affected_instances
                  }}</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="recovery_rate"
                label="恢复率"
                min-width="100"
                sortable="custom"
              >
                <template #default="scope">
                  <el-progress
                    :percentage="scope.row.recovery_rate"
                    :format="val => val.toFixed(2) + '%'"
                    :color="getProgressColor(scope.row.recovery_rate)"
                    :stroke-width="10"
                  />
                </template>
              </el-table-column>
              <el-table-column
                prop="avg_duration"
                label="平均持续时间"
                min-width="120"
                sortable="custom"
              >
                <template #default="scope">
                  <span class="font-medium text-purple-500">{{
                    formatDuration(scope.row.avg_duration)
                  }}</span>
                </template>
              </el-table-column>
            </el-table>
            <div class="flex justify-end mt-4">
              <el-pagination
                v-model:current-page="clusterCurrentPage"
                v-model:page-size="clusterPageSize"
                :page-sizes="[10, 20, 50, 100]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="clusterTableTotal"
                @size-change="handleClusterSizeChange"
                @current-change="handleClusterPageChange"
              />
            </div>
          </div>
        </el-card>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from "vue";
import {
  AlarmClock,
  Monitor,
  SetUp,
  Grid,
  DataAnalysis,
  Bell,
  WarningFilled,
  Warning,
  InfoFilled,
  SuccessFilled,
  Timer,
  PieChart,
  Document,
  Download,
  Refresh
} from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import DateRangeSelector from "./components/DateRangeSelector.vue";
import AlertChart from "./components/AlertChart.vue";
import {
  getAlertStatisticsAPI,
  getAlertStatisticsByInstanceAPI,
  getAlertStatisticsByRuleAPI,
  getAlertStatisticsByClusterAPI,
  type AlertStatisticsParams,
  type AlertStatistics,
  type InstanceAlertStatistics,
  type RuleAlertStatistics,
  type ClusterAlertStatistics
} from "@/api/statistic/monitor/n9e";

// 当前日期范围
const currentDateRange = reactive<AlertStatisticsParams>({
  start_time: "",
  end_time: ""
});

// 当前选中的日期
const selectedDay = ref<string>("");

// 总体告警统计
const overview = ref<AlertStatistics | null>(null);
const overviewLoading = ref(false);
const overviewPieChartData = computed(() => {
  if (!overview.value) return { pieData: [] };

  return {
    pieData: [
      {
        name: "紧急告警",
        value: overview.value.emergency_count,
        itemStyle: { color: "#ef4444" }
      },
      {
        name: "警告告警",
        value: overview.value.warning_count,
        itemStyle: { color: "#f97316" }
      },
      {
        name: "通知告警",
        value: overview.value.notice_count,
        itemStyle: { color: "#3b82f6" }
      }
    ].filter(item => item.value > 0) // 过滤掉数量为0的数据
  };
});

// 实例维度告警统计
const instanceTableData = ref<InstanceAlertStatistics[]>([]);
const instanceTableTotal = ref(0);
const instanceLoading = ref(false);
const instanceCurrentPage = ref(1);
const instancePageSize = ref(10);
const instanceSortColumn = ref("total_count");
const instanceSortOrder = ref("descending");


// 规则维度告警统计
const ruleTableData = ref<RuleAlertStatistics[]>([]);
const ruleTableTotal = ref(0);
const ruleLoading = ref(false);
const ruleCurrentPage = ref(1);
const rulePageSize = ref(10);
const ruleSortColumn = ref("trigger_count");
const ruleSortOrder = ref("descending");

// 集群维度告警统计
const clusterTableData = ref<ClusterAlertStatistics[]>([]);
const clusterTableTotal = ref(0);
const clusterLoading = ref(false);
const clusterCurrentPage = ref(1);
const clusterPageSize = ref(10);
const clusterSortColumn = ref("total_count");
const clusterSortOrder = ref("descending");

const handleDayChange = (day: string) => {
  selectedDay.value = day;
  
  // 将日期设置为当天的开始时间和结束时间
  const date = new Date(day);
  const startDate = new Date(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0);
  const endDate = new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59);
  
  // 使用formatDateTime函数格式化日期时间
  currentDateRange.start_time = formatDateTime(startDate);
  currentDateRange.end_time = formatDateTime(endDate);
  
  // 加载数据
  loadAllData();
};

const handleDateRangeChange = (range: {
  start_time: string;
  end_time: string;
}) => {
  currentDateRange.start_time = range.start_time;
  currentDateRange.end_time = range.end_time;

  // 重新加载所有数据
  loadAllData();
};

// 加载数据函数
const loadAllData = () => {
  loadOverviewData();
  loadInstanceData();
  loadRuleData();
  loadClusterData();
};

const loadOverviewData = async () => {
  if (!currentDateRange.start_time || !currentDateRange.end_time) {
    ElMessage.warning("请选择日期范围");
    return;
  }

  overviewLoading.value = true;
  try {
    const res = await getAlertStatisticsAPI(currentDateRange);
    if (res.success && res.data) {
      // 输出响应数据类型和平均持续时间的值
      console.log('Overview data type:', typeof res.data);
      console.log('Avg duration type:', typeof res.data.avg_duration, 'Value:', res.data.avg_duration);
      
      // 确保 avg_duration 是数字
      if (res.data.avg_duration) {
        res.data.avg_duration = Number(res.data.avg_duration);
      }
      
      overview.value = res.data;
    } else {
      ElMessage.error(res.msg || "获取总体告警统计失败");
    }
  } catch (error) {
    console.error("获取总体告警统计失败", error);
    ElMessage.error("获取总体告警统计失败");
  } finally {
    overviewLoading.value = false;
  }
};

const loadInstanceData = async () => {
  if (!currentDateRange.start_time || !currentDateRange.end_time) {
    ElMessage.warning("请选择日期范围");
    return;
  }

  instanceLoading.value = true;
  try {
    const params = {
      ...currentDateRange,
      page: instanceCurrentPage.value,
      limit: instancePageSize.value,
      sort_column: instanceSortColumn.value,
      sort_order: instanceSortOrder.value
    };

    const res = await getAlertStatisticsByInstanceAPI(params);
    if (res.success) {
      if (res.data && res.data.length > 0) {
        instanceTableData.value = res.data;
        instanceTableTotal.value = res.count || res.data.length;
      } else {
        // 无数据处理，不显示错误
        instanceTableData.value = [];
        instanceTableTotal.value = 0;
      }
    } else if (res.msg && res.msg !== "无数据") {
      // 只有在不是无数据的情况下才显示错误信息
      ElMessage.error(res.msg);
    }
  } catch (error) {
    console.error("获取实例告警统计失败", error);
    // 保留控制台错误，但不显示给用户
    instanceTableData.value = [];
    instanceTableTotal.value = 0;
  } finally {
    instanceLoading.value = false;
  }
};

const loadRuleData = async () => {
  if (!currentDateRange.start_time || !currentDateRange.end_time) {
    ElMessage.warning("请选择日期范围");
    return;
  }

  ruleLoading.value = true;
  try {
    const params = {
      ...currentDateRange,
      page: ruleCurrentPage.value,
      limit: rulePageSize.value,
      sort_column: ruleSortColumn.value,
      sort_order: ruleSortOrder.value
    };

    const res = await getAlertStatisticsByRuleAPI(params);
    if (res.success) {
      if (res.data && res.data.length > 0) {
        ruleTableData.value = res.data;
        ruleTableTotal.value = res.count || res.data.length;
      } else {
        // 无数据处理，不显示错误
        ruleTableData.value = [];
        ruleTableTotal.value = 0;
      }
    } else if (res.msg && res.msg !== "无数据") {
      // 只有在不是无数据的情况下才显示错误信息
      ElMessage.error(res.msg);
    }
  } catch (error) {
    console.error("获取规则告警统计失败", error);
    // 保留控制台错误，但不显示给用户
    ruleTableData.value = [];
    ruleTableTotal.value = 0;
  } finally {
    ruleLoading.value = false;
  }
};

const loadClusterData = async () => {
  if (!currentDateRange.start_time || !currentDateRange.end_time) {
    ElMessage.warning("请选择日期范围");
    return;
  }

  clusterLoading.value = true;
  try {
    const params = {
      ...currentDateRange,
      page: clusterCurrentPage.value,
      limit: clusterPageSize.value,
      sort_column: clusterSortColumn.value,
      sort_order: clusterSortOrder.value
    };

    const res = await getAlertStatisticsByClusterAPI(params);
    if (res.success) {
      if (res.data && res.data.length > 0) {
        clusterTableData.value = res.data;
        clusterTableTotal.value = res.count || res.data.length;
      } else {
        // 无数据处理，不显示错误
        clusterTableData.value = [];
        clusterTableTotal.value = 0;
      }
    } else if (res.msg && res.msg !== "无数据") {
      // 只有在不是无数据的情况下才显示错误信息
      ElMessage.error(res.msg);
    }
  } catch (error) {
    console.error("获取集群告警统计失败", error);
    // 保留控制台错误，但不显示给用户
    clusterTableData.value = [];
    clusterTableTotal.value = 0;
  } finally {
    clusterLoading.value = false;
  }
};

// 处理表格排序
const handleInstanceSortChange = (sort: { prop: string; order: string }) => {
  if (sort.prop && sort.order) {
    instanceSortColumn.value = sort.prop;
    instanceSortOrder.value = sort.order;
  } else {
    instanceSortColumn.value = "total_count";
    instanceSortOrder.value = "descending";
  }
  loadInstanceData();
};

const handleRuleSortChange = (sort: { prop: string; order: string }) => {
  if (sort.prop && sort.order) {
    ruleSortColumn.value = sort.prop;
    ruleSortOrder.value = sort.order;
  } else {
    ruleSortColumn.value = "trigger_count";
    ruleSortOrder.value = "descending";
  }
  loadRuleData();
};

const handleClusterSortChange = (sort: { prop: string; order: string }) => {
  if (sort.prop && sort.order) {
    clusterSortColumn.value = sort.prop;
    clusterSortOrder.value = sort.order;
  } else {
    clusterSortColumn.value = "total_count";
    clusterSortOrder.value = "descending";
  }
  loadClusterData();
};

// 处理分页
const handleInstanceSizeChange = (size: number) => {
  instancePageSize.value = size;
  loadInstanceData();
};

const handleInstancePageChange = (page: number) => {
  instanceCurrentPage.value = page;
  loadInstanceData();
};

const handleRuleSizeChange = (size: number) => {
  rulePageSize.value = size;
  loadRuleData();
};

const handleRulePageChange = (page: number) => {
  ruleCurrentPage.value = page;
  loadRuleData();
};

const handleClusterSizeChange = (size: number) => {
  clusterPageSize.value = size;
  loadClusterData();
};

const handleClusterPageChange = (page: number) => {
  clusterCurrentPage.value = page;
  loadClusterData();
};

// 处理导出
const handleInstanceExport = () => {
  ElMessage.success("实例告警统计导出功能待实现");
};

const handleRuleExport = () => {
  ElMessage.success("规则告警统计导出功能待实现");
};

const handleClusterExport = () => {
  ElMessage.success("集群告警统计导出功能待实现");
};

// 辅助函数
const formatDuration = (seconds: number | string) => {
  // 处理可能的字符串类型输入
  let numSeconds: number;
  if (typeof seconds === 'string') {
    numSeconds = parseFloat(seconds);
  } else {
    numSeconds = seconds;
  }
  
  // 确保秒数是有效数字
  const validSeconds = !isNaN(numSeconds) ? numSeconds : 0;
  
  if (validSeconds === 0) return "0秒";

  // 处理小数秒的情况
  if (validSeconds < 1) {
    // 将小数秒转换为毫秒并四舍五入
    const ms = Math.round(validSeconds * 1000);
    return `${ms}毫秒`;
  }

  const days = Math.floor(validSeconds / (24 * 60 * 60));
  const hours = Math.floor((validSeconds % (24 * 60 * 60)) / (60 * 60));
  const minutes = Math.floor((validSeconds % (60 * 60)) / 60);
  const remainingSeconds = Math.floor(validSeconds % 60);

  let result = "";
  if (days > 0) result += `${days}天`;
  if (hours > 0) result += `${hours}小时`;
  if (minutes > 0) result += `${minutes}分钟`;
  if (remainingSeconds > 0 || result === "") result += `${remainingSeconds}秒`;

  return result;
};

const getProgressColor = (rate: number) => {
  if (rate >= 80) return "#10b981"; // 绿色
  if (rate >= 60) return "#3b82f6"; // 蓝色
  if (rate >= 40) return "#f97316"; // 橙色
  return "#ef4444"; // 红色
};

const tableRowClassName = () => {
  return "alert-table-row";
};

// 日期时间格式化为 YYYY-MM-DD HH:MM:SS
const formatDateTime = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");
  
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

// 页面加载时执行
onMounted(() => {
  // 默认加载当前日期的数据
  const now = new Date();
  selectedDay.value = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, "0")}-${String(now.getDate()).padStart(2, "0")}`;
  handleDayChange(selectedDay.value);
  // 加载所有数据
  loadAllData();
});
</script>

<style scoped>
.statistic-alert-container {
  padding: 16px;
}

.dashboard-header {
  background-color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
}

.stat-card {
  transition: all 0.3s;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow:
    0 10px 15px -3px rgb(0 0 0 / 0.1),
    0 4px 6px -4px rgb(0 0 0 / 0.1);
}

.alert-table {
  --el-table-header-bg-color: #f8fafc;
  --el-table-row-hover-bg-color: #f1f5f9;
}

.alert-table-row {
  transition: all 0.2s;
}

.alert-table-row:hover {
  background-color: #f1f5f9;
}

.refresh-btn,
.export-btn {
  transition: all 0.3s;
}
</style>
