<template>
  <div ref="chartRef" class="alert-chart-container"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from "vue";
import { init, type ECharts } from "echarts";

const props = defineProps<{
  chartType: "pie" | "bar" | "line";
  chartData: any;
  height?: string;
  width?: string;
}>();

const chartRef = ref<HTMLElement | null>(null);
let chart: ECharts | null = null;

// 注意：暂时没有使用SeverityType进行颜色映射，直接在每个图表中定义了颜色

// 饼图配置
const getPieOption = () => {
  if (!props.chartData || !props.chartData.pieData) return {};

  return {
    tooltip: {
      trigger: "item",
      formatter: "{a} <br/>{b}: {c} ({d}%)"
    },
    legend: {
      orient: "horizontal",
      bottom: "bottom",
      data: props.chartData.pieData.map((item: any) => item.name)
    },
    series: [
      {
        name: "告警统计",
        type: "pie",
        radius: ["40%", "70%"],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: "#fff",
          borderWidth: 2
        },
        label: {
          show: false,
          position: "center"
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 16,
            fontWeight: "bold"
          }
        },
        labelLine: {
          show: false
        },
        data: props.chartData.pieData
      }
    ]
  };
};

// 柱状图配置
const getBarOption = () => {
  if (
    !props.chartData ||
    !props.chartData.xAxisData ||
    !props.chartData.seriesData
  )
    return {};

  return {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow"
      }
    },
    legend: {
      data: props.chartData.legendData || [],
      bottom: "bottom"
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "15%",
      top: "8%",
      containLabel: true
    },
    xAxis: {
      type: "category",
      data: props.chartData.xAxisData,
      axisLabel: {
        interval: 0,
        rotate: props.chartData.xAxisData.length > 8 ? 30 : 0
      }
    },
    yAxis: {
      type: "value"
    },
    series: props.chartData.seriesData
  };
};

// 折线图配置
const getLineOption = () => {
  if (
    !props.chartData ||
    !props.chartData.xAxisData ||
    !props.chartData.seriesData
  )
    return {};

  return {
    tooltip: {
      trigger: "axis"
    },
    legend: {
      data: props.chartData.legendData || [],
      bottom: "bottom"
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "15%",
      top: "8%",
      containLabel: true
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: props.chartData.xAxisData,
      axisLabel: {
        interval: 0,
        rotate: props.chartData.xAxisData.length > 8 ? 30 : 0
      }
    },
    yAxis: {
      type: "value"
    },
    series: props.chartData.seriesData
  };
};

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return;

  // 销毁已有的图表实例
  if (chart) {
    chart.dispose();
  }

  // 创建新的图表实例
  chart = init(chartRef.value);

  // 根据图表类型设置不同的配置
  let option = {};
  switch (props.chartType) {
    case "pie":
      option = getPieOption();
      break;
    case "bar":
      option = getBarOption();
      break;
    case "line":
      option = getLineOption();
      break;
  }

  // 设置图表配置
  chart.setOption(option);
};

// 监听窗口大小变化
const handleResize = () => {
  if (chart) {
    chart.resize();
  }
};

// 监听数据变化
watch(
  () => props.chartData,
  () => {
    nextTick(() => {
      initChart();
    });
  },
  { deep: true }
);

// 监听图表类型变化
watch(
  () => props.chartType,
  () => {
    nextTick(() => {
      initChart();
    });
  }
);

onMounted(() => {
  nextTick(() => {
    initChart();
    window.addEventListener("resize", handleResize);
  });
});

// 组件卸载时销毁事件监听
onUnmounted(() => {
  // 移除窗口大小变化监听
  window.removeEventListener("resize", handleResize);

  // 销毁图表实例
  if (chart) {
    chart.dispose();
    chart = null;
  }
});

// 导出变量
defineExpose({
  chart
});
</script>

<style scoped>
.alert-chart-container {
  width: 100%;
  height: 100%;
  min-height: 300px;
}
</style>
