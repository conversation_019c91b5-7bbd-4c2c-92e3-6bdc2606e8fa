<template>
  <div ref="chartRef" class="alert-chart-container"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from "vue";
import { init, type ECharts } from "echarts";

const props = defineProps<{
  chartType: "pie" | "doughnut" | "bar" | "line";
  chartData: any;
  height?: string;
  width?: string;
}>();

const chartRef = ref<HTMLElement | null>(null);
let chart: ECharts | null = null;

// 注意：暂时没有使用SeverityType进行颜色映射，直接在每个图表中定义了颜色

// 饼图配置
const getPieOption = () => {
  if (!props.chartData || !props.chartData.pieData) return {};

  const isDoughnut = props.chartType === "doughnut";

  return {
    tooltip: {
      trigger: "item",
      formatter: "{a} <br/>{b}: {c} ({d}%)",
      backgroundColor: "rgba(50, 50, 50, 0.9)",
      borderColor: "rgba(255, 255, 255, 0.2)",
      borderWidth: 1,
      textStyle: {
        color: "#fff"
      }
    },
    legend: {
      orient: "horizontal",
      bottom: "10",
      data: props.chartData.pieData.map((item: any) => item.name),
      textStyle: {
        fontSize: 12,
        color: "#666"
      }
    },
    series: [
      {
        name: "告警统计",
        type: "pie",
        radius: isDoughnut ? ["45%", "75%"] : ["0%", "75%"],
        center: ["50%", "45%"],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: isDoughnut ? 8 : 4,
          borderColor: "#fff",
          borderWidth: 2,
          shadowBlur: 10,
          shadowColor: "rgba(0, 0, 0, 0.1)"
        },
        label: {
          show: false,
          position: "center"
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 18,
            fontWeight: "bold",
            color: "#333"
          },
          itemStyle: {
            shadowBlur: 20,
            shadowColor: "rgba(0, 0, 0, 0.2)"
          }
        },
        labelLine: {
          show: false
        },
        animationType: "scale",
        animationEasing: "elasticOut",
        animationDelay: (idx: number) => idx * 100,
        data: props.chartData.pieData
      }
    ]
  };
};

// 柱状图配置
const getBarOption = () => {
  if (
    !props.chartData ||
    !props.chartData.xAxisData ||
    !props.chartData.seriesData
  )
    return {};

  return {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow"
      }
    },
    legend: {
      data: props.chartData.legendData || [],
      bottom: "bottom"
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "15%",
      top: "8%",
      containLabel: true
    },
    xAxis: {
      type: "category",
      data: props.chartData.xAxisData,
      axisLabel: {
        interval: 0,
        rotate: props.chartData.xAxisData.length > 8 ? 30 : 0
      }
    },
    yAxis: {
      type: "value"
    },
    series: props.chartData.seriesData
  };
};

// 折线图配置
const getLineOption = () => {
  if (
    !props.chartData ||
    !props.chartData.xAxisData ||
    !props.chartData.seriesData
  )
    return {};

  return {
    tooltip: {
      trigger: "axis"
    },
    legend: {
      data: props.chartData.legendData || [],
      bottom: "bottom"
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "15%",
      top: "8%",
      containLabel: true
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: props.chartData.xAxisData,
      axisLabel: {
        interval: 0,
        rotate: props.chartData.xAxisData.length > 8 ? 30 : 0
      }
    },
    yAxis: {
      type: "value"
    },
    series: props.chartData.seriesData
  };
};

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return;

  // 销毁已有的图表实例
  if (chart) {
    chart.dispose();
  }

  // 创建新的图表实例
  chart = init(chartRef.value);

  // 根据图表类型设置不同的配置
  let option = {};
  switch (props.chartType) {
    case "pie":
    case "doughnut":
      option = getPieOption();
      break;
    case "bar":
      option = getBarOption();
      break;
    case "line":
      option = getLineOption();
      break;
  }

  // 设置图表配置
  chart.setOption(option);
};

// 监听窗口大小变化
const handleResize = () => {
  if (chart) {
    chart.resize();
  }
};

// 监听数据变化
watch(
  () => props.chartData,
  () => {
    nextTick(() => {
      initChart();
    });
  },
  { deep: true }
);

// 监听图表类型变化
watch(
  () => props.chartType,
  () => {
    nextTick(() => {
      initChart();
    });
  }
);

onMounted(() => {
  nextTick(() => {
    initChart();
    window.addEventListener("resize", handleResize);
  });
});

// 组件卸载时销毁事件监听
onUnmounted(() => {
  // 移除窗口大小变化监听
  window.removeEventListener("resize", handleResize);

  // 销毁图表实例
  if (chart) {
    chart.dispose();
    chart = null;
  }
});

// 导出变量
defineExpose({
  chart
});
</script>

<style scoped>
.alert-chart-container {
  width: 100%;
  height: 100%;
  min-height: 300px;
}
</style>
