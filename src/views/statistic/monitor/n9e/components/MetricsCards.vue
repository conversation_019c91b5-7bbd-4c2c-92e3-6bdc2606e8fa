<template>
  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
    <!-- 恢复率卡片 -->
    <el-card shadow="hover" class="metrics-card border-l-4 border-green-500">
      <div class="flex items-center">
        <el-icon class="text-2xl text-green-500 mr-3">
          <SuccessFilled />
        </el-icon>
        <div class="flex-1">
          <div class="flex justify-between items-center mb-2">
            <span class="text-sm text-gray-500 font-medium">告警恢复率</span>
            <span class="font-bold text-green-500 text-lg">
              {{ recoveryRateText }}
            </span>
          </div>
          <el-progress
            :percentage="recoveryRate"
            :format="() => ''"
            :color="getProgressColor(recoveryRate)"
            :stroke-width="12"
            class="progress-bar"
          />
        </div>
      </div>
    </el-card>

    <!-- 平均持续时间卡片 -->
    <el-card shadow="hover" class="metrics-card border-l-4 border-purple-500">
      <div class="flex items-center">
        <el-icon class="text-2xl text-purple-500 mr-3">
          <Timer />
        </el-icon>
        <div class="flex-1">
          <div class="text-sm text-gray-500 mb-1 font-medium">平均持续时间</div>
          <div class="text-2xl font-bold text-purple-500">
            {{ durationText }}
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { SuccessFilled, Timer } from '@element-plus/icons-vue'
import type { AlertStatistics } from '@/api/statistic/monitor/n9e'

interface Props {
  overview: AlertStatistics | null
}

const props = defineProps<Props>()

const recoveryRate = computed(() => props.overview?.recovery_rate || 0)

const recoveryRateText = computed(() => {
  return `${recoveryRate.value.toFixed(2)}%`
})

const durationText = computed(() => {
  if (!props.overview?.avg_duration) return '0秒'
  return formatDuration(Number(props.overview.avg_duration))
})

const formatDuration = (seconds: number): string => {
  const validSeconds = !isNaN(seconds) ? seconds : 0
  
  if (validSeconds === 0) return '0秒'

  if (validSeconds < 1) {
    const ms = Math.round(validSeconds * 1000)
    return `${ms}毫秒`
  }

  const days = Math.floor(validSeconds / (24 * 60 * 60))
  const hours = Math.floor((validSeconds % (24 * 60 * 60)) / (60 * 60))
  const minutes = Math.floor((validSeconds % (60 * 60)) / 60)
  const remainingSeconds = Math.floor(validSeconds % 60)

  let result = ''
  if (days > 0) result += `${days}天`
  if (hours > 0) result += `${hours}小时`
  if (minutes > 0) result += `${minutes}分钟`
  if (remainingSeconds > 0 || result === '') result += `${remainingSeconds}秒`

  return result
}

const getProgressColor = (rate: number): string => {
  if (rate >= 80) return '#10b981' // 绿色
  if (rate >= 60) return '#3b82f6' // 蓝色
  if (rate >= 40) return '#f97316' // 橙色
  return '#ef4444' // 红色
}
</script>

<style scoped>
.metrics-card {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.metrics-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.metrics-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.metrics-card:hover::before {
  opacity: 1;
}

.progress-bar {
  margin-top: 4px;
}

:deep(.el-progress-bar__outer) {
  border-radius: 6px;
  background-color: rgba(0, 0, 0, 0.1);
}

:deep(.el-progress-bar__inner) {
  border-radius: 6px;
  transition: all 0.6s ease;
}
</style>
