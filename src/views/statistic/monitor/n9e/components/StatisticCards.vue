<template>
  <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
    <StatisticCard
      v-for="card in statisticCards"
      :key="card.key"
      :title="card.title"
      :value="card.value"
      :icon="card.icon"
      :color="card.color"
      :border-color="card.borderColor"
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Bell, WarningFilled, Warning, InfoFilled } from '@element-plus/icons-vue'
import StatisticCard from './StatisticCard.vue'
import type { AlertStatistics } from '@/api/statistic/monitor/n9e'

interface Props {
  overview: AlertStatistics | null
}

const props = defineProps<Props>()

const statisticCards = computed(() => [
  {
    key: 'total',
    title: '总告警数',
    value: props.overview?.total_count || 0,
    icon: Bell,
    color: 'text-primary',
    borderColor: 'border-primary'
  },
  {
    key: 'emergency',
    title: '紧急告警',
    value: props.overview?.emergency_count || 0,
    icon: WarningFilled,
    color: 'text-red-500',
    borderColor: 'border-red-500'
  },
  {
    key: 'warning',
    title: '警告告警',
    value: props.overview?.warning_count || 0,
    icon: Warning,
    color: 'text-orange-500',
    borderColor: 'border-orange-500'
  },
  {
    key: 'notice',
    title: '通知告警',
    value: props.overview?.notice_count || 0,
    icon: InfoFilled,
    color: 'text-blue-500',
    borderColor: 'border-blue-500'
  }
])
</script>
