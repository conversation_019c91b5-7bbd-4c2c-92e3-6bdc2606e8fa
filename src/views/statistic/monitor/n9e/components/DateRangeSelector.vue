<template>
  <div class="flex items-center">
    <el-radio-group
      v-model="dateType"
      class="mr-4"
      @change="handleDateTypeChange"
    >
      <el-radio-button label="day">按日查询</el-radio-button>
      <el-radio-button label="range">时间范围</el-radio-button>
    </el-radio-group>

    <div v-if="dateType === 'day'">
      <el-date-picker
        v-model="selectedDay"
        type="date"
        placeholder="选择日期"
        format="YYYY-MM-DD"
        value-format="YYYY-MM-DD"
        class="w-64"
        :shortcuts="dayShortcuts"
        @change="handleDayChange"
      />
    </div>

    <div v-else>
      <el-date-picker
        v-model="dateRange"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        format="YYYY-MM-DD"
        value-format="YYYY-MM-DD"
        class="w-96"
        :shortcuts="rangeShortcuts"
        @change="handleRangeChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from "vue";
import dayjs from "dayjs";

const emit = defineEmits<{
  (e: "day-change", day: string): void;
  (e: "range-change", range: { start_time: string; end_time: string }): void;
}>();

const dateType = ref<"day" | "range">("range"); // 默认改为时间范围

const selectedDay = ref<string>(dayjs().format("YYYY-MM-DD"));

// 初始化为最近1个月的时间范围
const endDate = dayjs();
const startDate = endDate.subtract(1, 'month');
const dateRange = ref<[string, string]>([startDate.format('YYYY-MM-DD'), endDate.format('YYYY-MM-DD')]);

// 快捷日期选项
const dayShortcuts = [
  {
    text: '今天',
    value: dayjs().format('YYYY-MM-DD')
  },
  {
    text: '昨天',
    value: dayjs().subtract(1, 'day').format('YYYY-MM-DD')
  },
  {
    text: '本周一',
    value: dayjs().startOf('week').add(1, 'day').format('YYYY-MM-DD')
  },
  {
    text: '上周一',
    value: dayjs().startOf('week').subtract(6, 'day').format('YYYY-MM-DD')
  }
];

// 时间范围快捷选项
const rangeShortcuts = [
  {
    text: '最近7天',
    value: () => {
      const end = dayjs();
      const start = dayjs().subtract(7, 'day');
      return [start.format('YYYY-MM-DD'), end.format('YYYY-MM-DD')];
    }
  },
  {
    text: '最近15天',
    value: () => {
      const end = dayjs();
      const start = dayjs().subtract(15, 'day');
      return [start.format('YYYY-MM-DD'), end.format('YYYY-MM-DD')];
    }
  },
  {
    text: '最近1个月',
    value: () => {
      const end = dayjs();
      const start = dayjs().subtract(1, 'month');
      return [start.format('YYYY-MM-DD'), end.format('YYYY-MM-DD')];
    }
  },
  {
    text: '最近3个月',
    value: () => {
      const end = dayjs();
      const start = dayjs().subtract(3, 'month');
      return [start.format('YYYY-MM-DD'), end.format('YYYY-MM-DD')];
    }
  },
  {
    text: '当月',
    value: () => {
      const end = dayjs().endOf('month');
      const start = dayjs().startOf('month');
      return [start.format('YYYY-MM-DD'), end.format('YYYY-MM-DD')];
    }
  },
  {
    text: '上个月',
    value: () => {
      const end = dayjs().subtract(1, 'month').endOf('month');
      const start = dayjs().subtract(1, 'month').startOf('month');
      return [start.format('YYYY-MM-DD'), end.format('YYYY-MM-DD')];
    }
  }
];

// 页面加载时初始化为最近1个月
onMounted(() => {
  // 默认使用时间范围，最近1个月
  handleRangeChange(dateRange.value);
});

function handleDateTypeChange() {
  if (dateType.value === "day") {
    handleDayChange(selectedDay.value);
  } else {
    // 自动填充最近1个月的时间范围
    const endDate = dayjs();
    const startDate = endDate.subtract(1, 'month');
    
    // 格式化为YYYY-MM-DD格式
    const startStr = startDate.format('YYYY-MM-DD');
    const endStr = endDate.format('YYYY-MM-DD');
    
    // 更新日期范围选择器的值
    dateRange.value = [startStr, endStr];
    
    // 触发事件
    handleRangeChange(dateRange.value);
  }
}

function handleDayChange(day: string | null) {
  if (day) {
    // 将日期转换为当天的起始时间和结束时间
    const startDate = dayjs(day).startOf('day').format('YYYY-MM-DD HH:mm:ss');
    const endDate = dayjs(day).endOf('day').format('YYYY-MM-DD HH:mm:ss');
    
    emit("day-change", day);
    emit("range-change", {
      start_time: startDate,
      end_time: endDate
    });
  }
}

function handleRangeChange(range: [string, string] | null) {
  if (range && range.length === 2) {
    const startTime = dayjs(range[0]).startOf('day').format('YYYY-MM-DD HH:mm:ss');
    const endTime = dayjs(range[1]).endOf('day').format('YYYY-MM-DD HH:mm:ss');
    
    emit("range-change", {
      start_time: startTime,
      end_time: endTime
    });
  }
}

// 监听变化以确保我们发出更新
watch(selectedDay, newDay => {
  if (dateType.value === "day" && newDay) {
    handleDayChange(newDay);
  }
});

watch(dateRange, newRange => {
  if (dateType.value === "range" && newRange) {
    handleRangeChange(newRange);
  }
});
</script>
