<template>
  <el-card
    shadow="hover"
    class="data-table-section"
    :class="[`border-t-4`, `border-${themeColor}-500`]"
  >
    <template #header>
      <div class="flex items-center justify-between py-2">
        <div class="flex items-center">
          <el-icon class="mr-3 text-xl" :class="`text-${themeColor}-500`">
            <component :is="icon" />
          </el-icon>
          <span class="text-xl font-bold" :class="`text-${themeColor}-500`">
            {{ title }}
          </span>
          <el-tag size="small" type="info" effect="plain" class="ml-3">
            {{ formatDateRange(dateRange) }}
          </el-tag>
        </div>
      </div>
    </template>

    <div 
      class="table-content p-4 rounded-lg backdrop-blur-sm"
      :class="`bg-gradient-to-r from-${themeColor}-50 to-${themeColor}-100`"
    >
      <DataTable
        :data="tableData"
        :columns="columns"
        :loading="loading"
        :total="total"
        :current-page="currentPage"
        :page-size="pageSize"
        :sort-column="sortColumn"
        :sort-order="sortOrder"
        :theme-color="themeColor"
        :empty-icon="icon"
        :empty-text="emptyText"
        @sort-change="$emit('sort-change', $event)"
        @size-change="$emit('size-change', $event)"
        @current-change="$emit('current-change', $event)"
        @export="$emit('export')"
        @refresh="$emit('refresh')"
      />
    </div>
  </el-card>
</template>

<script setup lang="ts">
import type { Component } from 'vue'
import DataTable from './DataTable.vue'
import type { AlertStatisticsParams } from '@/api/statistic/monitor/n9e'

interface TableColumn {
  prop: string
  label: string
  minWidth?: string
  sortable?: boolean | string
  showOverflowTooltip?: boolean
  formatter?: (row: any) => string
  component?: string
}

interface Props {
  title: string
  icon: Component
  themeColor: string
  dateRange: AlertStatisticsParams
  tableData: any[]
  columns: TableColumn[]
  loading: boolean
  total: number
  currentPage: number
  pageSize: number
  sortColumn: string
  sortOrder: string
  emptyText: string
}

defineProps<Props>()

defineEmits<{
  'sort-change': [sort: { prop: string; order: string }]
  'size-change': [size: number]
  'current-change': [page: number]
  'export': []
  'refresh': []
}>()

const formatDateRange = (range: AlertStatisticsParams) => {
  if (!range.start_time || !range.end_time) return ''
  return `${range.start_time.substring(0, 10)} 至 ${range.end_time.substring(0, 10)}`
}
</script>

<style scoped>
.data-table-section {
  overflow: hidden;
  transform: translateZ(0);
  transition: all 0.3s ease;
}

.data-table-section:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.table-content {
  transition: all 0.3s ease;
}

/* 主题颜色类 */
.border-orange-500 {
  border-top-color: #f97316;
}

.border-blue-500 {
  border-top-color: #3b82f6;
}

.border-green-500 {
  border-top-color: #10b981;
}

.text-orange-500 {
  color: #f97316;
}

.text-blue-500 {
  color: #3b82f6;
}

.text-green-500 {
  color: #10b981;
}

.bg-gradient-to-r.from-orange-50.to-orange-100 {
  background: linear-gradient(135deg, #fff7ed 0%, #fed7aa 100%);
}

.bg-gradient-to-r.from-blue-50.to-blue-100 {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
}

.bg-gradient-to-r.from-green-50.to-green-100 {
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
}
</style>
