<template>
  <el-card shadow="hover" :body-style="{ padding: '0' }">
    <template #header>
      <div class="flex justify-between items-center p-1">
        <div class="flex items-center">
          <el-icon class="mr-2 text-xl" :class="`text-${themeColor}-500`">
            <Document />
          </el-icon>
          <span class="text-lg font-bold text-gray-700">
            {{ tableTitle }}
          </span>
          <el-tag size="small" type="info" effect="plain" class="ml-2">
            共 {{ total }} 条记录
          </el-tag>
        </div>
        <div class="flex items-center">
          <el-button
            type="primary"
            :icon="Download"
            size="small"
            class="export-btn mr-2"
            @click="$emit('export')"
          >
            导出
          </el-button>
          <el-button
            type="primary"
            :icon="Refresh"
            size="small"
            class="refresh-btn"
            :loading="loading"
            @click="$emit('refresh')"
          >
            刷新
          </el-button>
        </div>
      </div>
    </template>

    <div class="table-wrapper p-2">
      <el-table
        :data="data"
        border
        stripe
        height="400"
        :loading="loading"
        class="data-table rounded-md overflow-hidden"
        :header-cell-style="headerCellStyle"
        :row-class-name="tableRowClassName"
        :empty-text="null"
        @sort-change="handleSortChange"
      >
        <template #empty>
          <div class="table-empty-wrapper py-6">
            <el-empty :image-size="0">
              <template #image>
                <el-icon class="empty-icon text-5xl text-gray-300">
                  <component :is="emptyIcon" />
                </el-icon>
              </template>
              <template #description>
                <p class="empty-text font-medium text-gray-500 mt-4">
                  {{ emptyText }}
                </p>
                <p class="empty-tip text-sm text-gray-400 mt-2">
                  请尝试选择其他时间范围
                </p>
              </template>
            </el-empty>
          </div>
        </template>

        <el-table-column
          v-for="column in columns"
          :key="column.prop"
          :prop="column.prop"
          :label="column.label"
          :min-width="column.minWidth || '100'"
          :sortable="column.sortable || false"
          :show-overflow-tooltip="column.showOverflowTooltip !== false"
        >
          <template #default="scope">
            <component
              :is="getColumnComponent(column)"
              :row="scope.row"
              :column="column"
              :theme-color="themeColor"
            />
          </template>
        </el-table-column>
      </el-table>

      <div class="flex justify-end mt-4">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="$emit('size-change', $event)"
          @current-change="$emit('current-change', $event)"
        />
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { Document, Download, Refresh } from "@element-plus/icons-vue";
import type { Component } from "vue";
import TableCellText from "./table-cells/TableCellText.vue";
import TableCellProgress from "./table-cells/TableCellProgress.vue";
import TableCellDuration from "./table-cells/TableCellDuration.vue";

interface TableColumn {
  prop: string;
  label: string;
  minWidth?: string;
  sortable?: boolean | string;
  showOverflowTooltip?: boolean;
  formatter?: (row: any) => string;
  component?: string;
}

interface Props {
  data: any[];
  columns: TableColumn[];
  loading: boolean;
  total: number;
  currentPage: number;
  pageSize: number;
  sortColumn: string;
  sortOrder: string;
  themeColor: string;
  emptyIcon: Component;
  emptyText: string;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  "sort-change": [sort: { prop: string; order: string }];
  "size-change": [size: number];
  "current-change": [page: number];
  export: [];
  refresh: [];
}>();

const tableTitle = computed(() => {
  const colorMap: Record<string, string> = {
    orange: "实例告警明细",
    blue: "规则告警明细",
    green: "集群告警明细"
  };
  return colorMap[props.themeColor] || "数据明细";
});

const headerCellStyle = computed(() => {
  const colorMap: Record<string, any> = {
    orange: {
      backgroundColor: "#fff7ed",
      color: "#f97316",
      fontWeight: "bold",
      padding: "12px 8px",
      borderBottom: "2px solid #ffedd5"
    },
    blue: {
      backgroundColor: "#eff6ff",
      color: "#3b82f6",
      fontWeight: "bold",
      padding: "12px 8px",
      borderBottom: "2px solid #dbeafe"
    },
    green: {
      backgroundColor: "#f0fdf4",
      color: "#10b981",
      fontWeight: "bold",
      padding: "12px 8px",
      borderBottom: "2px solid #dcfce7"
    }
  };
  return colorMap[props.themeColor] || colorMap.blue;
});

const getColumnComponent = (column: TableColumn) => {
  switch (column.component) {
    case "progress":
      return TableCellProgress;
    case "duration":
      return TableCellDuration;
    default:
      return TableCellText;
  }
};

const tableRowClassName = () => "data-table-row";

const handleSortChange = (sort: { prop: string; order: string }) => {
  emit("sort-change", sort);
};
</script>

<style scoped>
.export-btn,
.refresh-btn {
  transition: all 0.3s ease;
}

.export-btn:hover,
.refresh-btn:hover {
  transform: scale(1.05);
}

.data-table {
  --el-table-header-bg-color: #f8fafc;
  --el-table-row-hover-bg-color: #f1f5f9;
}

.data-table-row {
  transition: all 0.2s;
}

.data-table-row:hover {
  background-color: #f1f5f9;
}
</style>
