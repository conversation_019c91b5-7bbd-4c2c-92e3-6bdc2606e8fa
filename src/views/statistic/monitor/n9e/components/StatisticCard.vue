<template>
  <el-card 
    shadow="hover" 
    class="statistic-card"
    :class="[borderColor]"
  >
    <div class="flex items-center">
      <div class="icon-wrapper" :class="color">
        <el-icon class="text-2xl mr-3">
          <component :is="icon" />
        </el-icon>
      </div>
      <div class="flex-1">
        <div class="text-sm text-gray-500 mb-1 font-medium">{{ title }}</div>
        <div class="text-2xl font-bold" :class="color">
          <CountUp :end-val="value" />
        </div>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import type { Component } from 'vue'
import CountUp from './CountUp.vue'

interface Props {
  title: string
  value: number
  icon: Component
  color: string
  borderColor: string
}

defineProps<Props>()
</script>

<style scoped>
.statistic-card {
  border-left: 4px solid;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.statistic-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.statistic-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.statistic-card:hover::before {
  opacity: 1;
}

.icon-wrapper {
  transition: transform 0.3s ease;
}

.statistic-card:hover .icon-wrapper {
  transform: scale(1.1);
}

/* 边框颜色类 */
.border-primary {
  border-left-color: var(--el-color-primary);
}

.border-red-500 {
  border-left-color: #ef4444;
}

.border-orange-500 {
  border-left-color: #f97316;
}

.border-blue-500 {
  border-left-color: #3b82f6;
}

.border-green-500 {
  border-left-color: #10b981;
}

.border-purple-500 {
  border-left-color: #8b5cf6;
}
</style>
