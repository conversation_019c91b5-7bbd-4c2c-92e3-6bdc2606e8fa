<template>
  <span 
    class="font-medium"
    :class="textColorClass"
  >
    {{ displayValue }}
  </span>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface TableColumn {
  prop: string
  label: string
  formatter?: (row: any) => string
}

interface Props {
  row: any
  column: TableColumn
  themeColor: string
}

const props = defineProps<Props>()

const displayValue = computed(() => {
  if (props.column.formatter) {
    return props.column.formatter(props.row)
  }
  return props.row[props.column.prop] || 0
})

const textColorClass = computed(() => {
  const prop = props.column.prop
  
  // 根据字段类型设置颜色
  if (prop.includes('emergency')) {
    return 'text-red-500'
  } else if (prop.includes('warning')) {
    return 'text-orange-500'
  } else if (prop.includes('notice')) {
    return 'text-blue-500'
  } else if (prop.includes('affected_instances') || prop.includes('trigger_count')) {
    return 'text-indigo-500'
  } else {
    return 'text-gray-700'
  }
})
</script>
