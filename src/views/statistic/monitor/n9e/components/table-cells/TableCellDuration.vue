<template>
  <span class="font-medium text-purple-500">
    {{ formattedDuration }}
  </span>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface TableColumn {
  prop: string
  label: string
}

interface Props {
  row: any
  column: TableColumn
  themeColor: string
}

const props = defineProps<Props>()

const formattedDuration = computed(() => {
  const seconds = props.row[props.column.prop]
  return formatDuration(seconds)
})

const formatDuration = (seconds: number | string): string => {
  let numSeconds: number
  if (typeof seconds === 'string') {
    numSeconds = parseFloat(seconds)
  } else {
    numSeconds = seconds
  }
  
  const validSeconds = !isNaN(numSeconds) ? numSeconds : 0
  
  if (validSeconds === 0) return '0秒'

  if (validSeconds < 1) {
    const ms = Math.round(validSeconds * 1000)
    return `${ms}毫秒`
  }

  const days = Math.floor(validSeconds / (24 * 60 * 60))
  const hours = Math.floor((validSeconds % (24 * 60 * 60)) / (60 * 60))
  const minutes = Math.floor((validSeconds % (60 * 60)) / 60)
  const remainingSeconds = Math.floor(validSeconds % 60)

  let result = ''
  if (days > 0) result += `${days}天`
  if (hours > 0) result += `${hours}小时`
  if (minutes > 0) result += `${minutes}分钟`
  if (remainingSeconds > 0 || result === '') result += `${remainingSeconds}秒`

  return result
}
</script>
