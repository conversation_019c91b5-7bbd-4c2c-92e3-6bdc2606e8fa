<template>
  <el-card
    shadow="hover"
    class="overview-section"
  >
    <template #header>
      <div class="flex items-center justify-between py-2">
        <div class="flex items-center">
          <el-icon class="mr-3 text-primary text-xl">
            <DataAnalysis />
          </el-icon>
          <span class="text-xl font-bold text-primary">总体告警统计</span>
          <el-tag size="small" type="info" effect="plain" class="ml-3">
            {{ formatDateRange(dateRange) }}
          </el-tag>
        </div>
        <div class="flex items-center">
          <el-button
            type="primary"
            :icon="Refresh"
            size="small"
            class="refresh-btn"
            :loading="loading"
            @click="$emit('refresh')"
          >
            刷新
          </el-button>
        </div>
      </div>
    </template>

    <div class="overview-content">
      <!-- 统计卡片 -->
      <StatisticCards :overview="overview" class="mb-6" />
      
      <!-- 恢复率和持续时间指标 -->
      <MetricsCards :overview="overview" class="mb-6" />
      
      <!-- 告警分布图表 -->
      <ChartSection :chart-data="chartData" />
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { DataAnalysis, Refresh } from '@element-plus/icons-vue'
import StatisticCards from './StatisticCards.vue'
import MetricsCards from './MetricsCards.vue'
import ChartSection from './ChartSection.vue'
import type { AlertStatistics, AlertStatisticsParams } from '@/api/statistic/monitor/n9e'

interface Props {
  overview: AlertStatistics | null
  loading: boolean
  dateRange: AlertStatisticsParams
  chartData: any
}

defineProps<Props>()

defineEmits<{
  refresh: []
}>()

const formatDateRange = (range: AlertStatisticsParams) => {
  if (!range.start_time || !range.end_time) return ''
  return `${range.start_time.substring(0, 10)} 至 ${range.end_time.substring(0, 10)}`
}
</script>

<style scoped>
.overview-section {
  border-top: 4px solid var(--el-color-primary);
  overflow: hidden;
  transform: translateZ(0);
  transition: all 0.3s ease;
}

.overview-section:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.overview-content {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  padding: 1.5rem;
  border-radius: 0.5rem;
  backdrop-filter: blur(10px);
}

.refresh-btn {
  transition: all 0.3s ease;
}

.refresh-btn:hover {
  transform: scale(1.05);
}
</style>
