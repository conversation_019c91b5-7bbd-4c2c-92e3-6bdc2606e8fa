<template>
  <el-card class="page-header" shadow="hover">
    <template #header>
      <div class="flex justify-between items-center">
        <div class="flex items-center">
          <el-icon class="mr-3 text-primary text-xl">
            <component :is="icon" />
          </el-icon>
          <span class="text-xl font-bold text-primary">{{ title }}</span>
        </div>
        <slot name="actions" />
      </div>
    </template>
    
    <div class="header-content">
      <slot name="content" />
    </div>
  </el-card>
</template>

<script setup lang="ts">
import type { Component } from 'vue'

interface Props {
  title: string
  icon?: Component
}

defineProps<Props>()
</script>

<style scoped>
.page-header {
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(229, 231, 235, 0.8);
}

.header-content {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  padding: 1.5rem;
  border-radius: 0.75rem;
  transition: all 0.3s ease;
}

.header-content:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}
</style>
