<template>
  <div class="main-container">
    <el-card class="mb-4">
      <template #header>
        <div class="flex justify-between items-center">
          <span class="font-bold">{{ "云服务商账单统计" }}</span>
          <div class="flex items-center">
            <el-button type="primary" :icon="Download" @click="handleExport">
              导出
            </el-button>
          </div>
        </div>
      </template>
      <div>
        <DateRangeSelector
          @month-change="handleMonthChange"
          @range-change="handleRangeChange"
        />
      </div>
    </el-card>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4">
      <el-card>
        <template #header>
          <div class="font-bold">云服务商分布</div>
        </template>
        <BillChart
          chart-type="bar"
          :chart-data="cloudChartData"
          height="400px"
        />
      </el-card>

      <el-card>
        <template #header>
          <div class="font-bold">云服务商占比</div>
        </template>
        <BillChart chart-type="pie" :chart-data="cloudPieData" height="400px" />
      </el-card>
    </div>

    <el-card>
      <template #header>
        <div class="font-bold">云服务商账单明细</div>
      </template>
      <el-table
        :data="tableData"
        border
        stripe
        height="500"
        :loading="loading"
        @sort-change="handleSortChange"
      >
        <el-table-column
          prop="name"
          label="云服务商"
          min-width="120"
          sortable="custom"
        />
        <el-table-column
          prop="amount"
          label="金额"
          min-width="120"
          sortable="custom"
        >
          <template #default="scope">
            <span>{{ formatCurrency(scope.row.amount) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="month"
          label="账单周期"
          min-width="120"
          sortable="custom"
        />
        <!-- 占比列已移除 -->
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { Download } from "@element-plus/icons-vue";

import { ElMessage } from "element-plus";
import DateRangeSelector from "../components/DateRangeSelector.vue";
import BillChart from "../components/BillChart.vue";
import {
  getCloudBillStatisticAPI,
  getCloudBillRangeStatisticAPI,
  type CloudBillStatistic,
  type CloudBillRangeStatistic
} from "@/api/statistic/bill/cloud";

// Data
const loading = ref(false);
const isRangeMode = ref(false);
const tableData = ref<(CloudBillStatistic | CloudBillRangeStatistic)[]>([]);
const currentBillCycle = ref("");
const currentDateRange = ref<{ start_time: string; end_time: string } | null>(
  null
);

// Chart data
const cloudChartData = computed(() => {
  if (isRangeMode.value) {
    // 过滤掉金额为0的数据
    const filteredData = (tableData.value as CloudBillRangeStatistic[]).filter(
      item => item.amount > 0
    );
    return {
      xAxisData: filteredData.map(item => item.name),
      seriesData: filteredData.map(item => item.amount),
      seriesName: "金额"
    };
  // 这里移除了showPercentage相关逻辑
  } else {
    // 过滤掉金额为0的数据
    const filteredData = (tableData.value as CloudBillStatistic[]).filter(
      item => item.amount > 0
    );
    return {
      xAxisData: filteredData.map(item => item.name),
      seriesData: filteredData.map(item => item.amount),
      seriesName: "金额"
    };
  }
});

const cloudPieData = computed(() => {
  return {
    pieData: tableData.value.map(item => ({
      name: item.name,
      value: item.amount
    }))
  };
});

// Methods
const handleMonthChange = async (month: string) => {
  isRangeMode.value = false;
  currentBillCycle.value = month;
  await fetchCloudBillData();
};

const handleRangeChange = async (range: {
  start_time: string;
  end_time: string;
}) => {
  isRangeMode.value = true;
  currentDateRange.value = range;
  await fetchCloudBillRangeData();
};

const fetchCloudBillData = async () => {
  if (!currentBillCycle.value) return;

  loading.value = true;
  try {
    const res = await getCloudBillStatisticAPI({
      month: currentBillCycle.value
    });

    if (res && res.data) {
      // 直接使用API返回的month字段
      tableData.value = (res.data as CloudBillStatistic[]).map(item => ({
        ...item,
        month: item.month || currentBillCycle.value // 使用month字段
      }));
    } else {
      tableData.value = [];
    }
  } catch (error) {
    console.error("Failed to fetch cloud bill data:", error);
    ElMessage.error("获取数据失败");
  } finally {
    loading.value = false;
  }
};

const fetchCloudBillRangeData = async () => {
  if (!currentDateRange.value) return;

  loading.value = true;
  try {
    const res = await getCloudBillRangeStatisticAPI({
      start_time: currentDateRange.value.start_time,
      end_time: currentDateRange.value.end_time
    });

    if (res.data && res.data.bills) {
      // 在时间范围模式下使用bills数据
      const apiData = res.data.bills || [];
      
      // 添加start_time和end_time字段
      tableData.value = apiData.map(item => ({
        ...item,
        start_time: currentDateRange.value.start_time,
        end_time: currentDateRange.value.end_time,
        // 保留原有月份
        month: item.month || formatMonthFromDate(currentDateRange.value.start_time)
      }));
    } else {
      tableData.value = [];
    }
  } catch (error) {
    console.error("Failed to fetch cloud bill range data:", error);
    ElMessage.error("获取数据失败");
  } finally {
    loading.value = false;
  }
};

const handleSortChange = ({ prop, order }: { prop: string; order: string }) => {
  if (!prop || !order) return;

  tableData.value = [...tableData.value].sort((a: any, b: any) => {
    const valueA = (a as any)[prop];
    const valueB = (b as any)[prop];

    if (typeof valueA === "number" && typeof valueB === "number") {
      return order === "ascending" ? valueA - valueB : valueB - valueA;
    }

    if (valueA === null) return order === "ascending" ? -1 : 1;
    if (valueB === null) return order === "ascending" ? 1 : -1;

    return order === "ascending"
      ? String(valueA).localeCompare(String(valueB))
      : String(valueB).localeCompare(String(valueA));
  });
};

const formatCurrency = (value: number) => {
  return `¥${value.toFixed(2)}`;
};

const formatMonthFromDate = (dateStr: string) => {
  if (!dateStr) return "";
  const date = new Date(dateStr);
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  return `${year}-${month.toString().padStart(2, "0")}`;
};

const handleExport = () => {
  if (tableData.value.length === 0) {
    ElMessage.warning("无数据可导出");
    return;
  }

  // Prepare CSV data
  let headers: string[] = [];
  let rows: any[][] = [];

  // 不论是单月还是范围查询，导出的列都相同
  headers = ["云服务商", "金额", "账单周期"];
  
  // 所有数据都包含相同的字段
  rows = tableData.value.map(item => [
    item.name || "-",
    item.amount || 0,
    item.month || "-"
  ]);

  // Convert to CSV string
  const csvContent = [
    headers.join(","),
    ...rows.map(row => row.join(","))
  ].join("\n");

  // Create and trigger download
  const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
  const url = URL.createObjectURL(blob);
  const link = document.createElement("a");
  link.href = url;
  link.setAttribute(
    "download",
    `cloud-bill-statistics-${new Date().getTime()}.csv`
  );
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

// Initialize data
onMounted(() => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, "0");
  const defaultMonth = `${year}-${month}`;

  currentBillCycle.value = defaultMonth;
  fetchCloudBillData();
});
</script>

<style scoped>
.main-container {
  padding: 20px;
}
</style>
