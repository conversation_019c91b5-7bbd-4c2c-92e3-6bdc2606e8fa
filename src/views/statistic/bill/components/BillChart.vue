<template>
  <div class="chart-container" :class="{ 'loading': loading }">
    <div ref="chartRef" :style="{ width: width, height: height }" class="chart-inner"></div>
    <div v-if="loading" class="chart-loading">
      <el-icon class="is-loading"><Loading /></el-icon>
      <span>加载中...</span>
    </div>
    <div v-if="!loading && (!chartData || (Array.isArray(chartData.xAxisData) && chartData.xAxisData.length === 0))" class="chart-empty">
      <el-empty :image-size="0">
        <template #image>
          <el-icon class="chart-empty-icon"><DataAnalysis /></el-icon>
        </template>
        <template #description>
          <p class="empty-text">暂无账单数据</p>
          <p class="empty-tip">请尝试选择其他时间范围</p>
        </template>
      </el-empty>
    </div>
    <div v-if="watermark" class="chart-watermark">
      <span>{{ watermark }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, onBeforeUnmount } from "vue";
import * as echarts from "echarts";
import { Loading, DataAnalysis } from "@element-plus/icons-vue";

const props = defineProps<{
  chartType: "bar" | "pie" | "line";
  chartData: any;
  title?: string;
  width?: string;
  height?: string;
  theme?: "light" | "dark";
  isRangeMode?: boolean;
  watermark?: string;
}>();

const chartRef = ref<HTMLElement | null>(null);
let chartInstance: echarts.ECharts | null = null;
const loading = ref(false);

onMounted(() => {
  if (chartRef.value) {
    chartInstance = echarts.init(chartRef.value);
    renderChart();
  }

  window.addEventListener("resize", handleResize);
});

onBeforeUnmount(() => {
  if (chartInstance) {
    chartInstance.dispose();
  }
  window.removeEventListener("resize", handleResize);
});

const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize();
  }
};

// 定义全局主题色，确保所有图表风格一致
const themeColors = {
  primary: ['#4b97ff', '#0040ff'],  // 蓝色系 - 主要用于账号相关图表
  success: ['#67c23a', '#409a00'],  // 绿色系 - 主要用于云服务商相关图表
  warning: ['#e6a23c', '#b37e2b'],  // 橙色系 - 主要用于资源组相关图表
  danger: ['#f56c6c', '#c45656'],   // 红色系 - 用于强调或警告
  info: ['#909399', '#606266'],     // 灰色系 - 用于次要信息
  // 扩展色系 - 用于多系列数据
  purple: ['#8e44ad', '#6b2b83'],   // 紫色系
  teal: ['#1abc9c', '#16a085'],     // 青绿色系
  lightBlue: ['#3498db', '#2980b9'], // 亮蓝色系
  lavender: ['#9b59b6', '#8e44ad'],  // 淡紫色系
  yellow: ['#f1c40f', '#f39c12']     // 黄色系
};

// 根据图表类型自动选择合适的主题色
const getChartThemeColor = () => {
  // 根据图表数据中的标识决定使用哪种主题色
  if (props.chartData && props.chartData.themeType) {
    return themeColors[props.chartData.themeType] || themeColors.primary;
  }
  
  // 默认主题色映射
  const typeColorMap = {
    'account': themeColors.primary,
    'cloud': themeColors.success,
    'resource_group': themeColors.warning
  };
  
  // 如果chartData包含类型信息，使用对应的主题色
  if (props.chartData && props.chartData.dataType) {
    return typeColorMap[props.chartData.dataType] || themeColors.primary;
  }
  
  // 默认使用主题色
  return themeColors.primary;
};

const renderChart = () => {
  if (!chartInstance) return;
  
  loading.value = true;
  
  setTimeout(() => {
    try {
      let option: echarts.EChartsOption = {};

      if (props.chartType === "bar") {
        option = createBarChartOption();
      } else if (props.chartType === "pie") {
        option = createPieChartOption();
      } else if (props.chartType === "line") {
        option = createLineChartOption();
      }

      // 设置全局样式
      if (!option.textStyle) {
        option.textStyle = {
          fontFamily: '"Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif'
        };
      }
      
      // 设置动画效果
      option.animation = true;
      option.animationDuration = 1000;
      option.animationEasing = 'cubicOut';
      
      // 添加水印
      if (props.watermark) {
        if (!option.graphic) option.graphic = [];
        (option.graphic as any[]).push({
          type: 'group',
          left: 'center',
          top: 'center',
          children: [{
            type: 'text',
            z: -1,
            left: '10%',
            top: 'middle',
            style: {
              fill: 'rgba(200, 200, 200, 0.15)',
              text: props.watermark,
              font: 'bold 20px sans-serif',
              textAlign: 'center',
              textVerticalAlign: 'middle',
              textRotation: -40
            }
          }]
        });
      }
      
      chartInstance.setOption(option, true);
    } catch (error) {
      console.error('Chart render error:', error);
    } finally {
      loading.value = false;
    }
  }, 300); // 添加短暂延迟以便显示加载动画
};

const createBarChartOption = (): echarts.EChartsOption => {
  if (!props.chartData) {
    return {};
  }
  
  const { xAxisData = [], seriesData = [], seriesName } = props.chartData;
  if (!xAxisData || xAxisData.length === 0 || !seriesData) return {};
  
  // 获取当前图表类型的主题色
  const colors = getChartThemeColor();
  
  // 计算最大值，用于设置合适的Y轴范围
  const maxValue = Math.max(...seriesData.filter(v => v !== null && v !== undefined));
  const yAxisMax = maxValue * 1.2; // 增加上部空间

  // 定义渐变色
  const gradientColors = {
    default: [{
      offset: 0, color: colors[0] 
    }, {
      offset: 1, color: colors[1]
    }],
    hover: [{
      offset: 0, color: echarts.color.lift(colors[0], 0.2) 
    }, {
      offset: 1, color: echarts.color.lift(colors[1], 0.2)
    }]
  };

  return {
    title: {
      text: props.title,
      left: "center",
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow"
      },
      formatter: function(params: any) {
        const param = params[0];
        const formattedValue = new Intl.NumberFormat('zh-CN', {
          style: 'currency',
          currency: 'CNY',
          minimumFractionDigits: 2
        }).format(param.value);
        
        return `<div style="font-weight:bold;margin-bottom:5px;">${param.name}</div>
                <div style="display:flex;align-items:center;margin:3px 0;">
                  <span style="display:inline-block;margin-right:6px;border-radius:4px;width:10px;height:10px;background-color:${param.color};"></span>
                  <span style="flex:1;">${param.seriesName || '金额'}:</span>
                  <span style="font-weight:bold;margin-left:5px;">${formattedValue}</span>
                </div>`;
      },
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#eee',
      borderWidth: 1,
      padding: [10, 15],
      textStyle: {
        color: '#333',
        fontSize: 13
      },
      extraCssText: 'box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15); border-radius: 4px;'
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "10%",
      top: "15%",
      containLabel: true
    },
    xAxis: {
      type: "category",
      data: xAxisData,
      axisLine: {
        lineStyle: {
          color: '#ddd'
        }
      },
      axisTick: {
        alignWithLabel: true,
        lineStyle: {
          color: '#ddd'
        }
      },
      axisLabel: {
        interval: 0,
        rotate: xAxisData.length > 6 ? 30 : 0,
        color: '#666',
        fontSize: 12,
        margin: 12
      }
    },
    yAxis: {
      type: "value",
      name: "金额 (¥)",
      max: yAxisMax,
      nameTextStyle: {
        color: '#666',
        fontSize: 12,
        padding: [0, 0, 0, 5]
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#666',
        fontSize: 12,
        formatter: (value: number) => {
          if (value >= 10000) {
            return (value / 10000).toFixed(1) + '万';
          }
          return value.toString();
        }
      },
      splitLine: {
        lineStyle: {
          color: '#eee',
          type: 'dashed'
        }
      }
    },
    series: [
      {
        name: seriesName || "账单金额",
        type: "bar",
        data: seriesData,
        barWidth: '50%',
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, gradientColors.default),
          borderRadius: [6, 6, 0, 0],
          shadowColor: 'rgba(0, 0, 0, 0.1)',
          shadowBlur: 5,
          shadowOffsetY: 2
        },
        emphasis: {
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, gradientColors.hover),
            borderRadius: [8, 8, 0, 0],
            shadowBlur: 12,
            shadowColor: 'rgba(0, 0, 0, 0.25)'
          }
        },
        // 添加光效果
        showBackground: true,
        backgroundStyle: {
          color: 'rgba(180, 180, 180, 0.1)',
          borderRadius: [6, 6, 0, 0]
        },
        // 添加标签
        label: {
          show: seriesData.length <= 5, // 当数据项少于5个时显示标签
          position: 'top',
          distance: 5,
          color: '#666',
          fontSize: 12,
          formatter: (params: any) => {
            const value = params.value;
            if (value >= 10000) {
              return (value / 10000).toFixed(1) + '万';
            }
            return value;
          }
        },
        // 添加动画效果
        animationDelay: function(_idx: number) {
          return 100;
        }
      }
    ]
  };
};

const createPieChartOption = (): echarts.EChartsOption => {
  const { pieData } = props.chartData;
  if (!pieData || pieData.length === 0) return {};
  
  // 生成饼图颜色
  const generatePieColors = () => {
    // 基础颜色集
    const baseColors = [
      themeColors.primary[0],
      themeColors.success[0],
      themeColors.warning[0],
      themeColors.danger[0],
      themeColors.purple[0],
      themeColors.teal[0],
      themeColors.lightBlue[0],
      themeColors.lavender[0],
      themeColors.yellow[0],
      themeColors.info[0]
    ];
    
    // 如果数据项超过基础颜色数量，生成更多颜色
    if (pieData.length > baseColors.length) {
      // 添加更多颜色变体
      for (let i = 0; i < baseColors.length && baseColors.length + i < pieData.length; i++) {
        const color = baseColors[i];
        // 创建颜色的亮色变体
        const lighterColor = echarts.color.lift(color, 0.2);
        baseColors.push(lighterColor);
      }
    }
    
    return baseColors.slice(0, pieData.length);
  };

  // 计算总金额
  const totalAmount = pieData.reduce((sum: number, item: any) => sum + item.value, 0);
  const formattedTotal = new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY',
    minimumFractionDigits: 2
  }).format(totalAmount);

  return {
    title: {
      text: props.title,
      left: "center",
      subtext: `总金额: ${formattedTotal}`,
      subtextStyle: {
        color: '#666',
        fontSize: 12
      }
    },
    tooltip: {
      trigger: "item",
      formatter: function(params: any) {
        // 格式化金额
        const formattedValue = new Intl.NumberFormat('zh-CN', {
          style: 'currency',
          currency: 'CNY',
          minimumFractionDigits: 2
        }).format(params.value);
        
        return `${params.seriesName}<br/>
                <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${params.color};"></span>
                ${params.name}<br/>
                金额: ${formattedValue}<br/>
                占比: ${params.percent}%`;
      },
      backgroundColor: 'rgba(255, 255, 255, 0.9)',
      borderColor: '#eee',
      borderWidth: 1,
      padding: 10,
      textStyle: {
        color: '#333'
      },
      extraCssText: 'box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15);'
    },
    legend: {
      type: 'scroll',
      orient: "vertical",
      right: 10,
      top: 'center',
      itemWidth: 15,
      itemHeight: 10,
      itemGap: 12,
      textStyle: {
        fontSize: 12,
        color: '#666'
      },
      pageTextStyle: {
        color: '#666'
      },
      formatter: function(name) {
        // 查找对应的数据项
        const item = pieData.find((data: any) => data.name === name);
        if (item) {
          // 计算百分比
          const percent = ((item.value / totalAmount) * 100).toFixed(1);
          // 格式化金额
          const formattedValue = new Intl.NumberFormat('zh-CN', {
            style: 'currency',
            currency: 'CNY',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
          }).format(item.value);
          
          // 返回格式化后的图例文本
          return `${name}: ${formattedValue} (${percent}%)`;
        }
        return name;
      }
    },
    color: generatePieColors(),
    series: [
      {
        name: "账单分布",
        type: "pie",
        radius: ["35%", "70%"],
        center: ['40%', '50%'],
        avoidLabelOverlap: true,
        itemStyle: {
          borderRadius: 8,
          borderColor: "#fff",
          borderWidth: 2,
          shadowBlur: 10,
          shadowColor: 'rgba(0, 0, 0, 0.2)'
        },
        label: {
          show: true,
          formatter: '{b}: {d}%',
          position: 'outside',
          fontSize: 12,
          color: '#333',
          fontWeight: 'normal',
          backgroundColor: 'rgba(255, 255, 255, 0.7)',
          borderRadius: 4,
          padding: [4, 6],
          alignTo: 'labelLine',
          distanceToLabelLine: 5
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 14,
            fontWeight: "bold",
            backgroundColor: 'rgba(255, 255, 255, 0.9)',
            borderWidth: 1,
            borderColor: '#eee',
            borderRadius: 4,
            padding: [5, 7]
          },
          itemStyle: {
            shadowBlur: 20,
            shadowColor: 'rgba(0, 0, 0, 0.3)'
          }
        },
        labelLine: {
          show: true,
          length: 15,
          length2: 15,
          smooth: true,
          lineStyle: {
            width: 1,
            type: 'solid'
          }
        },
        data: pieData,
        animationType: 'scale',
        animationEasing: 'elasticOut',
        animationDelay: function (_idx) {
          return Math.random() * 200 + 100;
        }
      }
    ]
  };
};

const createLineChartOption = (): echarts.EChartsOption => {
  const { xAxisData, seriesData, seriesName, multiSeries } = props.chartData;
  if (!xAxisData || xAxisData.length === 0) return {};
  
  // 使用全局主题色调色板
  const colorPalette = [
    themeColors.primary,    // 蓝色
    themeColors.success,    // 绿色
    themeColors.warning,    // 橙色
    themeColors.danger,     // 红色
    themeColors.purple,     // 紫色
    themeColors.teal,       // 青绿色
    themeColors.lightBlue,  // 亮蓝色
    themeColors.lavender,   // 淡紫色
    themeColors.yellow,     // 黄色
    themeColors.info        // 灰色
  ];

  // 计算最大值和最小值，用于设置合适的Y轴范围
  let maxValue = 0;
  let minValue = 0;
  
  if (multiSeries) {
    multiSeries.forEach(series => {
      const seriesMax = Math.max(...series.data.filter(v => v !== null && v !== undefined));
      const seriesMin = Math.min(...series.data.filter(v => v !== null && v !== undefined));
      maxValue = Math.max(maxValue, seriesMax);
      minValue = Math.min(minValue, seriesMin);
    });
  } else if (seriesData) {
    maxValue = Math.max(...seriesData.filter(v => v !== null && v !== undefined));
    minValue = Math.min(...seriesData.filter(v => v !== null && v !== undefined));
  }
  
  // 调整Y轴范围，增加上下空间
  const yAxisMax = maxValue * 1.1;
  const yAxisMin = Math.max(0, minValue * 0.9); // 确保最小值不低于0

  // 基础配置
  const option: echarts.EChartsOption = {
    title: {
      text: props.title,
      left: "center",
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: "axis",
      formatter: function(params: any) {
        let tooltip = `<div style="font-weight:bold;margin-bottom:5px;">${params[0].name}</div>`;
        params.forEach((param: any) => {
          const formattedValue = new Intl.NumberFormat('zh-CN', {
            style: 'currency',
            currency: 'CNY',
            minimumFractionDigits: 2
          }).format(param.value);
          
          const colorSpan = `<span style="display:inline-block;margin-right:6px;border-radius:10px;width:10px;height:10px;background-color:${param.color};"></span>`;
          tooltip += `<div style="display:flex;align-items:center;margin:3px 0;">
                      ${colorSpan}
                      <span style="flex:1;">${param.seriesName}:</span>
                      <span style="font-weight:bold;margin-left:5px;">${formattedValue}</span>
                    </div>`;
        });
        return tooltip;
      },
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#eee',
      borderWidth: 1,
      padding: [10, 15],
      textStyle: {
        color: '#333',
        fontSize: 13
      },
      extraCssText: 'box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15); border-radius: 4px;'
    },
    legend: {
      orient: 'horizontal',
      bottom: 10,
      type: 'scroll',
      padding: [5, 20],
      itemWidth: 15,
      itemHeight: 10,
      itemGap: 15,
      textStyle: {
        fontSize: 12,
        color: '#666'
      },
      pageIconColor: '#999',
      pageTextStyle: {
        color: '#666'
      },
      // 如果有多系列数据，显示图例
      show: !!multiSeries,
      // 为所有多系列图表添加全选和反选按钮
      selector: multiSeries && multiSeries.length > 3 ? [
        {
          type: 'all',
          title: '全选'
        },
        {
          type: 'inverse',
          title: '反选'
        }
      ] : false,
      // 添加选择器样式
      selectorLabel: {
        fontSize: 12,
        color: '#666',
        borderRadius: 3,
        padding: [3, 8],
        borderWidth: 1,
        borderColor: '#ddd'
      },
      selectorPosition: 'end'
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: multiSeries ? "15%" : "10%",
      top: "15%",
      containLabel: true
    },
    xAxis: {
      type: "category",
      data: xAxisData,
      boundaryGap: false,
      axisLine: {
        lineStyle: {
          color: '#ddd'
        }
      },
      axisTick: {
        alignWithLabel: true,
        lineStyle: {
          color: '#ddd'
        }
      },
      axisLabel: {
        rotate: xAxisData && xAxisData.length > 6 ? 30 : 0,
        color: '#666',
        fontSize: 12,
        margin: 12
      },
      splitLine: {
        show: false
      }
    },
    yAxis: {
      type: "value",
      name: "金额 (¥)",
      nameTextStyle: {
        color: '#666',
        fontSize: 12,
        padding: [0, 0, 0, 5]
      },
      min: yAxisMin,
      max: yAxisMax,
      splitNumber: 5,
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#666',
        fontSize: 12,
        formatter: (value: number) => {
          if (value >= 10000) {
            return (value / 10000).toFixed(1) + '万';
          }
          return value.toString();
        }
      },
      splitLine: {
        lineStyle: {
          color: '#eee',
          type: 'dashed'
        }
      }
    },
    series: []
  };

  // 如果有多系列数据，使用多系列，否则使用单系列
  if (multiSeries) {
    // 判断是否有大量系列，如果系列超过10个，则采用简化的样式
    const hasManySeries = multiSeries.length > 10;
    
    option.series = multiSeries.map((series, index) => {
      const colorIndex = index % colorPalette.length;
      const colors = colorPalette[colorIndex];
      
      return {
        name: series.name,
        type: "line",
        data: series.data,
        smooth: true,
        symbol: 'circle',
        symbolSize: hasManySeries ? 4 : 6,
        showSymbol: !hasManySeries && series.data.length <= 12, // 当有大量系列时不显示标记
        sampling: hasManySeries ? 'average' : undefined, // 大量系列时采样以提高性能
        lineStyle: {
          width: hasManySeries ? 2 : 3, // 大量系列时使用更细的线条
          color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            { offset: 0, color: colors[0] },
            { offset: 1, color: colors[1] }
          ]),
          shadowColor: hasManySeries ? "rgba(0,0,0,0.05)" : "rgba(0,0,0,0.1)",
          shadowBlur: hasManySeries ? 3 : 5,
          cap: 'round'
        },
        itemStyle: {
          color: colors[0],
          borderColor: '#fff',
          borderWidth: hasManySeries ? 1 : 2,
          shadowColor: 'rgba(0, 0, 0, 0.2)',
          shadowBlur: hasManySeries ? 3 : 5
        },
        // 多系列时的区域填充
        areaStyle: props.isRangeMode && !hasManySeries ? {
          // 大量系列时不使用区域填充，避免视觉混乱
          opacity: 0.15,
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: colors[0].replace(')', ', 0.3)').replace('rgb', 'rgba') },
            { offset: 1, color: colors[1].replace(')', ', 0.05)').replace('rgb', 'rgba') }
          ])
        } : null,
        emphasis: {
          focus: "series",
          scale: !hasManySeries, // 大量系列时不使用缩放效果
          itemStyle: {
            borderWidth: hasManySeries ? 2 : 3,
            shadowBlur: hasManySeries ? 5 : 10
          },
          lineStyle: {
            width: hasManySeries ? 3 : 4
          }
        }
      };
    });
  } else {
    // 获取当前图表类型的主题色
    const colors = getChartThemeColor();
    
    option.series = [{
      name: seriesName || "账单金额",
      type: "line",
      data: seriesData,
      smooth: true,
      symbol: 'circle',
      symbolSize: 8,
      showSymbol: seriesData.length <= 10, // 当数据点较少时显示标记
      sampling: 'average', // 数据量大时进行采样
      lineStyle: {
        width: 4,
        color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
          { offset: 0, color: colors[0] },
          { offset: 1, color: colors[1] }
        ]),
        shadowColor: "rgba(0,0,0,0.2)",
        shadowBlur: 10,
        shadowOffsetY: 5,
        cap: 'round',
        join: 'round'
      },
      itemStyle: {
        color: colors[0],
        borderColor: '#fff',
        borderWidth: 2,
        shadowColor: 'rgba(0, 0, 0, 0.2)',
        shadowBlur: 5
      },
      areaStyle: {
        opacity: 0.25,
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: colors[0].replace('rgb', 'rgba').replace(')', ', 0.5)') },
          { offset: 0.5, color: colors[0].replace('rgb', 'rgba').replace(')', ', 0.15)') },
          { offset: 1, color: colors[1].replace('rgb', 'rgba').replace(')', ', 0.05)') }
        ])
      },
      emphasis: {
        focus: "series",
        itemStyle: {
          borderWidth: 3,
          shadowBlur: 10
        },
        lineStyle: {
          width: 5,
          shadowBlur: 15
        }
      },
      markPoint: seriesData.length > 0 ? {
        symbolSize: 60,
        data: [
          { type: 'max', name: '最大值', itemStyle: { color: 'rgba(255, 70, 131, 0.8)' } },
          { type: 'min', name: '最小值', itemStyle: { color: 'rgba(81, 187, 255, 0.8)' } }
        ],
        label: {
          formatter: function(param: any) {
            return param.name + '\n' + param.value;
          },
          color: '#fff'
        }
      } : undefined,
      markLine: {
        silent: true,
        lineStyle: {
          color: '#999',
          type: 'dashed',
          width: 1
        },
        data: [
          { type: 'average', name: '平均值' }
        ],
        label: {
          position: 'middle',
          formatter: '{b}: {c}',
          color: '#666',
          backgroundColor: 'rgba(255, 255, 255, 0.8)',
          padding: [3, 6],
          borderRadius: 3
        }
      },
      // 添加动画效果
      animationDuration: 1500,
      animationEasing: 'cubicInOut',
      animationDelay: function(_idx: number) {
        return 100;
      }
    }];
  }

  return option;
};

watch(
  () => props.chartData,
  () => {
    renderChart();
  },
  { deep: true }
);
</script>

<style scoped>
.chart-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.chart-inner {
  width: 100%;
  height: 100%;
  transition: opacity 0.3s ease;
}

.chart-loading {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 10;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: rgb(255 255 255 / 85%);
  backdrop-filter: blur(2px);
}

.chart-loading .el-icon {
  margin-bottom: 0.75rem;
  font-size: 2.5rem;
  color: #409eff;
  filter: drop-shadow(0 2px 5px rgb(0 0 0 / 10%));
}

.chart-loading span {
  font-size: 1rem;
  font-weight: 500;
  color: #606266;
}

.chart-empty {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 5;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: rgb(255 255 255 / 90%);
  backdrop-filter: blur(2px);
}

.chart-empty-icon {
  margin-bottom: 1.25rem;
  font-size: 3.5rem;
  color: #c0c4cc;
  filter: drop-shadow(0 2px 5px rgb(0 0 0 / 5%));
  opacity: 0.8;
}

.empty-text {
  margin-bottom: 0.75rem;
  font-size: 1.125rem;
  font-weight: 500;
  color: #606266;
}

.empty-tip {
  padding: 4px 12px;
  font-size: 0.875rem;
  color: #909399;
  background-color: rgb(144 147 153 / 10%);
  border-radius: 4px;
}

.chart-watermark {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  pointer-events: none;
  border-radius: 50%;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
}
</style>
