<template>
  <div class="flex items-center">
    <el-radio-group
      v-model="dateType"
      class="mr-4"
      @change="handleDateTypeChange"
    >
      <el-radio-button label="month">按月查询</el-radio-button>
      <el-radio-button label="range">时间范围</el-radio-button>
    </el-radio-group>

    <div v-if="dateType === 'month'">
      <el-date-picker
        v-model="selectedMonth"
        type="month"
        placeholder="选择月份"
        format="YYYY-MM"
        value-format="YYYY-MM"
        class="w-64"
        @change="handleMonthChange"
      />
    </div>

    <div v-else>
      <el-date-picker
        v-model="dateRange"
        type="monthrange"
        range-separator="至"
        start-placeholder="开始月份"
        end-placeholder="结束月份"
        format="YYYY-MM"
        value-format="YYYY-MM"
        class="w-96"
        @change="handleRangeChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from "vue";
import dayjs from "dayjs";




const emit = defineEmits<{
  (e: "month-change", month: string): void;
  (e: "range-change", range: { start_time: string; end_time: string }): void;
}>();

const dateType = ref<"month" | "range">("month");
const selectedMonth = ref<string>(dayjs().format("YYYY-MM"));
const dateRange = ref<[string, string] | null>(null);

// Initialize with the current month
onMounted(() => {
  handleMonthChange(selectedMonth.value);
});

function handleDateTypeChange() {
  if (dateType.value === "month") {
    handleMonthChange(selectedMonth.value);
  } else {
    // 自动填充最近半年的时间范围
    const endDate = dayjs();
    const startDate = endDate.subtract(5, 'month').startOf('month');
    
    // 格式化为YYYY-MM格式
    const startStr = startDate.format('YYYY-MM');
    const endStr = endDate.format('YYYY-MM');
    
    // 更新日期范围选择器的值
    dateRange.value = [startStr, endStr];
    
    // 触发事件
    handleRangeChange(dateRange.value);
  }
}

function handleMonthChange(month: string | null) {
  if (month) {
    emit("month-change", month);
  }
}

function handleRangeChange(range: [string, string] | null) {
  if (range && range.length === 2) {
    emit("range-change", {
      start_time: range[0],
      end_time: range[1]
    });
  }
}

// Watch for changes to make sure we emit updates
watch(selectedMonth, newMonth => {
  if (dateType.value === "month" && newMonth) {
    handleMonthChange(newMonth);
  }
});

watch(dateRange, newRange => {
  if (dateType.value === "range" && newRange) {
    handleRangeChange(newRange);
  }
});
</script>
