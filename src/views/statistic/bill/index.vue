<template>
  <div class="statistic-bill-container">
    <el-card class="dashboard-header mb-6" shadow="hover">
      <template #header>
        <div class="flex justify-between items-center">
          <div class="flex items-center">
            <el-icon class="mr-3 text-primary text-xl"><Money /></el-icon>
            <span class="text-xl font-bold text-primary">账单统计</span>
          </div>
        </div>
      </template>
      <div
        class="bg-gradient-to-r from-blue-50 to-indigo-50 p-5 rounded-xl transition-all duration-300 hover:shadow-lg"
      >
        <DateRangeSelector
          @month-change="handleBillCycleChange"
          @range-change="handleDateRangeChange"
        />
      </div>
    </el-card>

    <!-- 资源组专区 -->
    <el-card
      shadow="hover"
      class="mb-6 overflow-hidden transform transition-all duration-300 hover:shadow-xl border-t-4 border-amber-500"
    >
      <template #header>
        <div class="flex items-center justify-between py-2">
          <div class="flex items-center">
            <el-icon class="mr-3 text-amber-500 text-xl"
              ><FolderOpened
            /></el-icon>
            <span class="text-xl font-bold text-amber-500">资源组账单统计</span>
            <el-tag size="small" type="info" effect="plain" class="ml-3">
              <template v-if="isRangeMode">
                {{
                  currentDateRange?.start_time
                    ? currentDateRange.start_time.substring(0, 10)
                    : ""
                }}
                至
                {{
                  currentDateRange?.end_time
                    ? currentDateRange.end_time.substring(0, 10)
                    : ""
                }}
              </template>
              <template v-else>
                {{ currentBillCycle }}
              </template>
            </el-tag>
          </div>
          <div v-if="isRangeMode" class="text-sm text-gray-500">
            <span class="text-amber-500 font-medium">时间范围数据</span>
          </div>
        </div>
      </template>

      <div
        class="resource-group-section p-4 bg-gradient-to-r from-amber-50 to-orange-50 rounded-lg backdrop-blur-sm"
      >
        <!-- 资源组账单趋势图 - 仅在时间范围模式下显示 -->
        <el-card
          v-if="isRangeMode"
          shadow="hover"
          class="chart-card overflow-hidden transition-all duration-300 hover:shadow-lg border-l-4 border-amber-400 mb-6"
        >
          <template #header>
            <div class="flex items-center justify-between py-1">
              <div class="flex items-center">
                <el-icon class="mr-2 text-amber-500"><TrendCharts /></el-icon>
                <span class="font-bold text-gray-700">资源组账单趋势</span>
              </div>
              <el-tooltip content="查看时间范围内所有资源组账单的变化趋势">
                <el-icon><InfoFilled /></el-icon>
              </el-tooltip>
            </div>
          </template>
          <div class="flex items-center justify-center h-80">
            <BillChart
              v-if="
                resourceGroupTrendChartData.xAxisData &&
                resourceGroupTrendChartData.xAxisData.length > 0
              "
              :chart-type="'line'"
              :chart-data="resourceGroupTrendChartData"
              :is-range-mode="isRangeMode"
              class="w-full h-full"
            />
            <el-empty
              v-else
              description="暂无资源组账单趋势数据"
              :image-size="80"
            />
          </div>
        </el-card>

        <!-- 资源组分布图表和表格并排显示 -->
        <div class="flex flex-wrap gap-4 mb-4">
          <!-- 左侧图表 -->
          <el-card class="flex-1 min-w-[400px] overflow-hidden transition-all duration-300 hover:shadow-lg border-l-4 border-orange-400">
            <template #header>
              <div class="flex items-center py-1">
                <el-icon class="mr-2 text-amber-500"><PieChart /></el-icon>
                <span class="font-bold text-gray-700">资源组账单分布</span>
              </div>
            </template>
            <div class="flex items-center justify-center h-80">
              <BillChart
                v-if="resourceGroupChartData && resourceGroupChartData.pieData && resourceGroupChartData.pieData.length > 0"
                :chart-type="'pie'"
                :chart-data="{ pieData: resourceGroupChartData.pieData }"
                class="w-full h-full"
              />
              <el-empty
                v-else
                description="暂无资源组账单分布数据"
                :image-size="80"
              />
            </div>
          </el-card>
          
          <!-- 右侧表格 -->
          <el-card class="flex-1 min-w-[500px]" shadow="hover" :body-style="{ padding: '0' }">
            <template #header>
              <div class="flex justify-between items-center p-1">
                <div class="flex items-center">
                  <el-icon class="mr-2 text-amber-500 text-xl"><FolderOpened /></el-icon>
                  <span class="text-lg font-bold text-gray-700">资源组账单明细</span>
                  <el-tag size="small" type="info" effect="plain" class="ml-2">{{ resourceGroupTableData.length }}条记录</el-tag>
                </div>
                <el-button
                  type="primary"
                  :icon="Download"
                  size="small"
                  class="export-btn transition-all duration-300 hover:scale-105"
                  @click="handleResourceGroupExport"
                >
                  导出
                </el-button>
              </div>
            </template>
            <div class="bill-table-wrapper p-2">
              <el-table
                :data="resourceGroupTableData"
                border
                stripe
                height="400"
                :loading="resourceGroupLoading"
                class="bill-table rounded-md overflow-hidden"
                :header-cell-style="{
                  backgroundColor: '#fff8e1',
                  color: '#f59e0b',
                  fontWeight: 'bold',
                  padding: '12px 8px',
                  borderBottom: '2px solid #fef3c7'
                }"
                :row-class-name="tableRowClassName"
                :empty-text="null"
                @sort-change="handleResourceGroupSortChange"
              >
                <template #empty>
                  <div class="table-empty-wrapper py-6">
                    <el-empty :image-size="0">
                      <template #image>
                        <el-icon class="empty-icon text-5xl text-gray-300"><FolderOpened /></el-icon>
                      </template>
                      <template #description>
                        <p class="empty-text font-medium text-gray-500 mt-4">暂无资源组账单数据</p>
                        <p class="empty-tip text-sm text-gray-400 mt-2">请尝试选择其他时间范围</p>
                      </template>
                    </el-empty>
                  </div>
                </template>
                <el-table-column prop="resource_group_name" label="资源组名称" min-width="180" sortable="custom" />
                <el-table-column prop="amount" label="金额" min-width="120" sortable="custom">
                  <template #default="scope">
                    <span
                      class="font-medium"
                      :class="{
                        'text-red-500': scope.row.amount > 0,
                        'text-gray-500': scope.row.amount === 0
                      }"
                    >
                      {{ formatCurrency(scope.row.amount) }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column prop="month" label="账单周期" min-width="120" sortable="custom" />
              </el-table>
            </div>
          </el-card>
        </div>
      </div>
    </el-card>

    <!-- 账号专区 -->
    <el-card
      shadow="hover"
      class="mb-6 overflow-hidden transform transition-all duration-300 hover:shadow-xl border-t-4 border-blue-500"
    >
      <template #header>
        <div class="flex items-center justify-between py-2">
          <div class="flex items-center">
            <el-icon class="mr-3 text-blue-500 text-xl"><User /></el-icon>
            <span class="text-xl font-bold text-blue-500">账号账单统计</span>
            <el-tag size="small" type="info" effect="plain" class="ml-3">
              <template v-if="isRangeMode">
                {{
                  currentDateRange?.start_time
                    ? currentDateRange.start_time.substring(0, 10)
                    : ""
                }}
                至
                {{
                  currentDateRange?.end_time
                    ? currentDateRange.end_time.substring(0, 10)
                    : ""
                }}
              </template>
              <template v-else>
                {{ currentBillCycle }}
              </template>
            </el-tag>
          </div>
          <div v-if="isRangeMode" class="text-sm text-gray-500">
            <span class="text-blue-500 font-medium">时间范围数据</span>
          </div>
        </div>
      </template>

      <div
        class="account-section p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg backdrop-blur-sm"
      >
        <!-- 账号账单趋势图 - 仅在时间范围模式下显示 -->
        <el-card
          v-if="isRangeMode"
          shadow="hover"
          class="chart-card overflow-hidden transition-all duration-300 hover:shadow-lg border-l-4 border-blue-400 mb-6"
        >
          <template #header>
            <div class="flex items-center justify-between py-1">
              <div class="flex items-center">
                <el-icon class="mr-2 text-blue-500"><TrendCharts /></el-icon>
                <span class="font-bold text-gray-700">账号账单趋势</span>
              </div>
            </div>
          </template>
          <div class="flex items-center justify-center h-80">
            <BillChart
              chart-type="line"
              :chart-data="accountTrendChartData"
              class="w-full h-full"
            />
          </div>
        </el-card>

        <!-- 账号分布图表和表格并排显示 -->
        <div class="flex flex-wrap gap-4 mb-4">
          <!-- 左侧图表 -->
          <el-card class="flex-1 min-w-[400px] overflow-hidden transition-all duration-300 hover:shadow-lg border-l-4 border-indigo-400">
            <template #header>
              <div class="flex items-center py-1">
                <el-icon class="mr-2 text-blue-500"><PieChart /></el-icon>
                <span class="font-bold text-gray-700">账号账单分布</span>
              </div>
            </template>
            <div class="flex items-center justify-center h-80">
              <BillChart
                chart-type="pie"
                :chart-data="accountChartData"
                class="w-full h-full"
              />
            </div>
          </el-card>
          
          <!-- 右侧表格 -->
          <el-card class="flex-1 min-w-[500px]" shadow="hover" :body-style="{ padding: '0' }">
            <template #header>
              <div class="flex justify-between items-center p-1">
                <div class="flex items-center">
                  <el-icon class="mr-2 text-blue-500 text-xl"><User /></el-icon>
                  <span class="text-lg font-bold text-gray-700">账号级别账单明细</span>
                  <el-tag size="small" type="info" effect="plain" class="ml-2">{{ accountTableData.length }}条记录</el-tag>
                </div>
                <el-button
                  type="primary"
                  :icon="Download"
                  size="small"
                  class="export-btn transition-all duration-300 hover:scale-105"
                  @click="handleAccountExport"
                >
                  导出
                </el-button>
              </div>
            </template>
            <div class="bill-table-wrapper p-2">
              <el-table
                :data="accountTableData"
                border
                stripe
                height="400"
                :loading="accountLoading"
                class="bill-table rounded-md overflow-hidden"
                :header-cell-style="{
                  backgroundColor: '#f0f5ff',
                  color: '#409eff',
                  fontWeight: 'bold',
                  padding: '12px 8px',
                  borderBottom: '2px solid #e6effd'
                }"
                :row-class-name="tableRowClassName"
                :empty-text="null"
                @sort-change="handleAccountSortChange"
              >
                <template #empty>
                  <div class="table-empty-wrapper py-6">
                    <el-empty :image-size="0">
                      <template #image>
                        <el-icon class="empty-icon text-5xl text-gray-300"><User /></el-icon>
                      </template>
                      <template #description>
                        <p class="empty-text font-medium text-gray-500 mt-4">暂无账号账单数据</p>
                        <p class="empty-tip text-sm text-gray-400 mt-2">请尝试选择其他时间范围</p>
                      </template>
                    </el-empty>
                  </div>
                </template>
                <el-table-column prop="account_name" label="账号名称" min-width="150" sortable="custom" />
                <el-table-column prop="cloud_type" label="云服务商类型" min-width="120" sortable="custom" />
                <el-table-column prop="amount" label="金额" min-width="120" sortable="custom">
                  <template #default="scope">
                    <span
                      class="font-medium"
                      :class="{
                        'text-red-500': scope.row.amount > 0,
                        'text-gray-500': scope.row.amount === 0
                      }"
                    >
                      {{ formatCurrency(scope.row.amount) }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column prop="month" label="账单周期" min-width="120" sortable="custom" />
              </el-table>
            </div>
          </el-card>
        </div>
      </div>
    </el-card>

    <!-- 云服务商专区 -->
    <el-card
      shadow="hover"
      class="mb-6 overflow-hidden transform transition-all duration-300 hover:shadow-xl border-t-4 border-green-500"
    >
      <template #header>
        <div class="flex items-center justify-between py-2">
          <div class="flex items-center">
            <el-icon class="mr-3 text-green-500 text-xl"><Service /></el-icon>
            <span class="text-xl font-bold text-green-500"
              >云服务商账单统计</span
            >
            <el-tag size="small" type="info" effect="plain" class="ml-3">
              <template v-if="isRangeMode">
                {{
                  currentDateRange?.start_time
                    ? currentDateRange.start_time.substring(0, 10)
                    : ""
                }}
                至
                {{
                  currentDateRange?.end_time
                    ? currentDateRange.end_time.substring(0, 10)
                    : ""
                }}
              </template>
              <template v-else>
                {{ currentBillCycle }}
              </template>
            </el-tag>
          </div>
          <div v-if="isRangeMode" class="text-sm text-gray-500">
            <span class="text-green-500 font-medium">时间范围数据</span>
          </div>
        </div>
      </template>

      <div
        class="cloud-section p-4 bg-gradient-to-r from-green-50 to-teal-50 rounded-lg backdrop-blur-sm"
      >
        <!-- 云服务商账单趋势图 - 仅在时间范围模式下显示 -->
        <el-card
          v-if="isRangeMode"
          shadow="hover"
          class="chart-card overflow-hidden transition-all duration-300 hover:shadow-lg border-l-4 border-green-400 mb-6"
        >
          <template #header>
            <div class="flex items-center justify-between py-1">
              <div class="flex items-center">
                <el-icon class="mr-2 text-green-500"><TrendCharts /></el-icon>
                <span class="font-bold text-gray-700">云服务商账单趋势</span>
              </div>
            </div>
          </template>
          <div class="flex items-center justify-center h-80">
            <BillChart
              chart-type="line"
              :chart-data="cloudTrendChartData"
              class="w-full h-full"
            />
          </div>
        </el-card>

        <!-- 云服务商图表和表格区域 -->
        <div class="flex flex-wrap gap-4 mb-4">
          <!-- 左侧图表 -->
          <el-card class="flex-1 min-w-[400px] overflow-hidden transition-all duration-300 hover:shadow-lg border-l-4 border-teal-400">
            <template #header>
              <div class="flex items-center py-1">
                <el-icon class="mr-2 text-green-500"><DataAnalysis /></el-icon>
                <span class="font-bold text-gray-700">云服务商账单分布</span>
              </div>
            </template>
            <div class="flex items-center justify-center h-80">
              <BillChart
                chart-type="pie"
                :chart-data="cloudChartData"
                class="w-full h-full"
              />
            </div>
          </el-card>
          
          <!-- 右侧表格 -->
          <el-card class="flex-1 min-w-[500px]" shadow="hover" :body-style="{ padding: '0' }">
            <template #header>
              <div class="flex justify-between items-center p-1">
                <div class="flex items-center">
                  <el-icon class="mr-2 text-green-500 text-xl"><Service /></el-icon>
                  <span class="text-lg font-bold text-gray-700">云服务商账单明细</span>
                  <el-tag size="small" type="info" effect="plain" class="ml-2">{{ cloudTableData.length }}条记录</el-tag>
                </div>
                <el-button
                  type="primary"
                  :icon="Download"
                  size="small"
                  class="export-btn transition-all duration-300 hover:scale-105"
                  @click="handleCloudExport"
                >
                  导出
                </el-button>
              </div>
            </template>
            <div class="bill-table-wrapper p-2">
              <el-table
                :data="cloudTableData"
                border
                stripe
                height="400"
                :loading="cloudLoading"
                class="bill-table rounded-md overflow-hidden"
                :header-cell-style="{
                  backgroundColor: '#f0f9eb',
                  color: '#67c23a',
                  fontWeight: 'bold',
                  padding: '12px 8px',
                  borderBottom: '2px solid #e6f7d9'
                }"
                :row-class-name="tableRowClassName"
                :empty-text="null"
                @sort-change="handleCloudSortChange"
              >
                <template #empty>
                  <div class="table-empty-wrapper py-6">
                    <el-empty :image-size="0">
                      <template #image>
                        <el-icon class="empty-icon text-5xl text-gray-300"><Service /></el-icon>
                      </template>
                      <template #description>
                        <p class="empty-text font-medium text-gray-500 mt-4">暂无云服务商账单数据</p>
                        <p class="empty-tip text-sm text-gray-400 mt-2">请尝试选择其他时间范围</p>
                      </template>
                    </el-empty>
                  </div>
                </template>
                <el-table-column prop="name" label="云服务商" min-width="120" sortable="custom" />
                <el-table-column prop="amount" label="金额" min-width="120" sortable="custom">
                  <template #default="scope">
                    <span
                      class="font-medium"
                      :class="{
                        'text-red-500': scope.row.amount > 0,
                        'text-gray-500': scope.row.amount === 0
                      }"
                    >
                      {{ formatCurrency(scope.row.amount) }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column prop="month" label="账单周期" min-width="120" sortable="custom" />
              </el-table>
            </div>
          </el-card>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from "vue";
import { ElMessage } from "element-plus";
import {
  Download,
  Money,
  User,
  Service,
  PieChart,
  DataAnalysis,
  TrendCharts,
  FolderOpened,
  InfoFilled
} from "@element-plus/icons-vue";
import DateRangeSelector from "./components/DateRangeSelector.vue";
import BillChart from "./components/BillChart.vue";
import {
  getAccountBillStatisticAPI,
  getAccountBillRangeStatisticAPI,
  type AccountBillStatistic,
  type AccountBillRangeStatistic
} from "@/api/statistic/bill/account";
import {
  getCloudBillStatisticAPI,
  getCloudBillRangeStatisticAPI,
  type CloudBillStatistic,
  type CloudBillRangeStatistic,
  type CloudBillSummary
} from "@/api/statistic/bill/cloud";
import {
  getResourceGroupBillStatisticAPI,
  getResourceGroupBillRangeStatisticAPI,
  type ResourceGroupBill,
  type GetResourceGroupBillStatisticParams,
  type ResourceGroupBillRangeParams
} from "@/api/statistic/bill/resource-group";

// 共享状态
const isRangeMode = ref(false);
const currentBillCycle = ref("");
const currentDateRange = ref<{ start_time: string; end_time: string } | null>(
  null
);

// 账号级别数据
const accountLoading = ref(false);
const accountTableData = ref<
  (AccountBillStatistic | AccountBillRangeStatistic)[]
>([]);

// 资源组数据
const resourceGroupLoading = ref(false);
const resourceGroupTableData = ref<ResourceGroupBill[]>([]);

// 云服务商数据
const cloudLoading = ref(false);
const cloudTableData = ref<
  (CloudBillStatistic | CloudBillRangeStatistic | CloudBillSummary)[]
>([]);

// 图表数据
const accountChartData = computed(() => {
  try {
    if (isRangeMode.value) {
      // 过滤掉没有账户名称的数据，保留金额为0的数据以便于检查
      const filteredData = (accountTableData.value || []).filter(
        item => item && item.account_name && item.account_name !== "-"
      );

      // 检查是否所有金额都为0
      const allAmountsAreZero =
        filteredData.length > 0 &&
        filteredData.every(item => item.amount === 0);

      // 如果所有金额都是0，则返回空数据，触发无数据状态
      if (allAmountsAreZero) {
        return { pieData: [] };
      }

      // 按账户名称分组并汇总金额
      const accountSummary: Record<string, number> = {};
      filteredData.forEach(item => {
        const accountName = item.account_name || "Unknown";
        accountSummary[accountName] =
          (accountSummary[accountName] || 0) + item.amount;
      });

      // 将汇总数据转换为饥图所需的格式
      const pieData = Object.entries(accountSummary).map(([name, value]) => ({
        name,
        value
      }));

      // 按金额降序排序
      pieData.sort((a, b) => (b.value as number) - (a.value as number));

      return { pieData };
    } else {
      // 过滤掉没有账户名称的数据，保留金额为0的数据以便于检查
      const filteredData = (accountTableData.value || []).filter(
        item => item && item.account_name && item.account_name !== "-"
      );

      // 检查是否所有金额都为0
      const allAmountsAreZero =
        filteredData.length > 0 &&
        filteredData.every(item => item.amount === 0);

      // 如果所有金额都是0，则返回空数据，触发无数据状态
      if (allAmountsAreZero) {
        return { pieData: [] };
      }

      // 转换为饥图格式
      const pieData = filteredData.map(item => ({
        name: item.account_name,
        value: item.amount
      }));

      // 按金额降序排序
      pieData.sort((a, b) => (b.value as number) - (a.value as number));

      return { pieData };
    }
  } catch (e) {
    console.error("Error in accountChartData:", e);
    return { pieData: [] };
  }
});

const cloudChartData = computed(() => {
  try {
    // 过滤无效的云服务商
    const filteredData = (cloudTableData.value || []).filter(
      item => item && item.name && item.name !== "-"
    );

    // 如果没有数据，返回空数组
    if (filteredData.length === 0) return { pieData: [] };

    // 按云服务商分组并汇总金额
    const cloudSummary: Record<string, number> = {};
    filteredData.forEach(item => {
      const name = item.name || "Unknown";
      cloudSummary[name] = (cloudSummary[name] || 0) + item.amount;
    });

    // 将汇总数据转换为饥图所需的格式
    const pieData = Object.entries(cloudSummary).map(([name, value]) => ({
      name,
      value
    }));

    // 按金额降序排序
    pieData.sort((a, b) => (b.value as number) - (a.value as number));

    return { pieData };
  } catch (e) {
    console.error("Error in cloudChartData:", e);
    return { pieData: [] };
  }
});

const resourceGroupChartData = computed(() => {
  try {
    if (isRangeMode.value) {
      // 过滤出有效的资源组数据
      const filteredData = (resourceGroupTableData.value || []).map(item => ({
        ...item,
        resource_group_name: item.resource_group_name
          ? item.resource_group_name
          : "(未设置资源组)"
      }));

      // 如果没有数据，返回空数组
      if (filteredData.length === 0) return { pieData: [] };

      // 按资源组名称分组并汇总金额
      const resourceGroupSummary: Record<string, number> = {};
      filteredData.forEach(item => {
        const name = item.resource_group_name || "Unknown";
        resourceGroupSummary[name] =
          (resourceGroupSummary[name] || 0) + item.amount;
      });

      // 将汇总数据转换为饥图所需的格式
      const pieData = Object.entries(resourceGroupSummary).map(
        ([name, value]) => ({
          name,
          value
        })
      );

      // 按金额降序排序
      pieData.sort((a, b) => (b.value as number) - (a.value as number));

      return { pieData };
    }
    // 单月查询模式
    else {
      // 过滤有效资源组数据
      const filteredData = (resourceGroupTableData.value || []).map(item => ({
        ...item,
        resource_group_name: item.resource_group_name
          ? item.resource_group_name
          : "(未设置资源组)"
      }));

      // 如果没有数据，返回空图表
      if (filteredData.length === 0) {
        return { pieData: [] };
      }

      // 转换为饼图格式
      const pieData = filteredData.map(item => ({
        name: item.resource_group_name,
        value: item.amount
      }));

      // 按金额降序排序
      pieData.sort((a, b) => (b.value as number) - (a.value as number));

      return { pieData };
    }
  } catch (e) {
    console.error("Error in resourceGroupChartData:", e);
    return { pieData: [] };
  }
});

// 账号趋势图数据
const accountTrendChartData = computed(() => {
  try {
    // 定义返回空数据的函数
    const emptyData = () => ({
      xAxisData: [],
      seriesData: [],
      seriesName: "账号月度金额"
    });

    // 在范围查询模式下
    if (isRangeMode.value) {
      // 按账号和月份分组的数据
      const accountMonthlyData: Record<string, Record<string, number>> = {};

      // 先获取所有月份
      const allMonths = new Set<string>();

      // 处理账号数据
      (accountTableData.value || []).forEach(item => {
        if (
          item &&
          item.month &&
          item.account_name &&
          item.account_name !== "-"
        ) {
          // 添加月份
          allMonths.add(item.month);

          // 初始化账号数据
          if (!accountMonthlyData[item.account_name]) {
            accountMonthlyData[item.account_name] = {};
          }

          // 累加金额
          accountMonthlyData[item.account_name][item.month] =
            (accountMonthlyData[item.account_name][item.month] || 0) +
            item.amount;
        }
      });

      // 按月份排序（升序）
      const sortedMonths = Array.from(allMonths).sort((a, b) =>
        a.localeCompare(b)
      );

      // 如果没有数据，返回空图表
      if (
        sortedMonths.length === 0 ||
        Object.keys(accountMonthlyData).length === 0
      ) {
        return emptyData();
      }

      // 将每个账号作为一个系列
      const series = Object.keys(accountMonthlyData).map(accountName => {
        // 确保每个月份都有数据，没有的话填0
        const data = sortedMonths.map(
          month => accountMonthlyData[accountName][month] || 0
        );

        return {
          name: accountName,
          data: data
        };
      });

      // 返回多系列数据
      return {
        xAxisData: sortedMonths,
        multiSeries: series,
        seriesName: "账号月度金额"
      };
    }
    // 单月查询模式 - 显示当前月不同账号的数据
    else {
      // 过滤有效账号数据
      const filteredData = (accountTableData.value || []).filter(
        item => item && item.account_name && item.account_name !== "-"
      );

      // 如果没有数据，返回空图表
      if (filteredData.length === 0) {
        return emptyData();
      }

      // 按金额降序排序，取前5个账号
      const topAccounts = [...filteredData]
        .sort((a, b) => b.amount - a.amount)
        .slice(0, 5);

      return {
        xAxisData: topAccounts.map(item => item.account_name),
        seriesData: topAccounts.map(item => item.amount),
        seriesName: "账号金额排名"
      };
    }
  } catch (e) {
    console.error("Error in accountTrendChartData:", e);
    return { xAxisData: [], seriesData: [], seriesName: "账号月度金额" };
  }
});

// 资源组趋势图数据
const resourceGroupTrendChartData = computed(() => {
  try {
    // 定义返回空数据的函数
    const emptyData = () => ({
      xAxisData: [],
      seriesData: [],
      seriesName: "资源组月度金额"
    });

    // 在范围查询模式下
    if (isRangeMode.value) {
      // 按资源组和月份分组的数据
      const resourceGroupMonthlyData: Record<
        string,
        Record<string, number>
      > = {};

      // 先获取所有月份
      const allMonths = new Set<string>();

      // 处理资源组数据
      (resourceGroupTableData.value || []).forEach(item => {
        if (item && item.month) {
          const resourceGroupName = item.resource_group_name
            ? item.resource_group_name
            : "(未设置资源组)";

          // 添加月份
          allMonths.add(item.month);

          // 初始化资源组数据
          if (!resourceGroupMonthlyData[resourceGroupName]) {
            resourceGroupMonthlyData[resourceGroupName] = {};
          }

          // 累加金额
          resourceGroupMonthlyData[resourceGroupName][item.month] =
            (resourceGroupMonthlyData[resourceGroupName][item.month] || 0) +
            item.amount;
        }
      });

      // 按月份排序（升序）
      const sortedMonths = Array.from(allMonths).sort((a, b) =>
        a.localeCompare(b)
      );

      // 如果没有数据，返回空图表
      if (
        sortedMonths.length === 0 ||
        Object.keys(resourceGroupMonthlyData).length === 0
      ) {
        return emptyData();
      }

      // 将每个资源组作为一个系列
      const series = Object.keys(resourceGroupMonthlyData).map(
        resourceGroupName => {
          // 确保每个月份都有数据，没有的话填0
          const data = sortedMonths.map(
            month => resourceGroupMonthlyData[resourceGroupName][month] || 0
          );

          return {
            name: resourceGroupName,
            data: data
          };
        }
      );

      // 对系列按总金额排序，但不限制数量，显示所有资源组
      series.sort((a, b) => {
        const sumA = a.data.reduce((sum, val) => sum + val, 0);
        const sumB = b.data.reduce((sum, val) => sum + val, 0);
        return sumB - sumA;
      });

      return {
        xAxisData: sortedMonths.map(month => formatMonth(month)),
        multiSeries: series,
        seriesName: "资源组月度金额"
      };
    }
    // 单月查询模式
    else {
      // 无法显示趋势，返回空数据
      return emptyData();
    }
  } catch (e) {
    console.error("Error in resourceGroupTrendChartData:", e);
    return {
      xAxisData: [],
      seriesData: [],
      seriesName: "资源组月度金额"
    };
  }
});

// 云服务商趋势图数据
const cloudTrendChartData = computed(() => {
  try {
    // 定义返回空数据的函数
    const emptyData = () => ({
      xAxisData: [],
      seriesData: [],
      seriesName: "云服务商月度金额"
    });

    // 在范围查询模式下
    if (isRangeMode.value) {
      // 按云服务商和月份分组的数据
      const cloudMonthlyData: Record<string, Record<string, number>> = {};

      // 先获取所有月份
      const allMonths = new Set<string>();

      // 处理云服务商数据
      (cloudTableData.value || []).forEach(item => {
        if (item && item.month && item.name && item.name !== "-") {
          // 添加月份
          allMonths.add(item.month);

          // 初始化云服务商数据
          if (!cloudMonthlyData[item.name]) {
            cloudMonthlyData[item.name] = {};
          }

          // 累加金额
          cloudMonthlyData[item.name][item.month] =
            (cloudMonthlyData[item.name][item.month] || 0) + item.amount;
        }
      });

      // 按月份排序（升序）
      const sortedMonths = Array.from(allMonths).sort((a, b) =>
        a.localeCompare(b)
      );

      // 如果没有数据，返回空图表
      if (
        sortedMonths.length === 0 ||
        Object.keys(cloudMonthlyData).length === 0
      ) {
        return emptyData();
      }

      // 将每个云服务商作为一个系列
      const series = Object.keys(cloudMonthlyData).map(cloudName => {
        // 确保每个月份都有数据，没有的话填0
        const data = sortedMonths.map(
          month => cloudMonthlyData[cloudName][month] || 0
        );

        return {
          name: cloudName,
          data: data
        };
      });

      // 返回多系列数据
      return {
        xAxisData: sortedMonths,
        multiSeries: series,
        seriesName: "云服务商月度金额"
      };
    }
    // 单月查询模式 - 显示当前月不同云服务商的数据
    else {
      // 过滤有效云服务商数据
      const filteredData = (cloudTableData.value || []).filter(
        item => item && item.name && item.name !== "-"
      );

      // 如果没有数据，返回空图表
      if (filteredData.length === 0) {
        return emptyData();
      }

      // 按金额降序排序
      const sortedData = [...filteredData].sort((a, b) => b.amount - a.amount);

      return {
        xAxisData: sortedData.map(item => item.name),
        seriesData: sortedData.map(item => item.amount),
        seriesName: "云服务商金额"
      };
    }
  } catch (e) {
    console.error("Error in cloudTrendChartData:", e);
    return { xAxisData: [], seriesData: [], seriesName: "云服务商月度金额" };
  }
});

// 加载资源组账单数据
const loadResourceGroupBillData = async () => {
  resourceGroupLoading.value = true;

  try {
    // 判断查询模式，根据不同的模式调用不同的API
    if (isRangeMode.value && currentDateRange.value) {
      // 范围查询模式
      const { start_time, end_time } = currentDateRange.value;
      const params: ResourceGroupBillRangeParams = {
        start_time,
        end_time
      };
      const res = await getResourceGroupBillRangeStatisticAPI(params);
      // 确保所有资源组账单数据都有有效的账单周期
      resourceGroupTableData.value = (res.data || []).map(item => {
        // 如果账单周期为空，使用当前查询的时间范围的开始时间的月份
        if (!item.month && currentDateRange.value?.start_time) {
          return {
            ...item,
            month: currentDateRange.value.start_time.substring(0, 7) // 提取YYYY-MM格式
          };
        }
        return item;
      });
    } else if (currentBillCycle.value) {
      // 单月查询模式
      const params: GetResourceGroupBillStatisticParams = {
        month: currentBillCycle.value
      };
      const res = await getResourceGroupBillStatisticAPI(params);
      // 确保所有资源组账单数据都有有效的账单周期
      resourceGroupTableData.value = (res.data || []).map(item => {
        // 如果账单周期为空，使用当前查询的账单周期
        if (!item.month && currentBillCycle.value) {
          return {
            ...item,
            month: currentBillCycle.value
          };
        }
        return item;
      });
    }
  } catch (error) {
    ElMessage.error("获取资源组账单数据失败");
    console.error("Load resource group bill data error:", error);
    resourceGroupTableData.value = [];
  } finally {
    resourceGroupLoading.value = false;
  }
};

// 加载账号账单数据
const loadAccountBillData = async () => {
  accountLoading.value = true;

  try {
    // 判断查询模式，根据不同的模式调用不同的API
    if (isRangeMode.value && currentDateRange.value) {
      // 范围查询模式
      const { start_time, end_time } = currentDateRange.value;
      const res = await getAccountBillRangeStatisticAPI({
        start_time,
        end_time
      });
      accountTableData.value = res.data || [];
    } else if (currentBillCycle.value) {
      // 单月查询模式
      const res = await getAccountBillStatisticAPI({
        month: currentBillCycle.value
      });
      accountTableData.value = res.data || [];
    }
  } catch (error) {
    ElMessage.error("获取账号账单数据失败");
    console.error("Load account bill data error:", error);
    accountTableData.value = [];
  } finally {
    accountLoading.value = false;
  }
};

// 加载云服务商账单数据
const loadCloudBillData = async () => {
  cloudLoading.value = true;

  try {
    // 判断查询模式，根据不同的模式调用不同的API
    if (isRangeMode.value && currentDateRange.value) {
      // 范围查询模式
      const { start_time, end_time } = currentDateRange.value;
      const res = await getCloudBillRangeStatisticAPI({
        start_time,
        end_time
      });
      if (res.data && res.data.bills) {
        cloudTableData.value = res.data.bills;
      } else {
        cloudTableData.value = [];
      }
    } else if (currentBillCycle.value) {
      // 单月查询模式
      const res = await getCloudBillStatisticAPI({
        month: currentBillCycle.value
      });
      cloudTableData.value = res.data || [];
    }
  } catch (error) {
    ElMessage.error("获取云服务商账单数据失败");
    console.error("Load cloud bill data error:", error);
    cloudTableData.value = [];
  } finally {
    cloudLoading.value = false;
  }
};

// DateRangeSelector组件的事件处理函数已经定义在下面

// DateRangeSelector组件事件处理
const handleBillCycleChange = async (billCycle: string) => {
  currentBillCycle.value = billCycle;
  currentDateRange.value = null;
  isRangeMode.value = false;

  try {
    // 先清空数据以触发重新渲染
    resourceGroupTableData.value = [];
    accountTableData.value = [];
    cloudTableData.value = [];

    // 然后加载新数据
    await Promise.all([
      loadResourceGroupBillData(),
      loadAccountBillData(),
      loadCloudBillData()
    ]);

    // 强制触发更新
    nextTick(() => {
      // 仅用于触发视图更新
      console.log("Bill cycle data loaded, updating charts...");
    });
  } catch (error) {
    console.error("Failed to load data after bill cycle change:", error);
    ElMessage.error("加载账单数据失败");
  }
};

const handleDateRangeChange = async (dateRange: {
  start_time: string;
  end_time: string;
}) => {
  currentDateRange.value = dateRange;
  isRangeMode.value = true;

  try {
    // 先清空数据以触发重新渲染
    resourceGroupTableData.value = [];
    accountTableData.value = [];
    cloudTableData.value = [];

    // 然后加载新数据
    await Promise.all([
      loadResourceGroupBillData(),
      loadAccountBillData(),
      loadCloudBillData()
    ]);

    // 强制触发更新
    nextTick(() => {
      // 仅用于触发视图更新
      console.log("Range data loaded, updating charts...");
    });
  } catch (error) {
    console.error("Failed to load data after date range change:", error);
    ElMessage.error("加载账单数据失败");
  }
};

// 注：isRangeMode的值现在由handleBillCycleChange和handleDateRangeChange函数直接设置

// 排序处理
const handleAccountSortChange = ({
  prop,
  order
}: {
  prop: string;
  order: string;
}) => {
  if (!prop || !order) return;

  // 直接使用order参数排序

  accountTableData.value.sort((a, b) => {
    const valueA = a[prop as keyof typeof a];
    const valueB = b[prop as keyof typeof b];

    if (typeof valueA === "number" && typeof valueB === "number") {
      return order === "ascending" ? valueA - valueB : valueB - valueA;
    }

    if (valueA === null) return order === "ascending" ? -1 : 1;
    if (valueB === null) return order === "ascending" ? 1 : -1;

    return order === "ascending"
      ? String(valueA).localeCompare(String(valueB))
      : String(valueB).localeCompare(String(valueA));
  });
};

const handleCloudSortChange = ({
  prop,
  order
}: {
  prop: string;
  order: string;
}) => {
  if (!prop || !order) return;

  // 直接使用order参数排序
  cloudTableData.value.sort((a, b) => {
    const valueA = a[prop as keyof typeof a];
    const valueB = b[prop as keyof typeof b];

    if (typeof valueA === "number" && typeof valueB === "number") {
      return order === "ascending" ? valueA - valueB : valueB - valueA;
    }

    if (valueA === null) return order === "ascending" ? -1 : 1;
    if (valueB === null) return order === "ascending" ? 1 : -1;

    return order === "ascending"
      ? String(valueA).localeCompare(String(valueB))
      : String(valueB).localeCompare(String(valueA));
  });
};

const handleResourceGroupSortChange = ({
  prop,
  order
}: {
  prop: string;
  order: string;
}) => {
  if (!prop || !order) return;

  // 直接使用order参数排序
  resourceGroupTableData.value.sort((a, b) => {
    const valueA = a[prop as keyof typeof a];
    const valueB = b[prop as keyof typeof b];

    if (typeof valueA === "number" && typeof valueB === "number") {
      return order === "ascending" ? valueA - valueB : valueB - valueA;
    }

    if (valueA === null) return order === "ascending" ? -1 : 1;
    if (valueB === null) return order === "ascending" ? 1 : -1;

    return order === "ascending"
      ? String(valueA).localeCompare(String(valueB))
      : String(valueB).localeCompare(String(valueA));
  });
};

// 表格行类名函数
const tableRowClassName = ({ rowIndex }: { row: any; rowIndex: number }) => {
  if (rowIndex % 2 === 0) {
    return "even-row";
  }
  return "odd-row";
};

// 格式化月份字符串（用于图表标签）
const formatMonth = (month: string) => {
  if (!month) return "";
  // 直接返回原始字符串，不做额外处理
  return month;
};

// 格式化金额
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat("zh-CN", {
    style: "currency",
    currency: "CNY",
    minimumFractionDigits: 2
  }).format(amount);
};

// 导出功能
const handleAccountExport = () => {
  if (accountTableData.value.length === 0) {
    ElMessage.warning("无数据可导出");
    return;
  }

  // 准备CSV数据
  const headers = ["账号ID", "账号名称", "云服务商", "金额", "账单周期"];

  const rows = accountTableData.value.map(item => [
    item.account_id,
    item.account_name,
    item.cloud_type,
    item.amount,
    item.month
  ]);

  // 转换为CSV字符串
  const csvContent = [
    headers.join(","),
    ...rows.map(row => row.join(","))
  ].join("\n");

  // 创建并触发下载
  const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
  const url = URL.createObjectURL(blob);
  const link = document.createElement("a");
  link.href = url;
  link.setAttribute(
    "download",
    `account-bill-statistics-${new Date().getTime()}.csv`
  );
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

const handleCloudExport = () => {
  if (cloudTableData.value.length === 0) {
    ElMessage.warning("无数据可导出");
    return;
  }

  // 准备CSV数据
  let headers: string[] = [];
  let rows: any[][] = [];

  if (isRangeMode.value) {
    // Cloud bill summary with percentage
    headers = ["云服务商", "金额", "账单周期"];

    rows = cloudTableData.value.map(item => [
      item.name,
      item.amount,
      item.month
    ]);
  } else {
    // 标准导出格式
    headers = ["云服务商", "金额", "账单周期"];

    // 简化组织数据，所有类型都只需要这三个字段
    rows = cloudTableData.value.map(item => [
      item.name,
      item.amount,
      item.month
    ]);
  }

  // 转换为CSV字符串
  const csvContent = [
    headers.join(","),
    ...rows.map(row => row.join(","))
  ].join("\n");

  // 创建并触发下载
  const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
  const url = URL.createObjectURL(blob);
  const link = document.createElement("a");
  link.href = url;
  link.setAttribute(
    "download",
    `cloud-bill-statistics-${new Date().getTime()}.csv`
  );
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

const handleResourceGroupExport = () => {
  // 将数据转换为导出格式
  const exportData = resourceGroupTableData.value.map(item => ({
    资源组名称: item.resource_group_name,
    金额: item.amount,
    账单周期: item.month
  }));

  // 创建CSV内容
  const headers = Object.keys(exportData[0] || {}).join(",") + "\n";
  const rows = exportData.map(item => Object.values(item).join(",")).join("\n");
  const csvContent = headers + rows;

  // 创建并下载CSV文件
  const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
  const link = document.createElement("a");
  const url = URL.createObjectURL(blob);

  // 设置文件名
  const fileName = isRangeMode.value
    ? `资源组账单_${currentDateRange.value?.start_time}_至_${currentDateRange.value?.end_time}.csv`
    : `资源组账单_${currentBillCycle.value}.csv`;

  link.setAttribute("href", url);
  link.setAttribute("download", fileName);
  link.style.visibility = "hidden";
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

// 页面加载时获取数据
onMounted(async () => {
  // 默认加载当前月份的数据
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, "0");
  currentBillCycle.value = `${year}-${month}`;

  // 等待所有数据加载完成
  try {
    await Promise.all([
      loadAccountBillData(),
      loadCloudBillData(),
      loadResourceGroupBillData()
    ]);
  } catch (error) {
    console.error("Failed to load initial data:", error);
    ElMessage.error("加载账单数据失败，请刷新页面重试");
  }
});
</script>

<style scoped>
.statistic-bill-container {
  padding: 8px;
}

.dashboard-header {
  transition: all 0.3s ease;
}

.dashboard-header:hover {
  box-shadow: 0 4px 12px rgb(0 0 0 / 10%);
}

.text-primary {
  color: #409eff;
}

.bill-table-card {
  overflow: hidden;
  transition: all 0.3s ease-in-out;
}

.bill-table-card:hover {
  transform: translateY(-3px);
}

.bill-table-wrapper {
  margin-top: 10px;
}

.bill-table {
  overflow: hidden;
  border-radius: 8px;
}

.bill-table :deep(.el-table__header) {
  font-weight: bold;
}

.bill-table :deep(.el-table__row.even-row) {
  background-color: #fafafa;
}

.bill-table :deep(.el-table__row.odd-row) {
  background-color: #fff;
}

.bill-table :deep(.el-table__row:hover) {
  background-color: #f0f9ff !important;
}

.export-btn {
  transition: all 0.2s ease;
}

.export-btn:hover {
  box-shadow: 0 2px 6px rgb(0 0 0 / 10%);
  transform: translateY(-2px);
}

.chart-container {
  position: relative;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.chart-loading {
  position: absolute;
  inset: 0;
  z-index: 10;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgb(255 255 255 / 70%);
}

.chart-empty {
  position: absolute;
  inset: 0;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgb(255 255 255 / 70%);
}

.table-empty-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  padding: 30px 0;
}

.empty-icon {
  margin-bottom: 16px;
  font-size: 64px;
  color: #dcdfe6;
  animation: pulse 2s infinite ease-in-out;
}

@keyframes pulse {
  0% {
    opacity: 0.6;
  }

  50% {
    opacity: 1;
  }

  100% {
    opacity: 0.6;
  }
}

.empty-text {
  margin-bottom: 8px;
  font-size: 16px;
  color: #606266;
}

.empty-tip {
  font-size: 14px;
  color: #909399;
}
</style>
