<template>
  <div class="main-container">
    <el-card class="mb-4">
      <template #header>
        <div class="flex justify-between items-center">
          <span class="font-bold">账号级别账单统计</span>
          <div class="flex items-center">
            <el-button type="primary" :icon="Download" @click="handleExport">
              导出
            </el-button>
          </div>
        </div>
      </template>
      <div>
        <DateRangeSelector
          @month-change="handleMonthChange"
          @range-change="handleRangeChange"
        />
      </div>
    </el-card>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4">
      <el-card>
        <template #header>
          <div class="font-bold">
            账号分布
          </div>
        </template>
        <BillChart
          chart-type="bar"
          :chart-data="accountChartData"
          height="400px"
        />
      </el-card>

      <el-card>
        <template #header>
          <div class="font-bold">
            云服务商类型分布
          </div>
        </template>
        <BillChart
          chart-type="pie"
          :chart-data="cloudTypeChartData"
          height="400px"
        />
      </el-card>
    </div>

    <el-card>
      <template #header>
        <div class="font-bold">账号级别账单明细列表</div>
      </template>
      <el-table
        :data="tableData"
        border
        stripe
        height="500"
        :loading="loading"
        @sort-change="handleSortChange"
      >
        <el-table-column
          prop="account_id"
          label="账号ID"
          width="100"
          sortable="custom"
        />
        <el-table-column
          prop="account_name"
          label="账号名称"
          min-width="150"
          sortable="custom"
        />
        <el-table-column
          prop="cloud_type"
          label="云服务商类型"
          min-width="120"
          sortable="custom"
        />

        <el-table-column
          prop="amount"
          label="金额"
          min-width="120"
          sortable="custom"
        >
          <template #default="scope">
            <span>{{ formatCurrency(scope.row.amount) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="month"
          label="账单周期"
          min-width="120"
          sortable="custom"
        />
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { Download } from "@element-plus/icons-vue";


import { ElMessage } from "element-plus";
import DateRangeSelector from "../components/DateRangeSelector.vue";
import BillChart from "../components/BillChart.vue";
import {
  getAccountBillStatisticAPI,
  getAccountBillRangeStatisticAPI,
  getCloudTypeAccountBillStatisticAPI,
  type AccountBillStatistic,
  type AccountBillRangeStatistic,
  type CloudTypeAccountBillStatistic
} from "@/api/statistic/bill/account";



// Data
const loading = ref(false);
const isRangeMode = ref(false);
const tableData = ref<(AccountBillStatistic | AccountBillRangeStatistic)[]>([]);
const cloudTypeData = ref<CloudTypeAccountBillStatistic[]>([]);
const currentBillCycle = ref("");
const currentDateRange = ref<{ start_time: string; end_time: string } | null>(
  null
);

// Chart data
const accountChartData = computed(() => {
  if (isRangeMode.value) {
    // 过滤掉金额为0的数据
    const filteredData = (tableData.value as AccountBillRangeStatistic[]).filter(item => item.amount > 0);
    return {
      xAxisData: filteredData.map(item => item.account_name),
      seriesData: filteredData.map(item => item.amount),
      seriesName: "金额"
    };
  } else {
    // 过滤掉金额为0的数据
    const filteredData = (tableData.value as AccountBillStatistic[]).filter(item => item.amount > 0);
    return {
      xAxisData: filteredData.map(item => item.account_name),
      seriesData: filteredData.map(item => item.amount),
      seriesName: "金额"
    };
  }
});

const cloudTypeChartData = computed(() => {
  return {
    pieData: cloudTypeData.value.map(item => ({
      name: item.cloud_name,
      value: item.amount
    }))
  };
});

// Methods
const handleMonthChange = async (month: string) => {
  isRangeMode.value = false;
  currentBillCycle.value = month;
  await fetchAccountBillData();
  await fetchCloudTypeData();
};

const handleRangeChange = async (range: {
  start_time: string;
  end_time: string;
}) => {
  isRangeMode.value = true;
  currentDateRange.value = range;
  await fetchAccountBillRangeData();
};

const fetchAccountBillData = async () => {
  if (!currentBillCycle.value) return;

  loading.value = true;
  try {
    const res = await getAccountBillStatisticAPI({
      month: currentBillCycle.value
    });

    if (res && res.data) {
      // 直接使用API返回的month字段
      tableData.value = (res.data as AccountBillStatistic[]).map(item => ({
        ...item,
        month: item.month || currentBillCycle.value // 使用month字段
      }));
    } else {
      tableData.value = [];
    }
  } catch (error) {
    console.error("Failed to fetch account bill data:", error);
    ElMessage.warning("无数据可导出");
  } finally {
    loading.value = false;
  }
};

const fetchAccountBillRangeData = async () => {
  if (!currentDateRange.value) return;

  loading.value = true;
  try {
    const res = await getAccountBillRangeStatisticAPI({
      start_time: currentDateRange.value.start_time,
      end_time: currentDateRange.value.end_time
    });

    if (res.data) {
      // 在时间范围模式下也显示账期信息
      tableData.value = (res.data as AccountBillRangeStatistic[]).map(item => ({
        ...item,
        // 使用开始时间的年月作为账期
        month: item.start_time ? item.start_time.substring(0, 7).replace("-", "年") + "月" : ""
      }));
    } else {
      tableData.value = [];
    }
  } catch (error) {
    console.error("Failed to fetch account bill range data:", error);
    ElMessage.warning("无数据可导出");
  } finally {
    loading.value = false;
  }
};

const fetchCloudTypeData = async () => {
  if (!currentBillCycle.value) return;

  try {
    const res = await getCloudTypeAccountBillStatisticAPI({
      bill_cycle: currentBillCycle.value
    });

    if (res.data) {
      cloudTypeData.value = res.data;
    } else {
      cloudTypeData.value = [];
    }
  } catch (error) {
    console.error("Failed to fetch cloud type data:", error);
  }
};

const handleSortChange = ({ prop, order }: { prop: string; order: string }) => {
  if (!prop || !order) return;

  tableData.value = [...tableData.value].sort((a: any, b: any) => {
    const valueA = (a as any)[prop];
    const valueB = (b as any)[prop];

    if (typeof valueA === "number" && typeof valueB === "number") {
      return order === "ascending" ? valueA - valueB : valueB - valueA;
    }

    if (valueA === null) return order === "ascending" ? -1 : 1;
    if (valueB === null) return order === "ascending" ? 1 : -1;

    return order === "ascending"
      ? String(valueA).localeCompare(String(valueB))
      : String(valueB).localeCompare(String(valueA));
  });
};

const formatCurrency = (value: number) => {
  return new Intl.NumberFormat("zh-CN", {
    style: "currency",
    currency: "CNY",
    minimumFractionDigits: 2
  }).format(value);
};

const handleExport = () => {
  if (tableData.value.length === 0) {
    ElMessage.warning("无数据可导出");
    return;
  }

  // Prepare CSV data
  const headers = isRangeMode.value
    ? [
        "账号ID",
        "账号名称",
        "云服务商类型",
        "金额",
        "开始时间",
        "结束时间",
        "账单周期"
      ]
    : [
        "账号ID",
        "账号名称",
        "云服务商类型",
        "金额",
        "账单周期"
      ];

  const rows = tableData.value.map(item => {
    if (isRangeMode.value) {
      const rangeItem = item as AccountBillRangeStatistic;
      return [
        rangeItem.account_id,
        rangeItem.account_name,
        rangeItem.cloud_type,
        rangeItem.amount,
        rangeItem.month
      ];
    } else {
      const cycleItem = item as AccountBillStatistic;
      return [
        cycleItem.account_id,
        cycleItem.account_name,
        cycleItem.cloud_type,
        cycleItem.amount,
        cycleItem.month
      ];
    }
  });

  // Convert to CSV string
  const csvContent = [
    headers.join(","),
    ...rows.map(row => row.join(","))
  ].join("\n");

  // Create and trigger download
  const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
  const url = URL.createObjectURL(blob);
  const link = document.createElement("a");
  link.href = url;
  link.setAttribute(
    "download",
    `account-bill-statistics-${new Date().getTime()}.csv`
  );
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

// Initialize data
onMounted(() => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, "0");
  const defaultMonth = `${year}-${month}`;

  currentBillCycle.value = defaultMonth;
  fetchAccountBillData();
  fetchCloudTypeData();
});
</script>

<style scoped>
.main-container {
  padding: 20px;
}
</style>
