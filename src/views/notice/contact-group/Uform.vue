<template>
  <div>
    <el-card shadow="never">
      <el-form
        ref="ruleFormRef"
        label-width="auto"
        style="max-width: 600px"
        :model="newFormInline.form"
        size="large"
        status-icon
        label-position="top"
        @submit.prevent
      >
        <el-form-item
          label="联系人分组名称"
          prop="name"
          :rules="[
            {
              required: true,
              message: '请输入联系人分组名称',
              trigger: 'blur'
            },
            { max: 255, message: '请输入最大255', trigger: 'blur' }
          ]"
        >
          <el-input v-model="newFormInline.form.name" />
        </el-form-item>
        <el-form-item
          label="备注"
          prop="remark"
          :rules="[{ max: 255, message: '请输入最大255', trigger: 'blur' }]"
        >
          <el-input v-model="newFormInline.form.remark" type="textarea" />
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { defineProps, ref } from "vue";
import type { FormInstance } from "element-plus";
import type { ContactGroupForm } from "@/api/notice/contact-group";
export interface FormProps {
  formInline: {
    form: ContactGroupForm;
  };
}
const props = withDefaults(defineProps<FormProps>(), {
  formInline: () => ({ row: undefined, form: undefined })
});

const newFormInline = ref(props.formInline);
const ruleFormRef = ref<FormInstance>();
defineExpose({ ruleFormRef });
</script>
