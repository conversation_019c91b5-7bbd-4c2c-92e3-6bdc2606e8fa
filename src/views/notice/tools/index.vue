<template>
  <el-card shadow="never" class="container">
    <template #header>
      <div class="title">钉钉机器人测试</div>
    </template>
    <el-form
      ref="ruleFormRef"
      label-width="auto"
      :model="form"
      size="large"
      status-icon
      label-position="top"
      @submit.prevent
    >
      <el-form-item
        label="机器人地址"
        prop="robot_url"
        :rules="[
          { required: true, message: '请输入机器人地址', trigger: 'blur' }
        ]"
      >
        <el-input v-model="form.robot_url" />
      </el-form-item>
      <el-form-item
        label="类型"
        prop="message_type"
        :rules="[{ required: true, message: '请选择类型', trigger: 'change' }]"
      >
        <el-select v-model="form.message_type" placeholder="请选择类型">
          <el-option label="文本" value="text" />
          <el-option label="Markdown" value="markdown" />
        </el-select>
      </el-form-item>
      <el-form-item
        label="内容"
        prop="content"
        :rules="[{ required: true, message: '请输入内容', trigger: 'blur' }]"
      >
        <el-input
          v-model="form.content"
          type="textarea"
          :autosize="{ minRows: 14 }"
        />
      </el-form-item>
      <el-form-item label="签名" prop="sign">
        <el-input v-model="form.sign" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="sendDingTalkMessage">发送</el-button>
      </el-form-item>
    </el-form>
  </el-card>
</template>

<script setup lang="ts">
import { dingtalkTestAPI, type DingtalkTestContent } from "@/api/notice/tool";
import { message } from "@/utils/message";
import { ref } from "vue";

const form = ref<DingtalkTestContent>({
  robot_url: "",
  message_type: "text",
  sign: "",
  content: ""
});

const sendDingTalkMessage = async () => {
  const res = await dingtalkTestAPI(form.value);
  if (res.success) {
    message("发送成功", { type: "success" });
  } else {
    message(res.msg, { type: "error" });
  }
};
</script>

<style scoped>
.container {
  align-items: center;
  justify-content: center;
  width: 800px;
  margin: 0 auto;
}

.title {
  padding-bottom: 10px;
  font-size: 28px;
  font-weight: bold;
  text-align: center;
  transition: color 0.3s;
}

.el-form {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  max-width: 500px;
  padding: 0 20px;
  margin: 0 auto;
  margin-top: 20px;
}

.el-form-item {
  width: 100%;
  margin-bottom: 20px;
}

.el-input,
.el-select {
  border: 1px solid #ccc;
  border-radius: 5px;
  transition: border-color 0.3s;
}

.el-input:focus,
.el-select:focus {
  border-color: #007bff;
}

.el-button {
  width: 100%;
  padding: 12px;
  font-size: 16px;
  color: white;
  background-color: #007bff;
  border: none;
  border-radius: 5px;
  transition:
    background-color 0.3s,
    transform 0.2s;
}

.el-button:hover {
  background-color: #0056b3;
  transform: translateY(-2px);
}
</style>
