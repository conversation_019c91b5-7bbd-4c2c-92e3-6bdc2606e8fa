<script setup lang="ts">
import { ref } from "vue";
import { useRole } from "./hook";
import { getPickerShortcuts } from "../../../utils/date";
import { PureTableBar } from "@/components/RePureTableBar";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { ContactTypes } from "@/config/enum";
import Refresh from "@iconify-icons/ep/refresh";

defineOptions({
  name: "NoticeMessage"
});

const formRef = ref();
const tableRef = ref();

const {
  form,
  loading,
  columns,
  dataList,
  pagination,
  onSearch,
  resetForm,
  handleSizeChange,
  handleCurrentChange
} = useRole();
</script>

<template>
  <div class="main">
    <el-card class="search-card">
      <el-form
        ref="formRef"
        :inline="true"
        :model="form"
        class="search-form"
        @submit.prevent
      >
        <el-form-item label="消息类型" prop="message_type">
          <el-select
            v-model="form.message_type"
            filterable
            clearable
            @change="onSearch"
          >
            <el-option
              v-for="(item, index) in ContactTypes"
              :key="index"
              :label="item[1]"
              :value="item[0]"
            >
              {{ item[1] }}
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select
            v-model="form.status"
            filterable
            clearable
            @change="onSearch"
          >
            <el-option :value="0" label="未发送">未发送</el-option>
            <el-option :value="2" label="发送成功">发送成功</el-option>
            <el-option :value="3" label="发送失败">发送失败</el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="联系人" prop="contact">
          <el-input
            v-model="form.contact"
            placeholder="请输入联系人"
            clearable
            @keyup.enter="onSearch"
          />
        </el-form-item>
        <el-form-item label="操作时间" prop="op_time">
          <el-date-picker
            v-model="form.op_time"
            :shortcuts="getPickerShortcuts()"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期时间"
            end-placeholder="结束日期时间"
            @change="onSearch"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            :icon="useRenderIcon('ri:search-line')"
            :loading="loading"
            class="search-button"
            style="
              background: linear-gradient(135deg, #409eff 0%, #4facff 100%);
              border: none;
              box-shadow: 0 4px 12px rgb(64 158 255 / 30%);
              transition: all 0.3s ease;
            "
            @click="onSearch"
          >
            搜索
          </el-button>
          <el-button
            :icon="useRenderIcon(Refresh)"
            class="reset-button"
            style="
              color: #606266;
              border-color: #dcdfe6;
              transition: all 0.3s ease;
            "
            @click="resetForm(formRef)"
          >
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <PureTableBar title="操作日志" :columns="columns" @refresh="onSearch">
      <template v-slot="{ size, dynamicColumns }">
        <pure-table
          ref="tableRef"
          row-key="id"
          align-whole="left"
          table-layout="auto"
          :loading="loading"
          :size="size"
          :minHeight="500"
          :data="dataList"
          :columns="dynamicColumns"
          :pagination="{ ...pagination, size }"
          :header-cell-style="{
            fontWeight: '600'
          }"
          :row-style="{
            cursor: 'pointer',
            transition: 'background-color 0.2s ease'
          }"
          :cell-style="{
            padding: '12px 10px'
          }"
          border
          stripe
          highlight-current-row
          @page-size-change="handleSizeChange"
          @page-current-change="handleCurrentChange"
        >
          <template #empty>
            <el-empty
              description="暂无数据"
              :image-size="120"
              style="padding: 40px 0"
            />
          </template>
        </pure-table>
      </template>
    </PureTableBar>
  </div>
</template>

<style lang="scss">
// 消息通知公共样式
.drawer-content {
  padding: 0;

  .detail-section {
    margin-bottom: 24px;

    .section-title {
      display: flex;
      align-items: center;
      padding: 12px 16px;
      font-weight: 500;
      color: var(--el-text-color-primary);
      background-color: var(--el-fill-color-light);
      border-left: 4px solid var(--el-color-primary);

      i {
        margin-right: 8px;
        font-size: 16px;
        color: var(--el-color-primary);
      }
    }

    .section-content {
      padding: 16px;

      .detail-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 16px;

        &:last-child {
          margin-bottom: 0;
        }

        .label {
          flex-shrink: 0;
          width: 80px;
          font-size: 13px;
          color: var(--el-text-color-secondary);
        }

        .value {
          flex: 1;
          color: var(--el-text-color-primary);
          word-break: break-all;

          &.time-value {
            display: flex;
            align-items: center;
            
            i {
              margin-right: 4px;
              font-size: 14px;
              color: var(--el-text-color-secondary);
            }
          }

          &.link-value {
            display: flex;
            align-items: center;
            
            i {
              margin-right: 4px;
              font-size: 14px;
              color: var(--el-text-color-secondary);
            }

            .el-link {
              font-size: 13px;
            }
          }
        }
      }
    }

    .content-box {
      padding: 16px;
      font-size: 13px;
      line-height: 1.8;
      color: var(--el-text-color-primary);
      word-break: break-all;
      white-space: pre-line;
      background-color: var(--el-fill-color-lighter);
      border-radius: 4px;

      p {
        display: flex;
        align-items: flex-start;
        margin: 0;

        &:not(:last-child) {
          margin-bottom: 8px;
        }
      }
    }
  }
}

.content-item {
  flex-direction: column;

  .label {
    margin-bottom: 8px;
  }
}

.content-box {
  width: 100%;
  padding: 16px;
  font-size: 14px;
  line-height: 1.6;
  color: var(--el-text-color-primary);
  white-space: pre-wrap;
  background-color: var(--el-fill-color-light);
  border-radius: 4px;
}

.log-cell {
  display: flex;
  gap: 8px;
  align-items: center;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 4px 0;
}

.contact-name {
  display: flex;
  gap: 6px;
  align-items: center;
  font-size: 14px;
  
  i {
    font-size: 16px;
    color: var(--el-text-color-secondary);
  }
  
  span {
    font-weight: 500;
    color: var(--el-text-color-primary);
  }
}

.contact-link {
  display: flex;
  gap: 6px;
  align-items: center;
  font-size: 13px;
  
  i {
    font-size: 14px;
    color: var(--el-text-color-secondary);
  }
  
  .el-link {
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.message-status {
  display: flex;
  gap: 8px;
  justify-content: center;
  padding: 4px;

  .status-tag {
    min-width: 70px;
    text-align: center;
    border-radius: 4px;
  }
}

.time-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 8px 4px;
}

.time-row {
  display: flex;
  gap: 8px;
  align-items: center;

  .time-item {
    display: flex;
    gap: 4px;
    align-items: center;
    min-width: 55px;

    i {
      font-size: 14px;
      color: var(--el-text-color-secondary);
    }

    .time-label {
      font-size: 13px;
      color: var(--el-text-color-secondary);
    }
  }

  .time-value {
    font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, monospace;
    font-size: 13px;
    color: var(--el-text-color-regular);
    letter-spacing: 0.5px;
  }
}
</style>

<style scoped lang="scss">
.main {
  padding: 20px;
  border-radius: 12px;
}

.search-card {
  margin-bottom: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 8%);
  transition: box-shadow 0.3s ease;

  &:hover {
    box-shadow: 0 6px 20px rgb(0 0 0 / 12%);
  }
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;

  :deep(.el-form-item) {
    margin-bottom: 0;
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: #606266;
  }

  .el-select,
  .el-input,
  .el-date-picker {
    width: 220px;

    :deep(input) {
      border-color: #e4e7ed;
      border-radius: 8px;

      &:focus {
        border-color: #409eff;
        box-shadow: 0 0 0 1px rgb(64 158 255 / 20%);
      }
    }
  }
}

.search-button,
.reset-button {
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
    transform: translateY(-2px);
  }
}

.search-button {
  color: white;
  background-color: #409eff;
  border-color: #409eff;
}

:deep(.pure-table) {
  overflow: hidden;
  border: 1px solid #e4e7ed;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 4%);
}
</style>
