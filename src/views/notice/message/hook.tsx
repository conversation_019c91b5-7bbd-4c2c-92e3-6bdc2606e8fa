import dayjs from "dayjs";
import type { PaginationProps } from "@pureadmin/table";
import { reactive, ref, onMounted } from "vue";
import { getMessagesAPI, getMessageAPI } from "@/api/notice/message";
import { message } from "@/utils/message";
import { addDrawer } from "@/components/ReDrawer";
import {
  ContactTypeColors,
  ContactTypes,
  MessageStatuColors,
  MessageStatus
} from "@/config/enum";

export function useRole() {
  const form = reactive({
    contact: undefined,
    status: undefined,
    message_type: undefined,
    op_time: undefined
  });
  const dataList = ref([]);
  const loading = ref(true);

  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true,
    pageSizes: [10, 20, 30, 40, 50, 100]
  });
  const columns: TableColumnList = [
    {
      label: "标题",
      prop: "title",
      minWidth: 120,
      showOverflowTooltip: true
    },
    {
      label: "联系信息",
      prop: "contact",
      minWidth: 200,
      cellRenderer: ({ row }) => (
        <div class="contact-info">
          <div class="contact-name">
            <i class="el-icon-user" />
            <span>{row.contact}</span>
          </div>
          {row.link && (
            <div class="contact-link">
              <i class="el-icon-link" />
              <el-link type="primary" href={row.link} target="_blank">
                {row.link}
              </el-link>
            </div>
          )}
        </div>
      )
    },
    {
      label: "消息状态",
      prop: "message_type",
      width: 180,
      align: "center",
      cellRenderer: ({ row }) => (
        <div class="message-status">
          <el-tag
            class="status-tag"
            size="small"
            effect="plain"
            type={ContactTypeColors.get(row.message_type)}
          >
            {ContactTypes.get(row.message_type)}
          </el-tag>
          <el-tag
            class="status-tag"
            size="small"
            effect="plain"
            type={MessageStatuColors.get(row.status)}
          >
            {MessageStatus.get(row.status)}
          </el-tag>
        </div>
      )
    },
    {
      label: "时间信息",
      prop: "send_time",
      minWidth: 200,
      align: "center",
      cellRenderer: ({ row }) => (
        <div class="time-info">
          <div class="time-row">
            <div class="time-item">
              <i class="el-icon-time" />
              <span class="time-label">发送</span>
            </div>
            <span class="time-value">
              {row.send_time
                ? dayjs(row.send_time).format("YYYY-MM-DD HH:mm:ss")
                : "-"}
            </span>
          </div>
          <div class="time-row">
            <div class="time-item">
              <i class="el-icon-date" />
              <span class="time-label">创建</span>
            </div>
            <span class="time-value">
              {dayjs(row.created_at).format("YYYY-MM-DD HH:mm:ss")}
            </span>
          </div>
        </div>
      )
    },
    {
      label: "操作",
      width: 120,
      cellRenderer: ({ row }) => (
        <div class="log-cell operation-cell">
          <el-button type="primary" link onClick={() => handleView(row)}>
            <iconify-icon-online icon="ep:view" class="mr-1" />
            查看
          </el-button>
        </div>
      )
    }
  ];

  function handleSizeChange(val: number) {
    pagination.pageSize = val;
    onSearch();
  }

  function handleCurrentChange(val: number) {
    pagination.currentPage = val;
    onSearch();
  }

  async function onSearch() {
    loading.value = true;
    getMessagesAPI({
      page: pagination.currentPage,
      limit: pagination.pageSize,
      status: form.status,
      message_type: form.message_type,
      op_time: form.op_time,
      contact: form.contact,
      start_time: form.op_time ? form.op_time[0] : undefined,
      end_time: form.op_time ? form.op_time[1] : undefined
    })
      .then(res => {
        if (res.success) {
          dataList.value = Array.isArray(res.data) ? res.data : [res.data];
          pagination.total = res.count;
          pagination.pageSize = pagination.pageSize;
          pagination.currentPage = pagination.currentPage;
        } else {
          dataList.value = [];
          pagination.total = 0;
          pagination.pageSize = pagination.pageSize;
          pagination.currentPage = pagination.currentPage;
        }
      })
      .catch(() => {
        message("获取数据失败", { type: "error" });
      })
      .finally(() => {
        loading.value = false;
      });
  }

  const resetForm = formEl => {
    if (!formEl) return;
    formEl.resetFields();
    onSearch();
  };

  // 查看消息详情
  const handleView = async row => {
    loading.value = true;
    try {
      const res = await getMessageAPI(row.id);
      if (res.success) {
        const detailData = Array.isArray(res.data) ? res.data[0] : res.data;
        addDrawer({
          title: "消息详情",
          size: "50%",
          showFooter: false,
          contentRenderer: () => (
            <div class="drawer-content">
              <div class="detail-section">
                <div class="section-title">
                  <i class="el-icon-info" />
                  <span>基本信息</span>
                </div>
                <div class="section-content">
                  <div class="detail-item">
                    <span class="label">标题</span>
                    <span class="value">{detailData.title}</span>
                  </div>

                  <div class="detail-item">
                    <span class="label">消息类型</span>
                    <div class="value">
                      <el-tag
                        size="small"
                        effect="plain"
                        type={ContactTypeColors.get(detailData.message_type)}
                      >
                        {ContactTypes.get(detailData.message_type)}
                      </el-tag>
                      <el-tag
                        size="small"
                        effect="plain"
                        type={MessageStatuColors.get(detailData.status)}
                        style="margin-left: 8px"
                      >
                        {MessageStatus.get(detailData.status)}
                      </el-tag>
                    </div>
                  </div>

                  <div class="detail-item">
                    <span class="label">发送时间</span>
                    <span class="value time-value">
                      <i class="el-icon-time" />
                      {detailData.send_time
                        ? dayjs(detailData.send_time).format(
                            "YYYY-MM-DD HH:mm:ss"
                          )
                        : "-"}
                    </span>
                  </div>

                  <div class="detail-item">
                    <span class="label">创建时间</span>
                    <span class="value time-value">
                      <i class="el-icon-date" />
                      {dayjs(detailData.created_at).format(
                        "YYYY-MM-DD HH:mm:ss"
                      )}
                    </span>
                  </div>
                </div>
              </div>

              <div class="detail-section">
                <div class="section-title">
                  <i class="el-icon-user" />
                  <span>联系信息</span>
                </div>
                <div class="section-content">
                  <div class="detail-item">
                    <span class="label">联系人</span>
                    <span class="value">{detailData.contact}</span>
                  </div>

                  <div class="detail-item">
                    <span class="label">联系地址</span>
                    <span class="value">{detailData.link || '-'}</span>
                  </div>
                </div>
              </div>

              <div class="detail-section">
                <div class="section-title">
                  <i class="el-icon-message" />
                  <span>消息内容</span>
                </div>
                <div class="section-content">
                  <div class="content-box">
                    {detailData.content.split("\n").map((line, index) => (
                      <p key={index}>{line}</p>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          ),
          draggable: true,
          headerRenderer: ({ titleId, titleClass }) => (
            <div class="flex items-center">
              <h4 id={titleId} class={[titleClass, "flex items-center"]}>
                <iconify-icon-online icon="ep:message" class="mr-2" />
                <span>消息详情</span>
              </h4>
            </div>
          )
        } as any);
      } else {
        message("获取消息详情失败", { type: "error" });
      }
    } catch (error) {
      message("获取消息详情失败", { type: "error" });
    } finally {
      loading.value = false;
    }
  };

  onMounted(() => {
    onSearch();
  });

  return {
    form,
    loading,
    columns,
    dataList,
    pagination,
    onSearch,
    resetForm,
    handleSizeChange,
    handleCurrentChange
  };
}
