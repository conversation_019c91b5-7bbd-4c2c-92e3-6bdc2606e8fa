import dayjs from "dayjs";
import {
  type ContactForm,
  deleteContactAPI,
  getContactsAPI,
  updateContactAPI,
  type Contact,
  addContactAPI
} from "@/api/notice/contact";
import type { PaginationProps } from "@pureadmin/table";
import { reactive, ref, onMounted, h } from "vue";
import { message } from "@/utils/message";
import { addDialog } from "@/components/ReDialog";
import Uform from "./Uform.vue";
import { addDrawer } from "@/components/ReDrawer";
import { ContactTypeColors, ContactTypes } from "@/config/enum";

export function useRole() {
  const form = reactive({
    keyword: undefined
  });
  const dataList = ref<Contact[]>([]);
  const loading = ref(true);

  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true,
    pageSizes: [10, 20, 30, 40, 50, 100]
  });
  const columns: TableColumnList = [
    {
      label: "名称",
      prop: "name",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <div style="display: flex; align-items: center">
          <span style="margin-left: 10px">{row.name}</span>
        </div>
      )
    },
    {
      label: "类型",
      prop: "contact_type",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <div style="display: flex; align-items: center">
          <el-text
            style="margin-left: 10px;"
            type={ContactTypeColors.get(row.contact_type)}
          >
            {ContactTypes.get(row.contact_type)}
          </el-text>
        </div>
      )
    },
    {
      label: "联系地址",
      prop: "link",
      minWidth: 100
    },
    {
      label: "分组",
      prop: "remark",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <div style="white-space: pre-line ; margin: 3px;">
          {row.groups?.map(item => {
            return <el-tag type="warning">{item.name}</el-tag>;
          })}
        </div>
      )
    },
    {
      label: "备注",
      prop: "remark",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <div style="white-space: pre-line ; margin: 3px;">{row.remark}</div>
      )
    },
    {
      label: "创建时间",
      prop: "created_at",
      width: 180,
      cellRenderer: ({ row }) => (
        <div style="display: flex; align-items: center">
          <span style="margin-left: 10px">
            {dayjs(row.created_at).format("YYYY-MM-DD HH:mm:ss")}
          </span>
        </div>
      )
    },
    {
      label: "更新时间  ",
      prop: "updated_at",
      width: 180,
      cellRenderer: ({ row }) => (
        <div style="display: flex; align-items: center">
          <span style="margin-left: 10px">
            {dayjs(row.updated_at).format("YYYY-MM-DD HH:mm:ss")}
          </span>
        </div>
      )
    },
    {
      label: "操作",
      prop: "action",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <div style="display: flex; align-items: center">
          <el-button type="primary" link onClick={() => updateFunc(row)}>
            <iconify-icon-online icon="ep:edit" style="margin:3px;" />
            编辑
          </el-button>
          <el-button type="danger" link onClick={() => deleteFunc(row)}>
            <iconify-icon-online icon="ep:delete" style="margin:3px;" />
            删除
          </el-button>
        </div>
      )
    }
  ];

  const editForm = ref<ContactForm>();
  const childrenRef = ref(null);
  function addFunc() {
    editForm.value = {
      name: "",
      contact_type: "email",
      link: "",
      remark: "",
      group_ids: []
    };
    addDrawer({
      headerRenderer: ({ titleId, titleClass }) => (
        // jsx 语法
        <div class="flex flex-row justify-between">
          <h4 id={titleId} class={titleClass}>
            添加联系人
          </h4>
        </div>
      ),
      contentRenderer: () =>
        h(Uform, {
          formInline: {
            form: editForm.value
          },
          ref: childrenRef
        }),
      beforeSure: done => {
        if (childrenRef.value?.ruleFormRef) {
          childrenRef.value.ruleFormRef.validate((valid: boolean) => {
            if (valid) {
              addContactAPI(editForm.value)
                .then(res => {
                  if (res.success) {
                    message(res.msg, { type: "success" });
                    editForm.value = undefined;
                    onSearch();
                  } else {
                    message(res.msg, { type: "error" });
                  }
                  done();
                })
                .catch(error => {
                  message(error, { type: "error" });
                });
            } else {
              message("请检查表单数据", { type: "error" });
            }
          });
        } else {
          message("请检查表单", { type: "error" });
        }
      }
    });
  }
  function deleteFunc(row: Contact) {
    addDialog({
      title: "",
      width: 500,
      sureBtnLoading: true,
      contentRenderer: () => (
        <p>
          确认删除：
          <b style="color:red">{row.name}</b>
        </p>
      ),
      beforeSure: done => {
        deleteContactAPI(row.id)
          .then(res => {
            if (res.success) {
              done();
              message(res.msg, { type: "success" });
              onSearch();
            } else {
              message(res.msg, { type: "error" });
            }
          })
          .catch(error => {
            message(error, { type: "error" });
          })
          .finally(() => {
            done();
          });
      }
    });
  }

  function updateFunc(row: Contact) {
    editForm.value = {
      name: row.name,
      contact_type: row.contact_type,
      link: row.link,
      remark: row.remark,
      group_ids: row.groups?.map(item => item.id)
    };
    addDrawer({
      headerRenderer: ({ titleId, titleClass }) => (
        <div class="flex flex-row justify-between">
          <h4 id={titleId} class={titleClass}>
            编辑 联系人
            <b>{row.name}</b>
          </h4>
        </div>
      ),
      contentRenderer: () =>
        h(Uform, {
          formInline: {
            form: editForm.value
          },
          ref: childrenRef
        }),
      beforeSure: done => {
        if (childrenRef.value?.ruleFormRef) {
          childrenRef.value.ruleFormRef
            .validate((valid: boolean) => {
              if (valid) {
                updateContactAPI(row.id, editForm.value)
                  .then(res => {
                    if (res.success) {
                      message(res.msg, { type: "success" });
                      done();
                      editForm.value = undefined;
                      onSearch();
                    } else {
                      message(res.msg, { type: "error" });
                    }
                  })
                  .catch(error => {
                    message(error, { type: "error" });
                  });
              } else {
                message("请检查表单数据", { type: "error" });
              }
            })
            .catch(error => {
              message("请检查表单数据:" + error, { type: "error" });
            });
        }
      }
    });
  }
  function handleSizeChange(val: number) {
    pagination.pageSize = val;
    onSearch();
  }

  function handleCurrentChange(val: number) {
    pagination.currentPage = val;
    onSearch();
  }

  async function onSearch() {
    loading.value = true;
    getContactsAPI({
      page: pagination.currentPage,
      limit: pagination.pageSize,
      keyword: form.keyword
    })
      .then(res => {
        if (res.success) {
          dataList.value = res.data;
          pagination.total = res.count;
          pagination.pageSize = pagination.pageSize;
          pagination.currentPage = pagination.currentPage;
        } else {
          dataList.value = [];
          pagination.total = 0;
          pagination.pageSize = pagination.pageSize;
          pagination.currentPage = pagination.currentPage;
        }
      })
      .catch(() => {
        message("请求失败", { type: "error" });
      })
      .finally(() => {
        loading.value = false;
      });
  }

  const resetForm = formEl => {
    if (!formEl) return;
    formEl.resetFields();
    onSearch();
  };

  onMounted(() => {
    onSearch();
  });

  return {
    form,
    loading,
    columns,
    dataList,
    pagination,
    onSearch,
    resetForm,
    handleSizeChange,
    handleCurrentChange,
    addFunc
  };
}
