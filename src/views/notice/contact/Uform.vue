<template>
  <div>
    <el-card shadow="never">
      <el-form
        ref="ruleFormRef"
        label-width="auto"
        style="max-width: 600px"
        :model="newFormInline.form"
        size="large"
        status-icon
        label-position="top"
        @submit.prevent
      >
        <el-form-item
          label="联系人名称"
          prop="name"
          :rules="[
            { required: true, message: '请输入联系人名称', trigger: 'blur' },
            { max: 255, message: '请输入最大255', trigger: 'blur' }
          ]"
        >
          <el-input v-model="newFormInline.form.name" />
        </el-form-item>
        <el-form-item
          label="类型"
          prop="contact_type"
          :rules="[
            { required: true, message: '请选择类型', trigger: 'change' }
          ]"
        >
          <el-select
            v-model="newFormInline.form.contact_type"
            placeholder="请选择类型"
          >
            <el-option label="美柚工作通知" value="meiyou_notice" />
            <el-option label="钉钉机器人通知" value="dingtalk_robot" />
            <el-option label="邮件通知" value="email" />
          </el-select>
        </el-form-item>
        <el-form-item
          label="链接"
          prop="link"
          :rules="[
            { required: true, message: '请输入链接', trigger: 'blur' },
            { max: 255, message: '请输入最大255', trigger: 'blur' }
          ]"
        >
          <el-input v-model="newFormInline.form.link" />
        </el-form-item>
        <el-form-item
          label="备注"
          prop="remark"
          :rules="[{ max: 255, message: '请输入最大255', trigger: 'blur' }]"
        >
          <el-input v-model="newFormInline.form.remark" type="textarea" />
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { defineProps, ref } from "vue";
import type { FormInstance } from "element-plus";
import type { ContactForm } from "@/api/notice/contact";
export interface FormProps {
  formInline: {
    form: ContactForm;
  };
}
const props = withDefaults(defineProps<FormProps>(), {
  formInline: () => ({ row: undefined, form: undefined })
});

const newFormInline = ref(props.formInline);
const ruleFormRef = ref<FormInstance>();
defineExpose({ ruleFormRef });
</script>
