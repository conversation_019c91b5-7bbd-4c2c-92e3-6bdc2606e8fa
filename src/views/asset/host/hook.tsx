import dayjs from "dayjs";
import {
  type CloudAccount,
  getAllCloudAccountsAPI
} from "@/api/asset/cloud-account";
import type { PaginationProps } from "@pureadmin/table";
import { reactive, ref, onMounted, h } from "vue";
import { message } from "@/utils/message";
import {
  ChargeTypeColors,
  ChargeTypes,
  HostStatus,
  HostStatusColors,
  HostTypeColors,
  HostTypes
} from "@/config/enum";
import { addDrawer } from "@/components/ReDrawer";
import { addDialog } from "@/components/ReDialog";
import Uform from "./Uform.vue";
import TagForm from "./TagForm.vue";
import HostDetails from "./HostDetails.vue";
import {
  getHostsAPI,
  type TagHostsFrom,
  type Host,
  type BatSetMonitorForm,
  tagHostsAPI,
  type HostForm,
  addHostAPI,
  updateHostAPI,
  deleteHostAPI,
  batSetMonitorAPI
} from "@/api/asset/host";
import { getAllTagsAPI, type Tag } from "@/api/asset/tag";
import { getAllDatacentersAPI, type Datacenter } from "@/api/asset/datacenter";
import SetMonitorForm from "./SetMonitorForm.vue";
import AccountMouthlyBill from "../bill/AccountMouthlyBill.vue";
import {
  getAllResourceGroupesAPI,
  type ResourceGroup
} from "@/api/asset/resource-group";
import HostChangeLog from "./ChangeLog.vue";
import LoadBalancer from "./Loadbancer.vue";
import Asset from "./Asset.vue";
import type { Loadbalancer } from "@/api/asset/loadbalancer";
import HostMetrics from "@/views/monitor/prometheus/host/Metrics.vue";
export function useRole() {
  const form = reactive({
    ip: undefined,
    host_type: undefined,
    keyword: undefined,
    tag_ids: undefined,
    account_id: undefined,
    datacenter_id: undefined,
    status: undefined,
    ping_monitor: undefined,
    n9e_monitor: undefined,
    is_amd: undefined,
    resource_group_id: undefined
  });
  const dataList = ref<Host[]>([]);
  const loading = ref(true);
  const tags = ref<Tag[]>([]);
  const editForm = ref<HostForm>();
  const childrenRef = ref(null);
  const batTagForm = ref<TagHostsFrom>();
  const accounts = ref<CloudAccount[]>([]);
  const resourceGroupes = ref<ResourceGroup[]>([]);
  const datacenters = ref<Datacenter[]>([]);
  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 5,
    currentPage: 1,
    background: true,
    pageSizes: [5, 10, 20, 30, 40, 50, 100]
  });
  const changeLogColumns: TableColumnList = [
    {
      label: "时间",
      prop: "created_at",
      width: 200,
      cellRenderer: ({ row }) => (
        <div>{dayjs(row.created_at).format("YYYY-MM-DD HH:mm:ss")}</div>
      )
    },
    {
      label: "操作人",
      prop: "operator"
    },
    {
      label: "操作内容",
      prop: "content",
      minWidth: 200,
      cellRenderer: ({ row }) => (
        <div style={{ whiteSpace: "pre-line" }}>{row.content}</div>
      )
    }
  ];
  const columns: TableColumnList = [
    {
      type: "selection",
      align: "center",
      width: 50
    },
    {
      label: "基本信息",
      prop: "basic",
      minWidth: 280,
      cellRenderer: ({ row }) => (
        <div class="flex flex-col gap-2">
          {/* 主机名称 */}
          <div class="flex items-center gap-2">
            <iconify-icon-online
              icon="mdi:server-network"
              class="text-primary text-lg"
            />
            <el-text
              onClick={() => openHostDetailsDrawer(row)}
              class="cursor-pointer hover:text-primary hover:underline font-medium"
            >
              {row.name}
            </el-text>
          </div>

          {/* SN信息 */}
          <el-tooltip content={`点击复制SN：${row.sn}`} placement="top">
            <div
              class="flex items-center gap-2 cursor-pointer hover:text-primary group"
              onClick={() => {
                navigator.clipboard.writeText(row.sn);
                message("已复制SN到剪贴板", { type: "success" });
              }}
            >
              <iconify-icon-online
                icon="mdi:content-copy"
                class="text-gray-400 group-hover:text-primary transition-colors"
              />
              <span class="font-mono text-gray-500 group-hover:text-primary transition-colors">
                SN: {row.sn}
              </span>
            </div>
          </el-tooltip>

          {/* 资源组信息 */}
          {row.resource_group_id && (
            <div class="flex items-center gap-2">
              <iconify-icon-online
                icon="mdi:folder-network"
                class="text-warning text-lg"
              />
              <el-text size="small" type="warning" class="font-medium">
                资源组: {row.resource_group_name}
              </el-text>
            </div>
          )}

          {/* 标签信息 */}
          {row.tags && row.tags.length > 0 && (
            <div class="flex flex-col gap-1.5">
              <div class="flex items-center gap-2">
                <iconify-icon-online
                  icon="mdi:tag-multiple"
                  class="text-success text-lg"
                />
                <span class="text-sm text-gray-500">标签:</span>
              </div>
              <div class="flex flex-wrap gap-1.5 ml-6">
                {row.tags.map((tag, index) => (
                  <el-tag
                    key={index}
                    size="small"
                    type="success"
                    effect="light"
                    class="flex items-center gap-1 px-2 py-1 rounded-md shadow-sm hover:shadow transition-all"
                  >
                    {tag.key}={tag.value}
                  </el-tag>
                ))}
              </div>
            </div>
          )}

          {/* 云平台控制台链接 */}
          {(row.host_type === 2 || row.host_type === 3) && (
            <div class="flex items-center gap-2 mt-1">
              {row.host_type === 3 && (
                <el-link
                  href={`https://console.huaweicloud.com/ecm/?region=${row.region_id}&locale=zh-cn#/ecs/manager/vmList/vmDetail/basicinfo?instanceId=${row.sn}`}
                  target="_blank"
                  type="primary"
                  class="text-xs flex items-center gap-1.5 hover:shadow-sm transition-all"
                >
                  <iconify-icon-online icon="cbi:huawei" class="text-lg" />
                  华为云控制台
                </el-link>
              )}
              {row.host_type === 2 && (
                <el-link
                  href={`https://ecs.console.aliyun.com/server/${row.sn}/detail?regionId=${row.region_id}`}
                  target="_blank"
                  type="primary"
                  class="text-xs flex items-center gap-1.5 hover:shadow-sm transition-all"
                >
                  <iconify-icon-online
                    icon="simple-icons:alibabacloud"
                    class="text-lg"
                  />
                  阿里云控制台
                </el-link>
              )}
            </div>
          )}
        </div>
      )
    },
    {
      label: "网络信息",
      prop: "network",
      width: 180,
      cellRenderer: ({ row }) => (
        <div class="flex flex-col gap-2">
          <div class="flex items-center gap-2">
            <iconify-icon-online
              icon="mdi:ip-network"
              class="text-primary text-lg"
            />
            <span class="font-mono">{row.ip}</span>
          </div>
          {row.public_ip && (
            <div class="flex items-center gap-2">
              <iconify-icon-online
                icon="mdi:earth"
                class="text-success text-lg"
              />
              <span class="font-mono">{row.public_ip}</span>
            </div>
          )}
        </div>
      )
    },
    {
      label: "状态信息",
      prop: "status",
      width: 180,
      cellRenderer: ({ row }) => (
        <div class="flex flex-col gap-1.5">
          {/* 主机类型和付费类型 */}
          <div class="flex items-center gap-1.5">
            <el-tag
              size="small"
              type={getTagType(HostTypeColors.get(row.host_type))}
              effect="light"
              class="!h-6 !px-2"
            >
              <div class="flex items-center gap-1">
                <iconify-icon-online
                  icon="mdi:server-network"
                  class="text-sm"
                />
                <span class="text-xs leading-none">
                  {HostTypes.get(row.host_type)}
                </span>
              </div>
            </el-tag>

            {row.charge_type > 0 && ChargeTypes.get(row.charge_type) && (
              <el-tag
                size="small"
                type={getTagType(ChargeTypeColors.get(row.charge_type))}
                effect="light"
                class="!h-6 !px-2"
              >
                <div class="flex items-center gap-1">
                  <iconify-icon-online
                    icon="mdi:currency-cny"
                    class="text-sm"
                  />
                  <span class="text-xs leading-none">
                    {ChargeTypes.get(row.charge_type)}
                  </span>
                </div>
              </el-tag>
            )}
          </div>

          {/* 运行状态 */}
          <div class="flex items-center">
            <el-tag
              size="small"
              type={getTagType(HostStatusColors.get(row.status))}
              effect="plain"
              class="!h-6 !px-2"
            >
              <div class="flex items-center gap-1">
                <iconify-icon-online
                  icon={getStatusIcon(row.status)}
                  class={`text-sm ${getStatusIconColor(row.status)}`}
                />
                <span class="text-xs leading-none">
                  {HostStatus.get(row.status)}
                </span>
              </div>
            </el-tag>
          </div>

          {/* 监控状态组 */}
          <div class="flex items-center gap-1.5">
            <div class="flex gap-1">
              <el-tag
                size="small"
                type={row.ping_monitor ? "success" : "info"}
                effect="plain"
                class="!h-6 !px-2"
              >
                <div class="flex items-center gap-1">
                  <iconify-icon-online
                    icon={row.ping_monitor ? "mdi:signal" : "mdi:signal-off"}
                    class="text-sm"
                  />
                  <span class="text-xs leading-none">
                    {row.ping_monitor ? "PING" : "未监控"}
                  </span>
                </div>
              </el-tag>
            </div>

            <el-tag
              size="small"
              type={row.n9e_monitor ? "success" : "danger"}
              effect="plain"
              class="!h-6 !px-2"
            >
              <div class="flex items-center gap-1">
                <iconify-icon-online
                  icon={row.n9e_monitor ? "mdi:eye" : "mdi:eye-off"}
                  class="text-sm"
                />
                <span class="text-xs leading-none">
                  {row.n9e_monitor ? "夜莺" : "夜莺未监控"}
                </span>
                {row.n9e_monitor_time && (
                  <el-tooltip
                    content={`同步时间: ${dayjs(row.n9e_monitor_time).format("YYYY-MM-DD HH:mm:ss")}`}
                    placement="top"
                  >
                    <iconify-icon-online
                      icon="mdi:clock-outline"
                      class="ml-0.5 text-gray-400 text-sm"
                    />
                  </el-tooltip>
                )}
              </div>
            </el-tag>
          </div>
        </div>
      )
    },
    {
      label: "配置信息",
      prop: "config",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <div class="flex flex-col gap-2">
          {/* 型号信息 */}
          <div class="flex items-center gap-2">
            <div class="flex items-center gap-1">
              <iconify-icon-online
                icon="mdi:server"
                class="text-primary text-sm"
              />
              <span class="text-sm font-medium truncate" title={row.model}>
                {row.model}
              </span>
            </div>
            {row.is_amd && (
              <el-tag
                size="small"
                type="warning"
                effect="light"
                class="!h-6 !px-2"
              >
                <span class="text-xs leading-none">AMD</span>
              </el-tag>
            )}
          </div>

          {/* 操作系统 */}
          <div class="flex items-center gap-1">
            <iconify-icon-online
              icon={
                row.os_type === "Linux" ? "mdi:linux" : "mdi:microsoft-windows"
              }
              class={`text-sm ${row.os_type === "Linux" ? "text-success" : "text-primary"}`}
            />
            <span class="text-sm text-gray-700">
              {row.os_type}: <span class="font-medium">{row.os}</span>
            </span>
          </div>

          {/* 硬件配置 */}
          <div class="flex flex-wrap gap-1.5">
            <el-tag size="small" type="info" effect="plain" class="!h-6 !px-2">
              <div class="flex items-center gap-1">
                <iconify-icon-online icon="mdi:chip" class="text-sm" />
                <span class="text-xs leading-none">CPU {row.cpu_thread}核</span>
              </div>
            </el-tag>
            <el-tag size="small" type="info" effect="plain" class="!h-6 !px-2">
              <div class="flex items-center gap-1">
                <iconify-icon-online icon="mdi:memory" class="text-sm" />
                <span class="text-xs leading-none">
                  内存 {row.memory / 1024}G
                </span>
              </div>
            </el-tag>
            {row.gpu_amount > 0 && (
              <el-tag
                size="small"
                type="warning"
                effect="plain"
                class="!h-6 !px-2"
              >
                <div class="flex items-center gap-1">
                  <iconify-icon-online icon="mdi:gpu" class="text-sm" />
                  <span class="text-xs leading-none">
                    GPU <b>{row.gpu_amount}</b>张
                  </span>
                </div>
              </el-tag>
            )}
          </div>
          {row.gpu_spec && (
            <el-tag
              size="small"
              type="warning"
              effect="light"
              class="!h-6 !px-2 w-fit"
            >
              <div class="flex items-center gap-1">
                <iconify-icon-online icon="mdi:chip-default" class="text-sm" />
                <span class="text-xs leading-none">
                  GPU规格: {row.gpu_spec}
                </span>
              </div>
            </el-tag>
          )}
        </div>
      )
    },
    {
      label: "位置信息",
      prop: "location",
      minWidth: 180,
      cellRenderer: ({ row }) => (
        <div class="flex flex-col gap-2">
          {/* 账号信息 */}
          {row.account && (
            <div class="flex items-center gap-2">
              <iconify-icon-online icon="mdi:account" class="text-primary" />
              <span>账号: {row.account}</span>
            </div>
          )}

          {/* 数据中心信息 */}
          <div class="flex flex-col gap-1">
            <div class="flex items-center gap-2">
              <iconify-icon-online icon="mdi:database" class="text-success" />
              <span>数据中心: {row.datacenter}</span>
            </div>
            {row.cabinet && (
              <div class="flex items-center gap-2">
                <iconify-icon-online icon="uil:server" class="text-gray-500" />
                <span>机柜号: {row.cabinet}</span>
              </div>
            )}
            {row.position && (
              <div class="flex items-center gap-2">
                <iconify-icon-online icon="uil:server" class="text-gray-500" />
                <span>位置: {row.position}</span>
              </div>
            )}
          </div>
        </div>
      )
    },
    {
      label: "操作",
      prop: "action",
      minWidth: 200,
      cellRenderer: ({ row }) => (
        <div class="flex flex-col gap-3 p-2">
          {/* 监控类操作 */}
          <div class="flex flex-col gap-1 bg-gray-50 rounded-md p-2">
            <div class="text-xs text-gray-500 font-medium border-b border-gray-200 pb-1 mb-1">
              监控
            </div>
            <div class="flex flex-wrap gap-2 ml-1">
              <el-link
                href={`https://grafana-monitor.meiyou.com/d/G0qaY74Iz/ye-ying-zhu-ji-jian-kong?orgId=1&refresh=1m&var-datasource=n9e&var-ident=${row.ip}`}
                target="_blank"
                type="success"
                class="flex items-center gap-1 text-sm hover:scale-105 transition-transform"
              >
                <iconify-icon-online icon="eos-icons:monitoring" />
                实时监控
              </el-link>
              <el-link
                type="success"
                class="flex items-center gap-1 text-sm hover:scale-105 transition-transform"
                onClick={() => openHostMetricsDrawer(row)}
              >
                <iconify-icon-online icon="mdi:chart-line" />
                统计报表
              </el-link>
            </div>
          </div>

          {/* 查看类操作 */}
          <div class="flex flex-col gap-1 bg-gray-50 rounded-md p-2">
            <div class="text-xs text-gray-500 font-medium border-b border-gray-200 pb-1 mb-1">
              查看
            </div>
            <div class="flex flex-wrap gap-2 ml-1">
              <el-link
                type="info"
                class="flex items-center gap-1 text-sm hover:scale-105 transition-transform"
                onClick={() => openHostDetailsDrawer(row)}
              >
                <iconify-icon-online icon="ep:view" />
                详情
              </el-link>
              <el-link
                type="info"
                class="flex items-center gap-1 text-sm hover:scale-105 transition-transform"
                onClick={() => openHostChangeLogDrawer(row)}
              >
                <iconify-icon-online icon="mdi:history" />
                变更记录
              </el-link>
              {row.host_type > 1 && (
                <el-link
                  type="info"
                  class="flex items-center gap-1 text-sm hover:scale-105 transition-transform"
                  onClick={() => openLoadBalancerDrawer(row)}
                >
                  <iconify-icon-online icon="mdi:server-network" />
                  负载均衡
                </el-link>
              )}
              <el-link
                type="info"
                class="flex items-center gap-1 text-sm hover:scale-105 transition-transform"
                onClick={() => openHostAssetsDrawer(row)}
              >
                <iconify-icon-online icon="mdi:server-network" />
                关联资产
              </el-link>
            </div>
          </div>

          {/* 管理类操作 */}
          <div class="flex flex-col gap-1 bg-gray-50 rounded-md p-2">
            <div class="text-xs text-gray-500 font-medium border-b border-gray-200 pb-1 mb-1">
              管理
            </div>
            <div class="flex flex-wrap gap-2 ml-1">
              {row.host_type < 2 ? (
                <div class="flex items-center gap-2">
                  <el-link
                    type="primary"
                    class="flex items-center gap-1 text-sm hover:scale-105 transition-transform"
                    onClick={() => updateFunc(row)}
                  >
                    <iconify-icon-online icon="ep:edit" />
                    编辑
                  </el-link>
                  <el-link
                    type="danger"
                    class="flex items-center gap-1 text-sm hover:scale-105 transition-transform"
                    onClick={() => deleteFunc(row)}
                  >
                    <iconify-icon-online icon="ep:delete" />
                    删除
                  </el-link>
                </div>
              ) : (
                <el-link
                  type="warning"
                  class="flex items-center gap-1 text-sm hover:scale-105 transition-transform"
                  onClick={() =>
                    handleHostMonthlyBill(
                      row.account_id,
                      row.name + "(" + row.ip + ")",
                      row.sn
                    )
                  }
                >
                  <iconify-icon-online icon="ri:bill-line" />
                  账单
                </el-link>
              )}
            </div>
          </div>
        </div>
      )
    }
  ];

  const handleHostMonthlyBill = (
    account_id: number,
    account_name: string,
    resource_id: string
  ) => {
    addDialog({
      title: "主机月度账单",
      hideFooter: true,
      draggable: true,
      contentRenderer: () =>
        h(AccountMouthlyBill, {
          account_id: account_id,
          account_name: account_name,
          resource_id: resource_id
        })
    });
  };

  const openHostDetailsDrawer = (host: Host) => {
    addDrawer({
      title: `主机详情 - ${host.name}`,
      size: "50%",
      contentRenderer: () => h(HostDetails, { host }),
      hideFooter: true
    });
  };
  function openHostChangeLogDrawer(host: Host) {
    addDrawer({
      title: `主机变更记录 - ${host.name}`,
      size: "80%",
      contentRenderer: () => h(HostChangeLog, { host_id: host.id }),
      hideFooter: true
    });
  }
  function openHostMetricsDrawer(host: Host) {
    addDrawer({
      title: `每日监控统计 - ${host.name}`,
      size: "80%",
      contentRenderer: () => h(HostMetrics, { ip: host.ip }),
      hideFooter: true
    });
  }

  function openLoadBalancerDrawer(host: Host) {
    addDrawer({
      title: `主机关联负载均衡 - ${host.name}`,
      size: "80%",
      contentRenderer: () => h(LoadBalancer, { host_id: host.id }),
      hideFooter: true
    });
  }
  function openHostAssetsDrawer(host: Host) {
    addDrawer({
      title: `主机关联资产 - ${host.name}`,
      size: "80%",
      contentRenderer: () => h(Asset, { host_id: host.id }),
      hideFooter: true
    });
  }

  function batchTag(rows: Host[]) {
    if (rows.length === 0) {
      message("请选择要操作的主机 ", { type: "warning" });
      return;
    }
    batTagForm.value = {
      tag_ids: [],
      host_ids: rows.map(row => row.id),
      op: "append"
    };
    addDrawer({
      headerRenderer: ({ titleId, titleClass }) => (
        <div class="flex flex-row justify-between">
          <h4 id={titleId} class={titleClass}>
            批量设置标签
          </h4>
        </div>
      ),
      contentRenderer: () =>
        h(TagForm, {
          formInline: {
            form: batTagForm.value,
            rows: rows,
            syncTagsFunc: syncAllTags,
            tags: tags.value
          },
          ref: childrenRef
        }),
      beforeSure: done => {
        if (childrenRef.value.ruleFormRef) {
          childrenRef.value.ruleFormRef
            .validate((valid: boolean) => {
              if (valid) {
                tagHostsAPI(batTagForm.value)
                  .then(res => {
                    if (res.success) {
                      message(res.msg, { type: "success" });
                      done();
                      onSearch();
                      editForm.value = undefined;
                    } else {
                      message(res.msg, { type: "error" });
                    }
                  })
                  .catch(error => {
                    message(error, { type: "error" });
                  });
              } else {
                message("请检查表单数据", { type: "error" });
              }
            })
            .catch(error => {
              message(error, { type: "error" });
            });
        }
      }
    });
  }

  function updateFunc(row: Host) {
    editForm.value = {
      name: row.name,
      sn: row.sn,
      ip: row.ip,
      public_ip: row.public_ip,
      host_type: row.host_type,
      datacenter_id: row.datacenter_id,
      cabinet: row.cabinet,
      position: row.position,
      remark: row.remark,
      status: row.status,
      remote_port: row.remote_port,
      ping_monitor: row.ping_monitor,
      cpu_thread: row.cpu_thread,
      gpu_amount: row.gpu_amount,
      gpu_spec: row.gpu_spec,
      memory: row.memory,
      model: row.model,
      os: row.os,
      os_type: row.os_type
    };
    addDrawer({
      size: "50%",
      headerRenderer: ({ titleId, titleClass }) => (
        <div class="flex flex-row justify-between">
          <h4 id={titleId} class={titleClass}>
            编辑主机
            <b>{row.name}</b>
          </h4>
        </div>
      ),
      contentRenderer: () =>
        h(Uform, {
          formInline: {
            form: editForm.value
          },
          ref: childrenRef
        }),
      beforeSure: done => {
        if (childrenRef.value.ruleFormRef) {
          childrenRef.value.ruleFormRef
            .validate()
            .then(() => {
              updateHostAPI(row.id, editForm.value)
                .then(res => {
                  if (res.success) {
                    message(res.msg, { type: "success" });
                    done();
                    onSearch();
                  } else {
                    message(res.msg, { type: "error" });
                  }
                })
                .catch(error => {
                  message(error, { type: "error" });
                });
            })
            .catch(() => {
              message("请检查表单数据", { type: "error" });
            });
        } else {
          message("请检查表单数", { type: "error" });
        }
      }
    });
  }

  function addFunc() {
    editForm.value = {
      name: "",
      sn: "",
      ip: "",
      public_ip: "",
      host_type: 1,
      datacenter_id: undefined,
      cabinet: "",
      position: "",
      remark: "",
      status: 1,
      remote_port: 22,
      ping_monitor: true,
      cpu_thread: 1,
      gpu_amount: 0,
      gpu_spec: "",
      memory: 1024,
      model: "",
      os: "",
      os_type: "Linux"
    };
    addDrawer({
      size: "50%",
      headerRenderer: ({ titleId, titleClass }) => (
        <div class="flex flex-row justify-between">
          <h4 id={titleId} class={titleClass}>
            添加主机
          </h4>
        </div>
      ),
      contentRenderer: () =>
        h(Uform, {
          formInline: {
            form: editForm.value
          },
          ref: childrenRef
        }),
      beforeSure: done => {
        if (childrenRef.value.ruleFormRef) {
          childrenRef.value.ruleFormRef
            .validate((valid: boolean) => {
              if (valid) {
                addHostAPI(editForm.value)
                  .then(res => {
                    if (res.success) {
                      message(res.msg, { type: "success" });
                      done();
                      onSearch();
                      editForm.value = undefined;
                    } else {
                      message(res.msg, { type: "error" });
                    }
                  })
                  .catch(error => {
                    message(error, { type: "error" });
                  });
              } else {
                message("请检查表单数据", { type: "error" });
              }
            })
            .catch(error => {
              message("请检查表单数据:" + error, { type: "error" });
            });
        }
      }
    });
  }

  function deleteFunc(row: Host) {
    addDialog({
      title: "",
      width: 500,
      sureBtnLoading: true,
      contentRenderer: () => (
        <p>
          确认删除：
          <el-text type="primary">
            {row.name} ({row.ip} {row.public_ip})
          </el-text>
        </p>
      ),
      beforeSure: done => {
        deleteHostAPI(row.id)
          .then(res => {
            if (res.success) {
              message(res.msg, { type: "success" });
              onSearch();
            } else {
              message(res.msg, { type: "error" });
            }
          })
          .catch(error => {
            message("请求失败" + error, { type: "error" });
          })
          .finally(() => {
            done();
          });
      }
    });
  }

  function syncAllTags() {
    getAllTagsAPI()
      .then(res => {
        if (res.success) {
          tags.value = res.data;
        } else {
          message(res.msg, { type: "error" });
        }
      })
      .catch(error => {
        message(error, { type: "error" });
      });
  }
  function syncAllResourceGroupes() {
    getAllResourceGroupesAPI()
      .then(res => {
        if (res.success) {
          resourceGroupes.value = res.data;
        } else {
          message(res.msg, { type: "error" });
        }
      })
      .catch(error => {
        message(error, { type: "error" });
      });
  }
  function syncAllDatacenters() {
    getAllDatacentersAPI()
      .then(res => {
        if (res.success) {
          datacenters.value = res.data;
        } else {
          message(res.msg, { type: "error" });
        }
      })
      .catch(error => {
        message(error, { type: "error" });
      });
  }
  function syncAllAccounts() {
    getAllCloudAccountsAPI()
      .then(res => {
        if (res.success) {
          accounts.value = res.data;
        } else {
          message(res.msg, { type: "error" });
        }
      })
      .catch(error => {
        message(error, { type: "error" });
      });
  }

  function handleSizeChange(val: number) {
    pagination.pageSize = val;
    onSearch();
  }

  function handleCurrentChange(val: number) {
    pagination.currentPage = val;
    onSearch();
  }

  async function onSearch() {
    loading.value = true;
    let tagIds: string | undefined = undefined;
    if (form.tag_ids) {
      tagIds = form?.tag_ids.join(",");
    } else {
      tagIds = undefined;
    }
    getHostsAPI({
      page: pagination.currentPage,
      limit: pagination.pageSize,
      keyword: form.keyword,
      status: form.status,
      ip: form.ip,
      tag_ids: tagIds,
      account_id: form.account_id,
      datacenter_id: form.datacenter_id,
      host_type: form.host_type,
      resource_group_id: form.resource_group_id,
      ping_monitor: form.ping_monitor,
      n9e_monitor: form.n9e_monitor,
      is_amd: form.is_amd
    })
      .then(res => {
        if (res.success) {
          dataList.value = res.data;
          pagination.total = res.count;
          pagination.pageSize = pagination.pageSize;
          pagination.currentPage = pagination.currentPage;
        } else {
          dataList.value = [];
          pagination.total = 0;
          pagination.pageSize = pagination.pageSize;
          pagination.currentPage = pagination.currentPage;
        }
      })
      .catch(() => {
        message("请求失败", { type: "error" });
      })
      .finally(() => {
        loading.value = false;
      });
  }

  const resetForm = formEl => {
    if (!formEl) return;
    formEl.resetFields();
    form.datacenter_id = undefined;
    form.tag_ids = undefined;
    form.account_id = undefined;
    form.ip = undefined;
    form.resource_group_id = undefined;
    onSearch();
  };

  const batSetMonitorForm = ref<BatSetMonitorForm>({
    hosts_ids: [],
    monitor: true
  });
  const setMonitoring = async (hosts: Host[]) => {
    batSetMonitorForm.value.hosts_ids = hosts.map(host => host.id);
    addDrawer({
      headerRenderer: ({ titleId, titleClass }) => (
        <div class="flex flex-row justify-between">
          <h4 id={titleId} class={titleClass}>
            设置监控
          </h4>
        </div>
      ),
      contentRenderer: () =>
        h(SetMonitorForm, {
          formInline: {
            form: batSetMonitorForm.value,
            rows: hosts
          }
        }),
      beforeSure: async done => {
        batSetMonitorAPI(batSetMonitorForm.value)
          .then(res => {
            if (res.success) {
              message(res.msg, { type: "success" });
              done();
              onSearch();
            } else {
              message(res.msg, { type: "error" });
            }
          })
          .catch(error => {
            message("表单验证失败: " + error.message, { type: "error" });
          });
      }
    });
  };

  onMounted(() => {
    onSearch();
    syncAllAccounts();
    syncAllTags();
    syncAllDatacenters();
    syncAllResourceGroupes();
  });
  return {
    form,
    loading,
    columns,
    changeLogColumns,
    dataList,
    pagination,
    onSearch,
    resetForm,
    handleSizeChange,
    handleCurrentChange,
    addFunc,
    tags,
    syncAllTags,
    accounts,
    syncAllAccounts,
    batchTag,
    datacenters,
    syncAllDatacenters,
    setMonitoring,
    resourceGroupes,
    syncAllResourceGroupes,
    openHostDetailsDrawer
  };
}
const getLoadBalancerLink = (row: Loadbalancer) => {
  switch (row.loadbalancer_type) {
    case "elb":
      return `https://console.huaweicloud.com/vpc/?region=${row.region_id}&locale=zh-cn#/elb/detail/basicInfo?ulbId=${row.loadbalancer_id}`;
    case "slb":
      return `https://slb.console.aliyun.com/slb/${row.region_id}/slbs/${row.loadbalancer_id}`;
    default:
      return "#";
  }
};

const getStatusText = (status: string) => {
  switch (status) {
    case "running":
      return { text: "运行中", type: "success" };
    case "active":
    case "Active":
      return { text: "活跃", type: "success" };
    case "ONLINE":
      return { text: "在线", type: "success" };
    case "inactive":
      return { text: "已停止", type: "danger" };
    case "stopped":
      return { text: "已停止", type: "danger" };
    case "error":
      return { text: "错误", type: "danger" };
    default:
      return { text: "未知", type: "info" };
  }
};
export const loadbalancerColumns: TableColumnList = [
  {
    label: "负载均衡ID",
    prop: "loadbalancer_id",
    minWidth: 70,
    cellRenderer: ({ row }) => (
      <div>
        <p>
          <el-text type="info" style={{ marginRight: "10px" }}>
            {row.name}
          </el-text>
        </p>
        <p style={{ marginTop: "10px" }}>
          <el-link
            href={getLoadBalancerLink(row)}
            target="_blank"
            type="primary"
          >
            {row.loadbalancer_id}
          </el-link>
        </p>
      </div>
    )
  },
  {
    label: "地址",
    prop: "host",
    minWidth: 120
  },
  {
    label: "类型",
    prop: "loadbalancer_type",
    minWidth: 100,
    cellRenderer: ({ row }) => (
      <div>
        <p>
          <el-text type="info" style={{ marginRight: "10px" }}>
            {row.loadbalancer_type}
          </el-text>
        </p>
        {row.spec !== "" && (
          <p style={{ marginTop: "10px" }}>
            <el-text type="info" style={{ marginRight: "10px" }}>
              {row.spec}
            </el-text>
          </p>
        )}
      </div>
    )
  },
  {
    label: "账户",
    prop: "account",
    minWidth: 100
  },
  {
    label: "数据中心",
    prop: "datacenter",
    minWidth: 100
  },
  {
    label: "状态",
    prop: "status",
    minWidth: 80,
    cellRenderer: ({ row }) => {
      const { text, type } = getStatusText(row.status);
      return (
        <div style="white-space: pre-line;">
          <el-text type={type} size="large" style="margin: 5px">
            {text}
          </el-text>
          <p style={{ margin: 0 }}>原始状态: {row.status}</p>
        </div>
      );
    }
  }
];

// 辅助函数
const getTagType = (color: string | undefined) => {
  switch (color) {
    case "success":
      return "success";
    case "warning":
      return "warning";
    case "danger":
      return "danger";
    default:
      return "info";
  }
};

const getStatusIcon = (status: number) => {
  switch (status) {
    case 1: // 运行中
      return "mdi:play-circle";
    case 2: // 已停止
      return "mdi:stop-circle";
    case 3: // 已过期
      return "mdi:clock-alert";
    default:
      return "mdi:help-circle";
  }
};

const getStatusIconColor = (status: number) => {
  switch (status) {
    case 1: // 运行中
      return "text-success";
    case 2: // 已停止
      return "text-danger";
    case 3: // 已过期
      return "text-warning";
    default:
      return "text-gray-400";
  }
};
