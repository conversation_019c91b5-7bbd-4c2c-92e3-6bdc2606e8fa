<template>
  <el-descriptions class="host-details" :title="'主机详情'" :column="1" border>
    <el-descriptions-item>
      <h2>基本信息</h2>
    </el-descriptions-item>
    <el-descriptions-item label="名称">
      <iconify-icon-online icon="mdi:server-network" />
      {{ host.name }}
      <template v-if="host.host_type === 2 || host.host_type === 3">
        <div class="flex items-center gap-2 ml-6">
          <el-link
            v-if="host.host_type === 3"
            :href="`https://console.huaweicloud.com/ecm/?region=${host.region_id}&locale=zh-cn#/ecs/manager/vmList/vmDetail/basicinfo?instanceId=${host.sn}`"
            target="_blank"
            type="primary"
            class="text-xs flex items-center gap-1"
          >
            <iconify-icon-online icon="cbi:huawei" />
            <span>华为云控制台</span>
          </el-link>
          <el-link
            v-if="host.host_type === 2"
            :href="`https://ecs.console.aliyun.com/server/${host.sn}/detail?regionId=${host.region_id}`"
            target="_blank"
            type="primary"
            class="text-xs flex items-center gap-1"
          >
            <iconify-icon-online icon="simple-icons:alibabacloud" />
            <span>阿里云控制台</span>
          </el-link>
        </div>
      </template>
    </el-descriptions-item>
    <el-descriptions-item label="类型">
      <iconify-icon-online icon="mdi:information-outline" />
      <el-text :type="hostTypeColor">
        {{ hostTypeName }}
      </el-text>
    </el-descriptions-item>
    <el-descriptions-item label="状态">
      <el-text :type="statusColor">
        <iconify-icon-online icon="mdi:circle" />
        {{ hostStatusName }}
      </el-text>
    </el-descriptions-item>
    <el-descriptions-item label="监控状态">
      <el-text v-if="host.ping_monitor" type="success">
        <iconify-icon-online icon="mdi:eye" />
        监控中
      </el-text>
      <el-text v-else type="danger">
        <iconify-icon-online icon="mdi:eye-off" />
        未监控
      </el-text>
    </el-descriptions-item>
    <el-descriptions-item label="备注">
      <iconify-icon-online icon="mdi:note-text-outline" />
      {{ host.remark }}
    </el-descriptions-item>
    <el-descriptions-item label="付费模式">
      <el-text :type="chargeTypeColor">
        <iconify-icon-online
          icon="mdi:cash-multiple"
          :class="`text-${chargeTypeColor}`"
        />
        {{ ChargeTypes.get(host.charge_type) || "未知" }}
      </el-text>
    </el-descriptions-item>

    <el-descriptions-item>
      <h2>网络信息</h2>
    </el-descriptions-item>
    <el-descriptions-item label="IP地址">
      <iconify-icon-online icon="mdi:ip-network" class="text-primary" />
      {{ host.ip }}
    </el-descriptions-item>
    <el-descriptions-item v-if="host.public_ip" label="公网IP地址">
      <iconify-icon-online icon="mdi:earth" />
      {{ host.public_ip }}
    </el-descriptions-item>
    <el-descriptions-item label="数据中心">
      <iconify-icon-online icon="mdi:database" />
      {{ host?.datacenter }}
    </el-descriptions-item>
    <el-descriptions-item label="区域ID">
      <iconify-icon-online icon="mdi:map-marker-radius" />
      {{ host.region_id }}
    </el-descriptions-item>
    <el-descriptions-item label="机柜">
      <iconify-icon-online icon="mdi:server" />
      {{ host.cabinet }}
    </el-descriptions-item>
    <el-descriptions-item v-if="host.position" label="位置">
      <iconify-icon-online icon="mdi:map-marker" />
      {{ host.position }}
    </el-descriptions-item>
    <el-descriptions-item label="远程端口">
      <iconify-icon-online icon="mdi:network" />
      {{ host.remote_port }}
    </el-descriptions-item>

    <el-descriptions-item>
      <h2>硬件信息</h2>
    </el-descriptions-item>
    <el-descriptions-item label="型号">
      <iconify-icon-online icon="mdi:chip" />
      {{ host.model }} {{ host.is_amd ? "(amd机型)" : "" }}
    </el-descriptions-item>
    <el-descriptions-item label="CPU线程数">
      <iconify-icon-online icon="mdi:cpu-64-bit" />
      {{ host.cpu_thread }}
    </el-descriptions-item>
    <el-descriptions-item label="内存">
      <iconify-icon-online icon="mdi:memory" />
      {{ formattedMemory }}
    </el-descriptions-item>
    <el-descriptions-item v-if="host.gpu_amount > 0" label="GPU数量">
      <iconify-icon-online icon="mdi:gpu" />
      共 {{ host.gpu_amount }} 张
      <br />
      {{ host.gpu_spec }}
    </el-descriptions-item>
    <el-descriptions-item label="操作系统">
      <iconify-icon-online
        :icon="
          host.os_type === 'windows' ? 'mdi:microsoft-windows' : 'mdi:linux'
        "
        :class="host.os_type === 'windows' ? 'text-primary' : 'text-success'"
      />
      {{ host.os }}
    </el-descriptions-item>
    <el-descriptions-item label="操作系统类型">
      <iconify-icon-online icon="mdi:desktop-classic" />
      {{ host.os_type }}
    </el-descriptions-item>

    <el-descriptions-item>
      <h2>资源信息</h2>
    </el-descriptions-item>
    <el-descriptions-item label="资源组ID">
      <iconify-icon-online icon="mdi:folder-outline" />
      {{ host.resource_group_id }}
    </el-descriptions-item>
    <el-descriptions-item label="资源组名称">
      <iconify-icon-online icon="mdi:folder" />
      {{ host.resource_group_name }}
    </el-descriptions-item>
    <el-descriptions-item v-if="host.tags && host.tags.length > 0" label="标签">
      <div class="flex flex-wrap gap-2">
        <template v-for="(tag, index) in host.tags" :key="index">
          <el-tag
            size="small"
            type="success"
            effect="light"
            style="
              display: inline-flex;
              align-items: center;
              white-space: nowrap;
            "
          >
            <iconify-icon-online
              icon="mdi:tag"
              style="flex-shrink: 0; margin-right: 4px"
              class="text-success"
            />
            <span style="flex-shrink: 0">{{ tag.key }}={{ tag.value }}</span>
          </el-tag>
        </template>
      </div>
    </el-descriptions-item>

    <el-descriptions-item>
      <h2>时间信息</h2>
    </el-descriptions-item>
    <el-descriptions-item label="创建时间">
      <iconify-icon-online icon="mdi:calendar-plus" />
      {{ formattedCreateAt }}
    </el-descriptions-item>
    <el-descriptions-item v-if="host.expired_time" label="过期时间">
      <iconify-icon-online icon="mdi:calendar-remove" />
      {{ formattedExpiredTime }}
    </el-descriptions-item>
    <el-descriptions-item v-if="host.sync_time" label="同步时间">
      <iconify-icon-online icon="mdi:calendar-sync" />
      {{ formattedSyncTime }}
    </el-descriptions-item>
    <el-descriptions-item v-if="host.updated_at" label="更新时间">
      <iconify-icon-online icon="mdi:calendar-refresh" />
      {{ formattedUpdatedAt }}
    </el-descriptions-item>
  </el-descriptions>
</template>

<script setup lang="ts">
import { computed, defineProps, type PropType } from "vue";
import {
  HostStatus,
  HostStatusColors,
  HostTypes,
  ChargeTypes,
  ChargeTypeColors,
  CloudTypesColors
} from "@/config/enum";
import dayjs from "dayjs";
import type { Host } from "@/api/asset/host";

const props = defineProps({
  host: {
    type: Object as PropType<Host>,
    required: true
  }
});

const hostStatusName = computed(() => HostStatus.get(props.host.status));
const hostTypeName = computed(() => HostTypes.get(props.host.host_type));
const hostTypeColor = computed(() => {
  const color = CloudTypesColors.get(props.host.host_type);
  return color === "info" ||
    color === "success" ||
    color === "danger" ||
    color === "warning" ||
    color === "primary"
    ? color
    : "info";
});
const statusColor = computed(() => {
  const color = HostStatusColors.get(props.host.status);
  return color === "info" ||
    color === "success" ||
    color === "danger" ||
    color === "warning" ||
    color === "primary"
    ? color
    : "info";
});
const chargeTypeColor = computed(() => {
  const color = ChargeTypeColors.get(props.host.charge_type);
  return color === "info" ||
    color === "success" ||
    color === "danger" ||
    color === "warning" ||
    color === "primary"
    ? color
    : "info";
});
const formattedCreateAt = computed(() =>
  dayjs(props.host.created_at).format("YYYY-MM-DD HH:mm:ss")
);
const formattedUpdatedAt = computed(() =>
  dayjs(props.host.updated_at).format("YYYY-MM-DD HH:mm:ss")
);
const formattedSyncTime = computed(() =>
  dayjs(props.host.sync_time).format("YYYY-MM-DD HH:mm:ss")
);
const formattedMemory = computed(() => {
  const bytes = props.host.memory;
  if (bytes < 1024) return bytes + " B";
  const k = 1024;
  const sizes = ["MB", "GB", "TB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
});
const formattedExpiredTime = computed(() => {
  return props.host.expired_time
    ? dayjs(props.host.expired_time).format("YYYY-MM-DD HH:mm:ss")
    : "N/A";
});
</script>

<style scoped>
.host-details {
  box-sizing: border-box;
  width: 100%;
  max-width: 1200px;
  padding: 10px;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgb(0 0 0 / 10%);
  transition: all 0.3s ease;
}

h2 {
  padding-bottom: 2px;
  margin-top: 2px;
  border-bottom: 2px solid #e0e0e0;
  transition: color 0.3s ease;
}

.el-text {
  display: inline-flex;
  gap: 5px;
  align-items: center;
  transition:
    background-color 0.3s ease,
    color 0.3s ease;
}

@media (width <= 768px) {
  .host-details {
    padding: 15px;
  }
}
</style>
