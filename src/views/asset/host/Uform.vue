<template>
  <div>
    <el-card shadow="never" class="form-card">
      <el-form
        ref="ruleFormRef"
        label-width="auto"
        style="max-width: 600px"
        :model="newFormInline.form"
        size="default"
        status-icon
        label-position="top"
        class="form-layout"
      >
        <el-form-item
          label="实例名称"
          prop="name"
          :rules="[formRules.required, formRules.maxLength]"
        >
          <el-input v-model="newFormInline.form.name" class="input-field" />
        </el-form-item>
        <el-form-item
          label="IP"
          prop="ip"
          :rules="[
            { required: true, message: '请输入ip', trigger: 'blur' },
            { max: 255, message: '请输入最大255', trigger: 'blur' }
          ]"
        >
          <el-input v-model="newFormInline.form.ip" class="input-field" />
        </el-form-item>
        <el-form-item
          label="公网IP"
          prop="public_ip"
          :rules="[{ max: 255, message: '请输入最大255', trigger: 'blur' }]"
        >
          <el-input
            v-model="newFormInline.form.public_ip"
            class="input-field"
          />
        </el-form-item>
        <el-form-item
          label="数据中心"
          prop="datacenter_id"
          :rules="[
            { required: true, message: '请选择数据中心', trigger: 'blur' }
          ]"
          style="margin: 5px"
        >
          <el-select
            v-model="newFormInline.form.datacenter_id"
            class="select-field"
            style="min-width: 200px"
          >
            <el-option
              v-for="item in filteredDatacenters"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
          <el-button
            :icon="useRenderIcon('ep:refresh')"
            type="primary"
            link
            class="refresh-button"
            @click="syncAllDatacenters"
          />
        </el-form-item>
        <el-form-item>
          <el-form-item
            label="机柜"
            prop="cabinet"
            :rules="[{ max: 255, message: '请输入最大255', trigger: 'blur' }]"
            style="margin: 5px"
          >
            <el-input
              v-model="newFormInline.form.cabinet"
              class="input-field"
            />
          </el-form-item>
          <el-form-item
            label="柜位"
            prop="position"
            :rules="[{ max: 255, message: '请输入最大255', trigger: 'blur' }]"
          >
            <el-input
              v-model="newFormInline.form.position"
              class="input-field"
            />
          </el-form-item>
        </el-form-item>
        <el-form-item>
          <el-form-item label="类型" style="margin: 5px">
            <el-select
              v-model="newFormInline.form.host_type"
              class="select-field"
              style="min-width: 150px"
            >
              <el-option
                v-for="(item, index) in NonCloudHostTypes"
                :key="index"
                :label="item[1]"
                :value="item[0]"
              >
                {{ item[1] }}
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label="型号"
            prop="model"
            :rules="[{ max: 255, message: '请输入最大255', trigger: 'blur' }]"
            class="input-field"
            style="margin: 5px"
          >
            <el-input v-model="newFormInline.form.model" class="input-field" />
          </el-form-item>
          <el-form-item
            label="序列号"
            prop="sn"
            :rules="[{ max: 255, message: '请输入最大255', trigger: 'blur' }]"
            class="input-field"
            style="margin: 5px"
          >
            <el-input v-model="newFormInline.form.sn" class="input-field" />
          </el-form-item>
        </el-form-item>
        <el-form-item label="配置">
          <el-form-item label="CPU核数" prop="cpu_thread" style="margin: 5px">
            <el-input-number
              v-model="newFormInline.form.cpu_thread"
              :step="2"
              step-strictly
              class="input-number"
            />
          </el-form-item>
          <el-form-item label="内存大小(M)" prop="memory" style="margin: 5px">
            <el-input-number
              v-model="newFormInline.form.memory"
              step-strictly
              class="input-number"
            />
          </el-form-item>
        </el-form-item>
        <el-form-item>
          <el-form-item label="GPU数量" prop="gpu_amount" style="margin: 5px">
            <el-input-number
              v-model="newFormInline.form.gpu_amount"
              step-strictly
              class="input-number"
            />
          </el-form-item>
          <el-form-item label="GPU规格">
            <el-input
              v-model="newFormInline.form.gpu_spec"
              class="input-field"
            />
          </el-form-item>
        </el-form-item>
        <el-form-item label="操作系统">
          <el-form-item label="系统类型" style="margin: 5px">
            <el-select
              v-model="newFormInline.form.os_type"
              class="select-field"
              style="min-width: 150px"
            >
              <el-option
                v-for="type in OS_TYPES"
                :key="type.value"
                v-bind="type"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="远程端口" style="margin: 5px">
            <el-input-number
              v-model="newFormInline.form.remote_port"
              step-strictly
              class="input-number"
            />
          </el-form-item>
          <el-form-item
            label="发行版本"
            prop="os"
            :rules="[{ max: 255, message: '请输入最大255', trigger: 'blur' }]"
            style="margin: 10px"
            class="input-field"
          >
            <el-input v-model="newFormInline.form.os" class="input-field" />
          </el-form-item>
        </el-form-item>
        <el-form-item>
          <el-form-item label="状态" style="margin: 5px">
            <el-select
              v-model="newFormInline.form.status"
              class="select-field"
              style="min-width: 150px"
            >
              <el-option label="运行中" :value="1"> 运行中 </el-option>
              <el-option label="停止" :value="2"> 停止 </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="Ping监控" style="margin: 5px">
            <el-switch
              v-model="newFormInline.form.ping_monitor"
              class="switch-field"
            />
          </el-form-item>
        </el-form-item>
        <el-form-item
          label="备注"
          prop="remark"
          :rules="[{ max: 255, message: '请输入最大255', trigger: 'blur' }]"
        >
          <el-input
            v-model="newFormInline.form.remark"
            type="textarea"
            class="textarea-field"
          />
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { defineProps, ref, computed } from "vue";
import type { FormInstance } from "element-plus";
import { NonCloudHostTypes } from "@/config/enum";
import type { HostForm } from "@/api/asset/host";
import { useRole } from "./hook";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";

const OS_TYPES = [
  { label: "linux", value: "linux" },
  { label: "windows", value: "windows" },
  { label: "unix", value: "unix" }
] as const;

const formRules = {
  required: { required: true, message: "此项为必填项", trigger: "blur" },
  maxLength: { max: 255, message: "最大长度255个字符", trigger: "blur" }
};

interface FormProps {
  formInline: {
    form: HostForm;
  };
}

const props = withDefaults(defineProps<FormProps>(), {
  formInline: () => ({ form: {} as HostForm })
});

const filteredDatacenters = computed(() =>
  datacenters.value.filter(item => item.cloud_type < 2)
);

const newFormInline = ref(props.formInline);
const ruleFormRef = ref<FormInstance>();

const { datacenters, syncAllDatacenters } = useRole();

defineExpose({ ruleFormRef });
</script>

<style scoped>
.form-card {
  border-radius: 12px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 8%);
  transition: box-shadow 0.3s ease;

  &:hover {
    box-shadow: 0 6px 20px rgb(0 0 0 / 12%);
  }
}

.form-layout {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.input-field {
  border-radius: 8px;
  transition: border-color 0.3s ease;

  &:focus-within {
    border-color: #409eff;
    box-shadow: 0 0 0 1px rgb(64 158 255 / 20%);
  }
}

.select-field {
  border-radius: 8px;
}

.input-number {
  border-radius: 8px;
}

.switch-field {
  margin-top: 10px;
}

.textarea-field {
  border-radius: 8px;
}

.refresh-button {
  margin-left: 10px;
}
</style>
