<template>
  <div class="tag-form">
    <!-- 已选主机卡片 -->
    <el-card shadow="hover" class="mb-4 host-card">
      <template #header>
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <iconify-icon-online
              icon="mdi:server-security"
              class="text-primary mr-2 text-lg"
            />
            <span class="text-base font-medium">已选主机</span>
          </div>
          <el-tag type="info" size="small" effect="plain">
            共 {{ formInline.rows.length }} 台
          </el-tag>
        </div>
      </template>

      <div class="max-h-[240px] overflow-y-auto custom-scrollbar">
        <div
          v-for="(item, index) in formInline.rows"
          :key="index"
          class="host-item"
        >
          <div class="flex items-center">
            <iconify-icon-online
              icon="mdi:server-network"
              class="text-gray-600 mr-2"
            />
            <span class="font-medium text-gray-700">{{ item.name }}</span>
            <el-tag size="small" class="ml-2" effect="plain">{{
              item.ip
            }}</el-tag>
            <el-tag
              v-if="item.public_ip"
              size="small"
              type="warning"
              class="ml-2"
              effect="plain"
            >
              {{ item.public_ip }}
            </el-tag>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 标签操作卡片 -->
    <el-card shadow="hover" class="tag-operation-card">
      <template #header>
        <div class="flex items-center">
          <iconify-icon-online
            icon="ri:price-tag-3-line"
            class="text-primary mr-2 text-lg"
          />
          <span class="text-base font-medium">标签操作</span>
        </div>
      </template>

      <el-form
        ref="ruleFormRef"
        :model="formInline.form"
        label-position="top"
        class="mt-2"
      >
        <!-- 操作类型选择 -->
        <el-form-item label="操作类型" required>
          <el-select
            v-model="formInline.form.op"
            class="w-full operation-select"
          >
            <el-option
              v-for="(label, value) in operations"
              :key="value"
              :value="value"
              :label="label"
            >
              <div class="flex items-center">
                <iconify-icon-online
                  :icon="getOperationIcon(value)"
                  class="mr-2"
                  :class="[`text-${getOperationType(value)}`]"
                />
                <span>{{ label }}</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>

        <!-- 标签选择区域 -->
        <el-form-item
          v-if="formInline.form.op !== 'clear'"
          label="选择标签"
          required
        >
          <div class="flex flex-col gap-4">
            <div class="flex items-center">
              <el-select
                v-model="formInline.form.tag_ids"
                class="flex-1 min-w-[300px]"
                multiple
                filterable
                clearable
                placeholder="请选择标签"
              >
                <el-option
                  v-for="tag in formInline.tags"
                  :key="tag.id"
                  :label="`${tag.key}=${tag.value}`"
                  :value="tag.id"
                >
                  <div class="flex items-center">
                    <iconify-icon-online
                      icon="ri:price-tag-3-line"
                      class="mr-2 text-primary"
                    />
                    <span>{{ tag.key }}={{ tag.value }}</span>
                  </div>
                </el-option>
              </el-select>
              <el-button
                type="primary"
                class="ml-2 refresh-btn !flex items-center"
                link
                @click="formInline.syncTagsFunc()"
              >
                <iconify-icon-online icon="ep:refresh" class="mr-1" />
              </el-button>
            </div>

            <!-- 已选标签展示区 -->
            <div v-if="formInline.form.tag_ids?.length" class="selected-tags">
              <div class="text-sm text-gray-500 mb-2">已选标签：</div>
              <div class="flex flex-wrap gap-2">
                <el-tag
                  v-for="tagId in formInline.form.tag_ids"
                  :key="tagId"
                  :type="getTagType()"
                  closable
                  effect="light"
                  class="tag-item"
                  @close="removeTag(tagId)"
                >
                  <div class="flex items-center gap-1">
                    <iconify-icon-online icon="ri:price-tag-3-line" />
                    <span>{{ getTagLabel(tagId) }}</span>
                  </div>
                </el-tag>
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { defineProps, ref, computed } from "vue";
import type { FormInstance } from "element-plus";
import type { Tag } from "@/api/asset/tag";
import type { Host, TagHostsFrom } from "@/api/asset/host";

interface FormProps {
  formInline: {
    rows: Host[];
    tags: Tag[];
    syncTagsFunc: () => void;
    form: TagHostsFrom;
  };
}

const props = withDefaults(defineProps<FormProps>(), {
  formInline: () => ({
    rows: [],
    tags: [],
    form: {} as TagHostsFrom,
    syncTagsFunc: () => {}
  })
});

const emit = defineEmits(["update:formInline"]);

const formInline = computed({
  get: () => props.formInline,
  set: value => emit("update:formInline", value)
});

const ruleFormRef = ref<FormInstance>();

const operations = {
  append: "追加标签",
  replace: "替换标签",
  delete: "删除标签",
  clear: "清空标签"
};

const getOperationType = (op: string) => {
  const types: Record<string, string> = {
    append: "success",
    replace: "warning",
    delete: "danger",
    clear: "danger"
  };
  return types[op] || "info";
};

const getOperationIcon = (op: string) => {
  const icons: Record<string, string> = {
    append: "material-symbols:add-circle-outline",
    replace: "material-symbols:swap-horiz",
    delete: "material-symbols:delete-outline",
    clear: "material-symbols:clear-all"
  };
  return icons[op] || "material-symbols:label-outline";
};

const getTagLabel = (tagId: number) => {
  const tag = formInline.value.tags.find(t => t.id === tagId);
  return tag ? `${tag.key}=${tag.value}` : "";
};

const removeTag = (tagId: number) => {
  formInline.value.form.tag_ids = formInline.value.form.tag_ids.filter(
    id => id !== tagId
  );
};

const getTagType = () => {
  const types = ["success", "warning", "info", "primary"] as const;
  return types[Math.floor(Math.random() * types.length)];
};

defineExpose({ ruleFormRef });
</script>

<style scoped>
.tag-form :deep(.el-card__header) {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.tag-form :deep(.el-card) {
  border-radius: 8px;
  transition: all 0.3s ease;
}

.host-item {
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.3s ease;
}

.host-item:last-child {
  border-bottom: none;
}

.host-item:hover {
  background-color: #f8f9fa;
}

.selected-tags {
  padding: 16px;
  background-color: #f8f9fa;
  border: 1px dashed #e4e7ed;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.selected-tags:hover {
  background-color: var(--el-color-primary-light-9);
  border-color: var(--el-color-primary);
}

.tag-item {
  padding: 6px 10px;
  transition: all 0.3s ease;
}

.tag-item:hover {
  box-shadow: 0 2px 4px rgb(0 0 0 / 5%);
  transform: translateY(-1px);
}

.operation-select :deep(.el-select-dropdown__item) {
  display: flex;
  align-items: center;
  height: 40px;
  padding: 10px 12px;
}

.refresh-btn {
  height: 40px;
  transition: all 0.3s ease;
}

.refresh-btn:hover {
  transform: rotate(180deg);
}

.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #e4e4e4 transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: #e4e4e4;
  border-radius: 3px;
}

.tag-form :deep(.el-select .el-tag) {
  display: flex;
  gap: 4px;
  align-items: center;
  height: 28px;
  padding: 0 8px;
  border-radius: 4px;
}
</style>
