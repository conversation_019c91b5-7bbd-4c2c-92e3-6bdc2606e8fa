<template>
  <div class="host-assets">
    <el-card shadow="hover" class="mb-4">
      <template #header>
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <iconify-icon-online
              icon="mdi:server-network"
              class="text-primary mr-2 text-lg"
            />
            <span class="text-base font-medium">主机关联资产</span>
          </div>
          <el-button
            type="primary"
            size="small"
            :icon="RefreshIcon"
            :loading="loading"
            @click="fetchAssets"
          >
            刷新
          </el-button>
        </div>
      </template>

      <div v-if="loading" class="flex justify-center py-8">
        <el-skeleton :rows="5" animated />
      </div>

      <div v-else-if="error" class="flex justify-center py-8">
        <el-empty description="获取资产数据失败" :image-size="120">
          <template #description>
            <p class="text-danger">{{ error }}</p>
          </template>
          <el-button type="primary" @click="fetchAssets">重试</el-button>
        </el-empty>
      </div>

      <div v-else>
        <!-- 资产信息 -->
        <div v-if="assetInfos.length > 0" class="mb-6">
          <h3 class="text-lg font-medium mb-3 flex items-center">
            <iconify-icon-online icon="mdi:information-outline" class="mr-2" />
            资产信息
          </h3>
          <el-table
            :data="assetInfos"
            border
            stripe
            style="width: 100%"
            :header-cell-style="{
              background: 'var(--el-fill-color-light)',
              color: 'var(--el-text-color-primary)',
              fontWeight: '600'
            }"
            :row-style="{
              cursor: 'pointer',
              transition: 'background-color 0.2s ease'
            }"
            @row-click="row => navigateToAsset(row.asset_type, row.ip)"
          >
            <el-table-column prop="ip" label="IP地址" min-width="120">
              <template #default="{ row }">
                <div class="flex items-center">
                  <iconify-icon-online
                    icon="mdi:ip-network"
                    class="mr-1 text-primary"
                  />
                  {{ row.ip }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="name" label="名称" min-width="150">
              <template #default="{ row }">
                <div class="flex items-center">
                  <iconify-icon-online icon="mdi:tag-text" class="mr-1" />
                  {{ row.name }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="account_name"
              label="账户名"
              min-width="120"
            />
            <el-table-column
              prop="assetTypeName"
              label="资产类型"
              min-width="120"
            >
              <template #default="{ row }">
                <el-tag
                  :type="getAssetTypeColor(row.asset_type)"
                  size="small"
                  effect="light"
                >
                  {{ getAssetTypeName(row.asset_type) || row.asset_type_name }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="ext_info" label="扩展信息" min-width="150" />
            <el-table-column label="操作" width="100" fixed="right">
              <template #default="{ row }">
                <el-button
                  v-if="row.asset_cloud_url"
                  link
                  type="primary"
                  @click="openCloudUrl(row.asset_cloud_url)"
                >
                  <iconify-icon-online icon="mdi:cloud" class="mr-1" />
                  控制台
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 负载均衡器 -->
        <div v-if="loadbalancers.length > 0" class="mb-6">
          <h3 class="text-lg font-medium mb-3 flex items-center">
            <iconify-icon-online icon="mdi:server-network" class="mr-2" />
            {{ AssetTypeMap[AssetType.Loadbalancer] }}
          </h3>
          <el-table
            :data="loadbalancers"
            border
            stripe
            style="width: 100%"
            :header-cell-style="{
              background: 'var(--el-fill-color-light)',
              color: 'var(--el-text-color-primary)',
              fontWeight: '600'
            }"
            :row-style="{
              cursor: 'pointer',
              transition: 'background-color 0.2s ease'
            }"
            @row-click="
              row => navigateToAsset(AssetType.Loadbalancer, row.host)
            "
          >
            <el-table-column prop="name" label="名称" min-width="150" />
            <el-table-column prop="host" label="主机" min-width="120" />
            <el-table-column
              prop="loadbalancer_type"
              label="类型"
              min-width="120"
            >
              <template #default="{ row }">
                <el-tag
                  :type="getLoadbalancerTypeColor()"
                  size="small"
                  effect="light"
                >
                  {{ row.loadbalancer_type }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="spec" label="规格" min-width="120" />
            <el-table-column prop="account" label="账户" min-width="120" />
            <el-table-column
              prop="datacenter"
              label="数据中心"
              min-width="120"
            />
            <el-table-column label="操作" width="100" fixed="right">
              <template #default="{ row }">
                <el-button
                  link
                  type="primary"
                  @click="openLoadBalancerLink(row)"
                >
                  <iconify-icon-online icon="mdi:cloud" class="mr-1" />
                  控制台
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- DDoS域名 -->
        <div v-if="ddosDomains.length > 0" class="mb-6">
          <h3 class="text-lg font-medium mb-3 flex items-center">
            <iconify-icon-online icon="mdi:shield-check" class="mr-2" />
            {{ AssetTypeMap[AssetType.CloudDDosDomainAssetType] }}
          </h3>
          <el-table
            :data="ddosDomains"
            border
            stripe
            style="width: 100%"
            :header-cell-style="{
              background: 'var(--el-fill-color-light)',
              color: 'var(--el-text-color-primary)',
              fontWeight: '600'
            }"
            :row-style="{
              cursor: 'pointer',
              transition: 'background-color 0.2s ease'
            }"
            @row-click="
              row =>
                navigateToAsset(AssetType.CloudDDosDomainAssetType, row.domain)
            "
          >
            <el-table-column prop="domain" label="域名" min-width="180" />
            <el-table-column prop="cname" label="CNAME" min-width="180" />
            <el-table-column
              prop="instance_id"
              label="实例ID"
              min-width="150"
            />
            <el-table-column prop="account" label="账户" min-width="120" />
          </el-table>
        </div>

        <el-empty
          v-if="!hasAnyAssets"
          description="暂无关联资产"
          :image-size="120"
          style="padding: 40px 0"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { getHostAssetsAPI } from "@/api/asset/host";
import type { AssetInfo } from "@/api/search";
import type { Loadbalancer } from "@/api/asset/loadbalancer";
import type { DDosDomain } from "@/api/asset/ddos";
import { message } from "@/utils/message";
import { useRouter } from "vue-router";
import RefreshIcon from "@/assets/table-bar/refresh.svg?component";
import {
  AssetType,
  AssetTypeMap,
  AssetTypeColors,
  assetTypeLinks
} from "@/config/asset-type";

// 定义props
const props = defineProps({
  host_id: {
    type: Number,
    required: true
  }
});

// 获取路由实例
const router = useRouter();

// 状态变量
const loading = ref(false);
const error = ref("");
const assetInfos = ref<AssetInfo[]>([]);
const loadbalancers = ref<Loadbalancer[]>([]);
const ddosDomains = ref<DDosDomain[]>([]);

// 计算属性
const hasAnyAssets = computed(() => {
  return (
    assetInfos.value.length > 0 ||
    loadbalancers.value.length > 0 ||
    ddosDomains.value.length > 0
  );
});

// 获取资产数据
const fetchAssets = async () => {
  loading.value = true;
  error.value = "";

  try {
    const { data, success, msg } = await getHostAssetsAPI(props.host_id);

    if (success && data) {
      assetInfos.value = data.asset_infos || [];
      loadbalancers.value = data.loadbalancers || [];
      ddosDomains.value = data.ddos_domains || [];
    } else {
      error.value = msg || "获取资产数据失败";
      message(error.value, { type: "error" });
    }
  } catch (err: any) {
    error.value = err?.message || "获取资产数据出错";
    message(error.value, { type: "error" });
  } finally {
    loading.value = false;
  }
};

// 获取资产类型名称
const getAssetTypeName = (assetType: number): string => {
  return AssetTypeMap[assetType] || "";
};

// 获取资产类型颜色
const getAssetTypeColor = (
  assetType: number
): "primary" | "success" | "warning" | "danger" | "info" => {
  const color = AssetTypeColors[assetType];
  // Make sure we only return valid el-tag types
  return color === "primary" ||
    color === "success" ||
    color === "warning" ||
    color === "danger" ||
    color === "info"
    ? color
    : "info";
};

// 获取负载均衡器类型颜色
const getLoadbalancerTypeColor = ():
  | "primary"
  | "success"
  | "warning"
  | "danger"
  | "info" => {
  const color = AssetTypeColors[AssetType.Loadbalancer];
  return color === "primary" ||
    color === "success" ||
    color === "warning" ||
    color === "danger" ||
    color === "info"
    ? color
    : "info";
};

// 导航到资产详情页
const navigateToAsset = (assetType: number, ip: string) => {
  const link = assetTypeLinks[assetType];
  if (link) {
    router.push(link + ip);
  }
};

// 打开云控制台链接
const openCloudUrl = (url: string) => {
  if (url) {
    window.open(url, "_blank");
  }
};

// 打开负载均衡器链接
const openLoadBalancerLink = (row: Loadbalancer) => {
  let url = "#";

  switch (row.loadbalancer_type) {
    case "elb":
      url = `https://console.huaweicloud.com/vpc/?region=${row.region_id}&locale=zh-cn#/elb/detail/basicInfo?ulbId=${row.loadbalancer_id}`;
      break;
    case "slb":
      url = `https://slb.console.aliyun.com/slb/${row.region_id}/slbs/${row.loadbalancer_id}`;
      break;
    default:
      url = "#";
  }

  if (url !== "#") {
    window.open(url, "_blank");
  }
};

// 组件挂载时获取数据
onMounted(() => {
  fetchAssets();
});
</script>

<style scoped lang="scss">
.host-assets {
  .mb-4 {
    margin-bottom: 1rem;
  }

  .mb-6 {
    margin-bottom: 1.5rem;
  }

  :deep(.el-table) {
    overflow: hidden;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgb(0 0 0 / 4%);

    .el-table__row {
      &:hover {
        background-color: var(--el-fill-color-lighter);
      }
    }
  }
}
</style>
