<template>
  <div class="loadbancer">
    <PureTableBar
      title="负载均衡器"
      :columns="loadbalancerColumns"
      @refresh="onSearch"
    >
      <template v-slot="{ size, dynamicColumns }">
        <div class="table-wrapper">
          <pure-table
            ref="tableRef"
            row-key="id"
            align-whole="left"
            table-layout="auto"
            :loading="loading"
            :size="size"
            adaptive
            :adaptiveConfig="{ offsetBottom: 108 }"
            :data="dataList"
            :columns="dynamicColumns"
            :header-cell-style="{
              color: '#303133',
              fontWeight: '600',
              borderBottom: '2px solid #e4e7ed'
            }"
            :row-style="{
              cursor: 'pointer',
              transition: 'background-color 0.2s ease',
              backgroundColor: 'white'
            }"
            :cell-style="{
              padding: '12px 10px'
            }"
            border
            stripe
            highlight-current-row
          >
            <template #empty>
              <el-empty
                description="暂无数据"
                :image-size="120"
                style="padding: 40px 0"
              />
            </template>
          </pure-table>
        </div>
      </template>
    </PureTableBar>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted } from "vue";
import { PureTableBar } from "@/components/RePureTableBar";
import { message } from "@/utils/message";
import { getHostLoadBalancersAPI } from "@/api/asset/host";
import type { Loadbalancer } from "@/api/asset/loadbalancer";
import { loadbalancerColumns } from "./hook";

const props = defineProps<{ host_id: number }>();
const loading = ref(true);
const dataList = ref<Loadbalancer[]>([]);

const onSearch = async () => {
  loading.value = true;
  try {
    const { data, success, msg } = await getHostLoadBalancersAPI(props.host_id);
    if (success) {
      dataList.value = data;
    } else {
      message(msg || "获取负载均衡器数据失败", { type: "error" });
      dataList.value = [];
    }
  } catch (error) {
    message("获取负载均衡器数据出错", { type: "error" });
    dataList.value = [];
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  onSearch();
});
</script>
<style scoped lang="less"></style>
