<template>
  <div class="monitor-form">
    <!-- 已选主机卡片 -->
    <el-card shadow="hover" class="mb-4 host-card">
      <template #header>
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <iconify-icon-online
              icon="material-symbols:monitoring"
              class="text-primary mr-2 text-lg"
            />
            <span class="text-base font-medium">已选主机</span>
          </div>
          <el-tag type="info" size="small" effect="plain">
            共 {{ formInline.rows.length }} 台
          </el-tag>
        </div>
      </template>

      <div class="max-h-[240px] overflow-y-auto custom-scrollbar">
        <div
          v-for="(item, index) in formInline.rows"
          :key="index"
          class="host-item"
        >
          <div class="flex items-center">
            <iconify-icon-online
              icon="mdi:server-network"
              class="text-gray-600 mr-2"
            />
            <span class="font-medium text-gray-700">{{ item.name }}</span>
            <el-tag size="small" class="ml-2" effect="plain">{{
              item.ip
            }}</el-tag>
            <el-tag
              v-if="item.public_ip"
              size="small"
              type="warning"
              class="ml-2"
              effect="plain"
            >
              {{ item.public_ip }}
            </el-tag>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 监控操作卡片 -->
    <el-card shadow="hover" class="monitor-card">
      <template #header>
        <div class="flex items-center">
          <iconify-icon-online
            icon="material-symbols:settings"
            class="text-primary mr-2 text-lg"
          />
          <span class="text-base font-medium">监控设置</span>
        </div>
      </template>

      <el-form
        ref="ruleFormRef"
        :model="formInline.form"
        label-position="top"
        class="mt-2"
      >
        <el-form-item label="操作类型" required>
          <el-radio-group
            v-model="formInline.form.monitor"
            class="w-full monitor-radio-group"
          >
            <el-radio :value="true" class="monitor-radio">
              <div class="flex items-center">
                <iconify-icon-online
                  icon="material-symbols:monitoring-on"
                  class="text-success mr-1"
                />
                开启监控
              </div>
            </el-radio>
            <el-radio :value="false" class="monitor-radio">
              <div class="flex items-center">
                <iconify-icon-online
                  icon="material-symbols:monitoring-off"
                  class="text-danger mr-1"
                />
                关闭监控
              </div>
            </el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { defineProps, ref, computed } from "vue";
import type { FormInstance } from "element-plus";
import type { Host, BatSetMonitorForm } from "@/api/asset/host";

interface FormProps {
  formInline: {
    rows: Host[];
    form: BatSetMonitorForm;
  };
}

const props = withDefaults(defineProps<FormProps>(), {
  formInline: () => ({
    rows: [],
    form: {} as BatSetMonitorForm
  })
});

const emit = defineEmits(["update:formInline"]);

const formInline = computed({
  get: () => props.formInline,
  set: value => emit("update:formInline", value)
});

const ruleFormRef = ref<FormInstance>();

defineExpose({ ruleFormRef });
</script>

<style scoped>
.monitor-form :deep(.el-card__header) {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.monitor-form :deep(.el-card) {
  border-radius: 8px;
  transition: all 0.3s ease;
}

.host-item {
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.3s ease;
}

.host-item:last-child {
  border-bottom: none;
}

.monitor-radio-group {
  display: flex;
  gap: 24px;
}

.monitor-radio {
  height: 40px;
  padding: 0 16px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #e4e4e4 transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: #e4e4e4;
  border-radius: 3px;
}
</style>
