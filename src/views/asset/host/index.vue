<template>
  <div class="main">
    <el-card class="search-card" shadow="hover">
      <el-form
        ref="formRef"
        :inline="true"
        position="top"
        :model="form"
        class="search-form"
        @submit.prevent
      >
        <div class="search-area">
          <div class="search-row primary-filters">
            <!-- 关键字搜索 -->
            <el-form-item label="关键字" prop="keyword">
              <el-input
                v-model="form.keyword"
                placeholder="搜索主机名称/描述"
                clearable
                class="custom-input search-keyword-input"
                :prefix-icon="Search"
                @keyup.enter="onSearch"
              />
            </el-form-item>
            <!-- IP搜索 -->
            <el-form-item label="IP 查询" prop="ip">
              <el-input
                v-model="form.ip"
                placeholder="搜索IP地址,多个IP用逗号分隔"
                clearable
                class="custom-input search-ip-input"
                :prefix-icon="Monitor"
                @keyup.enter="onSearch"
              />
            </el-form-item>
            <!-- 类型选择 -->
            <el-form-item label="主机类型" prop="host_type">
              <el-select
                v-model="form.host_type"
                placeholder="选择类型"
                filterable
                clearable
                @change="onSearch"
              >
                <el-option
                  v-for="[value, label] in HostTypes"
                  :key="value"
                  :label="label"
                  :value="value"
                />
              </el-select>
            </el-form-item>
            <!-- 运行状态选择 -->
            <el-form-item label="运行状态" prop="status">
              <div class="flex-input-group">
                <el-select
                  v-model="form.status"
                  placeholder="选择运行状态"
                  filterable
                  clearable
                  :prefix-icon="Monitor"
                  @change="onSearch"
                >
                  <el-option
                    v-for="[value, label] in HostStatus"
                    :key="value"
                    :label="label"
                    :value="value"
                  >
                    <div class="status-option">
                      <div 
                        class="status-indicator"
                        :class="{
                          'status-running': value === 1,
                          'status-stopped': value === 2,
                          'status-other': value !== 1 && value !== 2
                        }"
                      ></div>
                      <span>{{ label }}</span>
                    </div>
                  </el-option>
                </el-select>
              </div>
            </el-form-item>
          </div>

          <div :class="['search-row advanced-filters', !expanded ? 'hidden' : '']">
            <!-- 云账户选择 -->
            <el-form-item label="云账户" prop="cloud_account">
              <div class="flex-input-group">
                <el-select
                  v-model="form.account_id"
                  placeholder="选择云账户"
                  filterable
                  clearable
                  @change="onSearch"
                >
                  <el-option
                    v-for="account in accounts"
                    :key="account.id"
                    :label="account.name"
                    :value="account.id"
                  />
                </el-select>
                <el-button
                  :icon="useRenderIcon('ep:refresh')"
                  class="sync-button"
                  @click="syncAllAccounts"
                />
              </div>
            </el-form-item>
            <!-- 数据中心选择 -->
            <el-form-item label="数据中心" prop="datacenter">
              <div class="flex-input-group">
                <el-select
                  v-model="form.datacenter_id"
                  placeholder="请选择数据中心"
                  filterable
                  clearable
                  @change="onSearch"
                >
                  <el-option
                    v-for="datacenter in datacenters"
                    :key="datacenter.id"
                    :label="datacenter.name"
                    :value="datacenter.id"
                  />
                </el-select>
                <el-button
                  :icon="useRenderIcon('ep:refresh')"
                  class="sync-button"
                  @click="syncAllDatacenters"
                />
              </div>
            </el-form-item>
            <!-- 标签选择 -->
            <el-form-item label="标签" prop="tag">
              <div class="flex-input-group">
                <el-select
                  v-model="form.tag_ids"
                  placeholder="请选择标签"
                  filterable
                  clearable
                  multiple
                >
                  <el-option
                    v-for="item in tags"
                    :key="item.id"
                    :label="`${item.key}=${item.value}`"
                    :value="item.id"
                  />
                </el-select>
                <el-button
                  :icon="useRenderIcon('ep:refresh')"
                  class="sync-button"
                  @click="syncAllTags"
                />
              </div>
            </el-form-item>

            <!-- 资源组选择 -->
            <el-form-item label="资源组" prop="resource_group_id">
              <div class="flex-input-group">
                <el-select
                  v-model="form.resource_group_id"
                  placeholder="选择资源组"
                  filterable
                  clearable
                  @change="onSearch"
                >
                  <el-option
                    v-for="resourceGroup in resourceGroupes"
                    :key="resourceGroup.id"
                    :label="
                      resourceGroup.group_display_name +
                      ' (' +
                      resourceGroup.account_name +
                      ' - ' +
                      resourceGroup.group_id +
                      ')'
                    "
                    :value="resourceGroup.group_id"
                  />
                </el-select>
                <el-button
                  :icon="useRenderIcon('ep:refresh')"
                  class="sync-button"
                  @click="syncAllResourceGroupes"
                />
              </div>
            </el-form-item>

            <!-- Ping状态选择 -->
            <el-form-item label="Ping状态" prop="ping_monitor">
              <div class="flex-input-group">
                <el-select
                  v-model="form.ping_monitor"
                  placeholder="选择Ping状态"
                  filterable
                  clearable
                  @change="onSearch"
                >
                  <el-option
                    v-for="[value, label] in HostPingStatus"
                    :key="label"
                    :label="label"
                    :value="value"
                  />
                </el-select>
              </div>
            </el-form-item>

            <!-- N9E监控 -->
            <el-form-item label="N9E监控" prop="n9e_monitor">
              <div class="flex-input-group">
                <el-select v-model="form.n9e_monitor" clearable @change="onSearch">
                  <el-option :value="true" label="已监控">已监控</el-option>
                  <el-option :value="false" label="未监控">未监控</el-option>
                </el-select>
              </div>
            </el-form-item>

            <!-- AMD -->
            <el-form-item label="CPU类型" prop="is_amd">
              <div class="flex-input-group">
                <el-select v-model="form.is_amd" clearable @change="onSearch">
                  <el-option :value="true" label="AMD">AMD</el-option>
                  <el-option :value="false" label="其他">其他</el-option>
                </el-select>
              </div>
            </el-form-item>
          </div>

          <!-- 展开/收起按钮 -->
          <div class="expand-button" @click="expanded = !expanded">
            <el-icon class="expand-icon" :class="{ 'is-active': expanded }">
              <ArrowDown />
            </el-icon>
            <span class="expand-text">{{ expanded ? "收起高级搜索" : "展开高级搜索" }}</span>
          </div>

          <!-- 操作按钮 -->
          <div class="action-row">
            <el-form-item>
              <el-button
                type="primary"
                :icon="icons.search"
                :loading="loading"
                class="search-button"
                @click="onSearch"
              >
                搜索
              </el-button>
              <el-button
                :icon="icons.refresh"
                class="reset-button"
                @click="() => resetForm(formRef)"
              >
                重置
              </el-button>
              <el-divider direction="vertical" />
              <el-button
                type="primary"
                link
                :icon="icons.plus"
                class="add-button"
                @click="addFunc"
              >
                添加主机
              </el-button>
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <!-- 批量操作区域 -->
    <el-card class="batch-card">
      <div class="batch-actions">
        <el-button
          type="primary"
          link
          :icon="icons.tag"
          :disabled="multipleSelection.length === 0"
          @click="batchTag(multipleSelection)"
        >
          设置标签 ({{ multipleSelection.length }})
        </el-button>
        <el-button
          type="danger"
          link
          :disabled="multipleSelection.length === 0"
          @click="setMonitoring(multipleSelection)"
        >
          设置监控 ({{ multipleSelection.length }})
        </el-button>
        <el-button
          type="info"
          link
          :icon="icons.info"
          :disabled="multipleSelection.length === 0"
          @click="showSummaryDialog"
        >
          查看汇总 ({{ multipleSelection.length }})
        </el-button>
      </div>
    </el-card>

    <!-- 汇总信息弹窗 -->
    <el-dialog
      v-model="summaryDialogVisible"
      :title="`已选主机信息汇总 (${multipleSelection.length}台)`"
      width="1000px"
      :close-on-click-modal="false"
      class="summary-dialog"
      :show-close="false"
    >
      <template #header>
        <div class="dialog-header">
          <div class="header-left">
            <iconify-icon-online
              icon="mdi:server-network"
              class="header-icon"
            />
            <span class="header-title">已选主机信息汇总</span>
          </div>
          <div class="header-right">
            <el-tag type="primary" effect="dark" size="large">
              {{ multipleSelection.length }} 台
            </el-tag>
          </div>
        </div>
      </template>
      <div class="summary-content">
        <div class="summary-grid">
          <!-- IP信息 -->
          <div class="summary-section ip-section">
            <div class="section-header">
              <iconify-icon-online icon="mdi:ip-network" class="text-primary" />
              <span>IP信息</span>
            </div>
            <div class="section-content">
              <div class="summary-item ip-list">
                <div class="item-icon">
                  <iconify-icon-online icon="mdi:ip" class="text-primary" />
                </div>
                <div class="item-info">
                  <span class="label">内网IP</span>
                  <div class="ip-list-content">
                    {{ selectedIps }}
                  </div>
                </div>
              </div>
              <div v-if="hasPublicIps" class="summary-item ip-list">
                <div class="item-icon">
                  <iconify-icon-online icon="mdi:earth" class="text-warning" />
                </div>
                <div class="item-info">
                  <span class="label">公网IP</span>
                  <div class="ip-list-content">
                    {{ selectedPublicIps }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 资源信息 -->
          <div class="summary-section">
            <div class="section-header">
              <iconify-icon-online icon="mdi:chip" class="text-primary" />
              <span>资源信息</span>
            </div>
            <div class="section-content">
              <div class="summary-item">
                <div class="item-icon">
                  <iconify-icon-online
                    icon="mdi:cpu-64-bit"
                    class="text-primary"
                  />
                </div>
                <div class="item-info">
                  <span class="label">CPU总核数</span>
                  <span class="value">{{ totalCpu }} 核</span>
                </div>
              </div>
              <div class="summary-item">
                <div class="item-icon">
                  <iconify-icon-online icon="mdi:memory" class="text-success" />
                </div>
                <div class="item-info">
                  <span class="label">内存总量</span>
                  <span class="value">{{ totalMemory }} GB</span>
                </div>
              </div>
              <div class="summary-item">
                <div class="item-icon">
                  <iconify-icon-online icon="mdi:gpu" class="text-warning" />
                </div>
                <div class="item-info">
                  <span class="label">GPU总量</span>
                  <span class="value">{{ totalGpu }} 张</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 状态信息 -->
          <div class="summary-section">
            <div class="section-header">
              <iconify-icon-online
                icon="mdi:information-outline"
                class="text-primary"
              />
              <span>状态信息</span>
            </div>
            <div class="section-content">
              <div class="summary-item">
                <div class="item-icon">
                  <iconify-icon-online
                    icon="mdi:play-circle"
                    class="text-success"
                  />
                </div>
                <div class="item-info">
                  <span class="label">运行中</span>
                  <span class="value">{{ runningCount }} 台</span>
                </div>
              </div>
              <div class="summary-item">
                <div class="item-icon">
                  <iconify-icon-online
                    icon="mdi:stop-circle"
                    class="text-danger"
                  />
                </div>
                <div class="item-info">
                  <span class="label">已停止</span>
                  <span class="value">{{ stoppedCount }} 台</span>
                </div>
              </div>
              <div class="summary-item">
                <div class="item-icon">
                  <iconify-icon-online icon="mdi:eye" class="text-info" />
                </div>
                <div class="item-info">
                  <span class="label">监控中</span>
                  <span class="value">{{ monitoredCount }} 台</span>
                </div>
              </div>
              <div class="summary-item">
                <div class="item-icon">
                  <iconify-icon-online
                    icon="mdi:cpu-64-bit"
                    class="text-warning"
                  />
                </div>
                <div class="item-info">
                  <span class="label">AMD机型</span>
                  <span class="value">{{ amdCount }} 台</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 主机类型信息 -->
          <div class="summary-section">
            <div class="section-header">
              <iconify-icon-online icon="mdi:server" class="text-primary" />
              <span>主机类型</span>
            </div>
            <div class="section-content">
              <div
                v-for="[type, count] in hostTypeCounts"
                :key="type"
                class="summary-item"
              >
                <div class="item-icon">
                  <iconify-icon-online
                    icon="mdi:server-network"
                    class="text-primary"
                  />
                </div>
                <div class="item-info">
                  <span class="label">{{ type }}</span>
                  <span class="value">{{ count }} 台</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 区域信息 -->
          <div class="summary-section">
            <div class="section-header">
              <iconify-icon-online icon="mdi:map-marker" class="text-primary" />
              <span>数据中心分布</span>
            </div>
            <div class="section-content">
              <div
                v-for="[datacenter, count] in datacenterCounts"
                :key="datacenter"
                class="summary-item"
              >
                <div class="item-icon">
                  <iconify-icon-online
                    icon="mdi:map-marker-radius"
                    class="text-success"
                  />
                </div>
                <div class="item-info">
                  <span class="label">{{ datacenter }}</span>
                  <span class="value">{{ count }} 台</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="summaryDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="copyAllIps">
            <iconify-icon-online icon="mdi:content-copy" class="mr-1" />
            复制所有IP
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 表格 -->
    <PureTableBar title="主机列表" :columns="columns" @refresh="onSearch">
      <template v-slot="{ size, dynamicColumns }">
        <div class="table-wrapper">
          <pure-table
            ref="tableRef"
            row-key="id"
            align-whole="left"
            table-layout="auto"
            :loading="loading"
            :size="size"
            :data="dataList"
            :minHeight="500"
            :columns="dynamicColumns"
            :pagination="{ ...pagination, size }"
            :header-cell-style="{
              color: '#303133',
              fontWeight: '600',
              borderBottom: '2px solid #e4e7ed'
            }"
            :row-style="{
              cursor: 'pointer',
              transition: 'background-color 0.2s ease'
            }"
            :cell-style="{
              padding: '12px 10px'
            }"
            border
            stripe
            highlight-current-row
            @page-size-change="handleSizeChange"
            @page-current-change="handleCurrentChange"
            @selection-change="handleSelectionChange"
          >
            <template #empty>
              <el-empty
                description="暂无数据"
                :image-size="120"
                style="padding: 40px 0"
              />
            </template>
          </pure-table>
        </div>
      </template>
    </PureTableBar>
  </div>
</template>

<script setup lang="ts">
import { onBeforeMount, ref, computed } from "vue";
import { Search, ArrowDown, Monitor } from "@element-plus/icons-vue";
import { useRoute } from "vue-router";
import { useRole } from "./hook";
import { HostTypes, HostStatus, HostPingStatus } from "@/config/enum";
import { PureTableBar } from "@/components/RePureTableBar";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { parmaQueryNumber } from "@/utils/parmaQuery";
import type { TableInstance } from "element-plus";
import type { Host } from "@/api/asset/host";
import { message } from "@/utils/message";

defineOptions({
  name: "Host"
});

// refs
const formRef = ref<TableInstance>();
const tableRef = ref();
const multipleSelection = ref<Host[]>([]);
const expanded = ref(false);

// 计算汇总数据
const totalCpu = computed(() => {
  return multipleSelection.value.reduce(
    (sum, host) => sum + (host.cpu_thread || 0),
    0
  );
});

const totalMemory = computed(() => {
  return (
    multipleSelection.value.reduce((sum, host) => sum + (host.memory || 0), 0) /
    1024
  );
});

const totalGpu = computed(() => {
  return multipleSelection.value.reduce(
    (sum, host) => sum + (host.gpu_amount || 0),
    0
  );
});

const amdCount = computed(() => {
  return multipleSelection.value.filter(host => host.is_amd).length;
});

const hostTypeCounts = computed(() => {
  const counts = new Map();
  multipleSelection.value.forEach(host => {
    const type = HostTypes.get(host.host_type);
    counts.set(type, (counts.get(type) || 0) + 1);
  });
  return counts;
});

const datacenterCounts = computed(() => {
  const counts = new Map();
  multipleSelection.value.forEach(host => {
    if (host.datacenter) {
      counts.set(host.datacenter, (counts.get(host.datacenter) || 0) + 1);
    }
  });
  return counts;
});

const runningCount = computed(() => {
  return multipleSelection.value.filter(host => host.status === 1).length;
});

const stoppedCount = computed(() => {
  return multipleSelection.value.filter(host => host.status === 2).length;
});

const monitoredCount = computed(() => {
  return multipleSelection.value.filter(host => host.ping_monitor).length;
});

// IP信息计算属性
const selectedIps = computed(() => {
  return multipleSelection.value.map(host => host.ip).join(", ");
});

const selectedPublicIps = computed(() => {
  return multipleSelection.value
    .filter(host => host.public_ip)
    .map(host => host.public_ip)
    .join(", ");
});

const hasPublicIps = computed(() => {
  return multipleSelection.value.some(host => host.public_ip);
});

// 从hook中获取方法和数据
const {
  form,
  loading,
  columns,
  dataList,
  pagination,
  onSearch,
  resetForm,
  handleSizeChange,
  handleCurrentChange,
  addFunc,
  accounts,
  syncAllAccounts,
  tags,
  syncAllTags,
  batchTag,
  datacenters,
  syncAllDatacenters,
  setMonitoring,
  resourceGroupes,
  syncAllResourceGroupes
} = useRole();

// 表格选择处理
const handleSelectionChange = val => {
  multipleSelection.value = val;
};

// 路由参数处理
onBeforeMount(() => {
  const { query } = useRoute();
  const { account_id, datacenter_id, tag_id, ip, resource_group_id } = query;
  if (query) {
    form.account_id = parmaQueryNumber(account_id);
    form.ip = ip;
    form.resource_group_id = resource_group_id;
    form.datacenter_id = parmaQueryNumber(datacenter_id);
    parmaQueryNumber(tag_id) && (form.tag_ids = [parmaQueryNumber(tag_id)]);
  }
});

// 图标配置
const icons = {
  search: useRenderIcon("ep:search"),
  refresh: useRenderIcon("ep:refresh"),
  plus: useRenderIcon("ep:plus"),
  tag: useRenderIcon("ep:price-tag"),
  info: useRenderIcon("ep:info")
};

// 汇总信息弹窗
const summaryDialogVisible = ref(false);

const showSummaryDialog = () => {
  summaryDialogVisible.value = true;
};

const copyAllIps = () => {
  const allIps = [
    ...multipleSelection.value.map(host => host.ip),
    ...multipleSelection.value
      .filter(host => host.public_ip)
      .map(host => host.public_ip)
  ].join(", ");

  navigator.clipboard
    .writeText(allIps)
    .then(() => {
      message("已复制所有IP到剪贴板", { type: "success" });
    })
    .catch(() => {
      message("复制失败", { type: "error" });
    });
};
</script>

<style lang="scss" scoped>
.main {
  padding: 20px;
  border-radius: 12px;
}

.search-card {
  margin-bottom: 16px;
  border-radius: 10px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 8%);
  transition: box-shadow 0.3s ease;

  &:hover {
    box-shadow: 0 6px 20px rgb(0 0 0 / 12%);
  }
}

.action-row {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
}

.search-button,
.reset-button {
  transition: all 0.3s ease;
}

.search-button {
  margin-right: 8px;
}

.expand-button {
  display: flex;
  align-items: center;
  padding: 6px 10px;
  margin-top: 8px;
  font-size: 14px;
  color: #409eff;
  cursor: pointer;
  border-radius: 4px;
  transition: background 0.3s;
}

.expand-button:hover {
  background-color: #ecf5ff;
}

.expand-text {
  margin-left: 4px;
}

.expand-icon {
  transition: transform 0.3s;
}

.expand-icon.is-active {
  transform: rotate(180deg);
}

.el-select {
  width: 180px;
}

.flex-input-group {
  display: flex;
  gap: 8px;
  align-items: center;
}

.sync-button {
  padding: 8px;
  color: #606266;
  border-radius: 4px;
  transition: all 0.3s;
}

.sync-button:hover {
  color: #409eff;
  background-color: #ecf5ff;
}

.search-row {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: flex-start;
}

.search-row.primary-filters {
  margin-bottom: 4px;
}

.status-option {
  display: flex;
  gap: 8px;
  align-items: center;
  width: 100%;
  padding: 5px 0;
}

.status-indicator {
  flex-shrink: 0;
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-indicator.status-running {
  background-color: #67c23a;
}

.status-indicator.status-stopped {
  background-color: #f56c6c;
}

.status-indicator.status-other {
  background-color: #909399;
}

.el-select-dropdown__item {
  padding: 0 10px !important;
}

:deep(.el-select-dropdown__item) {
  padding: 0 10px !important;
}

:deep(.el-select-dropdown__item span) {
  display: inline-block;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.search-row.advanced-filters {
  padding-top: 8px;
  border-top: 1px dashed #ebeef5;
  transition: all 0.3s ease;
}

.search-row.hidden {
  display: none;
}

.search-keyword-input,
.search-ip-input,
.flex-input-group .el-select {
  width: 280px;
  transition: all 0.3s ease;
}

.search-keyword-input:focus,
.search-ip-input:focus {
  box-shadow: 0 0 0 2px rgb(64 158 255 / 10%);
}

.summary-dialog {
  :deep(.el-dialog) {
    border-radius: 12px;
    box-shadow: 0 4px 24px rgb(0 0 0 / 10%);
  }

  :deep(.el-dialog__header) {
    padding: 0;
    margin: 0;
  }

  :deep(.el-dialog__body) {
    padding: 0;
  }

  .dialog-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 24px;
    border-bottom: 1px solid #ebeef5;

    .header-left {
      display: flex;
      gap: 12px;
      align-items: center;

      .header-icon {
        font-size: 24px;
        color: var(--el-color-primary);
      }

      .header-title {
        font-size: 18px;
        font-weight: 600;
        color: #303133;
      }
    }

    .header-right {
      .el-tag {
        padding: 8px 16px;
        font-size: 16px;
        border-radius: 20px;
      }
    }
  }

  .summary-content {
    max-height: 70vh;
    padding: 24px;
    overflow-y: auto;
    background: #f5f7fa;

    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #dcdfe6;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
      background: #f5f7fa;
      border-radius: 3px;
    }
  }

  .summary-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 24px;
  }

  .summary-section {
    padding: 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgb(0 0 0 / 5%);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 16px rgb(0 0 0 / 8%);
      transform: translateY(-2px);
    }

    &.ip-section {
      grid-column: 1 / -1;
    }

    .section-header {
      display: flex;
      gap: 12px;
      align-items: center;
      padding-bottom: 12px;
      margin-bottom: 20px;
      border-bottom: 1px solid #ebeef5;

      .iconify-icon-online {
        font-size: 20px;
        color: var(--el-color-primary);
      }

      span {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }
    }

    .section-content {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }
  }

  .summary-item {
    display: flex;
    gap: 16px;
    align-items: center;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    transition: all 0.3s ease;

    &:hover {
      background: #f0f2f5;
      transform: translateX(4px);
    }

    .item-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      font-size: 24px;
      background: rgb(64 158 255 / 10%);
      border-radius: 8px;
    }

    .item-info {
      display: flex;
      flex: 1;
      flex-direction: column;
      gap: 4px;

      .label {
        font-size: 14px;
        color: #606266;
      }

      .value {
        font-size: 18px;
        font-weight: 600;
        color: #303133;
      }
    }
  }

  .ip-list {
    .ip-list-content {
      padding: 8px;
      font-size: 14px;
      line-height: 1.6;
      color: #303133;
      word-break: break-all;
      white-space: pre-wrap;
      background: #f8f9fa;
      border: 1px solid #ebeef5;
      border-radius: 4px;
    }
  }

  .dialog-footer {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    padding: 16px 24px;
    background: white;
    border-top: 1px solid #ebeef5;

    .el-button {
      padding: 8px 20px;
      font-size: 14px;
      border-radius: 6px;
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
        transform: translateY(-2px);
      }
    }
  }
}
</style>
