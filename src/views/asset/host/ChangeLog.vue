<script setup lang="ts">
import { ref, onMounted, reactive } from "vue";
import { PureTableBar } from "@/components/RePureTableBar";
import type { TableInstance } from "element-plus";
import type { PaginationProps } from "@pureadmin/table";
import { message } from "@/utils/message";
import { useRole } from "./hook";
import {
  getHostChangeLogAPI,
  createHostChangeLogAPI,
  type HostChangeLog
} from "@/api/asset/host-change-log";
import dayjs from "dayjs";

const props = defineProps<{
  host_id: number;
}>();

const { changeLogColumns } = useRole();

const formRef = ref<TableInstance>();
const tableRef = ref();
const form = reactive({
  keyword: undefined,
  dateRange: [
    dayjs().subtract(1, "month").format("YYYY-MM-DD HH:mm:ss"),
    dayjs().add(1, "day").format("YYYY-MM-DD HH:mm:ss")
  ],
  newRecord: ""
});
const content = ref<string>("");
const dataList = ref<HostChangeLog[]>([]);
const loading = ref<boolean>(true);
const pagination = reactive<PaginationProps>({
  total: 0,
  pageSize: 10,
  currentPage: 1,
  background: true,
  pageSizes: [10, 20, 30, 40, 50, 100]
});

function resetForm() {
  form.dateRange = [
    dayjs().subtract(1, "month").format("YYYY-MM-DD HH:mm:ss"),
    dayjs().add(1, "day").format("YYYY-MM-DD HH:mm:ss")
  ];
  onSearch();
}

function handleSizeChange(val: number) {
  pagination.pageSize = val;
  onSearch();
}

function handleCurrentChange(val: number) {
  pagination.currentPage = val;
  onSearch();
}

async function onSearch() {
  loading.value = true;

  const startTime =
    form.dateRange && form.dateRange[0] ? form.dateRange[0] : undefined;
  const endTime =
    form.dateRange && form.dateRange[1] ? form.dateRange[1] : undefined;

  getHostChangeLogAPI(props.host_id, {
    page: pagination.currentPage,
    limit: pagination.pageSize,
    start_time: startTime,
    end_time: endTime
  })
    .then(res => {
      if (res.success) {
        dataList.value = res.data;
        pagination.total = res.count;
      } else {
        dataList.value = [];
        pagination.total = 0;
      }
    })
    .catch(() => {
      message("请求失败", { type: "error" });
    })
    .finally(() => {
      loading.value = false;
    });
}

const addRecord = async () => {
  if (content.value.trim() !== "") {
    try {
      const response = await createHostChangeLogAPI(props.host_id, {
        content: content.value
      });
      if (response.success) {
        content.value = "";
        message("变更记录添加成功", { type: "success" });
        onSearch();
      } else {
        message("添加变更记录失败", { type: "error" });
      }
    } catch (error) {
      message("请求失败", { type: "error" });
    }
  } else {
    message("请输入变更记录", { type: "warning" });
  }
};

onMounted(() => {
  onSearch();
});
</script>

<template>
  <div class="main">
    <el-card class="add-record-card">
      <el-form-item label="新增记录" class="add-record-form">
        <el-input
          v-model="content"
          placeholder="输入变更记录"
          clearable
          type="textarea"
          :rows="4"
          maxlength="3000"
          show-word-limit
          class="input-record"
        />
        <el-button type="primary" class="add-button" @click="addRecord"
          >添加记录</el-button
        >
      </el-form-item>
    </el-card>

    <el-card class="search-card">
      <el-form
        ref="formRef"
        :inline="true"
        :model="form"
        class="search-form"
        @submit.prevent
      >
        <el-form-item label="时间范围" prop="dateRange">
          <el-date-picker
            v-model="form.dateRange"
            type="datetimerange"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            range-separator="至"
            clearable
            @change="onSearch"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">搜索</el-button>
        </el-form-item>
        <el-form-item>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <PureTableBar
      title="解析记录"
      :columns="changeLogColumns"
      @refresh="onSearch"
    >
      <template v-slot="{ size, dynamicColumns }">
        <div class="table-wrapper">
          <pure-table
            ref="tableRef"
            row-key="id"
            align-whole="left"
            table-layout="auto"
            :loading="loading"
            :size="size"
            adaptive
            :adaptiveConfig="{ offsetBottom: 108 }"
            :data="dataList"
            :columns="dynamicColumns"
            :pagination="{ ...pagination, size }"
            :header-cell-style="{
              color: '#303133',
              fontWeight: '600',
              borderBottom: '2px solid #e4e7ed'
            }"
            :row-style="{
              cursor: 'pointer',
              transition: 'background-color 0.2s ease',
              backgroundColor: 'white'
            }"
            :cell-style="{
              padding: '12px 10px'
            }"
            border
            stripe
            highlight-current-row
            @page-size-change="handleSizeChange"
            @page-current-change="handleCurrentChange"
          >
            <template #empty>
              <el-empty
                description="暂无数据"
                :image-size="120"
                style="padding: 40px 0"
              />
            </template>
          </pure-table>
        </div>
      </template>
    </PureTableBar>
  </div>
</template>

<style scoped lang="scss">
.main {
  padding: 20px;
  border-radius: 12px;
}

.search-card,
.add-record-card {
  margin-bottom: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 8%);
  transition: box-shadow 0.3s ease;

  &:hover {
    box-shadow: 0 6px 20px rgb(0 0 0 / 12%);
  }
}

.input-record {
  width: 100%;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.add-button {
  margin-top: 10px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;

  :deep(.el-form-item) {
    margin-bottom: 0;
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: #606266;
  }

  .el-input {
    width: 100%;
    max-width: 350px;

    :deep(input) {
      border-color: #e4e7ed;
      border-radius: 8px;

      &:focus {
        border-color: #409eff;
        box-shadow: 0 0 0 1px rgb(64 158 255 / 20%);
      }
    }
  }
}

.table-wrapper {
  overflow-x: auto;
}

.search-button,
.reset-button {
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
    transform: translateY(-2px);
  }
}

.search-button {
  color: white;
  background-color: #409eff;
  border-color: #409eff;
}

:deep(.pure-table) {
  overflow: hidden;
  border: 1px solid #e4e7ed;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 4%);
}

@media (width <= 768px) {
  .search-form {
    flex-direction: column;
    align-items: stretch;
  }

  .el-input {
    width: 100%;
  }
}
</style>
