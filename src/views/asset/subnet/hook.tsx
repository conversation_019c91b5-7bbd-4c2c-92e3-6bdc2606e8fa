import dayjs from "dayjs";
import type { PaginationProps } from "@pureadmin/table";
import { reactive, ref, onMounted } from "vue";
import { message } from "@/utils/message";
import { getSubnetsAPI, type Subnet } from "@/api/asset/subnet";
import {
  getAllCloudAccountsAPI,
  type CloudAccount
} from "@/api/asset/cloud-account";
import { getAllDatacentersAPI, type Datacenter } from "@/api/asset/datacenter";
import { CloudTypes, CloudTypesColors } from "@/config/enum";

export function useRole() {
  const form = reactive({
    account_id: undefined,
    datacenter_id: undefined,
    keyword: undefined,
    ip: undefined,
    cidr: undefined
  });
  const dataList = ref<Subnet[]>([]);
  const loading = ref<boolean>(true);
  const accounts = ref<CloudAccount[]>([]);
  const datacenters = ref<Datacenter[]>([]);
  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 5,
    currentPage: 1,
    background: true,
    pageSizes: [5, 10, 20, 30, 40, 50, 100]
  });
  const columns: TableColumnList = [
    {
      label: "账户/数据中心",
      prop: "account",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <div style="white-space: pre-line;">
          <p>
            <el-text type="primary" style="margin: 10px;">
              <b>{row.account}</b>
            </el-text>
          </p>
          <p style="margin-top: 10px;margin-bottom: 10px;">
            <el-text type={CloudTypesColors.get(row.cloud_type)}>
              {CloudTypes.get(row.cloud_type)}
            </el-text>
            <el-text type="info" style="margin: 10px;">
              {row.datacenter}
            </el-text>
          </p>
          <p>
            <el-text type="info">可用区：{row.zone_id}</el-text>
          </p>
        </div>
      )
    },
    {
      label: "名称",
      prop: "name",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <div style="white-space: pre-line;">
          {row.name !== "" && <p>名称：{row.name}</p>}
          <p style="margin-top: 10px; margin-bottom: 10px;">
            <el-text type="info" style="margin-right: 10px;">
              ID：{row.v_switch_id}
            </el-text>
          </p>
          <p>
            <el-text type="info" style="margin-right: 10px;">
              VPC ID：{row.vpc_id}
            </el-text>
          </p>
        </div>
      )
    },
    {
      label: "地址",
      prop: "ip",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <div style="white-space: pre-line;">
          <p>{row.ipv4_cidr}</p>
          <p>{row.ipv6_cidr}</p>
        </div>
      )
    },
    {
      label: "资源组",
      prop: "resource_group_name",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <div style="white-space: pre-line;">
          {row.resource_group_name !== "" && <p>{row.resource_group_name}</p>}
          {row.resource_group_id !== "" && <p>{row.resource_group_id}</p>}
        </div>
      )
    },
    {
      label: "描述",
      prop: "description",
      minWidth: 100,
      cellRenderer: ({ row }) => <p>{row.description}</p>
    },
    {
      label: "时间",
      prop: "sync_time",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <div style="white-space: normal;">
          <p>创建时间：{dayjs(row.created_at).format("YYYY-MM-DD HH:mm:ss")}</p>
          <p>
            同步时间：
            <b>{dayjs(row.sync_time).format("YYYY-MM-DD HH:mm:ss")}</b>
          </p>
        </div>
      )
    }
  ];

  function handleSizeChange(val: number) {
    pagination.pageSize = val;
    onSearch();
  }

  function handleCurrentChange(val: number) {
    pagination.currentPage = val;
    onSearch();
  }

  async function onSearch() {
    loading.value = true;
    getSubnetsAPI({
      page: pagination.currentPage,
      limit: pagination.pageSize,
      keyword: form.keyword,
      account_id: form.account_id,
      datacenter_id: form.datacenter_id,
      cidr: form.cidr,
      ip: form.ip
    })
      .then(res => {
        if (res.success) {
          dataList.value = res.data;
          pagination.total = res.count;
          pagination.pageSize = pagination.pageSize;
          pagination.currentPage = pagination.currentPage;
        } else {
          dataList.value = [];
          pagination.total = 0;
          pagination.pageSize = pagination.pageSize;
          pagination.currentPage = pagination.currentPage;
        }
      })
      .catch(() => {
        message("请求失败", { type: "error" });
      })
      .finally(() => {
        loading.value = false;
      });
  }

  function syncAllAccounts() {
    getAllCloudAccountsAPI()
      .then(res => {
        if (res.success) {
          accounts.value = res.data;
        } else {
          message(res.msg, { type: "error" });
        }
      })
      .catch(error => {
        message(error, { type: "error" });
      });
  }

  function syncAllDatacenters() {
    getAllDatacentersAPI()
      .then(res => {
        if (res.success) {
          datacenters.value = res.data;
        } else {
          message(res.msg, { type: "error" });
        }
      })
      .catch(error => {
        message(error, { type: "error" });
      });
  }
  const resetForm = formEl => {
    if (!formEl) return;
    formEl.resetFields();
    onSearch();
  };

  onMounted(() => {
    onSearch();
    syncAllAccounts();
    syncAllDatacenters();
  });

  return {
    form,
    loading,
    columns,
    dataList,
    pagination,
    onSearch,
    resetForm,
    handleSizeChange,
    handleCurrentChange,
    accounts,
    syncAllAccounts,
    datacenters,
    syncAllDatacenters
  };
}
