import dayjs from "dayjs";
import {
  getCloudAccountList,
  type UpdateCloudAccountForm,
  type CloudAccount,
  updateCloudAccount,
  addCloudAccountAPI,
  deleteCloudAccountAPI
} from "@/api/asset/cloud-account";
import type { PaginationProps } from "@pureadmin/table";
import { reactive, ref, onMounted, h } from "vue";
import { message } from "@/utils/message";
import { CloudTypes, CloudTypesColors } from "@/config/enum";
import { addDrawer } from "@/components/ReDrawer";
import { addDialog } from "@/components/ReDialog";
import Uform from "./Uform.vue";
import { RouterLink } from "vue-router";
import AccountMouthlyBill from "../bill/AccountMouthlyBill.vue";
export function useRole() {
  const form = reactive({
    keyword: undefined,
    cloud_type: undefined
  });
  const dataList = ref([]);
  const loading = ref(true);

  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true,
    pageSizes: [10, 20, 30, 40, 50, 100]
  });
  const columns: TableColumnList = [
    {
      type: "selection",
      align: "left",
      width: 50,
      fixed: "left"
    },
    {
      label: "基本信息",
      prop: "name",
      minWidth: 260,
      cellRenderer: ({ row }) => (
        <div class="account-info">
          <div class="name-row">
            <span class="account-name">{row.name}</span>
            <el-tag
              type={CloudTypesColors.get(row.cloud_type)}
              size="small"
              effect="light"
              class="type-tag"
            >
              {CloudTypes.get(row.cloud_type)}
            </el-tag>
          </div>
          <div class="meta-row">
            <el-tooltip content="AccessKey" placement="top">
              <span class="access-key">{row.access_key}</span>
            </el-tooltip>
          </div>
        </div>
      )
    },
    {
      label: "资源统计",
      prop: "resources",
      minWidth: 380,
      cellRenderer: ({ row }) => (
        <div class="resource-stats">
          <div class="stat-item">
            <span class="label">主机：</span>
            <RouterLink
              to={{ name: "主机列表", query: { account_id: row.id } }}
              class="value-link"
              style="color: #409eff; font-weight: bold; text-decoration: underline;"
            >
              {row.host_total || 0}
            </RouterLink>
          </div>
        </div>
      )
    },
    {
      label: "时间信息",
      prop: "time",
      minWidth: 200,
      cellRenderer: ({ row }) => (
        <div class="time-info">
          <div class="time-item">
            <span class="label">创建时间</span>
            <span class="value">
              {dayjs(row.created_at).format("YYYY-MM-DD HH:mm")}
            </span>
          </div>
          <div class="time-item">
            <span class="label">更新时间</span>
            <span class="value">
              {dayjs(row.updated_at).format("YYYY-MM-DD HH:mm")}
            </span>
          </div>
        </div>
      )
    },
    {
      label: "备注",
      prop: "remark",
      minWidth: 150,
      cellRenderer: ({ row }) => (
        <el-tooltip content={row.remark} placement="top" disabled={!row.remark}>
          <span class="remark-text">{row.remark || "-"}</span>
        </el-tooltip>
      )
    },
    {
      label: "操作",
      minWidth: 150,
      cellRenderer: ({ row }) => (
        <div class="action-buttons">
          <el-button
            type="primary"
            link
            class="flex items-center gap-1 !p-0"
            onClick={() => handleHostMonthlyBill(row.id, row.name, undefined)}
          >
            <iconify-icon-online icon="ri:bill-line" />
            账单
          </el-button>
          <el-button
            type="primary"
            link
            class="action-button"
            onClick={() => updateFunc(row)}
          >
            <iconify-icon-online icon="ep:edit" />
            编辑
          </el-button>
          <el-divider direction="vertical" />
          <el-button
            type="danger"
            link
            class="action-button"
            onClick={() => deleteFunc(row)}
          >
            <iconify-icon-online icon="ep:delete" />
            删除
          </el-button>
        </div>
      )
    }
  ];

  const handleHostMonthlyBill = (
    account_id: number,
    account_name: string,
    resource_id: string
  ) => {
    addDialog({
      title: "账户月度账单",
      hideFooter: true,
      draggable: true,
      contentRenderer: () =>
        h(AccountMouthlyBill, {
          account_id: account_id,
          account_name: account_name,
          resource_id: resource_id
        })
    });
  };
  const editForm = ref<UpdateCloudAccountForm>();
  const childrenRef = ref(null);
  function updateFunc(row: CloudAccount) {
    editForm.value = {
      name: row.name,
      cloud_type: row.cloud_type,
      remark: row.remark,
      access_key: row.access_key,
      access_secret: ""
    };
    addDrawer({
      headerRenderer: ({ titleId, titleClass }) => (
        <div class="flex items-center">
          <h4 id={titleId} class={titleClass}>
            编辑云账户：<span class="text-primary">{row.name}</span>
          </h4>
        </div>
      ),
      contentRenderer: () =>
        h(Uform, {
          formInline: {
            form: editForm.value
          },
          ref: childrenRef
        }),
      beforeSure: done => {
        if (childrenRef.value.ruleFormRef) {
          childrenRef.value.ruleFormRef
            .validate()
            .then(() => {
              updateCloudAccount(row.id, editForm.value)
                .then(res => {
                  if (res.success) {
                    message(res.msg, { type: "success" });
                    done();
                    onSearch();
                  } else {
                    message(res.msg, { type: "error" });
                  }
                })
                .catch(error => {
                  message(error, { type: "error" });
                });
            })
            .catch(() => {
              message("请检查表单数据", { type: "error" });
            });
        } else {
          message("请检查表单数据", { type: "error" });
        }
      }
    });
  }

  function addFunc() {
    editForm.value = {
      name: "",
      cloud_type: 2,
      remark: "",
      access_key: "",
      access_secret: ""
    };
    addDrawer({
      headerRenderer: ({ titleId, titleClass }) => (
        <div class="flex items-center">
          <h4 id={titleId} class={titleClass}>
            添加云账户
          </h4>
        </div>
      ),
      contentRenderer: () =>
        h(Uform, {
          formInline: {
            form: editForm.value
          },
          ref: childrenRef
        }),
      beforeSure: done => {
        if (childrenRef.value.ruleFormRef) {
          childrenRef.value.ruleFormRef
            .validate((valid: boolean) => {
              if (valid) {
                addCloudAccountAPI(editForm.value)
                  .then(res => {
                    if (res.success) {
                      message(res.msg, { type: "success" });
                      done();
                      onSearch();
                      editForm.value = undefined;
                    } else {
                      message(res.msg, { type: "error" });
                    }
                  })
                  .catch(error => {
                    message(error, { type: "error" });
                  });
              } else {
                message("请检查表单数据", { type: "error" });
              }
            })
            .catch(error => {
              message(error, { type: "error" });
            });
        }
      }
    });
  }

  function deleteFunc(row: any) {
    addDialog({
      title: "",
      width: 500,
      sureBtnLoading: true,
      contentRenderer: () => (
        <p>
          确认删除：<b style="color:red"> {row.name}</b>
        </p>
      ),
      beforeSure: done => {
        deleteCloudAccountAPI(row.id)
          .then(res => {
            if (res.success) {
              message(res.msg, { type: "success" });
              onSearch();
              done();
            } else {
              message(res.msg, { type: "error" });
            }
          })
          .catch(error => {
            message("请求失败" + error, { type: "error" });
          });
      }
    });
  }
  function handleSizeChange(val: number) {
    pagination.pageSize = val;
    onSearch();
  }

  function handleCurrentChange(val: number) {
    pagination.currentPage = val;
    onSearch();
  }

  async function onSearch() {
    loading.value = true;
    getCloudAccountList({
      page: pagination.currentPage,
      limit: pagination.pageSize,
      keyword: form.keyword,
      cloud_type: form.cloud_type
    })
      .then(res => {
        if (res.success) {
          dataList.value = res.data;
          pagination.total = res.count;
          pagination.pageSize = pagination.pageSize;
          pagination.currentPage = pagination.currentPage;
        } else {
          dataList.value = [];
          pagination.total = 0;
          pagination.pageSize = pagination.pageSize;
          pagination.currentPage = pagination.currentPage;
        }
      })
      .catch(() => {
        message("请求失败", { type: "error" });
      })
      .finally(() => {
        loading.value = false;
      });
  }

  const resetForm = formEl => {
    if (!formEl) return;
    formEl.resetFields();
    onSearch();
  };

  onMounted(() => {
    onSearch();
  });

  return {
    form,
    loading,
    columns,
    dataList,
    pagination,
    onSearch,
    resetForm,
    handleSizeChange,
    handleCurrentChange,
    addFunc
  };
}
