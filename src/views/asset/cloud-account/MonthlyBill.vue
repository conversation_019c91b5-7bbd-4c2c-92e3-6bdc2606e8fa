<template>
  <div class="monthly-bill">
    <el-card class="account-card" shadow="hover">
      <el-card-header>
        <h3 class="account-header">选择的账户</h3>
        <template v-for="account in props.accounts" :key="account.id">
          <el-tag
            class="account-item"
            type="primary"
            style="margin: 10px"
            size="large"
            effect="dark"
          >
            <i class="ri-account-circle-line" /> {{ " " }}{{ account.name }}
          </el-tag>
        </template>
      </el-card-header>
      <el-card-body>
        <div class="sync-container">
          <h3 class="sync-title">请选择月份：</h3>
          <el-date-picker
            v-model="bill_cycle"
            type="month"
            value-format="YYYY-MM"
            class="sync-date"
            placeholder="选择月份"
            style="margin: 10px"
          />
          <el-button
            type="primary"
            class="sync-button"
            style="margin: 10px"
            @click="syncBill"
          >
            同步
          </el-button>
          <RouterLink :to="{ name: '月份账单' }" class="monthly-bill-link">
            查看月份账单
          </RouterLink>
        </div>
      </el-card-body>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { ElDatePicker } from "element-plus";
import {
  syncAccountMonthlyBillAPI,
  type CloudAccount
} from "@/api/asset/cloud-account";
import { message } from "@/utils/message";

const props = defineProps({
  accounts: {
    type: Array as PropType<CloudAccount[]>,
    required: true
  }
});

const bill_cycle = ref(
  new Date().toISOString().split("T")[0].split("-").slice(0, 2).join("-")
);

const syncBill = () => {
  props.accounts.forEach(account => {
    syncAccountMonthlyBillAPI(account.id, {
      bill_cycle: bill_cycle.value
    }).then(res => {
      if (res.success) {
        message(
          account.name + " 同步 " + bill_cycle.value + " 账单：" + res.msg,
          {
            type: "success"
          }
        );
      } else {
        message(
          account.name + " 同步 " + bill_cycle.value + " 账单：" + res.msg,
          {
            type: "error"
          }
        );
      }
    });
  });
};
</script>

<style scoped lang="scss">
.account-card {
  margin: 20px;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
}

.account-item {
  margin: 10px;
  border-radius: 20px;
}

.sync-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 20px;
}

.sync-title {
  margin: 10px;
  font-weight: bold;
}

.sync-date {
  width: 200px;
  margin: 10px;
}

.sync-button {
  padding: 10px 20px;
  margin: 10px;
  border-radius: 20px;
}

.monthly-bill-link {
  margin: 10px;
  color: #409eff;
  text-decoration: none;
}

@media (width <= 768px) {
  .sync-container {
    align-items: stretch;
  }

  .sync-date,
  .sync-button {
    width: 100%;
  }
}
</style>
