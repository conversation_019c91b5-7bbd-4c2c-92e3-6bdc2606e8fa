<script setup lang="ts">
import { h, ref } from "vue";
import { useRole } from "./hook";
import { CloudTypes } from "@/config/enum";
import { PureTableBar } from "@/components/RePureTableBar";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import type { TableInstance } from "element-plus";

import Refresh from "@iconify-icons/ep/refresh";
import Plus from "@iconify-icons/ep/plus";
import { syncCloudAccountAsset } from "@/api/asset/cloud-account";
import { message } from "@/utils/message";
import { addDialog } from "@/components/ReDialog";
import MouthlyBill from "./MonthlyBill.vue";

defineOptions({
  name: "CloudAccount"
});

const formRef = ref<TableInstance>();
const tableRef = ref();
const {
  form,
  loading,
  columns,
  dataList,
  pagination,
  onSearch,
  resetForm,
  handleSizeChange,
  handleCurrentChange,
  addFunc
} = useRole();
function syncData(assetType: string) {
  if (multipleSelection.value.length === 0) {
    message("请选择要同步的云账号", {
      type: "warning"
    });
  } else {
    if (assetType === "monthly-bill") {
      addDialog({
        title: "同步月度账单",
        draggable: true,
        width: "30%",
        contentRenderer: () =>
          h(MouthlyBill, { accounts: multipleSelection.value }),
        hideFooter: true
      });
    } else {
      multipleSelection.value.map(row => {
        syncCloudAccountAsset(row.id, assetType).then(res => {
          if (res.success) {
            message(row.name + " 同步 " + assetType + "：" + res.msg, {
              type: "success"
            });
          } else {
            message(row.name + " 同步 " + assetType + "：" + res.msg, {
              type: "error"
            });
          }
        });
      });
    }
  }
}
const multipleSelection = ref([]);

const handleSelectionChange = val => {
  multipleSelection.value = val;
};
</script>

<template>
  <div class="main">
    <el-card class="search-card">
      <el-form ref="formRef" :inline="true" :model="form" class="search-form">
        <el-form-item label="关键字" prop="keyword">
          <el-input
            v-model="form.keyword"
            placeholder="请输入关键字"
            clearable
            class="!w-[150px]"
            @keyup.enter="onSearch"
          />
        </el-form-item>
        <el-form-item label="账户类型" prop="cloud_type">
          <el-select
            v-model="form.cloud_type"
            class="!w-[150px]"
            filterable
            clearable
            @change="onSearch"
          >
            <el-option
              v-for="(item, index) in CloudTypes"
              :key="index"
              :label="item[1]"
              :value="item[0]"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            :icon="useRenderIcon('ri:search-line')"
            :loading="loading"
            class="search-button"
            @click="onSearch"
          >
            搜索
          </el-button>
          <el-button
            :icon="useRenderIcon(Refresh)"
            class="reset-button"
            @click="resetForm(formRef)"
          >
            重置
          </el-button>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            link
            :icon="useRenderIcon(Plus)"
            class="add-button"
            @click="addFunc"
          >
            添加
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card class="batch-actions-card">
      <div class="batch-actions-content">
        <div class="actions-container">
          <el-button-group>
            <el-button
              type="primary"
              :icon="useRenderIcon(Refresh)"
              :disabled="multipleSelection.length === 0"
              class="sync-button"
              @click="syncData('ecs')"
            >
              同步主机
            </el-button>
            <el-button
              type="primary"
              :icon="useRenderIcon(Refresh)"
              :disabled="multipleSelection.length === 0"
              class="sync-button"
              @click="syncData('loadbalancer')"
            >
              同步负载均衡
            </el-button>
            <el-button
              type="primary"
              :icon="useRenderIcon(Refresh)"
              :disabled="multipleSelection.length === 0"
              class="sync-button"
              @click="syncData('domain')"
            >
              同步域名
            </el-button>
            <el-button
              type="primary"
              :icon="useRenderIcon(Refresh)"
              :disabled="multipleSelection.length === 0"
              class="sync-button"
              @click="syncData('eip')"
            >
              同步EIP
            </el-button>
            <el-button
              type="primary"
              :icon="useRenderIcon(Refresh)"
              :disabled="multipleSelection.length === 0"
              class="sync-button"
              @click="syncData('region')"
            >
              同步数据中心
            </el-button>
            <el-button
              type="primary"
              :icon="useRenderIcon(Refresh)"
              :disabled="multipleSelection.length === 0"
              class="sync-button"
              @click="syncData('resource-group')"
            >
              同步资源组
            </el-button>
            <el-button
              type="primary"
              :icon="useRenderIcon(Refresh)"
              :disabled="multipleSelection.length === 0"
              class="sync-button"
              @click="syncData('monthly-bill')"
            >
              同步账单
            </el-button>
            <el-button
              type="primary"
              :icon="useRenderIcon(Refresh)"
              :disabled="multipleSelection.length === 0"
              class="sync-button"
              @click="syncData('ddos')"
            >
              同步DDOS
            </el-button>
            <el-button
              type="primary"
              :icon="useRenderIcon(Refresh)"
              :disabled="multipleSelection.length === 0"
              class="sync-button"
              @click="syncData('subnet')"
            >
              同步子网
            </el-button>
          </el-button-group>
          <div v-if="multipleSelection.length > 0" class="selected-info">
            已选择
            <span class="selected-count">{{ multipleSelection.length }}</span>
            个账户
          </div>
        </div>
      </div>
    </el-card>

    <PureTableBar title="云账户列表" :columns="columns" @refresh="onSearch">
      <template v-slot="{ size, dynamicColumns }">
        <div class="table-wrapper">
          <pure-table
            ref="tableRef"
            row-key="id"
            align-whole="left"
            table-layout="auto"
            :loading="loading"
            :size="size"
            :minHeight="500"
            :data="dataList"
            :columns="dynamicColumns"
            :pagination="{ ...pagination, size }"
            :header-cell-style="{
              color: '#303133',
              fontWeight: '600',
              borderBottom: '2px solid #e4e7ed'
            }"
            :row-style="{
              cursor: 'pointer',
              transition: 'background-color 0.2s ease'
            }"
            :cell-style="{
              padding: '12px 10px'
            }"
            border
            stripe
            highlight-current-row
            @page-size-change="handleSizeChange"
            @page-current-change="handleCurrentChange"
            @selection-change="handleSelectionChange"
          >
            <template #empty>
              <el-empty
                description="暂无数据"
                :image-size="120"
                style="padding: 40px 0"
              />
            </template>
          </pure-table>
        </div>
      </template>
    </PureTableBar>
  </div>
</template>

<style scoped lang="scss">
.main {
  padding: 20px;
  border-radius: 12px;
}

.search-card,
.batch-actions-card {
  margin-bottom: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 8%);
  transition: box-shadow 0.3s ease;

  &:hover {
    box-shadow: 0 6px 20px rgb(0 0 0 / 12%);
  }
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;

  :deep(.el-form-item) {
    margin-bottom: 0;
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: #606266;
  }

  .el-input,
  .el-select {
    width: 100%;
    max-width: 150px;

    :deep(input),
    :deep(.el-input__wrapper) {
      border-color: #e4e7ed;
      border-radius: 8px;

      &:focus {
        border-color: #409eff;
        box-shadow: 0 0 0 1px rgb(64 158 255 / 20%);
      }
    }
  }
}

.table-wrapper {
  width: 100%;
  overflow-x: auto;
}

.search-button,
.reset-button,
.add-button {
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
    transform: translateY(-2px);
  }
}

.search-button {
  color: white;
  background-color: #409eff;
  border-color: #409eff;
}

:deep(.pure-table) {
  min-width: 600px;
  overflow: hidden;
  border: 1px solid #e4e7ed;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 4%);
}

@media (width <= 768px) {
  .search-form {
    flex-direction: column;
    align-items: stretch;
  }

  .el-input,
  .el-select {
    width: 100%;
  }

  .batch-actions-card {
    .batch-actions-content {
      flex-direction: column;
      align-items: flex-start;
    }

    .selected-info {
      margin-top: 10px;
    }
  }

  .table-wrapper {
    overflow-x: auto;
  }
}

.batch-actions-card {
  margin-bottom: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 8%);
  transition: box-shadow 0.3s ease;

  &:hover {
    box-shadow: 0 6px 20px rgb(0 0 0 / 12%);
  }

  .batch-actions-content {
    padding: 16px 24px;

    .actions-container {
      display: flex;
      gap: 16px;
      align-items: center;

      .el-button-group {
        display: flex;
        flex-wrap: wrap;
        gap: 12px;
      }

      .selected-info {
        font-size: 14px;
        color: #606266;
        white-space: nowrap;

        .selected-count {
          margin: 0 4px;
          font-weight: 600;
          color: var(--el-color-primary);
        }
      }
    }

    .sync-button {
      display: flex;
      gap: 6px;
      align-items: center;
      padding: 8px 16px;
      font-size: 14px;
      border-radius: 6px;
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
        transform: translateY(-2px);
      }

      &:disabled {
        cursor: not-allowed;
        box-shadow: none;
        opacity: 0.5;
        transform: none;
      }
    }
  }
}
</style>
