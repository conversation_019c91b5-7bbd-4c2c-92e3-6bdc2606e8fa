<template>
  <div class="form-container">
    <el-card shadow="never">
      <template #header>
        <div class="form-header">
          <div class="header-left">
            <el-icon :size="20" color="var(--el-color-primary)">
              <component :is="isEdit ? 'Edit' : 'Plus'" />
            </el-icon>
            <span class="title">
              {{ isEdit ? "编辑云账户" : "新增云账户" }}
              <template v-if="isEdit">
                <span class="divider">-</span>
                <span class="account-name">{{ newFormInline.form.name }}</span>
              </template>
            </span>
          </div>
          <div v-if="isEdit" class="header-right">
            <el-tag
              :type="getCloudTypeTag(newFormInline.form.cloud_type)"
              size="small"
            >
              {{ getCloudTypeName(newFormInline.form.cloud_type) }}
            </el-tag>
          </div>
        </div>
      </template>

      <el-form
        ref="ruleFormRef"
        :model="newFormInline.form"
        :rules="rules"
        label-position="top"
        class="cloud-account-form"
        size="large"
        status-icon
      >
        <el-row :gutter="20">
          <el-col :xs="24" :sm="12">
            <el-form-item label="名称" prop="name">
              <el-input
                v-model="newFormInline.form.name"
                placeholder="请输入云账户名称"
                :prefix-icon="useRenderIcon('ri:cloud-line')"
              />
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="12">
            <el-form-item label="类型" prop="cloud_type">
              <el-select
                v-model="newFormInline.form.cloud_type"
                placeholder="请选择云账户类型"
                class="w-full"
              >
                <el-option
                  v-for="(item, index) in CloudTypes"
                  :key="index"
                  :label="item[1]"
                  :value="item[0]"
                >
                  <div class="cloud-type-option">
                    <el-icon><Monitor /></el-icon>
                    <span>{{ item[1] }}</span>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-divider content-position="left">
          <div class="divider-content">
            <el-icon><Key /></el-icon>
            <span>访问凭证</span>
          </div>
        </el-divider>

        <el-row :gutter="20">
          <el-col :xs="24" :sm="12">
            <el-form-item label="密钥Key" prop="access_key">
              <el-input
                v-model="newFormInline.form.access_key"
                placeholder="请输入访问密钥Key"
                :prefix-icon="useRenderIcon('ri:key-line')"
              />
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="12">
            <el-form-item label="密钥Secret" prop="access_secret">
              <el-input
                v-model="newFormInline.form.access_secret"
                type="password"
                show-password
                autocomplete="off"
                placeholder="留空表示不修改"
                :prefix-icon="useRenderIcon('ri:lock-line')"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="newFormInline.form.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息（选填）"
            resize="none"
          />
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { Key, Monitor } from "@element-plus/icons-vue";
import type { FormInstance, FormRules } from "element-plus";
import type { UpdateCloudAccountForm } from "@/api/asset/cloud-account";
import { CloudTypes } from "@/config/enum";

export interface FormProps {
  formInline: {
    form: UpdateCloudAccountForm;
  };
}

const props = withDefaults(defineProps<FormProps>(), {
  formInline: () => ({ row: undefined, form: undefined })
});

const newFormInline = ref(props.formInline);
const ruleFormRef = ref<FormInstance>();

const isEdit = computed(() => newFormInline.value.form.name !== "");

const rules = ref<FormRules>({
  name: [
    { required: true, message: "请输入云账户名称", trigger: "blur" },
    { max: 255, message: "长度不能超过255个字符", trigger: "blur" }
  ],
  cloud_type: [
    { required: true, message: "请选择云账户类型", trigger: "change" }
  ],
  access_key: [{ max: 255, message: "长度不能超过255个字符", trigger: "blur" }],
  remark: [{ max: 255, message: "长度不能超过255个字符", trigger: "blur" }]
});

defineExpose({ ruleFormRef });

const getCloudTypeTag = (type: number) => {
  const tagMap = {
    aliyun: "danger",
    tencent: "primary",
    huawei: "warning",
    aws: "success"
  };
  return tagMap[type] || "info";
};

const getCloudTypeName = (type: number) => {
  const found = Array.from(CloudTypes).find(([key]) => key === type);
  return found ? found[1] : type;
};
</script>

<style lang="scss" scoped>
.form-container {
  .form-header {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .header-left {
      display: flex;
      gap: 8px;
      align-items: center;

      .title {
        display: flex;
        align-items: center;
        font-size: 16px;
        font-weight: 500;
        color: var(--el-text-color-primary);

        .divider {
          margin: 0 8px;
          color: var(--el-text-color-secondary);
        }

        .account-name {
          font-weight: 600;
          color: var(--el-color-primary);
        }
      }
    }

    .header-right {
      .el-tag {
        font-weight: 500;
      }
    }
  }

  .cloud-account-form {
    padding: 20px 0;

    :deep(.el-form-item__label) {
      padding-bottom: 8px;
      font-weight: 500;
    }

    .cloud-type-option {
      display: flex;
      gap: 8px;
      align-items: center;

      .el-icon {
        color: var(--el-color-primary);
      }
    }
  }

  .divider-content {
    display: flex;
    gap: 4px;
    align-items: center;
    font-size: 14px;
    color: var(--el-text-color-secondary);

    .el-icon {
      font-size: 16px;
    }
  }

  :deep(.el-input__wrapper) {
    padding: 4px 11px;
  }

  :deep(.el-textarea__inner) {
    padding: 8px 11px;
  }
}

@media (width <= 768px) {
  .cloud-account-form {
    .el-col {
      width: 100%;
    }
  }
}
</style>
