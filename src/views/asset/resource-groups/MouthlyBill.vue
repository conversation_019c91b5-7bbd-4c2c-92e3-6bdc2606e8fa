<template>
  <div>
    <div class="date-picker-container">
      <el-date-picker
        v-model="form.date_range"
        type="monthrange"
        format="YYYY-MM"
        value-format="YYYY-MM"
        placeholder="选择一段时间"
        clearable
        range-separator="至"
        start-placeholder="开始月份"
        end-placeholder="结束月份"
      />
    </div>
    <div v-if="!billCycles.length" class="no-data-message">没有数据</div>
    <div ref="chartRef" class="chart-container" />
  </div>
</template>

<script setup lang="ts">
import { ref, onBeforeMount, reactive, watch } from "vue";
import * as echarts from "echarts";
import { getMonthlyBillAmountAPI } from "@/api/asset/resource-group";
import { message } from "@/utils/message";
import dayjs from "dayjs";

const props = defineProps<{
  id: number;
  resource_group_name: string;
}>();

const form = reactive({
  date_range: [
    dayjs().subtract(12, "month").format("YYYY-MM"),
    dayjs().format("YYYY-MM")
  ]
});

const chartRef = ref<HTMLDivElement | null>(null);
const billCycles = ref<string[]>([]);
const amounts = ref<number[]>([]);

const getMonthlyBillAmount = async () => {
  const [start_time, end_time] = form.date_range;
  const res = await getMonthlyBillAmountAPI(props.id, {
    start_time,
    end_time
  });
  if (res.success && res.data) {
    billCycles.value = res.data.map(item => item.bill_cycle);
    amounts.value = res.data.map(item => item.amount);
    updateChart(); // 更新图表
  } else {
    message(res.msg || "获取数据失败", { type: "error" });
  }
};

const updateChart = () => {
  if (chartRef.value) {
    const myChart = echarts.init(chartRef.value);
    const option = {
      title: {
        text: props.resource_group_name
          ? `${props.resource_group_name} 月度账单`
          : "资源组月度账单",
        left: "center",
        textStyle: {
          fontSize: 20,
          fontWeight: "bold",
          color: "#333"
        }
      },
      tooltip: {
        trigger: "item",
        formatter: "{a} <br/>{b}: {c}元"
      },
      xAxis: {
        type: "category",
        data: billCycles.value,
        axisLabel: {
          rotate: 45,
          margin: 10,
          textStyle: {
            color: "#666"
          }
        }
      },
      yAxis: {
        type: "value",
        axisLabel: {
          formatter: "{value} 元",
          textStyle: {
            color: "#666"
          }
        }
      },
      series: [
        {
          name: "金额",
          type: "bar",
          data: amounts.value.map(amount => amount.toFixed(2)), // 保留2位小数
          itemStyle: {
            color: "#4CAF50"
          },
          label: {
            show: true,
            position: "top",
            formatter: "{c}元" // 在柱状图上显示金额
          }
        },
        {
          name: "趋势线",
          type: "line",
          data: amounts.value.map(amount => amount.toFixed(2)), // 保留2位小数
          itemStyle: {
            color: "#FF5733"
          },
          label: {
            show: false
          },
          smooth: true // 平滑曲线
        }
      ]
    };
    myChart.setOption(option);
    // 自适应页面大小
    window.addEventListener("resize", () => {
      myChart.resize();
    });
  }
};

onBeforeMount(() => {
  getMonthlyBillAmount();
});

// 监视数据变化以更新图表
watch(
  () => form.date_range,
  () => {
    getMonthlyBillAmount();
  },
  { deep: true }
);
</script>

<style scoped>
.date-picker-container {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px; /* 添加底部间距 */
}

.chart-container {
  width: 100%; /* 自适应宽度 */
  height: 400px; /* 增加图表高度 */
  background-color: #f9f9f9; /* 设置背景色 */
  border-radius: 8px; /* 圆角 */
  box-shadow: 0 2px 10px rgb(0 0 0 / 10%); /* 添加阴影 */
}

.no-data-message {
  margin-top: 20px; /* 添加顶部间距 */
  color: #999;
  text-align: center;
}
</style>
