import dayjs from "dayjs";
import {
  getResourceGroupesAPI,
  type ResourceGroup
} from "@/api/asset/resource-group";
import type { PaginationProps } from "@pureadmin/table";
import { reactive, ref, onMounted, h } from "vue";
import { message } from "@/utils/message";
import { RouterLink } from "vue-router";
import {
  type CloudAccount,
  getAllCloudAccountsAPI
} from "@/api/asset/cloud-account";
import { addDialog } from "@/components/ReDialog";
import MonthlyBill from "./MouthlyBill.vue";
export function useRole() {
  const form = reactive({
    keyword: undefined,
    account_id: undefined
  });
  const dataList = ref<ResourceGroup[]>([]);
  const loading = ref(true);

  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true,
    pageSizes: [10, 20, 30, 40, 50, 100]
  });

  const columns: TableColumnList = [
    {
      label: "名称",
      prop: "group_name",
      minWidth: 150,
      cellRenderer: ({ row }) => (
        <div>
          <el-text type="primary" style="font-weight:bold">
            {row.group_display_name}
          </el-text>
          <br />
          <el-text type="info">{row.group_name}</el-text>
        </div>
      )
    },
    {
      label: "资源组ID",
      prop: "group_id",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <el-text type="info" class="datacenter-code">
          {row.group_id}
        </el-text>
      )
    },
    {
      label: "账户",
      prop: "account",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <RouterLink
          to={{ name: "主机列表", query: { account_id: row.account?.id } }}
          style={{
            color: "#409eff",
            fontWeight: "bold",
            textDecoration: "underline"
          }}
        >
          <div class="datacenter-account-id">
            {row.account?.name || "未知账户"}
          </div>
        </RouterLink>
      )
    },
    {
      label: "主机数量",
      prop: "host_total",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <RouterLink
          to={{ name: "主机列表", query: { resource_group_id: row.group_id } }}
          style={{
            color: "#409eff",
            fontWeight: "bold",
            textDecoration: "underline"
          }}
        >
          <span>{row.host_total || 0}</span>
        </RouterLink>
      )
    },
    {
      label: "时间信息",
      prop: "time",
      minWidth: 240,
      cellRenderer: ({ row }) => (
        <div class="time-info">
          <div class="time-item">
            <span class="label">创建时间:</span>
            <span class="value">
              {dayjs(row.created_at).format("YYYY-MM-DD HH:mm:ss")}
            </span>
          </div>
          <div class="time-item">
            <span class="label">更新时间:</span>
            <span class="value">
              {dayjs(row.updated_at).format("YYYY-MM-DD HH:mm:ss")}
            </span>
          </div>
        </div>
      )
    },
    {
      label: "操作",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <div>
          <el-link
            type="primary"
            size="small"
            onClick={() => handleMonthlyBill(row)}
          >
            月度账单
          </el-link>
        </div>
      )
    }
  ];
  function handleMonthlyBill(row: ResourceGroup) {
    addDialog({
      title: `${row.group_display_name} - 月度账单`,
      hideFooter: true,
      contentRenderer: () =>
        h(MonthlyBill, {
          id: row.id,
          resource_group_name: row.group_display_name
        })
    });
  }
  function handleSizeChange(val: number) {
    pagination.pageSize = val;
    onSearch();
  }
  const accounts = ref<CloudAccount[]>([]);
  function syncAllAccounts() {
    getAllCloudAccountsAPI().then(res => {
      if (res.success) {
        accounts.value = res.data;
      } else {
        message(res.msg, { type: "error" });
      }
    });
  }
  function handleCurrentChange(val: number) {
    pagination.currentPage = val;
    onSearch();
  }

  async function onSearch() {
    loading.value = true;
    getResourceGroupesAPI({
      page: pagination.currentPage,
      limit: pagination.pageSize,
      keyword: form.keyword,
      account_id: form.account_id
    })
      .then(res => {
        if (res.success) {
          dataList.value = res.data;
          pagination.total = res.count;
          pagination.pageSize = pagination.pageSize;
          pagination.currentPage = pagination.currentPage;
        } else {
          dataList.value = [];
          pagination.total = 0;
          pagination.pageSize = pagination.pageSize;
          pagination.currentPage = pagination.currentPage;
        }
      })
      .catch(() => {
        message("请求失败", { type: "error" });
      })
      .finally(() => {
        loading.value = false;
      });
  }

  const resetForm = formEl => {
    if (!formEl) return;
    formEl.resetFields();
    onSearch();
  };

  onMounted(() => {
    onSearch();
    syncAllAccounts();
  });

  return {
    form,
    loading,
    columns,
    dataList,
    pagination,
    onSearch,
    resetForm,
    handleSizeChange,
    handleCurrentChange,
    accounts,
    syncAllAccounts
  };
}
