import dayjs from "dayjs";
import type { PaginationProps } from "@pureadmin/table";
import { reactive, ref, onMounted } from "vue";
import { message } from "@/utils/message";
import { getPublicIPsAPI, type PublicIP } from "@/api/asset/public-ip";
import {
  getAllCloudAccountsAPI,
  type CloudAccount
} from "@/api/asset/cloud-account";
import { getAllDatacentersAPI, type Datacenter } from "@/api/asset/datacenter";
import { CloudTypes, CloudTypesColors } from "@/config/enum";

export function useRole() {
  const form = reactive({
    account_id: undefined,
    datacenter_id: undefined,
    keyword: undefined,
    ip: undefined,
    cloud_type: undefined
  });
  const dataList = ref<PublicIP[]>([]);
  const loading = ref<boolean>(true);
  const accounts = ref<CloudAccount[]>([]);
  const datacenters = ref<Datacenter[]>([]);
  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true,
    pageSizes: [10, 20, 30, 40, 50, 100]
  });
  const columns: TableColumnList = [
    {
      label: "账户/数据中心",
      prop: "account",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <div style="white-space: pre-line;">
          <p>
            <el-text type="primary" style="margin: 10px;">
              <b>{row.account}</b>
            </el-text>
          </p>
          <p>
            <el-text
              style="margin: 10px;"
              type={CloudTypesColors.get(row.cloud_type)}
            >
              {CloudTypes.get(row.cloud_type)}
            </el-text>
            <el-text type="info" style="margin: 10px;">
              {row.datacenter}
            </el-text>
          </p>
        </div>
      )
    },
    {
      label: "名称",
      prop: "name",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <div style="white-space: pre-line;">
          {row.name !== "" && <p>名称：{row.name}</p>}
          <p>
            <el-text type="info" style="margin-right: 10px;">
              ID：{row.sn}
            </el-text>
            {row.cloud_type === 2 && (
              <el-link
                href={`https://vpc.console.aliyun.com/eip/${row.region_id}/eips/${row.sn}`}
                target="_blank"
                type="primary"
              >
                <iconify-icon-online icon="ep:link" />
              </el-link>
            )}
            {row.cloud_type === 3 && (
              <el-link
                href={`https://console.huaweicloud.com/vpc/?region=${row.region_id}&locale=zh-cn#/eip/eips/detail/basicInfo?eipId=${row.sn}`}
                target="_blank"
                type="primary"
              >
                <iconify-icon-online icon="ep:link" />
              </el-link>
            )}
          </p>
        </div>
      )
    },
    {
      label: "地址",
      prop: "ip",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <div style="white-space: pre-line;">
          <p>{row.ip}</p>
          <p>{row.private_ip}</p>
        </div>
      )
    },
    {
      label: "状态",
      prop: "status",
      minWidth: 50,
      cellRenderer: ({ row }) => {
        const statusText =
          {
            running: "运行中",
            active: "活跃",
            Active: "活跃",
            ACTIVE: "活跃",
            InUse: "使用中",
            stopped: "已停止",
            terminated: "已终止"
          }[row.status] || "未知状态";

        return (
          <div style="white-space: pre-line; text-align: center;">
            <p>
              {statusText === "运行中" ||
              statusText === "活跃" ||
              statusText === "使用中" ? (
                <div>
                  <el-text type="success" size="large">
                    {statusText}
                  </el-text>
                  <el-text type="info" size="large" style="margin-left: 10px;">
                    {row.status}
                  </el-text>
                </div>
              ) : (
                <div>
                  <el-text type="danger" size="large">
                    {statusText}
                  </el-text>
                  <el-text type="info" size="large" style="margin-left: 10px;">
                    {row.status}
                  </el-text>
                </div>
              )}
            </p>
          </div>
        );
      }
    },
    {
      label: "时间",
      prop: "sync_time",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <div style="white-space: normal;">
          <p>创建时间：{dayjs(row.created_at).format("YYYY-MM-DD HH:mm:ss")}</p>
          <p>
            同步时间：
            <b>{dayjs(row.sync_time).format("YYYY-MM-DD HH:mm:ss")}</b>
          </p>
        </div>
      )
    }
  ];

  function handleSizeChange(val: number) {
    pagination.pageSize = val;
    onSearch();
  }

  function handleCurrentChange(val: number) {
    pagination.currentPage = val;
    onSearch();
  }

  async function onSearch() {
    loading.value = true;
    getPublicIPsAPI({
      page: pagination.currentPage,
      limit: pagination.pageSize,
      keyword: form.keyword,
      account_id: form.account_id,
      datacenter_id: form.datacenter_id,
      ip: form.ip,
      cloud_type: form.cloud_type
    })
      .then(res => {
        if (res.success) {
          dataList.value = res.data;
          pagination.total = res.count;
          pagination.pageSize = pagination.pageSize;
          pagination.currentPage = pagination.currentPage;
        } else {
          dataList.value = [];
          pagination.total = 0;
          pagination.pageSize = pagination.pageSize;
          pagination.currentPage = pagination.currentPage;
        }
      })
      .catch(() => {
        message("请求失败", { type: "error" });
      })
      .finally(() => {
        loading.value = false;
      });
  }

  function syncAllAccounts() {
    getAllCloudAccountsAPI()
      .then(res => {
        if (res.success) {
          accounts.value = res.data;
        } else {
          message(res.msg, { type: "error" });
        }
      })
      .catch(error => {
        message(error, { type: "error" });
      });
  }

  function syncAllDatacenters() {
    getAllDatacentersAPI()
      .then(res => {
        if (res.success) {
          datacenters.value = res.data;
        } else {
          message(res.msg, { type: "error" });
        }
      })
      .catch(error => {
        message(error, { type: "error" });
      });
  }
  const resetForm = formEl => {
    if (!formEl) return;
    formEl.resetFields();
    onSearch();
  };

  onMounted(() => {
    onSearch();
    syncAllAccounts();
    syncAllDatacenters();
  });

  return {
    form,
    loading,
    columns,
    dataList,
    pagination,
    onSearch,
    resetForm,
    handleSizeChange,
    handleCurrentChange,
    accounts,
    syncAllAccounts,
    datacenters,
    syncAllDatacenters
  };
}
