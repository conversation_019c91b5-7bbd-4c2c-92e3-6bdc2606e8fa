<template>
  <div class="detail-container">
    <el-card class="card">
      <div class="header">
        <el-button
          type="primary"
          class="refresh-button"
          :loading="loading"
          @click="refreshData"
        >
          <i class="el-icon-refresh"></i> 刷新
        </el-button>
      </div>
      <el-descriptions title="工作负载详情" :column="2" border>
        <el-descriptions-item label="服务名称" class="highlight">{{
          workloadDetail?.name
        }}</el-descriptions-item>
        <el-descriptions-item label="命名空间" class="highlight">{{
          workloadDetail?.namespace
        }}</el-descriptions-item>
        <el-descriptions-item label="集群信息" class="highlight">
          <span
            >{{ workloadDetail?.cluster_name }} (ID:
            {{ workloadDetail?.cluster_id }})</span
          >
        </el-descriptions-item>
        <el-descriptions-item label="类型" class="highlight">{{
          workloadDetail?.workload_type
        }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{
          formatDate(workloadDetail?.creation_time)
        }}</el-descriptions-item>
        <el-descriptions-item label="同步时间">{{
          formatDate(workloadDetail?.sync_time)
        }}</el-descriptions-item>
      </el-descriptions>
    </el-card>

    <el-card class="card">
      <h3 class="card-title">
        <iconify-icon-online icon="mdi:tag" /> 标签 ({{
          workloadDetail?.labels.length
        }})
      </h3>
      <div class="tag-container">
        <el-tag
          v-for="(label, index) in workloadDetail?.labels"
          :key="index"
          class="tag"
        >
          {{ label.label_key }}={{ label.label_value }}
        </el-tag>
      </div>
    </el-card>

    <el-card class="card">
      <h3 class="card-title">
        <iconify-icon-online icon="mdi:tag-multiple" /> 选择器标签 ({{
          workloadDetail?.selector_labels.length
        }})
      </h3>
      <div class="tag-container">
        <el-tag
          v-for="(selector, index) in workloadDetail?.selector_labels"
          :key="index"
          class="tag"
        >
          {{ selector.label_key }}={{ selector.label_value }}
        </el-tag>
      </div>
    </el-card>

    <el-card class="card">
      <h3 class="card-title">
        <iconify-icon-online icon="pajamas:cloud-pod" /> Pods 和容器信息 ({{
          workloadDetail?.pods.length
        }})
      </h3>
      <el-table
        v-if="workloadDetail?.pods"
        :data="workloadDetail.pods"
        style="width: 100%"
        class="table"
      >
        <el-table-column type="expand">
          <template #default="{ row }">
            <div>
              <h4>容器信息</h4>
              <div v-for="(container, index) in row.containers" :key="index">
                <el-card class="container-card" style="margin-bottom: 10px">
                  <el-row :gutter="20">
                    <el-col :span="16">
                      <strong style="font-size: 16px; color: #007bff"
                        >容器名称（镜像）:</strong
                      >
                      <span style="font-size: 16px">
                        <b>{{ container.name }}</b> ({{
                          container.image
                        }})</span
                      >
                      <br />
                    </el-col>
                    <el-col :span="8">
                      <strong style="font-size: 16px; color: #007bff"
                        >状态（重启策略）:</strong
                      >
                      <span style="font-size: 16px"
                        >{{ container.status }} ({{
                          container.restart_policy || "-"
                        }})</span
                      >
                    </el-col>
                  </el-row>
                  <el-row :gutter="20">
                    <el-col :span="8">
                      <strong style="font-size: 16px; color: #007bff"
                        >CPU资源（请求/限制）:</strong
                      >
                      <span style="font-size: 16px; color: #ff4500">
                        {{
                          container.request_cpu > 0
                            ? formatCpu(container.request_cpu)
                            : "无限制"
                        }}
                        /
                        {{
                          container.limit_cpu > 0
                            ? formatCpu(container.limit_cpu)
                            : "无限制"
                        }}
                      </span>
                    </el-col>
                    <el-col :span="8">
                      <strong style="font-size: 16px; color: #007bff"
                        >内存资源（请求/限制）:</strong
                      >
                      <span style="font-size: 16px; color: #ff4500">
                        {{
                          container.request_memory > 0
                            ? formatMemory(container.request_memory)
                            : "无限制"
                        }}
                        /
                        {{
                          container.limit_memory > 0
                            ? formatMemory(container.limit_memory)
                            : "无限制"
                        }}
                      </span>
                    </el-col>
                    <el-col :span="8">
                      <strong style="font-size: 16px; color: #007bff"
                        >同步时间:</strong
                      >
                      <span style="font-size: 16px">{{
                        formatDate(container.sync_time)
                      }}</span>
                      <br />
                    </el-col>
                    <el-col :span="24">
                      <strong style="font-size: 16px; color: #007bff"
                        >命令参数（工作目录）:</strong
                      >
                      <span style="font-size: 16px"
                        >{{ container.command }} {{ container.args }} ({{
                          container.working_dir
                        }})</span
                      >
                      <br />
                    </el-col>
                  </el-row>
                  <el-row :gutter="20">
                    <el-col :span="24">
                      <strong style="font-size: 16px; color: #007bff"
                        >环境变量:</strong
                      >
                      <span style="font-size: 16px">{{ container.envs }}</span>
                      <br />
                    </el-col>
                  </el-row>
                </el-card>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="Pod 名称" />
        <el-table-column label="容器数量">
          <template #default="{ row }">
            {{ row.containers.length }}
          </template>
        </el-table-column>
        <el-table-column label="资源(请求/限制)">
          <template #default="{ row }">
            CPU:
            {{ row.request_cpu > 0 ? formatCpu(row.request_cpu) : "无限制" }} /
            {{ row.limit_cpu > 0 ? formatCpu(row.limit_cpu) : "无限制"
            }}<br />内存:
            {{
              row.request_memory > 0
                ? formatMemory(row.request_memory)
                : "无限制"
            }}
            /
            {{
              row.limit_memory > 0 ? formatMemory(row.limit_memory) : "无限制"
            }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" />
        <el-table-column prop="pod_ip" label="Pod IP" />
        <el-table-column prop="host_ip" label="主机 IP" />
        <el-table-column prop="sync_time" label="同步时间">
          <template #default="{ row }">
            {{ formatDate(row.sync_time) }}
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <el-card class="card">
      <h3 class="card-title">
        <iconify-icon-online icon="carbon:cloud-satellite-services" /> Services
        ({{ workloadDetail?.services.length }})
      </h3>
      <el-table
        :data="workloadDetail?.services"
        style="width: 100%"
        class="table"
      >
        <el-table-column prop="name" label="服务名称" />
        <el-table-column prop="port_type" label="类型" />
        <el-table-column prop="cluster_ip" label="集群 IP" />
        <el-table-column prop="external_ip" label="外部 IP">
          <template #default="{ row }">
            <div style="text-align: left; white-space: normal">
              {{ row.external_ip }}
              <br />
              {{ row.external_ips }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="load_balance_ingress" label="负载均衡">
          <template #default="{ row }">
            <div style="text-align: left; white-space: normal">
              {{ row.load_balance_ingress }}
            </div>
            <div style="text-align: left; white-space: normal">
              {{ row.load_balance_ip }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="port" label="端口" />
        <el-table-column prop="sync_time" label="同步时间">
          <template #default="{ row }">
            {{ formatDate(row.sync_time) }}
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, defineProps } from "vue";
import { getWorkloadDetailAPI } from "@/api/asset/k8s/workload"; // 确保API存在
import type { Workload } from "@/api/asset/k8s/workload"; // 确保类型存在
import { message } from "@/utils/message";
import dayjs from "dayjs"; // 引入 dayjs

const props = defineProps<{
  id: number; // 接收 id 参数
}>();

const workloadDetail = ref<Workload | null>(null);
const loading = ref(false); // 添加 loading 变量

// 格式化时间函数
const formatDate = (dateString: string) => {
  return dayjs(dateString).format("YYYY-MM-DD HH:mm:ss"); // 自定义格式
};

// 格式化 CPU 函数
const formatCpu = (cpu: number) => {
  return `${cpu} m`; // 返回以毫核为单位的 CPU
};

// 格式化内存函数
const formatMemory = (memory: number) => {
  if (memory >= 1073741824) {
    return `${(memory / 1073741824).toFixed(2)} GB`; // 转换为 GB
  } else if (memory >= 1048576) {
    return `${(memory / 1048576).toFixed(2)} MB`; // 转换为 MB
  } else {
    return `${(memory / 1024).toFixed(2)} KB`; // 转换为 KB
  }
};

const loadWorkloadDetail = async () => {
  loading.value = true; // 开始加载
  getWorkloadDetailAPI(props.id)
    .then(res => {
      if (res.success) {
        workloadDetail.value = res.data;
      } else {
        message(res.msg, { type: "error" });
      }
    })
    .catch(err => {
      message(err, { type: "error" });
    })
    .finally(() => {
      loading.value = false; // 结束加载
    });
};

// 刷新数据
const refreshData = async () => {
  message("刷新中...", { type: "info" });
  await loadWorkloadDetail();
  message("刷新完成", { type: "success" });
};

onMounted(() => {
  loadWorkloadDetail();
});
</script>

<style scoped lang="scss">
.detail-container {
  padding: 20px;
  border-radius: 12px;
}

.card {
  margin-top: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgb(0 0 0 / 10%); /* 增加阴影效果 */
}

.card-title {
  margin-bottom: 10px; /* 增加标题与内容的间距 */
  font-size: 18px;
  font-weight: bold;
  color: #333; /* 标题颜色 */
}

.highlight {
  font-weight: bold; /* 加粗关键信息 */
  color: #007bff; /* 关键信息颜色 */
}

.tag-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px; /* 增加标签之间的间距 */
}

.tag {
  margin: 5px; /* 增加标签的外边距 */
  color: #007bff; /* 标签文字颜色 */
}

.table {
  margin-top: 10px;
  border-radius: 8px;
}

.table .el-table__header th {
  color: #333; /* 表头文字颜色 */
}

.container-card {
  padding: 10px; /* 增加内边距 */
  margin-bottom: 10px; /* 卡片之间的间距 */
  border-radius: 8px; /* 卡片圆角 */
}

.refresh-button {
  float: right; /* 右对齐 */
  padding: 10px 20px; /* 增加内边距 */
  margin-top: 20px; /* 增加按钮与内容的间距 */
  background-color: #409eff; /* 刷新按钮背景色 */
  border: none; /* 去掉边框 */
  border-radius: 5px; /* 圆角 */
  transition: background-color 0.3s; /* 添加过渡效果 */
}

.refresh-button:hover {
  background-color: #66b1ff; /* 悬停时的背景色 */
}
</style>
