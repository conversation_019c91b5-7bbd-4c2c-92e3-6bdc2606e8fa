import dayjs from "dayjs";
import { getWorkloadListAPI } from "@/api/asset/k8s/workload";
import type { PaginationProps } from "@pureadmin/table";
import { reactive, ref, onMounted, h } from "vue";
import { message } from "@/utils/message";
import type { Workload } from "@/api/asset/k8s/workload";
import type { Label } from "@/api/asset/k8s/label";
import { getAllClustersAPI, type Cluster } from "@/api/asset/k8s/cluster";
import { WorkloadTypeColors, WorkloadTypes } from "@/config/enum";
import {
  getClusterNamespacesAPI,
  type Namespace
} from "@/api/asset/k8s/namespace";
import { addDrawer } from "@/components/ReDrawer";
import Detail from "./Detail.vue";

export function useRole() {
  const form = reactive({
    keyword: undefined,
    cluster_id: undefined,
    namespace: undefined,
    workload_type: undefined
  });
  const dataList = ref<Workload[]>([]);
  const loading = ref(true);
  const clusters = ref<Cluster[]>([]);
  const namespaces = ref<Namespace[]>([]);
  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true,
    pageSizes: [5, 10, 20, 30, 40, 50, 100]
  });
  const columns: TableColumnList = [
    {
      label: "集群名称",
      prop: "cluster_name",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <div style="font-weight: bold;">{row.cluster_name}</div>
      )
    },
    {
      label: "名称/命名空间",
      prop: "name",
      cellRenderer: ({ row }) => (
        <div
          style="white-space: normal; cursor: pointer; color: #007bff; text-decoration: underline;"
          onClick={() => handleViewWorkloadDetail(row.id)}
        >
          <p style="margin: 0; font-weight: bold;">{row.name}</p>
          <p style="margin: 0; color: #666;">
            (<b>{row.namespace}</b>)
          </p>
        </div>
      )
    },
    {
      label: "部署类型",
      prop: "workload_type",
      cellRenderer: ({ row }) => (
        <div style="white-space: normal;">
          <el-text
            type={WorkloadTypeColors.get(row.workload_type)}
            style="font-weight: bold;"
          >
            {WorkloadTypes.get(row.workload_type)}
          </el-text>
          <br />
          <el-text
            type={WorkloadTypeColors.get(row.workload_type)}
            style="color: #666;"
          >
            {row.workload_type}
          </el-text>
        </div>
      )
    },
    {
      label: "标签",
      prop: "labels",
      width: 150,
      cellRenderer: ({ row }) => {
        if (!row.isExpanded) {
          row.isExpanded = false; // 初始化状态
        }

        const toggleExpand = () => {
          row.isExpanded = !row.isExpanded; // 切换状态
        };

        return (
          <div style="line-height: 1.5;">
            {row.labels.length > 0 && (
              <div>
                {row.isExpanded
                  ? row.labels.map((item: Label, index: number) => (
                      <el-tag key={index} size="large" style="margin: 3px;">
                        {item.label_key}={item.label_value}
                      </el-tag>
                    ))
                  : row.labels.slice(0, 3).map((item: Label, index: number) => (
                      <el-tag key={index} size="large" style="margin: 3px;">
                        {item.label_key}={item.label_value}
                      </el-tag>
                    ))}
                {row.labels.length > 3 && (
                  <span
                    style="cursor: pointer; color: blue;"
                    onClick={toggleExpand}
                  >
                    {row.isExpanded ? "收起" : "展开更多"}
                  </span>
                )}
              </div>
            )}
          </div>
        );
      }
    },
    {
      label: "选择器标签",
      prop: "selector_labels",
      width: 150,
      cellRenderer: ({ row }) => {
        if (!row.selectorIsExpanded) {
          row.selectorIsExpanded = false; // 初始化状态
        }

        const toggleSelectorExpand = () => {
          row.selectorIsExpanded = !row.selectorIsExpanded; // 切换状态
        };

        return (
          <div style="line-height: 1.5;">
            {row.selector_labels.length > 0 && (
              <div>
                {row.selectorIsExpanded
                  ? row.selector_labels.map((item: Label, index: number) => (
                      <el-tag key={index} size="large" style="margin: 3px;">
                        {item.label_key}={item.label_value}
                      </el-tag>
                    ))
                  : row.selector_labels
                      .slice(0, 3)
                      .map((item: Label, index: number) => (
                        <el-tag key={index} size="large" style="margin: 3px;">
                          {item.label_key}={item.label_value}
                        </el-tag>
                      ))}
                {row.selector_labels.length > 3 && (
                  <span
                    style="cursor: pointer; color: blue;"
                    onClick={toggleSelectorExpand}
                  >
                    {row.selectorIsExpanded ? "收起" : "展开更多"}
                  </span>
                )}
              </div>
            )}
          </div>
        );
      }
    },
    {
      label: "时间",
      prop: "sync_time",
      minWidth: 120,
      cellRenderer: ({ row }) => (
        <div style="white-space: normal;">
          <p style="margin: 0;">
            同步：
            <strong>
              {dayjs(row.sync_time).format("YYYY-MM-DD HH:mm:ss")}
            </strong>
          </p>
        </div>
      )
    },
    {
      label: "操作",
      prop: "action",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <el-button type="text" onClick={() => handleViewWorkloadDetail(row.id)}>
          查看
        </el-button>
      )
    }
  ];

  function getAllCluster() {
    getAllClustersAPI()
      .then(res => {
        if (res.success) {
          clusters.value = res.data;
          // message("刷新成功", { type: "success" });
        } else {
          message(res.msg, { type: "error" });
        }
      })
      .catch(error => {
        message("刷新失败" + error, { type: "error" });
      });
  }

  function getNamespaces() {
    if (!form.cluster_id) {
      namespaces.value = [];
      form.namespace = undefined;
      return;
    }
    getClusterNamespacesAPI(form.cluster_id)
      .then(res => {
        if (res.success) {
          namespaces.value = res.data;
        } else {
          message(res.msg, { type: "error" });
        }
      })
      .catch(error => {
        message("获取命名空间失败" + error, { type: "error" });
      });
  }

  function handleSizeChange(val: number) {
    pagination.pageSize = val;
    onSearch();
  }

  function handleCurrentChange(val: number) {
    pagination.currentPage = val;
    onSearch();
  }

  async function onSearch() {
    loading.value = true;
    getWorkloadListAPI({
      page: pagination.currentPage,
      limit: pagination.pageSize,
      keyword: form.keyword,
      cluster_id: form.cluster_id,
      namespace: form.namespace,
      workload_type: form.workload_type
    })
      .then(res => {
        if (res.success) {
          dataList.value = res.data;
          pagination.total = res.count;
          pagination.pageSize = pagination.pageSize;
          pagination.currentPage = pagination.currentPage;
        } else {
          dataList.value = [];
          pagination.total = 0;
          pagination.pageSize = pagination.pageSize;
          pagination.currentPage = pagination.currentPage;
        }
      })
      .catch(() => {
        message("请求失败", { type: "error" });
      })
      .finally(() => {
        loading.value = false;
      });
  }

  const resetForm = formEl => {
    if (!formEl) return;
    formEl.resetFields();
    form.cluster_id = undefined;
    form.namespace = undefined;
    onSearch();
  };

  async function handleViewWorkloadDetail(id: number) {
    addDrawer({
      title: "工作负载详情",
      size: "90%",
      hideFooter: true,
      contentRenderer: () => {
        return h(Detail, { id });
      }
    });
  }

  onMounted(() => {
    onSearch();
    getAllCluster();
    getNamespaces();
  });

  return {
    form,
    loading,
    columns,
    dataList,
    pagination,
    onSearch,
    resetForm,
    handleSizeChange,
    handleCurrentChange,
    getAllCluster,
    clusters,
    namespaces,
    getNamespaces,
    handleViewWorkloadDetail
  };
}
