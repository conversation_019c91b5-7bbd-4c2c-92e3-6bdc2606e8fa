<script setup lang="ts">
import { ref, onBeforeMount } from "vue";
import { useService } from "./hook";
import { PureTableBar } from "@/components/RePureTableBar";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import type { TableInstance } from "element-plus";
import { useRoute } from "vue-router";
import { parmaQueryNumber } from "@/utils/parmaQuery";

defineOptions({
  name: "K8SService"
});

const formRef = ref<TableInstance>();
const tableRef = ref();

const {
  form,
  loading,
  columns,
  dataList,
  pagination,
  onSearch,
  resetForm,
  handleSizeChange,
  handleCurrentChange,
  getAllCluster,
  clusters
} = useService();

onBeforeMount(() => {
  const router = useRoute();
  if (router.query) {
    const { cluster_id, keyword } = router.query;
    parmaQueryNumber(cluster_id) &&
      (form.cluster_id = parmaQueryNumber(cluster_id));
    form.keyword = keyword;
  }
});
</script>

<template>
  <div class="main">
    <el-card class="search-card">
      <el-form
        ref="formRef"
        :inline="true"
        :model="form"
        class="search-form"
        @submit.prevent
      >
        <el-form-item label="集群" prop="cluster_id">
          <div class="flex-input-group">
            <el-select
              v-model="form.cluster_id"
              placeholder="选择集群"
              filterable
              clearable
              @change="onSearch"
            >
              <el-option
                v-for="(item, index) in clusters"
                :key="index"
                :label="item.name"
                :value="item.id"
              >
                {{ item.name }}
              </el-option>
            </el-select>
            <el-button
              :icon="useRenderIcon('ep:refresh')"
              class="sync-button"
              @click="getAllCluster"
            >
              刷新
            </el-button>
          </div>
        </el-form-item>

        <el-form-item label="关键字" prop="keyword">
          <el-input
            v-model="form.keyword"
            placeholder="请输入关键字"
            clearable
            @keyup.enter="onSearch"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            :icon="useRenderIcon('ri:search-line')"
            :loading="loading"
            class="search-button"
            @click="onSearch"
          >
            搜索
          </el-button>
          <el-button
            :icon="useRenderIcon('ep:refresh')"
            class="reset-button"
            @click="resetForm(formRef)"
          >
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <PureTableBar title="K8S 服务" :columns="columns" @refresh="onSearch">
      <template v-slot="{ dynamicColumns }">
        <pure-table
          ref="tableRef"
          row-key="id"
          align-whole="left"
          table-layout="auto"
          :loading="loading"
          :data="dataList"
          :columns="dynamicColumns"
          :pagination="pagination"
          :minHeight="500"
          @page-size-change="handleSizeChange"
          @page-current-change="handleCurrentChange"
        >
          <template #empty>
            <el-empty description="暂无数据" />
          </template>
        </pure-table>
      </template>
    </PureTableBar>
  </div>
</template>

<style scoped lang="scss">
.main {
  padding: 20px;
  border-radius: 12px;
}

.search-card {
  margin-bottom: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 8%);
  transition: box-shadow 0.3s ease;

  &:hover {
    box-shadow: 0 6px 20px rgb(0 0 0 / 12%);
  }
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;

  :deep(.el-form-item) {
    margin-bottom: 0;
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: #606266;
  }

  .el-input,
  .el-select {
    width: 350px;

    :deep(input),
    :deep(.el-input__wrapper) {
      border-radius: 8px;

      &:focus {
        border-color: #409eff;
        box-shadow: 0 0 0 1px rgb(64 158 255 / 20%);
      }
    }
  }

  .flex-input-group {
    display: flex;
    gap: 8px;
    align-items: center;

    .sync-button {
      color: #606266;
      border-radius: 8px;
      transition: all 0.3s ease;

      &:hover {
        color: #409eff;
        border-color: #409eff;
      }
    }
  }
}

.search-button,
.reset-button {
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
    transform: translateY(-2px);
  }
}

.search-button {
  color: white;
  background: linear-gradient(135deg, #409eff 0%, #4facff 100%);
  border: none;
  box-shadow: 0 4px 12px rgb(64 158 255 / 30%);
}

:deep(.pure-table) {
  overflow: hidden;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 4%);
}
</style>
