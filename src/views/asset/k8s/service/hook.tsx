import { reactive, ref, onMounted } from "vue";
import { getServicesAPI } from "@/api/asset/k8s/service"; // 确保API存在
import { message } from "@/utils/message";
import type { Service } from "@/api/asset/k8s/service"; // 确保类型存在
import type { PaginationProps } from "@pureadmin/table";
import { type Cluster, getAllClustersAPI } from "@/api/asset/k8s/cluster";
import type { Label } from "@/api/asset/k8s/label";
import dayjs from "dayjs";

export function useService() {
  const form = reactive({
    keyword: undefined,
    namespace: undefined,
    cluster_id: undefined
  });
  const dataList = ref<Service[]>([]);
  const loading = ref(true);
  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true,
    pageSizes: [5, 10, 20, 30, 40, 50, 100]
  });

  const columns: TableColumnList = [
    {
      label: "服务名称",
      prop: "name",
      minWidth: 150,
      cellRenderer: ({ row }) => (
        <div style="font-weight: bold; text-align: left;">{row.name}</div>
      )
    },
    {
      label: "命名空间",
      prop: "namespace",
      minWidth: 100
    },
    {
      label: "集群名称",
      prop: "cluster_name",
      minWidth: 100
    },
    {
      label: "类型",
      prop: "port_type",
      minWidth: 100
    },
    {
      label: "集群IP",
      prop: "cluster_ip",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <>
          <div style="white-space: normal; text-align: left;">
            {row.cluster_ip}
          </div>
          <div style="white-space: normal; text-align: left;">
            {row.external_ip}
          </div>
          <div style="white-space: normal; text-align: left;">
            {row.external_ips}
          </div>
        </>
      )
    },
    {
      label: "负载均衡",
      prop: "load_balance_ingress",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <>
          <div style="white-space: normal; text-align: left;">
            {row.load_balance_ingress}
          </div>
          <div style="white-space: normal; text-align: left;">
            {row.load_balance_ip}
          </div>
        </>
      )
    },
    {
      label: "端口",
      prop: "port",
      minWidth: 100
    },
    {
      label: "标签",
      prop: "labels",
      width: 150,
      cellRenderer: ({ row }) => {
        if (!row.isExpanded) {
          row.isExpanded = false; // 初始化状态
        }

        const toggleExpand = () => {
          row.isExpanded = !row.isExpanded; // 切换状态
        };

        return (
          <div style="line-height: 1.5;">
            {row.labels.length > 0 && (
              <div>
                {row.isExpanded
                  ? row.labels.map((item: Label, index: number) => (
                      <el-tag key={index} size="large" style="margin: 3px;">
                        {item.label_key}={item.label_value}
                      </el-tag>
                    ))
                  : row.labels.slice(0, 3).map((item: Label, index: number) => (
                      <el-tag key={index} size="large" style="margin: 3px;">
                        {item.label_key}={item.label_value}
                      </el-tag>
                    ))}
                {row.labels.length > 3 && (
                  <span
                    style="cursor: pointer; color: blue;"
                    onClick={toggleExpand}
                  >
                    {row.isExpanded ? "收起" : "展开更多"}
                  </span>
                )}
              </div>
            )}
          </div>
        );
      }
    },
    {
      label: "选择器标签",
      prop: "selector_labels",
      width: 150,
      cellRenderer: ({ row }) => {
        if (!row.selectorIsExpanded) {
          row.selectorIsExpanded = false; // 初始化状态
        }

        const toggleSelectorExpand = () => {
          row.selectorIsExpanded = !row.selectorIsExpanded; // 切换状态
        };

        return (
          <div style="line-height: 1.5;">
            {row.selector_labels.length > 0 && (
              <div>
                {row.selectorIsExpanded
                  ? row.selector_labels.map((item: Label, index: number) => (
                      <el-tag key={index} size="large" style="margin: 3px;">
                        {item.label_key}={item.label_value}
                      </el-tag>
                    ))
                  : row.selector_labels
                      .slice(0, 3)
                      .map((item: Label, index: number) => (
                        <el-tag key={index} size="large" style="margin: 3px;">
                          {item.label_key}={item.label_value}
                        </el-tag>
                      ))}
                {row.selector_labels.length > 3 && (
                  <span
                    style="cursor: pointer; color: blue;"
                    onClick={toggleSelectorExpand}
                  >
                    {row.selectorIsExpanded ? "收起" : "展开更多"}
                  </span>
                )}
              </div>
            )}
          </div>
        );
      }
    },
    {
      label: "同步时间",
      prop: "sync_time",
      minWidth: 150,
      cellRenderer: ({ row }) => (
        <div>{dayjs(row.sync_time).format("YYYY-MM-DD HH:mm:ss")}</div>
      )
    }
  ];

  async function onSearch() {
    loading.value = true;
    getServicesAPI({
      page: pagination.currentPage,
      limit: pagination.pageSize,
      keyword: form.keyword,
      namespace: form.namespace,
      cluster_id: form.cluster_id
    })
      .then(res => {
        if (res.success) {
          dataList.value = res.data;
          pagination.total = res.count;
        } else {
          dataList.value = [];
          pagination.total = 0;
        }
      })
      .catch(() => {
        message("请求失败", { type: "error" });
      })
      .finally(() => {
        loading.value = false;
      });
  }

  function handleSizeChange(val: number) {
    pagination.pageSize = val;
    onSearch(); // 重新搜索以更新数据
  }

  function handleCurrentChange(val: number) {
    pagination.currentPage = val;
    onSearch(); // 重新搜索以更新数据
  }

  const resetForm = formEl => {
    if (!formEl) return;
    formEl.resetFields();
    form.cluster_id = undefined;
    onSearch();
  };

  const clusters = ref<Cluster[]>([]);

  function getAllCluster() {
    getAllClustersAPI()
      .then(res => {
        if (res.success) {
          clusters.value = res.data;
        } else {
          message(res.msg, { type: "error" });
        }
      })
      .catch(error => {
        message("刷新失败" + error, { type: "error" });
      });
  }

  onMounted(() => {
    onSearch();
    getAllCluster(); // 获取集群列表
  });

  return {
    form,
    loading,
    dataList,
    pagination,
    onSearch,
    resetForm,
    columns,
    handleSizeChange,
    handleCurrentChange,
    getAllCluster,
    clusters
  };
}
