import dayjs from "dayjs";
import {
  type Cluster,
  getClusters,
  addClusterAPI,
  updateClusterAPI,
  deleteClusterAPI,
  type ClusterForm
} from "@/api/asset/k8s/cluster";
import type { PaginationProps } from "@pureadmin/table";
import { reactive, ref, onMounted, h } from "vue";
import { message } from "@/utils/message";
import { addDrawer } from "@/components/ReDrawer";
import { addDialog } from "@/components/ReDialog";
import Uform from "./Uform.vue";
import { RouterLink } from "vue-router";

export function useRole() {
  const form = reactive({
    keyword: undefined
  });
  const dataList = ref([]);
  const loading = ref(true);

  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true,
    pageSizes: [10, 20, 30, 40, 50, 100]
  });
  const columns: TableColumnList = [
    {
      type: "selection",
      align: "left"
    },
    {
      label: "名称",
      prop: "name",
      minWidth: 150,
      cellRenderer: ({ row }) => (
        <div style="display: flex; align-items: center">
          <span style="margin-left: 10px; font-weight: bold;">{row.name}</span>
        </div>
      )
    },
    {
      label: "类型",
      prop: "cluster_type",
      minWidth: 100
    },
    {
      label: "集群版本",
      prop: "cluster_version",
      minWidth: 100
    },
    {
      label: "节点数量",
      prop: "node_total",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <div style="display: flex; align-items: center">
          <RouterLink
            to={{ name: "集群节点", query: { cluster_id: row.id } }}
            style="color:#409EFF; text-decoration: underline;"
          >
            <span style="margin-left: 10px">{row.node_total}</span>
          </RouterLink>
        </div>
      )
    },
    {
      label: "工作负载数量",
      prop: "workload_total",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <div style="display: flex; align-items: center">
          <RouterLink
            to={{ name: "工作负载", query: { cluster_id: row.id } }}
            style="color:#409EFF; text-decoration: underline;"
          >
            <span style="margin-left: 10px">{row.workload_total}</span>
          </RouterLink>
        </div>
      )
    },
    {
      label: "POD数量",
      prop: "pod_total",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <div style="display: flex; align-items: center">
          <RouterLink
            to={{ name: "Pod", query: { cluster_id: row.id } }}
            style="color:#409EFF; text-decoration: underline;"
          >
            <span style="margin-left: 10px">{row.pod_total}</span>
          </RouterLink>
        </div>
      )
    },
    {
      label: "容器数量",
      prop: "container_total",
      minWidth: 100
    },
    {
      label: "命名空间数量",
      prop: "namespace_total",
      minWidth: 100
    },
    {
      label: "服务数量",
      prop: "service_total",
      minWidth: 100
    },
    {
      label: "备注",
      prop: "remark",
      minWidth: 150
    },
    {
      label: "创建时间",
      prop: "created_at",
      minWidth: 150,
      cellRenderer: ({ row }) => (
        <div style="white-space: normal;">
          <p>{dayjs(row.created_at).format("YYYY-MM-DD HH:mm:ss")}</p>
        </div>
      )
    },
    {
      label: "更新时间",
      prop: "updated_at",
      minWidth: 150,
      cellRenderer: ({ row }) => (
        <div style="white-space: normal;">
          <p>{dayjs(row.updated_at).format("YYYY-MM-DD HH:mm:ss")}</p>
        </div>
      )
    },
    {
      label: "操作",
      prop: "action",
      minWidth: 150,
      cellRenderer: ({ row }) => (
        <div style="display: flex; align-items: center">
          <el-button type="primary" link onClick={() => updateFunc(row)}>
            <iconify-icon-online icon="ep:edit" style="margin:3px;" />
            编辑
          </el-button>
          <el-button type="danger" link onClick={() => deleteFunc(row)}>
            <iconify-icon-online icon="ep:delete" style="margin:3px;" />
            删除
          </el-button>
        </div>
      )
    }
  ];

  const editForm = ref<ClusterForm>();
  const childrenRef = ref(null);
  function updateFunc(row: Cluster) {
    editForm.value = {
      name: row.name,
      cluster_type: row.cluster_type,
      cluster_version: row.cluster_version,
      remark: row.remark,
      token: ""
    };
    addDrawer({
      headerRenderer: ({ titleId, titleClass }) => (
        <div class="flex flex-row justify-between">
          <h4 id={titleId} class={titleClass}>
            编辑K8S集群
            <b>{row.name}</b>
          </h4>
        </div>
      ),
      popConfirm: { title: "是否确认修改" },
      contentRenderer: () =>
        h(Uform, {
          formInline: {
            form: editForm.value
          },
          ref: childrenRef
        }),
      beforeSure: done => {
        if (childrenRef.value.ruleFormRef) {
          childrenRef.value.ruleFormRef.validate(valid => {
            if (valid) {
              updateClusterAPI(row.id, editForm.value)
                .then(res => {
                  if (res.success) {
                    message(res.msg, { type: "success" });
                    done();
                    // 重置表单数据
                    editForm.value = undefined;
                    onSearch();
                  } else {
                    message(res.msg, { type: "error" });
                  }
                })
                .catch(error => {
                  message(error, { type: "error" });
                });
            } else {
              message("请检查表单", { type: "error" });
            }
          });
        } else {
          message("请检查表单", { type: "error" });
        }
      }
    });
  }

  function addFunc() {
    editForm.value = {
      name: "",
      cluster_type: "",
      cluster_version: "",
      remark: "",
      token: ""
    };
    addDrawer({
      headerRenderer: ({ titleId, titleClass }) => (
        // jsx 语法
        <div class="flex flex-row justify-between">
          <h4 id={titleId} class={titleClass}>
            添加K8S集群
          </h4>
        </div>
      ),
      popConfirm: { title: "是否确认添加" },
      contentRenderer: () =>
        h(Uform, {
          formInline: {
            form: editForm.value
          },
          ref: childrenRef
        }),
      beforeSure: done => {
        if (childrenRef.value.ruleFormRef) {
          addClusterAPI(editForm.value)
            .then(res => {
              if (res.success) {
                message(res.msg, { type: "success" });
                done();
                editForm.value = undefined;
                onSearch();
              } else {
                message(res.msg, { type: "error" });
              }
            })
            .catch(error => {
              message(error, { type: "error" });
            });
        } else {
          message("请检查表单", { type: "error" });
        }
      }
    });
  }

  function deleteFunc(row: Cluster) {
    addDialog({
      title: "删除确认",
      width: 480,
      draggable: true,
      closeOnClickModal: false,
      contentRenderer: () => (
        <div class="flex flex-col gap-2">
          <p class="text-lg mb-4">您确定要删除以下K8S集群吗？</p>
          <el-descriptions border column={1}>
            <el-descriptions-item
              label="集群名称"
              label-class-name="font-bold w-[100px]"
            >
              <span class="text-red-500 font-bold">{row.name}</span>
            </el-descriptions-item>
            <el-descriptions-item label="集群类型" label-class-name="font-bold">
              {row.cluster_type}
            </el-descriptions-item>
            <el-descriptions-item label="集群版本" label-class-name="font-bold">
              {row.cluster_version}
            </el-descriptions-item>
            <el-descriptions-item label="节点数量" label-class-name="font-bold">
              {row.node_total}
            </el-descriptions-item>
          </el-descriptions>
          <div class="bg-[#fff3f3] text-red-500 p-3 rounded mt-4">
            <i class="el-icon-warning mr-2" />
            警告：此操作将永久删除该集群的管理配置，且无法恢复！
          </div>
        </div>
      ),
      beforeSure: done => {
        deleteClusterAPI(row.id)
          .then(res => {
            if (res.success) {
              message(res.msg, { type: "success" });
              onSearch();
            } else {
              message(res.msg, { type: "error" });
            }
          })
          .catch(error => {
            message("请求失败：" + error, { type: "error" });
          })
          .finally(() => {
            done();
          });
      }
    });
  }
  function handleSizeChange(val: number) {
    pagination.pageSize = val;
    onSearch();
  }

  function handleCurrentChange(val: number) {
    pagination.currentPage = val;
    onSearch();
  }

  async function onSearch() {
    loading.value = true;
    getClusters({
      page: pagination.currentPage,
      limit: pagination.pageSize,
      keyword: form.keyword
    })
      .then(res => {
        if (res.success) {
          dataList.value = res.data;
          pagination.total = res.count;
          pagination.pageSize = pagination.pageSize;
          pagination.currentPage = pagination.currentPage;
        } else {
          dataList.value = [];
          pagination.total = 0;
          pagination.pageSize = pagination.pageSize;
          pagination.currentPage = pagination.currentPage;
        }
      })
      .catch(() => {
        message("请求失败", { type: "error" });
      })
      .finally(() => {
        loading.value = false;
      });
  }

  const resetForm = formEl => {
    if (!formEl) return;
    formEl.resetFields();
    onSearch();
  };

  onMounted(() => {
    onSearch();
  });

  return {
    form,
    loading,
    columns,
    dataList,
    pagination,
    onSearch,
    resetForm,
    handleSizeChange,
    handleCurrentChange,
    addFunc
  };
}
