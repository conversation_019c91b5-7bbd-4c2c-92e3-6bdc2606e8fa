<template>
  <div class="main">
    <el-card class="search-card">
      <el-form
        ref="formRef"
        :inline="true"
        :model="form"
        class="search-form"
        @submit.prevent
      >
        <el-form-item label="关键字" prop="keyword">
          <el-input
            v-model="form.keyword"
            placeholder="请输入关键字"
            clearable
            @keyup.enter="onSearch"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            :icon="useRenderIcon('ri:search-line')"
            :loading="loading"
            class="search-button"
            style="
              background: linear-gradient(135deg, #409eff 0%, #4facff 100%);
              border: none;
              box-shadow: 0 4px 12px rgb(64 158 255 / 30%);
              transition: all 0.3s ease;
            "
            @click="onSearch"
          >
            搜索
          </el-button>
          <el-button
            :icon="useRenderIcon(Refresh)"
            class="reset-button"
            style="
              color: #606266;
              border-color: #dcdfe6;
              transition: all 0.3s ease;
            "
            @click="resetForm(formRef)"
          >
            重置
          </el-button>
          <el-button
            type="primary"
            link
            :icon="useRenderIcon(Plus)"
            class="add-button"
            @click="addFunc"
          >
            添加
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card class="sync-card">
      <div class="sync-content">
        <div class="sync-actions">
          <div class="sync-label">多选集群同步</div>
          <el-button
            type="primary"
            :icon="useRenderIcon(Refresh)"
            class="sync-button"
            @click="syncData"
          >
            同步集群信息
          </el-button>
          <span class="selected-count">
            已选择 {{ multipleSelection.length }} 个对象
          </span>
        </div>
      </div>
    </el-card>

    <PureTableBar title="K8S集群" :columns="columns" @refresh="onSearch">
      <template v-slot="{ size, dynamicColumns }">
        <pure-table
          ref="tableRef"
          row-key="id"
          align-whole="left"
          table-layout="auto"
          :loading="loading"
          :size="size"
          :minHeight="500"
          :data="dataList"
          :columns="dynamicColumns"
          :pagination="{ ...pagination, size }"
          :header-cell-style="{
            fontWeight: '600',
            borderBottom: '2px solid #e4e7ed'
          }"
          :row-style="{
            cursor: 'pointer',
            transition: 'background-color 0.2s ease'
          }"
          :cell-style="{
            padding: '12px 10px'
          }"
          border
          stripe
          highlight-current-row
          @page-size-change="handleSizeChange"
          @page-current-change="handleCurrentChange"
          @selection-change="handleSelectionChange"
        >
          <template #empty>
            <el-empty
              description="暂无数据"
              :image-size="120"
              style="padding: 40px 0"
            />
          </template>
        </pure-table>
      </template>
    </PureTableBar>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useRole } from "./hook";
import { PureTableBar } from "@/components/RePureTableBar";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import type { TableInstance } from "element-plus";

import Refresh from "@iconify-icons/ep/refresh";
import Plus from "@iconify-icons/ep/plus";
import { syncCluster } from "@/api/asset/k8s/cluster";
import { message } from "@/utils/message";
defineOptions({
  name: "K8SCluster"
});

const formRef = ref<TableInstance>();
const tableRef = ref();

const {
  form,
  loading,
  columns,
  dataList,
  pagination,
  onSearch,
  resetForm,
  handleSizeChange,
  handleCurrentChange,
  addFunc
} = useRole();

const syncData = async () => {
  if (multipleSelection.value.length === 0) {
    return message("请选择要同步的集群", { type: "warning" });
  }

  const syncPromises = multipleSelection.value.map(async row => {
    try {
      const res = await syncCluster(row.id);
      message(`${row.name}：${res.msg}`, {
        type: res.success ? "success" : "error"
      });
    } catch (error) {
      message(`${row.name}：同步失败`, { type: "error" });
    }
  });

  await Promise.all(syncPromises);
};

const multipleSelection = ref([]);

const handleSelectionChange = val => {
  multipleSelection.value = val;
};
</script>

<style scoped lang="scss">
.main {
  padding: 20px;
}

.search-card,
.sync-card {
  margin-bottom: 20px;
  transition: box-shadow 0.3s ease;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;

  :deep(.el-form-item) {
    margin-bottom: 0;
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: #606266;
  }

  .el-input {
    width: 350px;

    :deep(input) {
      border-radius: 8px;

      &:focus {
        border-color: #409eff;
        box-shadow: 0 0 0 1px rgb(64 158 255 / 20%);
      }
    }
  }
}

.search-button,
.reset-button,
.add-button {
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
    transform: translateY(-2px);
  }
}

.search-button {
  color: white;
  background-color: #409eff;
  border-color: #409eff;
}

.sync-card {
  .sync-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
  }

  .sync-actions {
    display: flex;
    gap: 16px;
    align-items: center;
  }

  .sync-label {
    font-weight: 600;
    color: #303133;
  }

  .sync-button {
    padding: 10px 16px;
    font-size: 14px;
    font-weight: 600;
    border: none;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgb(64 158 255 / 30%);
    transition: all 0.3s ease;

    &:hover {
      background: linear-gradient(135deg, #4facff 0%, #5bb8ff 100%);
      box-shadow: 0 6px 16px rgb(64 158 255 / 40%);
      transform: translateY(-2px);
    }
  }

  .selected-count {
    margin-left: 8px;
    font-size: 14px;
    color: #909399;
  }
}

:deep(.pure-table) {
  overflow: hidden;
  border: 1px solid #e4e7ed;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 4%);
}
</style>
