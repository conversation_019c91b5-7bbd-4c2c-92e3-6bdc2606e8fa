<template>
  <el-card shadow="never" class="cluster-form-card">
    <el-form
      ref="ruleFormRef"
      :model="formData.form"
      :rules="formRules"
      label-width="auto"
      label-position="top"
      size="large"
      status-icon
      class="form-container"
    >
      <el-form-item
        v-for="(field, key) in [
          { prop: 'name', label: '名称' },
          { prop: 'cluster_type', label: '集群类型' },
          { prop: 'cluster_version', label: '集群版本' },
          {
            prop: 'token',
            label: 'Token',
            type: 'textarea',
            placeholder: '留空表示不修改'
          },
          { prop: 'remark', label: '备注', type: 'textarea' }
        ]"
        :key="key"
        :label="field.label"
        :prop="field.prop"
        :maxlength="field.prop === 'token' ? null : 255"
        show-word-limit
      >
        <el-input
          v-model="formData.form[field.prop]"
          :type="field.type"
          :placeholder="field.placeholder"
          class="input-field"
          :maxlength="field.prop === 'token' ? null : 255"
          show-word-limit
        />
      </el-form-item>
    </el-form>
  </el-card>
</template>

<script setup lang="ts">
import { defineProps, ref } from "vue";
import type { ClusterForm } from "@/api/asset/k8s/cluster";
import type { FormInstance, FormRules } from "element-plus";

interface FormProps {
  formInline: {
    form: ClusterForm;
  };
}

// 提取公共验证规则
const commonLengthRule = {
  max: 255,
  message: "长度不能超过255个字符",
  trigger: "blur"
};

const formRules: FormRules = {
  name: [
    { required: true, message: "请输入名称", trigger: "blur" },
    commonLengthRule
  ],
  cluster_type: [
    { required: true, message: "请输入集群类型", trigger: "blur" },
    commonLengthRule
  ],
  cluster_version: [
    { required: true, message: "请输入集群版本", trigger: "blur" },
    commonLengthRule
  ],
  remark: [commonLengthRule]
};

const props = withDefaults(defineProps<FormProps>(), {
  formInline: () => ({ form: {} as ClusterForm })
});

const formData = ref(props.formInline);
const ruleFormRef = ref<FormInstance>();

defineExpose({ ruleFormRef });
</script>

<style lang="scss" scoped>
.cluster-form-card {
  :deep(.el-card__body) {
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgb(0 0 0 / 10%);
  }

  :deep(.el-form) {
    max-width: 600px;
    margin: 0 auto;
  }
}

.form-container {
  padding: 20px;
}

.input-field {
  border-radius: 4px;
  transition: border-color 0.3s;
}

.input-field:focus {
  border-color: #409eff;
}
</style>
