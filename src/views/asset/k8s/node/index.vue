<script setup lang="ts">
import { ref, onBeforeMount, computed } from "vue";
import { useRoute } from "vue-router";
import { useRole } from "./hook";
import { PureTableBar } from "@/components/RePureTableBar";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import type { TableInstance } from "element-plus";
import { parmaQueryNumber } from "@/utils/parmaQuery";
import { formatThousands } from "@/utils/format";
import Refresh from "@iconify-icons/ep/refresh";
defineOptions({
  name: "K8SNode"
});

const formRef = ref<TableInstance>();
const tableRef = ref();

const {
  form,
  loading,
  columns,
  dataList,
  pagination,
  onSearch,
  resetForm,
  handleSizeChange,
  handleCurrentChange,
  getAllCluster,
  clusters
} = useRole();

onBeforeMount(() => {
  const router = useRoute();
  if (router.query) {
    const { cluster_id } = router.query;
    parmaQueryNumber(cluster_id) &&
      (form.cluster_id = parmaQueryNumber(cluster_id));
  }
});

const multipleSelection = ref([]);

const handleSelectionChange = val => {
  multipleSelection.value = val;
};

// 格式化内存显示
const formatMemory = (memory: number) => {
  if (memory >= 1024 * 1024 * 1024) {
    return `${(memory / 1024 / 1024 / 1024).toFixed(2)} GB`;
  } else if (memory >= 1024 * 1024) {
    return `${(memory / 1024 / 1024).toFixed(2)} MB`;
  } else if (memory >= 1024) {
    return `${(memory / 1024).toFixed(2)} KB`;
  } else {
    return `${memory} B`;
  }
};

// 计算资源总量
const totalCpuRequest = computed(() => {
  return multipleSelection.value.reduce(
    (sum, node) => sum + (node.request_cpu || 0),
    0
  );
});

const totalCpuLimit = computed(() => {
  return multipleSelection.value.reduce(
    (sum, node) => sum + (node.limit_cpu || 0),
    0
  );
});

const totalCpuCapacity = computed(() => {
  return multipleSelection.value.reduce(
    (sum, node) => sum + (node.capacity_cpu || 0),
    0
  );
});

const totalMemoryRequest = computed(() => {
  return multipleSelection.value.reduce(
    (sum, node) => sum + (node.request_memory || 0),
    0
  );
});

const totalMemoryLimit = computed(() => {
  return multipleSelection.value.reduce(
    (sum, node) => sum + (node.limit_memory || 0),
    0
  );
});

const totalMemoryCapacity = computed(() => {
  return multipleSelection.value.reduce(
    (sum, node) => sum + (node.capacity_memory || 0),
    0
  );
});

// 计算可用资源
const availableCpu = computed(() => {
  return Math.max(0, totalCpuCapacity.value - totalCpuRequest.value);
});

const availableCpuPercentage = computed(() => {
  if (totalCpuCapacity.value === 0) return 0;
  return ((availableCpu.value / totalCpuCapacity.value) * 100).toFixed(2);
});

const availableMemory = computed(() => {
  return Math.max(0, totalMemoryCapacity.value - totalMemoryRequest.value);
});

const availableMemoryPercentage = computed(() => {
  if (totalMemoryCapacity.value === 0) return 0;
  return ((availableMemory.value / totalMemoryCapacity.value) * 100).toFixed(2);
});

// 计算资源百分比
const cpuRequestPercentage = computed(() => {
  if (totalCpuCapacity.value === 0) return 0;
  return ((totalCpuRequest.value / totalCpuCapacity.value) * 100).toFixed(2);
});

const cpuLimitPercentage = computed(() => {
  if (totalCpuCapacity.value === 0) return 0;
  return ((totalCpuLimit.value / totalCpuCapacity.value) * 100).toFixed(2);
});

const memoryRequestPercentage = computed(() => {
  if (totalMemoryCapacity.value === 0) return 0;
  return ((totalMemoryRequest.value / totalMemoryCapacity.value) * 100).toFixed(
    2
  );
});

const memoryLimitPercentage = computed(() => {
  if (totalMemoryCapacity.value === 0) return 0;
  return ((totalMemoryLimit.value / totalMemoryCapacity.value) * 100).toFixed(
    2
  );
});

// 根据百分比返回颜色类名
const getCpuRequestClass = computed(() => {
  const percentage = parseFloat(cpuRequestPercentage.value);
  return getColorClassByPercentage(percentage);
});

const getCpuLimitClass = computed(() => {
  const percentage = parseFloat(cpuLimitPercentage.value);
  return getColorClassByPercentage(percentage);
});

const getMemoryRequestClass = computed(() => {
  const percentage = parseFloat(memoryRequestPercentage.value);
  return getColorClassByPercentage(percentage);
});

const getMemoryLimitClass = computed(() => {
  const percentage = parseFloat(memoryLimitPercentage.value);
  return getColorClassByPercentage(percentage);
});

// 根据百分比获取颜色类名
const getColorClassByPercentage = (percentage: number) => {
  if (percentage >= 90) return "percentage-danger";
  if (percentage >= 70) return "percentage-warning";
  return "percentage-normal";
};
</script>

<template>
  <div class="main">
    <el-card class="search-card">
      <el-form
        ref="formRef"
        :inline="true"
        :model="form"
        class="search-form"
        @submit.prevent
      >
        <el-form-item label="集群" prop="cluster_id">
          <div class="flex-input-group">
            <el-select
              v-model="form.cluster_id"
              placeholder="选择集群"
              filterable
              clearable
              @change="onSearch"
            >
              <el-option
                v-for="(item, index) in clusters"
                :key="index"
                :label="item.name"
                :value="item.id"
              >
                {{ item.name }}
              </el-option>
            </el-select>
            <el-button
              :icon="useRenderIcon(Refresh)"
              class="sync-button"
              @click="getAllCluster"
            >
              刷新
            </el-button>
          </div>
        </el-form-item>

        <el-form-item label="关键字" prop="keyword">
          <el-input
            v-model="form.keyword"
            placeholder="请输入关键字"
            clearable
            @keyup.enter="onSearch"
          />
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            :icon="useRenderIcon('ri:search-line')"
            :loading="loading"
            class="search-button"
            @click="onSearch"
          >
            搜索
          </el-button>
          <el-button
            :icon="useRenderIcon(Refresh)"
            class="reset-button"
            @click="resetForm(formRef)"
          >
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card v-if="multipleSelection.length > 0" class="stats-card">
      <div class="stats-content">
        <div class="stats-info">
          <span class="selection-count"
            >已选择 {{ multipleSelection.length }} 个节点</span
          >
          <div class="resources-container">
            <div class="resource-group">
              <span class="resource-title">CPU:</span>
              <span class="resource-item">
                请求: <strong>{{ formatThousands(totalCpuRequest) }}m</strong>
                <span :class="getCpuRequestClass"
                  >({{ cpuRequestPercentage }}%)</span
                >
              </span>
              <span class="resource-item">
                限制: <strong>{{ formatThousands(totalCpuLimit) }}m</strong>
                <span :class="getCpuLimitClass"
                  >({{ cpuLimitPercentage }}%)</span
                >
              </span>
              <span class="resource-item"
                >总量:
                <strong>{{ formatThousands(totalCpuCapacity) }}m</strong></span
              >
              <span class="resource-item"
                >可用:
                <strong>{{ formatThousands(availableCpu) }}m</strong>
                <span class="percentage-normal"
                  >({{ availableCpuPercentage }}%)</span
                >
              </span>
            </div>
            <div class="resource-group">
              <span class="resource-title">内存:</span>
              <span class="resource-item">
                请求: <strong>{{ formatMemory(totalMemoryRequest) }}</strong>
                <span :class="getMemoryRequestClass"
                  >({{ memoryRequestPercentage }}%)</span
                >
              </span>
              <span class="resource-item">
                限制: <strong>{{ formatMemory(totalMemoryLimit) }}</strong>
                <span :class="getMemoryLimitClass"
                  >({{ memoryLimitPercentage }}%)</span
                >
              </span>
              <span class="resource-item"
                >总量:
                <strong>{{ formatMemory(totalMemoryCapacity) }}</strong></span
              >
              <span class="resource-item"
                >可用:
                <strong>{{ formatMemory(availableMemory) }}</strong>
                <span class="percentage-normal"
                  >({{ availableMemoryPercentage }}%)</span
                >
              </span>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <PureTableBar title="K8S节点" :columns="columns" @refresh="onSearch">
      <template v-slot="{ size, dynamicColumns }">
        <pure-table
          ref="tableRef"
          row-key="id"
          align-whole="left"
          table-layout="auto"
          :loading="loading"
          :size="size"
          :minHeight="500"
          :data="dataList"
          :columns="dynamicColumns"
          :pagination="{ ...pagination, size }"
          :header-cell-style="{
            fontWeight: '600',
            borderBottom: '2px solid #e4e7ed'
          }"
          :row-style="{
            cursor: 'pointer',
            transition: 'background-color 0.2s ease'
          }"
          :cell-style="{
            padding: '12px 10px'
          }"
          border
          stripe
          highlight-current-row
          @page-size-change="handleSizeChange"
          @page-current-change="handleCurrentChange"
          @selection-change="handleSelectionChange"
        >
          <template #empty>
            <el-empty
              description="暂无数据"
              :image-size="120"
              style="padding: 40px 0"
            />
          </template>
        </pure-table>
      </template>
    </PureTableBar>
  </div>
</template>

<style scoped lang="scss">
.main {
  padding: 20px;
  border-radius: 12px;
}

.search-card,
.stats-card {
  margin-bottom: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 8%);
  transition: box-shadow 0.3s ease;

  &:hover {
    box-shadow: 0 6px 20px rgb(0 0 0 / 12%);
  }
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;

  :deep(.el-form-item) {
    margin-bottom: 0;
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: #606266;
  }

  .el-input,
  .el-select {
    width: 350px;

    :deep(input),
    :deep(.el-input__wrapper) {
      border-radius: 8px;

      &:focus {
        border-color: #409eff;
        box-shadow: 0 0 0 1px rgb(64 158 255 / 20%);
      }
    }
  }

  .flex-input-group {
    display: flex;
    gap: 8px;
    align-items: center;

    .sync-button {
      color: #606266;
      border-color: #dcdfe6;
      border-radius: 8px;
      transition: all 0.3s ease;

      &:hover {
        color: #409eff;
        border-color: #409eff;
      }
    }
  }
}

.search-button,
.reset-button {
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
    transform: translateY(-2px);
  }
}

.search-button {
  background: linear-gradient(135deg, #409eff 0%, #4facff 100%);
  border: none;
  box-shadow: 0 4px 12px rgb(64 158 255 / 30%);
}

.reset-button {
  color: #606266;
  border-color: #dcdfe6;
}

.stats-card {
  .stats-content {
    padding: 12px 16px;
  }

  .stats-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .selection-count {
    font-size: 15px;
    font-weight: 600;
    color: #303133;
  }

  .resources-container {
    display: flex;
    flex-wrap: wrap;
    gap: 24px;
  }

  .resource-group {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    align-items: center;
  }

  .resource-title {
    margin-right: 4px;
    font-weight: 600;
    color: #303133;
  }

  .resource-item {
    color: #606266;
    white-space: nowrap;

    strong {
      color: #409eff;
    }

    .percentage-normal {
      color: #67c23a;
    }

    .percentage-warning {
      color: #e6a23c;
    }

    .percentage-danger {
      color: #f56c6c;
    }
  }
}

:deep(.pure-table) {
  border: 1px solid #e4e7ed;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 4%);
}
</style>
