import dayjs from "dayjs";
import { getNodeListAPI } from "@/api/asset/k8s/node";
import type { PaginationProps } from "@pureadmin/table";
import { reactive, ref, onMounted } from "vue";
import { message } from "@/utils/message";
import type { Node } from "@/api/asset/k8s/node";
import type { Label } from "@/api/asset/k8s/label";
import { getAllClustersAPI, type Cluster } from "@/api/asset/k8s/cluster";

export function useRole() {
  const form = reactive({
    keyword: undefined,
    cluster_id: undefined
  });
  const dataList = ref<Node[]>([]);
  const loading = ref(true);
  const clusters = ref<Cluster[]>([]);
  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 3,
    currentPage: 1,
    background: true,
    pageSizes: [1, 3, 5, 10, 20, 30, 40, 50, 100]
  });

  const columns: TableColumnList = [
    {
      type: "selection",
      width: 55
    },
    {
      label: "集群/名称/主机名/IP",
      prop: "cluster_name_name_ip",
      minWidth: 370,
      cellRenderer: ({ row }) => (
        <div style="white-space: pre-line; padding: 10px;">
          <p style="margin: 0; font-weight: bold; color: #333;">
            集群: <span style={{ color: "#007bff" }}>{row.cluster_name}</span>
          </p>
          <p style="margin: 0; font-weight: bold;">
            名称: <span style={{ color: "#007bff" }}>{row.name}</span>
          </p>
          {row.hostname && <p style="margin: 0;">主机名: {row.hostname}</p>}
          {row.internal_ip && (
            <p style="margin: 0;">内网IP: {row.internal_ip}</p>
          )}
          {row.external_ip && (
            <p style="margin: 0;">外网IP: {row.external_ip}</p>
          )}
        </div>
      )
    },
    {
      label: "状态/角色/版本",
      prop: "status_role_version",
      minWidth: 250,
      cellRenderer: ({ row }) => (
        <div style="white-space: pre-line; padding: 10px;">
          <div style="margin-bottom: 10px;">
            <strong>状态:</strong>
            <el-text
              type={row.status === "ready" ? "success" : "danger"}
              style={{
                margin: "5px",
                fontWeight: "bold",
                color: row.status === "ready" ? "#28a745" : "#dc3545"
              }}
            >
              {row.status}
            </el-text>
            {row.role && (
              <span style={{ marginLeft: "5px", color: "#dc3545" }}>
                ({row.role})
              </span>
            )}
          </div>
          <div>
            <strong>版本:</strong>
            <div style="margin-top: 5px;">
              <p style="margin: 0;">
                kubelet版本: <strong>{row.version}</strong>
              </p>
              <p style="margin: 0;">
                容器: <strong>{row.container_runtime_version}</strong>
              </p>
              <p style="margin: 0;">
                操作系统: <strong>{row.os_image}</strong>
              </p>
              <p style="margin: 0;">
                内核: <strong>{row.kernel_version}</strong>
              </p>
            </div>
          </div>
        </div>
      )
    },
    {
      label: "资源 （请求 ~ 限制）/总量",
      prop: "resources",
      minWidth: 250,
      cellRenderer: ({ row }) => {
        const cpuUsage = (row.request_cpu / row.capacity_cpu) * 100;
        const limitCpuUsage = (row.limit_cpu / row.capacity_cpu) * 100;
        const memoryUsage = (row.request_memory / row.capacity_memory) * 100;
        const limitMemoryUsage = (row.request_memory / row.limit_memory) * 100;

        const cpuUsageColor =
          cpuUsage > 80 ? "red" : cpuUsage > 70 ? "orange" : "green";

        const limitCpuUsageColor =
          limitCpuUsage > 80 ? "red" : limitCpuUsage > 70 ? "orange" : "green";

        const memoryUsageColor =
          memoryUsage > 80 ? "red" : memoryUsage > 70 ? "orange" : "green";

        const limitMemoryUsageColor =
          limitMemoryUsage > 80
            ? "red"
            : limitMemoryUsage > 70
              ? "orange"
              : "green";

        return (
          <div style="white-space: pre-line; line-height: 1.5; padding: 10px;">
            <div style="margin-bottom: 10px;">
              <strong>CPU资源:</strong>
              <div>
                <span>
                  {row.request_cpu}m ~ {row.limit_cpu}m /{" "}
                  <strong>{row.capacity_cpu}m</strong>
                </span>
              </div>
            </div>
            <div style="margin-bottom: 10px;">
              <strong>内存资源:</strong>
              <div>
                <span>
                  {formatMemory(row.request_memory)} ~{" "}
                  {formatMemory(row.limit_memory)} /{" "}
                  <strong>{formatMemory(row.capacity_memory)}</strong>
                </span>
              </div>
            </div>
            <div style="margin-bottom: 10px;">
              <strong>CPU 使用率:</strong>
              <div>
                <span style={{ color: cpuUsageColor }}>
                  {cpuUsage.toFixed(2)}%
                </span>{" "}
                ~{" "}
                <span style={{ color: limitCpuUsageColor }}>
                  {limitCpuUsage.toFixed(2)}%
                </span>
              </div>
            </div>
            <div>
              <strong>内存使用率:</strong>
              <div>
                <span style={{ color: memoryUsageColor }}>
                  {memoryUsage.toFixed(2)}%
                </span>{" "}
                ~{" "}
                <span style={{ color: limitMemoryUsageColor }}>
                  {limitMemoryUsage.toFixed(2)}%
                </span>
              </div>
            </div>
          </div>
        );
      }
    },
    {
      label: "标签",
      prop: "labels",
      width: 150,
      cellRenderer: ({ row }) => {
        if (!row.isExpanded) {
          row.isExpanded = false;
        }

        const toggleExpand = () => {
          console.log("Toggle Expand Clicked");
          row.isExpanded = !row.isExpanded;
        };

        return (
          <div style="line-height: 1.5;">
            {row.labels.length > 0 && (
              <div>
                {row.isExpanded
                  ? row.labels.map((item: Label, index: number) => (
                      <el-tag key={index} size="large" style="margin: 3px;">
                        {item.label_key}={item.label_value}
                      </el-tag>
                    ))
                  : row.labels.slice(0, 3).map((item: Label, index: number) => (
                      <el-tag key={index} size="large" style="margin: 3px;">
                        {item.label_key}={item.label_value}
                      </el-tag>
                    ))}
                {row.labels.length > 3 && (
                  <span
                    style="cursor: pointer; color: blue;"
                    onClick={toggleExpand}
                  >
                    {row.isExpanded ? "收起" : "展开更多"}
                  </span>
                )}
              </div>
            )}
          </div>
        );
      }
    },
    {
      label: "时间",
      prop: "sync_time",
      minWidth: 150,
      cellRenderer: ({ row }) => (
        <div style="white-space: normal;">
          <p style="margin: 0;">
            创建：
            <strong>
              {dayjs(row.created_at).format("YYYY-MM-DD HH:mm:ss")}
            </strong>
          </p>
          <p style="margin: 0;">
            同步：
            <strong>
              {dayjs(row.sync_time).format("YYYY-MM-DD HH:mm:ss")}
            </strong>
          </p>
        </div>
      )
    }
  ];

  const formatMemory = (memory: number) => {
    if (memory >= 1024 * 1024 * 1024) {
      return `${(memory / 1024 / 1024 / 1024).toFixed(2)} GB`;
    } else if (memory >= 1024 * 1024) {
      return `${(memory / 1024).toFixed(2)} MB`;
    } else if (memory > 1024) {
      return `${(memory / 1024).toFixed(2)} KB`;
    } else {
      return `${memory} B`;
    }
  };

  function getAllCluster() {
    getAllClustersAPI()
      .then(res => {
        if (res.success) {
          clusters.value = res.data;
        } else {
          message(res.msg, { type: "error" });
        }
      })
      .catch(error => {
        message("刷新失败" + error, { type: "error" });
      });
  }

  function handleSizeChange(val: number) {
    pagination.pageSize = val;
    onSearch();
  }

  function handleCurrentChange(val: number) {
    pagination.currentPage = val;
    onSearch();
  }

  async function onSearch() {
    loading.value = true;
    getNodeListAPI({
      page: pagination.currentPage,
      limit: pagination.pageSize,
      keyword: form.keyword,
      cluster_id: form.cluster_id
    })
      .then(res => {
        if (res.success) {
          dataList.value = res.data;
          pagination.total = res.count;
          pagination.pageSize = pagination.pageSize;
          pagination.currentPage = pagination.currentPage;
        } else {
          dataList.value = [];
          pagination.total = 0;
          pagination.pageSize = pagination.pageSize;
          pagination.currentPage = pagination.currentPage;
        }
      })
      .catch(() => {
        message("请求失败", { type: "error" });
      })
      .finally(() => {
        loading.value = false;
      });
  }

  const resetForm = formEl => {
    if (!formEl) return;
    formEl.resetFields();
    form.cluster_id = undefined;
    onSearch();
  };

  onMounted(() => {
    onSearch();
    getAllCluster();
  });

  return {
    form,
    loading,
    columns,
    dataList,
    pagination,
    onSearch,
    resetForm,
    handleSizeChange,
    handleCurrentChange,
    getAllCluster,
    clusters
  };
}
