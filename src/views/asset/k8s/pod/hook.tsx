import dayjs from "dayjs";
import type { PaginationProps } from "@pureadmin/table";
import { reactive, ref, onMounted } from "vue";
import { message } from "@/utils/message";
import type { Label } from "@/api/asset/k8s/label";
import { getAllClustersAPI, type Cluster } from "@/api/asset/k8s/cluster";
import { getPodListAPI, type Pod } from "@/api/asset/k8s/pod";

export function useRole() {
  const form = reactive({
    keyword: undefined,
    cluster_id: undefined,
    ip: undefined
  });
  const dataList = ref<Pod[]>([]);
  const loading = ref(true);
  const clusters = ref<Cluster[]>([]);
  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true,
    pageSizes: [5, 10, 20, 30, 40, 50, 100]
  });
  const columns: TableColumnList = [
    {
      label: "集群名称",
      prop: "cluster_name",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <div style="font-weight: bold; text-align: left;">
          {row.cluster_name}
        </div>
      )
    },
    {
      label: "名称/命名空间",
      prop: "name",
      cellRenderer: ({ row }) => (
        <div style="white-space: normal; text-align: left;">
          <p style="margin: 0; font-weight: bold;">{row.name}</p>
          <p style="margin: 0; color: #666;">
            (<b>{row.namespace}</b>)
          </p>
        </div>
      )
    },
    {
      label: "POD IP/主机IP",
      prop: "pod_ip",
      cellRenderer: ({ row }) => (
        <div style="white-space: normal; text-align: left;">
          <p style="margin: 0; font-weight: bold;">{row.pod_ip}</p>
          <p style="margin: 0; color: #666;">
            (<b>{row.host_ip}</b>)
          </p>
        </div>
      )
    },
    {
      label: "POD状态",
      prop: "status",
      cellRenderer: ({ row }) => (
        <div style="white-space: normal; text-align: left;">
          <p
            style={`margin: 0; font-weight: bold; color: ${row.status === "Running" ? "#67C23A" : row.status === "Pending" ? "#909399" : row.status === "Succeeded" ? "#67C23A" : row.status === "Failed" ? "#F56C6C" : "#C0C4CC"}`}
          >
            {row.status}
          </p>
        </div>
      )
    },
    {
      label: "标签",
      prop: "labels",
      width: 150,
      cellRenderer: ({ row }) => {
        if (!row.isExpanded) {
          row.isExpanded = false; // 初始化状态
        }

        const toggleExpand = () => {
          row.isExpanded = !row.isExpanded; // 切换状态
        };

        return (
          <div style="line-height: 1.5;">
            {row.labels?.length > 0 && (
              <div>
                {row.isExpanded
                  ? row.labels.map((item: Label, index: number) => (
                      <el-tag key={index} size="large" style="margin: 3px;">
                        {item.label_key}={item.label_value}
                      </el-tag>
                    ))
                  : row.labels.slice(0, 3).map((item: Label, index: number) => (
                      <el-tag key={index} size="large" style="margin: 3px;">
                        {item.label_key}={item.label_value}
                      </el-tag>
                    ))}
                {row.labels?.length > 3 && (
                  <span
                    style="cursor: pointer; color: blue;"
                    onClick={toggleExpand}
                  >
                    {row.isExpanded ? "收起" : "展开更多"}
                  </span>
                )}
              </div>
            )}
          </div>
        );
      }
    },
    {
      label: "时间",
      prop: "sync_time",
      minWidth: 180,
      cellRenderer: ({ row }) => (
        <div style="white-space: normal; text-align: left;">
          <p style="margin: 0;">
            创建：
            <strong>
              {dayjs(row.created_at).format("YYYY-MM-DD HH:mm:ss")}
            </strong>
          </p>
          <p style="margin: 0;">
            更新：
            <strong>
              {dayjs(row.updated_at).format("YYYY-MM-DD HH:mm:ss")}
            </strong>
          </p>
          <p style="margin: 0;">
            同步：
            <strong>
              {dayjs(row.sync_time).format("YYYY-MM-DD HH:mm:ss")}
            </strong>
          </p>
        </div>
      )
    }
  ];

  function getAllCluster() {
    getAllClustersAPI()
      .then(res => {
        if (res.success) {
          clusters.value = res.data;
          // message("刷新成功", { type: "success" });
        } else {
          message(res.msg, { type: "error" });
        }
      })
      .catch(error => {
        message("刷新失败" + error, { type: "error" });
      });
  }

  function handleSizeChange(val: number) {
    pagination.pageSize = val;
    onSearch();
  }

  function handleCurrentChange(val: number) {
    pagination.currentPage = val;
    onSearch();
  }

  async function onSearch() {
    loading.value = true;
    getPodListAPI({
      page: pagination.currentPage,
      limit: pagination.pageSize,
      keyword: form.keyword,
      cluster_id: form.cluster_id,
      ip: form.ip
    })
      .then(res => {
        if (res.success) {
          dataList.value = res.data;
          pagination.total = res.count;
          pagination.pageSize = pagination.pageSize;
          pagination.currentPage = pagination.currentPage;
        } else {
          dataList.value = [];
          pagination.total = 0;
          pagination.pageSize = pagination.pageSize;
          pagination.currentPage = pagination.currentPage;
        }
      })
      .catch(() => {
        message("请求失败", { type: "error" });
      })
      .finally(() => {
        loading.value = false;
      });
  }

  const resetForm = formEl => {
    if (!formEl) return;
    formEl.resetFields();
    form.cluster_id = undefined;
    onSearch();
  };

  onMounted(() => {
    onSearch();
    getAllCluster();
  });

  return {
    form,
    loading,
    columns,
    dataList,
    pagination,
    onSearch,
    resetForm,
    handleSizeChange,
    handleCurrentChange,
    getAllCluster,
    clusters
  };
}
