<template>
  <div>
    <div class="date-picker-container">
      <el-date-picker
        v-model="form.date_range"
        type="monthrange"
        format="YYYY-MM"
        value-format="YYYY-MM"
        placeholder="选择一段时间"
        clearable
        range-separator="至"
        start-placeholder="开始月份"
        end-placeholder="结束月份"
        :shortcuts="[
          {
            text: '最近三个月',
            value: () => {
              const end = dayjs();
              const start = dayjs().subtract(2, 'month');
              return [start.format('YYYY-MM'), end.format('YYYY-MM')];
            }
          },
          {
            text: '最近半年',
            value: () => {
              const end = dayjs();
              const start = dayjs().subtract(5, 'month');
              return [start.format('YYYY-MM'), end.format('YYYY-MM')];
            }
          },
          {
            text: '最近一年',
            value: () => {
              const end = dayjs();
              const start = dayjs().subtract(11, 'month');
              return [start.format('YYYY-MM'), end.format('YYYY-MM')];
            }
          },
          {
            text: '最近两年',
            value: () => {
              const end = dayjs();
              const start = dayjs().subtract(23, 'month');
              return [start.format('YYYY-MM'), end.format('YYYY-MM')];
            }
          }
        ]"
      />
    </div>
    <div v-if="!billCycles.length" class="no-data-message">没有数据</div>
    <div ref="chartRef" class="chart-container" />
  </div>
</template>

<script setup lang="ts">
import { ref, onBeforeMount, reactive, watch } from "vue";
import * as echarts from "echarts";
import {
  getMonthlyBillAmountAPI,
  type getMonthlyBillAmountParams
} from "@/api/asset/bill";
import { message } from "@/utils/message";
import dayjs from "dayjs";

const props = defineProps<{
  account_id: number;
  account_name: string | undefined;
  resource_id: string | undefined;
}>();

const form = reactive<getMonthlyBillAmountParams>({
  date_range: [
    dayjs().subtract(12, "month").format("YYYY-MM"),
    dayjs().format("YYYY-MM")
  ],
  resource_id: undefined
});

const chartRef = ref<HTMLDivElement | null>(null);
const billCycles = ref<string[]>([]);
const amounts = ref<number[]>([]);

const getMonthlyBillAmount = async () => {
  const [start_time, end_time] = form.date_range;
  const res = await getMonthlyBillAmountAPI(props.account_id, {
    start_time,
    end_time,
    resource_id: props.resource_id
  });
  if (res.success && res.data) {
    billCycles.value = res.data.map(item => item.bill_cycle);
    amounts.value = res.data.map(item => item.amount);
    updateChart(); // 更新图表
  } else {
    message(res.msg || "获取数据失败", { type: "error" });
  }
};

const updateChart = () => {
  if (chartRef.value) {
    const myChart = echarts.init(chartRef.value);
    const option = {
      title: {
        text: props.account_name
          ? `${props.account_name} 月度账单`
          : "月度账单",
        subtext: `${form.date_range[0]} 至 ${form.date_range[1]}`,
        left: "center",
        top: 50,
        textStyle: {
          fontSize: 22,
          fontWeight: "bold",
          color: "#333"
        },
        subtextStyle: {
          fontSize: 14,
          color: "#666",
          padding: [20, 0, 0, 0]
        }
      },
      tooltip: {
        trigger: "axis",
        backgroundColor: "rgba(255, 255, 255, 0.95)",
        borderWidth: 1,
        borderColor: "#ccc",
        padding: [10, 15],
        textStyle: {
          color: "#333"
        },
        formatter: params => {
          const barData = params[0];
          return `<div style="font-weight:bold;margin-bottom:8px;">${barData.name}</div>
                  <div style="display:flex;justify-content:space-between;align-items:center;margin:5px 0;">
                    <span style="color:#666;">金额：</span>
                    <span style="font-weight:bold;color:#409EFF;">${barData.value}元</span>
                  </div>`;
        }
      },
      grid: {
        left: "3%",
        right: "4%",
        bottom: "18%",
        top: "25%",
        containLabel: true
      },
      legend: {
        data: ["金额", "趋势线"],
        bottom: 0,
        icon: "roundRect",
        itemWidth: 12,
        itemHeight: 12,
        textStyle: {
          color: "#666"
        }
      },
      xAxis: {
        type: "category",
        data: billCycles.value,
        axisLabel: {
          rotate: 45,
          margin: 15,
          textStyle: {
            color: "#666"
          },
          formatter: value => {
            const [year, month] = value.split("-");
            return `${year}年${month}月`;
          }
        },
        axisLine: {
          lineStyle: {
            color: "#ddd"
          }
        },
        axisTick: {
          alignWithLabel: true,
          lineStyle: {
            color: "#ddd"
          }
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: "#eee",
            type: "dashed"
          }
        }
      },
      yAxis: {
        type: "value",
        name: "金额 (元)",
        nameTextStyle: {
          color: "#666",
          padding: [0, 0, 0, 50]
        },
        axisLabel: {
          formatter: value => {
            if (value >= 10000) {
              return (value / 10000).toFixed(2) + "万";
            }
            return value;
          },
          textStyle: {
            color: "#666"
          }
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: "#ddd"
          }
        },
        splitLine: {
          lineStyle: {
            color: "#eee",
            type: "dashed"
          }
        }
      },
      series: [
        {
          name: "金额",
          type: "bar",
          data: amounts.value.map(amount => amount.toFixed(2)),
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: "#83bff6" },
              { offset: 0.5, color: "#188df0" },
              { offset: 1, color: "#188df0" }
            ]),
            borderRadius: [4, 4, 0, 0]
          },
          emphasis: {
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: "#66a6ff" },
                { offset: 0.7, color: "#1170ff" },
                { offset: 1, color: "#1170ff" }
              ])
            }
          },
          label: {
            show: true,
            position: "top",
            distance: 15,
            formatter: params => {
              if (params.value >= 10000) {
                return (params.value / 10000).toFixed(2) + "万";
              }
              return params.value;
            },
            textStyle: {
              color: "#666",
              fontSize: 12
            }
          }
        },
        {
          name: "趋势线",
          type: "line",
          data: amounts.value.map(amount => amount.toFixed(2)),
          smooth: true,
          symbol: "circle",
          symbolSize: 8,
          itemStyle: {
            color: "#ff9f7f"
          },
          lineStyle: {
            width: 3,
            shadowColor: "rgba(255,159,127,0.3)",
            shadowBlur: 10
          },
          emphasis: {
            itemStyle: {
              borderWidth: 3,
              borderColor: "#ff9f7f",
              shadowColor: "rgba(255,159,127,0.5)",
              shadowBlur: 10
            }
          }
        }
      ]
    };
    myChart.setOption(option);
    window.addEventListener("resize", () => {
      myChart.resize();
    });
  }
};

onBeforeMount(() => {
  getMonthlyBillAmount();
});

// 监视数据变化以更新图表
watch(
  () => form.date_range,
  () => {
    getMonthlyBillAmount();
  }
);
</script>

<style scoped>
.date-picker-container {
  display: flex;
  justify-content: center;
  padding: 0 20px;
  margin-bottom: 20px;
}

:deep(.el-date-editor) {
  width: 380px !important;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 4%);
  transition: all 0.3s ease;
}

:deep(.el-date-editor .el-range-input) {
  font-size: 14px;
}

:deep(.el-date-editor .el-range__icon) {
  color: #409eff;
}

:deep(.el-picker-panel) {
  border-radius: 8px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 12%);
}

:deep(.el-picker-panel .el-picker-panel__sidebar) {
  background-color: #f8f9fb;
  border-right: 1px solid #ebeef5;
}

:deep(.el-picker-panel .el-picker-panel__shortcut) {
  padding: 12px 16px;
  font-size: 14px;
  color: #606266;
}

:deep(.el-picker-panel .el-picker-panel__shortcut:hover) {
  color: #409eff;
  background-color: #ecf5ff;
}

.chart-container {
  width: 100%;
  height: 650px;
  padding: 20px;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 8%);
  transition: all 0.3s ease;
}

.chart-container:hover {
  box-shadow: 0 6px 20px rgb(0 0 0 / 12%);
}

.no-data-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 500px;
  margin-top: 20px;
  font-size: 16px;
  color: #999;
  background-color: #f9f9f9;
  border-radius: 8px;
}
</style>
