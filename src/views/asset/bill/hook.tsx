import dayjs from "dayjs";
import { type MonthlyBill, getMonthlyBillAPI } from "@/api/asset/bill";
import type { PaginationProps } from "@pureadmin/table";
import { reactive, ref, onMounted, h } from "vue";
import { message } from "@/utils/message";
import {
  getAllCloudAccountsAPI,
  type CloudAccount
} from "@/api/asset/cloud-account";
import { addDialog } from "@/components/ReDialog";
import AccountMouthlyBill from "./AccountMouthlyBill.vue";
import ResourceTypeMonthlyBill from "./ResourceTypeMonthlyBill.vue";
export function useRole() {
  const form = reactive({
    account_id: undefined,
    resource_id: undefined,
    resource_name: undefined,
    account_name: undefined,
    bill_cycle: undefined
  });
  const dataList = ref<MonthlyBill[]>([]);
  const loading = ref(true);
  const accounts = ref<CloudAccount[]>([]);
  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true,
    pageSizes: [10, 20, 30, 40, 50, 100, 1000, 5000, 10000]
  });

  const columns: TableColumnList = [
    {
      type: "selection",
      align: "left",
      width: 50,
      fixed: "left"
    },
    {
      label: "账单周期",
      prop: "bill_cycle",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <div
          class="bill-cycle styled"
          style={{ fontWeight: "bold", color: "#4CAF50" }}
        >
          {row.bill_cycle}
        </div>
      )
    },
    {
      label: "账单类型",
      prop: "bill_type",
      minWidth: 150,
      cellRenderer: ({ row }) => (
        <div>
          <div class="bill-type styled" style={{ color: "#2196F3" }}>
            {row.bill_type}
          </div>
          <div class="bill-type styled" style={{ color: "#FF9800" }}>
            {row.charge_mode}
          </div>
        </div>
      )
    },
    {
      label: "区域",
      prop: "region", // 修正字段
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <div class="region-name styled" style={{ fontStyle: "italic" }}>
          {row.region}
        </div> // 修正字段
      )
    },
    {
      label: "资源类型/产品",
      prop: "resource_type", // 修正字段
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <div>
          <div class="product-name styled" style={{ fontWeight: "bold" }}>
            {row.product_name}
          </div>
          <div class="resource-type styled" style={{ color: "#9C27B0" }}>
            {row.resource_type}
          </div>
        </div>
      )
    },
    {
      label: "资源名称/ID",
      prop: "resource_name",
      minWidth: 150,
      cellRenderer: ({ row }) => (
        <div>
          {row.resource_name && (
            <div class="resource-name styled" style={{ color: "#3F51B5" }}>
              名称：{row.resource_name}
            </div>
          )}
          {row.resource_id && (
            <div class="resource-id styled" style={{ color: "#F44336" }}>
              ID：{row.resource_id}
            </div>
          )}
        </div>
      )
    },
    {
      label: "资源组",
      prop: "resource_group",
      minWidth: 150,
      cellRenderer: ({ row }) => (
        <div
          class="resource-group styled"
          style={{ textDecoration: "underline" }}
        >
          {row.resource_group}
        </div>
      )
    },
    {
      label: "金额",
      prop: "amount",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <div
          class="amount styled"
          style={{ color: "red", fontWeight: "bold", fontSize: "1.2em" }}
        >
          {row.amount
            .toFixed(2)
            .toString()
            .replace(/\B(?=(\d{3})+(?!\d))/g, ",")}
        </div>
      )
    },
    {
      label: "账户名称",
      prop: "account_name",
      minWidth: 150,
      cellRenderer: ({ row }) => (
        <div
          class="account-name styled"
          style={{ fontWeight: "bold", color: "#FF5722" }}
        >
          {row.account_name}
        </div>
      )
    },
    {
      label: "账单时间",
      prop: "time_info", // 修正字段
      minWidth: 240,
      cellRenderer: ({ row }) =>
        row.pay_time && (
          <div class="time-info styled" style={{ color: "#607D8B" }}>
            {dayjs(row.pay_time).format("YYYY-MM-DD HH:mm:ss")}
          </div>
        )
    }
  ];

  function handleSizeChange(val: number) {
    pagination.pageSize = val;
    onSearch();
  }

  function handleCurrentChange(val: number) {
    pagination.currentPage = val;
    onSearch();
  }

  function getAllCloudAccounts() {
    getAllCloudAccountsAPI()
      .then(res => {
        if (res.success) {
          accounts.value = res.data;
          // 自动选择第一个账户
          if (accounts.value.length > 0) {
            form.account_id = accounts.value[0].id;
            form.account_name = accounts.value[0].name;
          }
        } else {
          message(res.msg, { type: "error" });
        }
      })
      .catch(error => {
        message(error, { type: "error" });
      });
  }

  async function onSearch() {
    if (!form.account_id) {
      message("请选择账户", { type: "warning" });
      dataList.value = [];
      pagination.total = 0;
      loading.value = false;
    } else {
      loading.value = true;
      getMonthlyBillAPI(form.account_id, {
        page: pagination.currentPage,
        limit: pagination.pageSize,
        resource_id: form.resource_id,
        bill_cycle: form.bill_cycle,
        resource_name: form.resource_name
      })
        .then(res => {
          if (res.success) {
            dataList.value = res.data;
            pagination.total = res.count;
            pagination.pageSize = pagination.pageSize;
            pagination.currentPage = pagination.currentPage;
          } else {
            dataList.value = [];
            pagination.total = 0;
            pagination.pageSize = pagination.pageSize;
            pagination.currentPage = pagination.currentPage;
          }
        })
        .catch(() => {
          message("请求失败", { type: "error" });
        })
        .finally(() => {
          loading.value = false;
        });
    }
  }

  function handleAccountMonthlyBill() {
    if (!form.account_id) {
      message("请选择账户", { type: "warning" });
      return;
    } else {
      form.account_name = accounts.value.find(
        account => account.id === form.account_id
      )?.name;
      addDialog({
        title: "账户月度账单",
        hideFooter: true,
        draggable: true,
        contentRenderer: () =>
          h(AccountMouthlyBill, {
            account_id: form.account_id,
            account_name: form.account_name,
            resource_id: undefined
          })
      });
    }
  }
  function handleResourceTypeMonthlyBill() {
    if (!form.account_id) {
      message("请选择账户", { type: "warning" });
      return;
    } else {
      form.account_name = accounts.value.find(
        account => account.id === form.account_id
      )?.name;
      addDialog({
        title: "资源类型月度账单",
        hideFooter: true,
        draggable: true,
        contentRenderer: () =>
          h(ResourceTypeMonthlyBill, {
            account_id: form.account_id,
            account_name: form.account_name
          })
      });
    }
  }
  const resetForm = formEl => {
    if (!formEl) return;
    formEl.resetFields();
    onSearch();
  };

  onMounted(async () => {
    await getAllCloudAccounts();
    setTimeout(async () => {
      await onSearch();
    }, 100);
  });

  return {
    form,
    loading,
    columns,
    dataList,
    pagination,
    onSearch,
    resetForm,
    handleSizeChange,
    handleCurrentChange,
    accounts,
    getAllCloudAccounts,
    handleAccountMonthlyBill,
    handleResourceTypeMonthlyBill
  };
}
