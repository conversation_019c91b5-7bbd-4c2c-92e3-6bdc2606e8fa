<template>
  <div class="pie-chart-container">
    <div class="chart-header">
      <el-date-picker
        v-model="form.bill_cycle"
        type="month"
        format="YYYY-MM"
        value-format="YYYY-MM"
        placeholder="选择账单周期"
        clearable
        @change="fetchResourceTypeData"
      />
      <el-radio-group v-model="chartType" size="large">
        <el-radio-button label="pie">饼图</el-radio-button>
        <el-radio-button label="bar">柱状图</el-radio-button>
      </el-radio-group>
    </div>
    <div ref="pieChartRef" class="pie-chart" />
    <div
      v-if="resourceTypeData.length === 0 && !loading"
      class="no-data-message"
    >
      没有数据
    </div>
    <div v-if="loading" class="loading-message">加载中...</div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onBeforeMount, onBeforeUnmount } from "vue";
import * as echarts from "echarts";
import {
  getResourceTypeDataAPI,
  type getResourceTypeDataParams
} from "@/api/asset/bill";
import { message } from "@/utils/message";

const props = defineProps<{
  account_id: number;
  account_name: string;
}>();

const pieChartRef = ref<HTMLDivElement | null>(null);
const resourceTypeData = ref<{ name: string; value: number }[]>([]);
const loading = ref(false);
const chartType = ref("pie");
const form = reactive<getResourceTypeDataParams>({
  bill_cycle: new Date().toISOString().slice(0, 7)
});

const fetchResourceTypeData = async () => {
  loading.value = true; // 开始加载
  const res = await getResourceTypeDataAPI(props.account_id, {
    bill_cycle: form.bill_cycle
  });
  loading.value = false; // 结束加载
  if (res.success) {
    resourceTypeData.value = res.data.map(item => ({
      name: item.resource_type,
      value: item.amount
    }));
    updatePieChart();
  } else {
    resourceTypeData.value = []; // 确保在没有数据时清空数据
    message(res.msg || "获取资源类型数据失败", { type: "error" });
  }
};

const updatePieChart = () => {
  if (pieChartRef.value) {
    const myChart =
      echarts.getInstanceByDom(pieChartRef.value) ||
      echarts.init(pieChartRef.value);

    // 清除之前的图表配置
    myChart.clear();

    // 预设的渐变色方案
    const colorPalette = [
      { start: "#36CBCB", end: "#2B85E4" }, // 青蓝渐变
      { start: "#FF9900", end: "#ED4014" }, // 橙红渐变
      { start: "#2D8CF0", end: "#0C2A54" }, // 深蓝渐变
      { start: "#19BE6B", end: "#0C692B" }, // 绿色渐变
      { start: "#FF6B6B", end: "#CC0000" }, // 红色渐变
      { start: "#9A66E4", end: "#4C2E7E" }, // 紫色渐变
      { start: "#FF9900", end: "#CC6600" }, // 橙色渐变
      { start: "#00CC99", end: "#006666" }, // 青绿渐变
      { start: "#FF99CC", end: "#CC3366" }, // 粉色渐变
      { start: "#99CC33", end: "#669900" } // 黄绿渐变
    ];

    // 对数据进行排序（只影响柱状图）
    const sortedData = [...resourceTypeData.value].sort(
      (a, b) => b.value - a.value
    );

    const baseOption = {
      title: {
        text: `${props.account_name} 资源类型金额占比`,
        subtext: form.bill_cycle ? `${form.bill_cycle} 账单周期` : "",
        left: "center",
        top: 20,
        textStyle: {
          fontSize: 22,
          fontWeight: "bold",
          color: "#333"
        },
        subtextStyle: {
          fontSize: 14,
          color: "#666",
          margin: [10, 0, 0, 0]
        }
      },
      tooltip: {
        trigger: "item",
        backgroundColor: "rgba(255, 255, 255, 0.95)",
        borderWidth: 1,
        borderColor: "#ccc",
        padding: [10, 15],
        textStyle: {
          color: "#333"
        }
      },
      legend: {
        type: "scroll",
        orient: chartType.value === "pie" ? "vertical" : "horizontal",
        right: chartType.value === "pie" ? 20 : "auto",
        top: chartType.value === "pie" ? 60 : "bottom",
        left: chartType.value === "pie" ? "auto" : "center",
        bottom: chartType.value === "pie" ? 20 : 10,
        itemWidth: 15,
        itemHeight: 15,
        itemGap: 20,
        textStyle: {
          color: "#666",
          fontSize: 13
        },
        pageButtonItemGap: 5,
        pageButtonGap: 10,
        pageButtonPosition: "end",
        pageIconColor: "#409EFF",
        pageIconInactiveColor: "#ccc",
        pageIconSize: 12,
        pageTextStyle: {
          color: "#666"
        }
      }
    };

    const pieOption = {
      ...baseOption,
      xAxis: undefined, // 明确移除 x 轴配置
      yAxis: undefined, // 明确移除 y 轴配置
      grid: undefined, // 明确移除网格配置
      tooltip: {
        ...baseOption.tooltip,
        trigger: "item", // 确保使用饼图的 tooltip 触发方式
        formatter: params => {
          const total = resourceTypeData.value.reduce(
            (sum, item) => sum + item.value,
            0
          );
          const percentage = ((params.value / total) * 100).toFixed(2);
          return `<div style="font-weight:bold;margin-bottom:8px;color:${params.color}">
                    ${params.name}
                  </div>
                  <div style="display:flex;justify-content:space-between;align-items:center;margin:5px 0;">
                    <span style="color:#666;">金额：</span>
                    <span style="font-weight:bold;color:#409EFF;">${params.value.toFixed(2)} 元</span>
                  </div>
                  <div style="display:flex;justify-content:space-between;align-items:center;margin:5px 0;">
                    <span style="color:#666;">占比：</span>
                    <span style="font-weight:bold;color:#67C23A;">${percentage}%</span>
                  </div>`;
        }
      },
      series: [
        {
          name: "资源类型",
          type: "pie",
          radius: ["40%", "70%"],
          center: ["40%", "55%"],
          avoidLabelOverlap: true,
          itemStyle: {
            borderRadius: 10,
            borderColor: "#fff",
            borderWidth: 2
          },
          label: {
            show: true,
            position: "outside",
            formatter: params => {
              const percentage = (
                (params.value /
                  resourceTypeData.value.reduce(
                    (sum, item) => sum + item.value,
                    0
                  )) *
                100
              ).toFixed(1);
              return `${params.name}\n${percentage}%`;
            },
            fontSize: 12,
            color: "#666"
          },
          labelLine: {
            length: 15,
            length2: 15,
            maxSurfaceAngle: 80
          },
          data: resourceTypeData.value.map((item, index) => ({
            ...item,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: colorPalette[index % colorPalette.length].start
                },
                {
                  offset: 1,
                  color: colorPalette[index % colorPalette.length].end
                }
              ])
            }
          }))
        }
      ]
    };

    const barOption = {
      ...baseOption,
      tooltip: {
        ...baseOption.tooltip,
        trigger: "axis",
        axisPointer: {
          type: "shadow"
        },
        formatter: params => {
          const param = params[0];
          const total = resourceTypeData.value.reduce(
            (sum, item) => sum + item.value,
            0
          );
          const percentage = ((param.value / total) * 100).toFixed(2);
          return `<div style="font-weight:bold;margin-bottom:8px;color:${param.color}">
                    ${param.name}
                  </div>
                  <div style="display:flex;justify-content:space-between;align-items:center;margin:5px 0;">
                    <span style="color:#666;">金额：</span>
                    <span style="font-weight:bold;color:#409EFF;">${param.value.toFixed(2)} 元</span>
                  </div>
                  <div style="display:flex;justify-content:space-between;align-items:center;margin:5px 0;">
                    <span style="color:#666;">占比：</span>
                    <span style="font-weight:bold;color:#67C23A;">${percentage}%</span>
                  </div>`;
        }
      },
      grid: {
        left: "3%",
        right: "4%",
        bottom: "15%",
        top: "15%",
        containLabel: true
      },
      xAxis: {
        type: "category",
        data: sortedData.map(item => item.name),
        axisLabel: {
          interval: 0,
          rotate: 45,
          textStyle: {
            color: "#666"
          }
        }
      },
      yAxis: {
        type: "value",
        name: "金额 (元)",
        axisLabel: {
          formatter: value => {
            if (value >= 10000) {
              return (value / 10000).toFixed(2) + "万";
            }
            return value;
          }
        }
      },
      series: [
        {
          name: "金额",
          type: "bar",
          barWidth: "60%",
          data: sortedData.map((item, index) => ({
            ...item,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                {
                  offset: 0,
                  color: colorPalette[index % colorPalette.length].start
                },
                {
                  offset: 1,
                  color: colorPalette[index % colorPalette.length].end
                }
              ])
            }
          })),
          label: {
            show: true,
            position: "top",
            formatter: params => {
              if (params.value >= 10000) {
                return (params.value / 10000).toFixed(2) + "万";
              }
              return params.value.toFixed(2);
            }
          }
        }
      ]
    };

    // 根据图表类型设置配置并重新渲染
    myChart.setOption(chartType.value === "pie" ? pieOption : barOption, true);

    // 确保图表大小正确
    myChart.resize();

    // 移除之前的事件监听器
    const resizeHandler = () => myChart.resize();
    window.removeEventListener("resize", resizeHandler);
    window.addEventListener("resize", resizeHandler);

    // 在组件卸载时清理
    onBeforeUnmount(() => {
      window.removeEventListener("resize", resizeHandler);
      myChart.dispose();
    });
  }
};

onBeforeMount(() => {
  fetchResourceTypeData();
});

// 监视账单周期变化
watch(
  () => form.bill_cycle,
  () => {
    fetchResourceTypeData();
  }
);

// 监听图表类型变化
watch(chartType, () => {
  updatePieChart();
});
</script>

<style scoped>
.pie-chart-container {
  width: 100%;
  height: 600px;
  padding: 20px;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 8%);
  transition: all 0.3s ease;
}

.pie-chart-container:hover {
  box-shadow: 0 6px 20px rgb(0 0 0 / 12%);
}

.chart-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  margin-bottom: 20px;
}

:deep(.el-date-picker) {
  width: 220px;
  margin-bottom: 20px;
}

:deep(.el-input__wrapper) {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 4%);
}

:deep(.el-input__wrapper:hover) {
  box-shadow: 0 2px 12px rgb(0 0 0 / 8%);
}

:deep(.el-radio-group) {
  margin-left: 20px;
}

:deep(.el-radio-button__inner) {
  padding: 8px 20px;
}

.no-data-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 500px;
  margin-top: 20px;
  font-size: 16px;
  color: #999;
  background-color: #f9f9f9;
  border-radius: 8px;
}

.loading-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 500px;
  margin-top: 20px;
  font-size: 16px;
  color: #409eff;
  background-color: #f9f9f9;
  border-radius: 8px;
}

.pie-chart {
  height: 500px;
  margin-top: 20px;
}
</style>
