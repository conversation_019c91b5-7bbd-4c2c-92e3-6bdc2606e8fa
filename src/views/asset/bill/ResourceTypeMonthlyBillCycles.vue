<template>
  <div class="resource-type-bill-card">
    <h5>资源类型费用账单</h5>
    <el-form :inline="true" class="form-style">
      <el-form-item label="选择月份范围" class="form-item-style">
        <el-date-picker
          v-model="selectedMonthRange"
          type="monthrange"
          format="YYYY-MM"
          value-format="YYYY-MM"
          placeholder="选择月份范围"
          class="date-picker-style"
          :clearable="false"
          @change="fetchBillData"
        />
      </el-form-item>
    </el-form>
    <el-table
      :data="billData"
      style="width: 100%"
      :default-sort="{ prop: 'resource_type', order: 'ascending' }"
      class="table-style"
      @sort-change="handleSortChange"
    >
      <el-table-column
        v-for="column in columns"
        :key="column"
        :prop="column"
        :label="
          column === 'resource_type'
            ? '资源类型'
            : column.includes('_change')
              ? column.replace('_change', '') + '波动'
              : column
        "
        :sortable="true"
        class="table-column-style"
      >
        <template v-slot="scope">
          <div
            class="table-cell-style"
            :style="{
              fontWeight: column === 'resource_type' ? 'bold' : 'normal',
              fontSize: column.includes('_change') ? '12px' : '14px',
              color: column.includes('_change')
                ? scope.row[column] < 0
                  ? 'green'
                  : 'red'
                : 'normal'
            }"
          >
            <template
              v-if="
                typeof scope.row[column] === 'number' &&
                column.includes('_change')
              "
            >
              <el-tag
                :style="{
                  color:
                    scope.row[column] < 0
                      ? 'green'
                      : scope.row[column] === 0
                        ? 'grey'
                        : 'red'
                }"
                size="small"
              >
                {{ (scope.row[column] || 0).toFixed(2) }}
              </el-tag>
            </template>
            <template v-else-if="typeof scope.row[column] === 'number'">
              {{ (scope.row[column] || 0).toFixed(2) }}
            </template>
            <template v-else>
              {{ scope.row[column] }}
            </template>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, defineProps } from "vue";
import { getResourceTypeDataByCyclesAPI } from "@/api/asset/bill";
import { message } from "@/utils/message";

const props = defineProps<{
  account_id: number;
}>();

const billData = ref([]);
const selectedMonthRange = ref<string[]>([]);
const columns = ref<string[]>([]);

const setDefaultMonthRange = () => {
  const now = new Date();
  const currentYear = now.getFullYear();
  const currentMonth = now.getMonth() + 1;

  const startMonth = new Date(currentYear, currentMonth - 3, 1);
  const endMonth = new Date(currentYear, currentMonth, 0);

  selectedMonthRange.value = [
    `${startMonth.getFullYear()}-${String(startMonth.getMonth() + 1).padStart(2, "0")}`,
    `${endMonth.getFullYear()}-${String(endMonth.getMonth() + 1).padStart(2, "0")}`
  ];
};

const fetchBillData = async () => {
  if (!selectedMonthRange.value || selectedMonthRange.value.length !== 2) {
    message("请选择月份范围", { type: "warning" });
    return;
  }

  const [startMonth, endMonth] = selectedMonthRange.value;
  try {
    const res = await getResourceTypeDataByCyclesAPI(props.account_id, {
      start_time: startMonth,
      end_time: endMonth
    });
    if (res.success) {
      billData.value = res.data.data;
      columns.value = res.data.columns;
    } else {
      message(res.msg || "获取账单失败", { type: "error" });
    }
  } catch (error) {
    message("获取账单失败: " + error, { type: "error" });
  }
};

const handleSortChange = sort => {
  let sortedData = billData.value;
  if (sort.prop) {
    sortedData = billData.value.sort((a, b) => {
      if (sort.order === "ascending") {
        return a[sort.prop] - b[sort.prop];
      } else {
        return b[sort.prop] - a[sort.prop];
      }
    });
  }
  billData.value = sortedData;
};

onMounted(() => {
  setDefaultMonthRange();
  fetchBillData();
});
</script>

<style scoped>
.resource-type-bill-card {
  padding: 20px;
  margin: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgb(0 0 0 / 10%);
}

h5 {
  margin-bottom: 20px;
  font-size: 24px;
  color: #333;
}

.form-style {
  margin-bottom: 20px;
}

.form-item-style {
  margin-right: 20px;
}

.date-picker-style {
  width: 100%;
}

.table-style {
  margin-top: 20px;
}

.table-column-style {
  font-size: 14px;
  color: #333;
  text-align: center;
}

.table-cell-style {
  padding: 10px;
}
</style>
