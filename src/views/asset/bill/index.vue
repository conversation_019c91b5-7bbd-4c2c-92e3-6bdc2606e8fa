<template>
  <div class="main">
    <el-card class="search-card">
      <el-form
        ref="formRef"
        :inline="true"
        :model="form"
        class="search-form"
        @submit.prevent
      >
        <el-form-item label="账户" prop="account_id">
          <el-select
            v-model="form.account_id"
            class="search-select"
            filterable
            clearable
            @change="onSearch"
          >
            <el-option
              v-for="account in accounts"
              :key="account.id"
              :label="account.name"
              :value="account.id"
            />
          </el-select>
          <el-button
            class="sync-button"
            :icon="useRenderIcon('ep:refresh')"
            @click="getAllCloudAccounts"
          />
        </el-form-item>
        <el-form-item label="账单周期" prop="bill_cycle">
          <el-date-picker
            v-model="form.bill_cycle"
            type="month"
            format="YYYY-MM"
            value-format="YYYY-MM"
            placeholder="选择账单周期"
            clearable
            @change="onSearch"
          />
        </el-form-item>
        <el-form-item label="资源 ID" prop="resource_id">
          <el-input
            v-model="form.resource_id"
            placeholder="请输入资源 ID"
            clearable
            @keyup.enter="onSearch"
          />
        </el-form-item>
        <el-form-item label="资源名称" prop="resource_name">
          <el-input
            v-model="form.resource_name"
            placeholder="请输入资源名称"
            clearable
            @keyup.enter="onSearch"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            :icon="useRenderIcon('ri:search-line')"
            :loading="loading"
            class="search-button"
            style="
              background: linear-gradient(135deg, #409eff 0%, #4facff 100%);
              border: none;
              box-shadow: 0 4px 12px rgb(64 158 255 / 30%);
              transition: all 0.3s ease;
            "
            @click="onSearch"
          >
            搜索
          </el-button>
          <el-button
            :icon="useRenderIcon(Refresh)"
            class="reset-button"
            style="
              color: #606266;
              border-color: #dcdfe6;
              transition: all 0.3s ease;
            "
            @click="resetForm(formRef)"
          >
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card class="action-card">
      <div class="action-buttons">
        <el-button type="warning" class="action-button" @click="syncBill">
          同步账单
        </el-button>
        <el-button
          type="primary"
          class="action-button"
          @click="handleAccountMonthlyBill"
        >
          账号月度账单
        </el-button>
        <el-button
          type="primary"
          class="action-button"
          @click="handleResourceTypeMonthlyBill"
        >
          资源类型金额占比
        </el-button>
        <el-button
          type="primary"
          class="action-button"
          @click="handleResourceTypeMonthlyBillCycles"
        >
          资源类型账单周期
        </el-button>
        <el-button
          type="primary"
          class="action-button"
          @click="handleCloudTypeMonthlyBills"
        >
          云类型月度账单
        </el-button>
        <el-button
          type="success"
          class="action-button"
          :disabled="multipleSelection.length === 0"
          @click="showBillSummary"
        >
          账单汇总信息
        </el-button>
        <div v-if="multipleSelection.length > 0" class="selected-info">
          已选择
          <span class="selected-count">{{ multipleSelection.length }}</span>
          条账单
        </div>
      </div>
    </el-card>

    <PureTableBar title="月份账单" :columns="columns" @refresh="onSearch">
      <template v-slot="{ size, dynamicColumns }">
        <pure-table
          ref="tableRef"
          row-key="id"
          align-whole="left"
          table-layout="auto"
          :loading="loading"
          :size="size"
          :minHeight="500"
          :data="dataList"
          :columns="dynamicColumns"
          :pagination="{ ...pagination, size }"
          :header-cell-style="{
            fontWeight: '600'
          }"
          :row-style="{
            cursor: 'pointer',
            transition: 'background-color 0.2s ease'
          }"
          :cell-style="{
            padding: '12px 10px'
          }"
          border
          stripe
          highlight-current-row
          @page-size-change="handleSizeChange"
          @page-current-change="handleCurrentChange"
          @selection-change="handleSelectionChange"
        >
          <template #empty>
            <el-empty
              description="暂无数据"
              :image-size="120"
              style="padding: 40px 0"
            />
          </template>
        </pure-table>
      </template>
    </PureTableBar>

    <el-dialog
      v-model="summaryDialogVisible"
      :title="`账单汇总信息 (${multipleSelection.length}条)`"
      width="900px"
      destroy-on-close
      class="summary-dialog"
    >
      <div class="summary-content">
        <div class="summary-section total-section">
          <div class="summary-item total-item">
            <div class="label">
              <el-icon><Money /></el-icon>
              <span>总金额</span>
            </div>
            <div class="value">{{ totalAmount.toFixed(2) }} 元</div>
          </div>
        </div>
        <div class="summary-section">
          <div class="summary-title">
            <el-icon><Calendar /></el-icon>
            <span>账单周期及金额</span>
          </div>
          <div class="summary-list">
            <div
              v-for="item in sortedBillCycles"
              :key="item.name"
              class="summary-item"
            >
              <span class="item-name">{{ item.name }}</span>
              <span class="item-amount">{{ item.amount.toFixed(2) }} 元</span>
            </div>
          </div>
        </div>
        <div class="summary-section">
          <div class="summary-title">
            <el-icon><Document /></el-icon>
            <span>账单类型及金额</span>
          </div>
          <div class="summary-list">
            <div
              v-for="item in sortedBillTypes"
              :key="item.name"
              class="summary-item"
            >
              <span class="item-name">{{ item.name }}</span>
              <span class="item-amount">{{ item.amount.toFixed(2) }} 元</span>
            </div>
          </div>
        </div>
        <div class="summary-section">
          <div class="summary-title">
            <el-icon><Location /></el-icon>
            <span>区域及金额</span>
          </div>
          <div class="summary-list">
            <div
              v-for="item in sortedRegions"
              :key="item.name"
              class="summary-item"
            >
              <span class="item-name">{{ item.name }}</span>
              <span class="item-amount">{{ item.amount.toFixed(2) }} 元</span>
            </div>
          </div>
        </div>
        <div class="summary-section">
          <div class="summary-title">
            <el-icon><Cpu /></el-icon>
            <span>资源类型及金额</span>
          </div>
          <div class="summary-list">
            <div
              v-for="item in sortedResourceTypes"
              :key="item.name"
              class="summary-item"
            >
              <span class="item-name">{{ item.name }}</span>
              <span class="item-amount">{{ item.amount.toFixed(2) }} 元</span>
            </div>
          </div>
        </div>
        <div class="summary-section">
          <div class="summary-title">
            <el-icon><Folder /></el-icon>
            <span>资源组及金额</span>
          </div>
          <div class="summary-list">
            <div
              v-for="item in sortedResourceGroups"
              :key="item.name"
              class="summary-item"
            >
              <span class="item-name">{{ item.name || "未设置资源组" }}</span>
              <span class="item-amount">{{ item.amount.toFixed(2) }} 元</span>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { h, ref, onMounted, computed } from "vue";
import { useRole } from "./hook";
import { PureTableBar } from "@/components/RePureTableBar";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import Refresh from "@iconify-icons/ep/refresh";
import type { FormInstance } from "element-plus";
import MonthlyBill from "../cloud-account/MonthlyBill.vue";
import { addDialog } from "@/components/ReDialog";
import { message } from "@/utils/message";
import ResourceTypeMonthlyBillCycles from "./ResourceTypeMonthlyBillCycles.vue";
import CloudTypeMonthlyBills from "./CloudTypeMonthlyBills.vue";
import {
  Money,
  Calendar,
  Document,
  Location,
  Cpu,
  Folder
} from "@element-plus/icons-vue";

// 定义组件名
defineOptions({
  name: "MonthlyBill"
});

const formRef = ref<FormInstance>();
const tableRef = ref();

// 获取当前月份
const currentMonth = new Date().toISOString().slice(0, 7); // 格式为 YYYY-MM

const {
  form,
  loading,
  columns,
  dataList,
  pagination,
  onSearch,
  resetForm,
  handleSizeChange,
  handleCurrentChange,
  accounts,
  getAllCloudAccounts,
  handleAccountMonthlyBill,
  handleResourceTypeMonthlyBill
} = useRole();

// 初始化 form 对象
form.bill_cycle = currentMonth; // 默认选择当前月份

// 在组件挂载时获取账户数据
onMounted(() => {
  getAllCloudAccounts();
});

const syncBill = () => {
  if (!form.account_id) {
    message("请选择账户", {
      type: "warning"
    });
    return;
  } else {
    const selectAccounts = accounts.value.filter(
      account => account.id === form.account_id
    );
    addDialog({
      title: "同步账单",
      draggable: true,
      hideFooter: true,
      width: "30%",
      contentRenderer: () => h(MonthlyBill, { accounts: selectAccounts })
    });
  }
};

const handleResourceTypeMonthlyBillCycles = () => {
  if (!form.account_id) {
    message("请选择账户", { type: "warning" });
    return;
  }

  addDialog({
    title: "资源类型账单周期",
    hideFooter: true,
    draggable: true,
    width: "90%",
    contentRenderer: () =>
      h(ResourceTypeMonthlyBillCycles, { account_id: form.account_id })
  });
};

const handleCloudTypeMonthlyBills = () => {
  if (!form.account_id) {
    message("请选择账户", { type: "warning" });
    return;
  }

  addDialog({
    title: "云账号月度账单",
    hideFooter: true,
    width: "90%",
    contentRenderer: () => h(CloudTypeMonthlyBills)
  });
};

const multipleSelection = ref([]);
const summaryDialogVisible = ref(false);

const handleSelectionChange = val => {
  multipleSelection.value = val;
};

const totalAmount = computed(() => {
  return multipleSelection.value.reduce(
    (sum, item) => sum + (item.amount || 0),
    0
  );
});

const billCycles = computed(() => {
  return [...new Set(multipleSelection.value.map(item => item.bill_cycle))];
});

const billTypes = computed(() => {
  return [...new Set(multipleSelection.value.map(item => item.charge_mode))];
});

const regions = computed(() => {
  return [...new Set(multipleSelection.value.map(item => item.region))];
});

const resourceTypes = computed(() => {
  return [...new Set(multipleSelection.value.map(item => item.resource_type))];
});

const resourceGroups = computed(() => {
  return [...new Set(multipleSelection.value.map(item => item.resource_group))];
});

const getCycleAmount = cycle => {
  return multipleSelection.value
    .filter(item => item.bill_cycle === cycle)
    .reduce((sum, item) => sum + (item.amount || 0), 0);
};

const getTypeAmount = type => {
  return multipleSelection.value
    .filter(item => item.charge_mode === type)
    .reduce((sum, item) => sum + (item.amount || 0), 0);
};

const getRegionAmount = region => {
  return multipleSelection.value
    .filter(item => item.region === region)
    .reduce((sum, item) => sum + (item.amount || 0), 0);
};

const getResourceTypeAmount = type => {
  return multipleSelection.value
    .filter(item => item.resource_type === type)
    .reduce((sum, item) => sum + (item.amount || 0), 0);
};

const getResourceGroupAmount = group => {
  return multipleSelection.value
    .filter(item => item.resource_group === group)
    .reduce((sum, item) => sum + (item.amount || 0), 0);
};

const getSortedItems = (items, getAmount) => {
  return items
    .map(item => ({
      name: item,
      amount: getAmount(item)
    }))
    .sort((a, b) => b.amount - a.amount);
};

const sortedBillCycles = computed(() => {
  return getSortedItems(billCycles.value, getCycleAmount);
});

const sortedBillTypes = computed(() => {
  return getSortedItems(billTypes.value, getTypeAmount);
});

const sortedRegions = computed(() => {
  return getSortedItems(regions.value, getRegionAmount);
});

const sortedResourceTypes = computed(() => {
  return getSortedItems(resourceTypes.value, getResourceTypeAmount);
});

const sortedResourceGroups = computed(() => {
  return getSortedItems(resourceGroups.value, getResourceGroupAmount);
});

const showBillSummary = () => {
  summaryDialogVisible.value = true;
};
</script>

<style scoped lang="scss">
.main {
  padding: 20px;
  border-radius: 12px;
}

.search-card,
.action-card {
  margin-bottom: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 8%);
  transition: box-shadow 0.3s ease;

  &:hover {
    box-shadow: 0 6px 20px rgb(0 0 0 / 12%);
  }
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;

  :deep(.el-form-item) {
    margin-bottom: 0;
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: #606266;
  }

  .el-input,
  .el-select,
  .el-date-picker {
    width: 220px;

    :deep(input),
    :deep(.el-input__wrapper) {
      border-color: #e4e7ed;
      border-radius: 8px;

      &:focus {
        border-color: #409eff;
        box-shadow: 0 0 0 1px rgb(64 158 255 / 20%);
      }
    }
  }

  .sync-button {
    margin-left: 8px;
    color: #409eff;
    transition: color 0.3s ease;

    &:hover {
      color: #0056b3;
    }
  }
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  justify-content: flex-start;
}

.search-button,
.reset-button,
.action-button {
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
    transform: translateY(-2px);
  }
}

.search-button {
  color: white;
  background-color: #409eff;
  border-color: #409eff;
}

.action-button {
  margin-right: 8px;
}

:deep(.pure-table) {
  overflow: hidden;
  border: 1px solid #e4e7ed;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 4%);
}

.selected-info {
  display: inline-flex;
  align-items: center;
  margin-left: 16px;
  font-size: 14px;
  color: #606266;

  .selected-count {
    margin: 0 4px;
    font-weight: 600;
    color: var(--el-color-primary);
  }
}

.summary-dialog {
  :deep(.el-dialog__header) {
    padding: 20px;
    margin: 0;
    border-bottom: 1px solid #ebeef5;
  }

  :deep(.el-dialog__title) {
    font-size: 18px;
    font-weight: 600;
    color: #303133;
  }

  :deep(.el-dialog__body) {
    padding: 0;
  }
}

.summary-content {
  max-height: 600px;
  padding: 20px;
  overflow-y: auto;
  background-color: #f9f9f9;
  border-radius: 8px;

  .summary-section {
    padding: 16px;
    margin-bottom: 24px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 16px 0 rgb(0 0 0 / 15%);
    }

    .summary-title {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      font-size: 16px;
      font-weight: 600;
      color: #303133;

      .el-icon {
        margin-right: 8px;
        font-size: 18px;
        color: var(--el-color-primary);
      }
    }

    .summary-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px;
      margin-bottom: 8px;
      background-color: #f5f7fa;
      border-radius: 6px;
      transition: all 0.3s ease;

      &:hover {
        background-color: #e4e7ed;
        transform: translateX(4px);
      }

      .item-name {
        font-size: 14px;
        color: #606266;
      }

      .item-amount {
        font-size: 14px;
        font-weight: 600;
        color: var(--el-color-primary);
      }
    }
  }

  .total-section {
    background: linear-gradient(135deg, #f6f8fc 0%, #f0f4f9 100%);
    border: 1px solid #e4e7ed;

    .total-item {
      padding: 16px;
      background: transparent;

      .label {
        display: flex;
        align-items: center;
        font-size: 16px;
        color: #606266;

        .el-icon {
          margin-right: 8px;
          font-size: 20px;
          color: var(--el-color-primary);
        }
      }

      .value {
        font-size: 24px;
        font-weight: 600;
        color: var(--el-color-primary);
      }
    }
  }
}

.resource-list {
  display: flex;
  flex-direction: column;
  gap: 8px;

  .resource-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    background-color: #f5f7fa;
    border-radius: 4px;
    transition: background-color 0.3s ease;

    &:hover {
      background-color: #e4e7ed;
    }

    .resource-name {
      flex: 1;
      margin-right: 16px;
      overflow: hidden;
      font-size: 14px;
      color: #606266;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .resource-amount {
      font-size: 14px;
      font-weight: 600;
      color: var(--el-color-primary);
      white-space: nowrap;
    }
  }
}

.summary-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  margin-bottom: 8px;
  background-color: #f5f7fa;
  border-radius: 4px;
  transition: background-color 0.3s ease;

  &:hover {
    background-color: #e4e7ed;
  }

  .item-name {
    font-size: 14px;
    color: #606266;
  }

  .item-amount {
    font-size: 14px;
    font-weight: 600;
    color: var(--el-color-primary);
  }
}
</style>
