<template>
  <div>
    <div class="date-picker-container">
      <el-date-picker
        v-model="form.date_range"
        type="monthrange"
        format="YYYY-MM"
        value-format="YYYY-MM"
        placeholder="选择一段时间"
        clearable
        range-separator="至"
        start-placeholder="开始月份"
        end-placeholder="结束月份"
        :disabled="loading"
        :shortcuts="[
          {
            text: '最近三个月',
            value: () => {
              const end = dayjs();
              const start = dayjs().subtract(2, 'month');
              return [start.format('YYYY-MM'), end.format('YYYY-MM')];
            }
          },
          {
            text: '最近半年',
            value: () => {
              const end = dayjs();
              const start = dayjs().subtract(5, 'month');
              return [start.format('YYYY-MM'), end.format('YYYY-MM')];
            }
          },
          {
            text: '最近一年',
            value: () => {
              const end = dayjs();
              const start = dayjs().subtract(11, 'month');
              return [start.format('YYYY-MM'), end.format('YYYY-MM')];
            }
          },
          {
            text: '最近两年',
            value: () => {
              const end = dayjs();
              const start = dayjs().subtract(23, 'month');
              return [start.format('YYYY-MM'), end.format('YYYY-MM')];
            }
          }
        ]"
      />
    </div>
    <div v-if="!loading && billCycles.length > 0" class="tips-container"></div>
    <div v-if="loading" class="loading-container">
      <el-icon class="loading-icon"><Loading /></el-icon>
      <span>正在加载数据...</span>
    </div>
    <div v-else-if="!billCycles.length" class="no-data-message">
      <el-icon :size="32" color="#999"><DataLine /></el-icon>
      <p>没有找到相关账单数据</p>
      <p>请尝试选择其他时间范围</p>
    </div>
    <div ref="chartRef" class="chart-container" />
  </div>
</template>

<script setup lang="ts">
import {
  ref,
  onBeforeMount,
  reactive,
  watch,
  nextTick,
  onMounted,
  onBeforeUnmount
} from "vue";
import * as echarts from "echarts";
import { getCloudTypeMonthlyBillsAPI } from "@/api/asset/bill";
import { message } from "@/utils/message";
import dayjs from "dayjs";
import { Loading, DataLine } from "@element-plus/icons-vue";

const form = reactive({
  date_range: [
    dayjs().subtract(6, "month").format("YYYY-MM"),
    dayjs().format("YYYY-MM")
  ]
});

// 确保date_range有默认值
onBeforeMount(() => {
  if (!form.date_range || form.date_range.length !== 2) {
    form.date_range = [
      dayjs().subtract(6, "month").format("YYYY-MM"),
      dayjs().format("YYYY-MM")
    ];
  }
});

// 监听 date_range 变化，更新 start_time 和 end_time
watch(
  () => form.date_range,
  newVal => {
    if (newVal && newVal.length === 2) {
      getCloudTypeMonthlyBills();
    }
  }
);

const chartRef = ref<HTMLDivElement | null>(null);
const billCycles = ref<string[]>([]);
const cloudData = ref<any[]>([]);
const loading = ref(false);

const getCloudTypeMonthlyBills = async () => {
  loading.value = true;
  try {
    const res = await getCloudTypeMonthlyBillsAPI({
      start_time: form.date_range[0],
      end_time: form.date_range[1]
    });
    if (res.success && res.data) {
      cloudData.value = res.data;

      // 提取所有账单周期并去重排序
      const allCycles = new Set<string>();
      res.data.forEach(cloud => {
        cloud.monthly_bills.forEach(bill => {
          if (bill.bill_cycle) {
            allCycles.add(bill.bill_cycle);
          }
        });
      });

      billCycles.value = Array.from(allCycles).sort();
      updateChart(); // 更新图表
    } else {
      message(res.msg || "获取数据失败", { type: "error" });
      billCycles.value = [];
      cloudData.value = [];
    }
  } catch (error) {
    console.error(error);
    message("获取数据失败", { type: "error" });
    billCycles.value = [];
    cloudData.value = [];
  } finally {
    loading.value = false;
  }
};

const updateChart = () => {
  // 添加调试信息
  console.log("更新图表", {
    chartRef: !!chartRef.value,
    billCycles: billCycles.value.length,
    cloudData: cloudData.value.length
  });

  if (!chartRef.value) {
    console.error("图表容器不存在");
    return;
  }

  let myChart = echarts.getInstanceByDom(chartRef.value);
  if (myChart) {
    myChart.dispose();
  }

  myChart = echarts.init(chartRef.value);

  if (billCycles.value.length > 0) {
    // 预设的渐变色方案
    const colorPalette = [
      { start: "#36CBCB", end: "#2B85E4" }, // 青蓝渐变
      { start: "#FF9900", end: "#ED4014" }, // 橙红渐变
      { start: "#2D8CF0", end: "#0C2A54" }, // 深蓝渐变
      { start: "#19BE6B", end: "#0C692B" }, // 绿色渐变
      { start: "#FF6B6B", end: "#CC0000" } // 红色渐变
    ];

    const series = cloudData.value.map((cloud, index) => {
      const cloudName = cloud.name || `云服务${cloud.cloud_type}`;
      const data = billCycles.value.map(cycle => {
        const bill = cloud.monthly_bills.find(b => b.bill_cycle === cycle);
        return bill ? (bill.amount !== null ? Number(bill.amount) : 0) : 0;
      });

      return {
        name: cloudName,
        type: "line",
        data: data,
        smooth: true,
        symbolSize: 8,
        stack: "Total",
        areaStyle: {
          opacity: 0.15,
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: colorPalette[index % colorPalette.length].start
            },
            { offset: 1, color: colorPalette[index % colorPalette.length].end }
          ])
        },
        lineStyle: {
          width: 3,
          color: colorPalette[index % colorPalette.length].end
        },
        itemStyle: {
          color: colorPalette[index % colorPalette.length].end,
          borderWidth: 2,
          borderColor: "#fff"
        },
        emphasis: {
          focus: "series",
          itemStyle: {
            borderWidth: 3,
            shadowBlur: 10,
            shadowColor: "rgba(0,0,0,0.3)"
          },
          label: {
            show: true,
            formatter: params => {
              if (params.value >= 10000) {
                return (params.value / 10000).toFixed(2) + "万";
              }
              return params.value.toFixed(2);
            },
            backgroundColor: "rgba(255,255,255,0.9)",
            padding: [4, 8],
            borderRadius: 4
          }
        }
      };
    });

    const option = {
      title: {
        text: "云服务月度账单趋势",
        subtext: `${form.date_range[0]} 至 ${form.date_range[1]}`,
        left: "center",
        top: 50,
        textStyle: {
          fontSize: 22,
          fontWeight: "bold",
          color: "#333"
        },
        subtextStyle: {
          fontSize: 14,
          color: "#666",
          padding: [20, 0, 0, 0]
        }
      },
      tooltip: {
        trigger: "axis",
        backgroundColor: "rgba(255,255,255,0.95)",
        borderWidth: 1,
        borderColor: "#ccc",
        padding: [10, 15],
        textStyle: {
          color: "#333"
        },
        formatter: params => {
          let result = `<div style="font-weight:bold;margin-bottom:8px;">${params[0].axisValue}</div>`;

          params.sort((a, b) => b.value - a.value);

          let total = params.reduce(
            (sum, param) => sum + (param.value || 0),
            0
          );

          params.forEach(param => {
            let valueDisplay =
              param.value >= 10000
                ? (param.value / 10000).toFixed(2) + "万元"
                : param.value.toFixed(2) + "元";

            let percentage = ((param.value / total) * 100).toFixed(1);

            result += `
              <div style="display:flex;justify-content:space-between;align-items:center;margin:5px 0;">
                <span style="display:flex;align-items:center;">
                  <span style="display:inline-block;width:10px;height:10px;border-radius:50%;background-color:${param.color};margin-right:8px;"></span>
                  <span style="color:#666;">${param.seriesName}:</span>
                </span>
                <span style="margin-left:15px;font-weight:bold;color:${param.color}">
                  ${valueDisplay} (${percentage}%)
                </span>
              </div>`;
          });

          result += `
            <div style="margin-top:8px;padding-top:8px;border-top:1px solid #eee;">
              <div style="display:flex;justify-content:space-between;align-items:center;">
                <span style="color:#666;">总计：</span>
                <span style="font-weight:bold;color:#409EFF">
                  ${total >= 10000 ? (total / 10000).toFixed(2) + "万元" : total.toFixed(2) + "元"}
                </span>
              </div>
            </div>`;

          return result;
        }
      },
      legend: {
        type: "scroll",
        bottom: 10,
        icon: "roundRect",
        itemWidth: 12,
        itemHeight: 12,
        itemGap: 20,
        textStyle: {
          color: "#666",
          fontSize: 13
        },
        pageButtonItemGap: 5,
        pageButtonGap: 10,
        pageButtonPosition: "end",
        pageIconColor: "#409EFF",
        pageIconInactiveColor: "#ccc",
        pageIconSize: 12,
        pageTextStyle: {
          color: "#666"
        }
      },
      grid: {
        left: "3%",
        right: "4%",
        bottom: "18%",
        top: "25%",
        containLabel: true
      },
      xAxis: {
        type: "category",
        data: billCycles.value,
        boundaryGap: false,
        axisLine: {
          lineStyle: {
            color: "#ddd"
          }
        },
        axisTick: {
          alignWithLabel: true,
          lineStyle: {
            color: "#ddd"
          }
        },
        axisLabel: {
          rotate: 45,
          margin: 15,
          formatter: value => {
            const [year, month] = value.split("-");
            return `${year}年${month}月`;
          },
          textStyle: {
            color: "#666",
            fontSize: 12
          }
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: "#eee",
            type: "dashed"
          }
        }
      },
      yAxis: {
        type: "value",
        name: "金额 (元)",
        nameTextStyle: {
          color: "#666",
          fontSize: 14,
          padding: [0, 0, 0, 50]
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: "#ddd"
          }
        },
        axisTick: {
          show: true
        },
        splitLine: {
          lineStyle: {
            color: "#eee",
            type: "dashed"
          }
        },
        axisLabel: {
          formatter: value => {
            if (value >= 1000000) {
              return (value / 1000000).toFixed(2) + "百万";
            } else if (value >= 10000) {
              return (value / 10000).toFixed(2) + "万";
            }
            return value + "元";
          },
          textStyle: {
            color: "#666",
            fontSize: 12
          }
        }
      },
      series: series
    };

    myChart.setOption(option);
  } else {
    console.warn("没有数据可供显示");
  }
};

// 将resize处理函数提取出来以便于移除事件监听器
const handleResize = () => {
  if (chartRef.value) {
    const chart = echarts.getInstanceByDom(chartRef.value);
    if (chart) {
      chart.resize();
    }
  }
};

// 生命周期钩子已经在上面导入

// 组件挂载时初始化图表
onMounted(() => {
  // 确保图表容器已经渲染
  nextTick(() => {
    if (chartRef.value && billCycles.value.length > 0) {
      updateChart();
    } else {
      // 如果还没有数据，获取数据
      getCloudTypeMonthlyBills();
    }
  });
});

// 组件卸载时清除事件监听器
onBeforeUnmount(() => {
  window.removeEventListener("resize", handleResize);
  // 清除图表实例
  if (chartRef.value) {
    const chart = echarts.getInstanceByDom(chartRef.value);
    if (chart) {
      chart.dispose();
    }
  }
});

// 监视数据变化以更新图表 - 使用深度监听
watch(
  () => form.date_range,
  newVal => {
    if (newVal && newVal.length === 2) {
      getCloudTypeMonthlyBills();
    }
  },
  { deep: true, immediate: true }
);

// 监听图表数据变化，更新图表
watch(
  [() => cloudData.value, () => billCycles.value],
  () => {
    // 使用nextTick确保在DOM更新后再更新图表
    nextTick(() => {
      if (chartRef.value && !loading.value) {
        updateChart();
      }
    });
  },
  { deep: true }
);
</script>

<style scoped>
.date-picker-container {
  display: flex;
  justify-content: center;
  padding: 0 20px;
  margin-bottom: 20px;
}

.tips-container {
  display: flex;
  justify-content: center;
  margin-bottom: 10px;
}

.ml-5 {
  margin-left: 5px;
}

:deep(.el-date-editor) {
  width: 380px !important;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 4%);
  transition: all 0.3s ease;
}

:deep(.el-date-editor:hover) {
  box-shadow: 0 4px 12px rgb(0 0 0 / 8%);
}

:deep(.el-date-editor .el-range-input) {
  font-size: 14px;
}

:deep(.el-date-editor .el-range__icon) {
  color: #409eff;
}

:deep(.el-picker-panel) {
  border-radius: 8px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 12%);
}

:deep(.el-picker-panel .el-picker-panel__sidebar) {
  background-color: #f8f9fb;
  border-right: 1px solid #ebeef5;
}

:deep(.el-picker-panel .el-picker-panel__shortcut) {
  padding: 12px 16px;
  font-size: 14px;
  color: #606266;
}

:deep(.el-picker-panel .el-picker-panel__shortcut:hover) {
  color: #409eff;
  background-color: #ecf5ff;
}

.chart-container {
  width: 100%;
  height: 650px;
  padding: 20px;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 8%);
  transition: all 0.3s ease;
}

.chart-container:hover {
  box-shadow: 0 6px 20px rgb(0 0 0 / 12%);
}

.no-data-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 600px;
  font-size: 16px;
  color: #999;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgb(0 0 0 / 10%);
}

.no-data-message .el-icon {
  margin-bottom: 16px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 600px;
  color: #409eff;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgb(0 0 0 / 10%);
}

.loading-icon {
  margin-bottom: 10px;
  font-size: 32px;
  animation: rotate 1.5s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}
</style>
