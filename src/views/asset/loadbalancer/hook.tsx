import dayjs from "dayjs";
import type { PaginationProps } from "@pureadmin/table";
import { reactive, ref, onMounted } from "vue";
import { message } from "@/utils/message";
import {
  getLoadbalancersAPI,
  type Loadbalancer
} from "@/api/asset/loadbalancer";
import {
  getAllCloudAccountsAPI,
  type CloudAccount
} from "@/api/asset/cloud-account";
import { getAllDatacentersAPI, type Datacenter } from "@/api/asset/datacenter";

export function useRole() {
  const form = reactive({
    account_id: undefined,
    datacenter_id: undefined,
    keyword: undefined,
    host: undefined,
    loadbalancer_type: undefined,
    loadbalancer_id: undefined
  });
  const dataList = ref<Loadbalancer[]>([]);
  const loading = ref<boolean>(true);
  const accounts = ref<CloudAccount[]>([]);
  const datacenters = ref<Datacenter[]>([]);
  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true,
    pageSizes: [10, 20, 30, 40, 50, 100]
  });

  const getStatusText = (status: string) => {
    switch (status) {
      case "running":
        return { text: "运行中", type: "success" };
      case "active":
      case "Active":
        return { text: "活跃", type: "success" };
      case "ONLINE":
        return { text: "在线", type: "success" };
      case "inactive":
        return { text: "已停止", type: "danger" };
      case "stopped":
        return { text: "已停止", type: "danger" };
      case "error":
        return { text: "错误", type: "danger" };
      default:
        return { text: "未知", type: "info" };
    }
  };

  const columns: TableColumnList = [
    {
      label: "账户/数据中心",
      prop: "account",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <div style="white-space: pre-line; line-height: 1.5;">
          <p style={{ margin: 0 }}>{row.account || "无"}</p>
          <p style={{ margin: 0 }}>{row.datacenter || "无"}</p>
        </div>
      )
    },
    {
      label: "资源组",
      prop: "resource_group",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <div style="white-space: pre-line; line-height: 1.5;">
          <p style={{ margin: 0 }}>{row.resource_group_name || "无"}</p>
          <p style={{ margin: 0 }}>{row.resource_group_id || "无"}</p>
        </div>
      )
    },
    {
      label: "名称",
      prop: "name",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <div style="white-space: pre-line; line-height: 1.5;">
          <p style={{ margin: 0 }}>名称：{row.name || "无"}</p>
          <p style={{ margin: 0 }}>
            <el-text type="info" style={{ marginRight: "10px" }}>
              ID：{row.loadbalancer_id || "无"}
            </el-text>
            {row.loadbalancer_type && (
              <el-link
                href={getLoadBalancerLink(row)}
                target="_blank"
                type="primary"
              >
                <iconify-icon-online icon="ep:link" />
              </el-link>
            )}
          </p>
        </div>
      )
    },
    {
      label: "地址",
      prop: "host",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <p style={{ margin: 0, lineHeight: "1.5" }}>{row.host || "无"}</p>
      )
    },
    {
      label: "规格",
      prop: "spec",
      minWidth: 80,
      cellRenderer: ({ row }) => (
        <div style="white-space: pre-line; line-height: 1.5;">
          <p style={{ margin: 0 }}>规格：{row.spec || "无"}</p>
          <p style={{ margin: 0 }}>类型：{row.loadbalancer_type || "无"}</p>
        </div>
      )
    },
    {
      label: "状态",
      prop: "status",
      minWidth: 50,
      cellRenderer: ({ row }) => {
        const { text, type } = getStatusText(row.status);
        return (
          <div style="white-space: pre-line;">
            <el-text type={type} size="large" style="margin: 5px">
              {text}
            </el-text>
            <p style={{ margin: 0 }}>原始状态: {row.status}</p>
          </div>
        );
      }
    },
    {
      label: "时间",
      prop: "sync_time",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <div style="white-space: normal; line-height: 1.5;">
          <p style={{ margin: 0 }}>
            创建时间：
            {dayjs(row.created_at).format("YYYY-MM-DD HH:mm:ss") || "无"}
          </p>
          <p style={{ margin: 0 }}>
            同步时间：
            <b>{dayjs(row.sync_time).format("YYYY-MM-DD HH:mm:ss") || "无"}</b>
          </p>
        </div>
      )
    }
  ];

  function getLoadBalancerLink(row: Loadbalancer) {
    switch (row.loadbalancer_type) {
      case "elb":
        return `https://console.huaweicloud.com/vpc/?region=${row.region_id}&locale=zh-cn#/elb/detail/basicInfo?ulbId=${row.loadbalancer_id}`;
      case "alb":
        return `https://slb.console.aliyun.com/alb/${row.region_id}/albs/${row.loadbalancer_id}`;
      case "nlb":
        return `https://slb.console.aliyun.com/nlb/${row.region_id}/nlbs/${row.loadbalancer_id}`;
      case "slb":
        return `https://slb.console.aliyun.com/slb/${row.region_id}/slbs/${row.loadbalancer_id}`;
      default:
        return "#";
    }
  }

  function handleSizeChange(val: number) {
    pagination.pageSize = val;
    onSearch();
  }

  function handleCurrentChange(val: number) {
    pagination.currentPage = val;
    onSearch();
  }

  async function onSearch() {
    loading.value = true;
    try {
      const res = await getLoadbalancersAPI({
        page: pagination.currentPage,
        limit: pagination.pageSize,
        keyword: form.keyword,
        account_id: form.account_id,
        datacenter_id: form.datacenter_id,
        host: form.host,
        loadbalancer_type: form.loadbalancer_type,
        loadbalancer_id: form.loadbalancer_id
      });
      if (res.success) {
        dataList.value = res.data;
        pagination.total = res.count;
      } else {
        dataList.value = [];
        pagination.total = 0;
      }
    } catch (error) {
      message("请求失败", { type: "error" });
    } finally {
      loading.value = false;
    }
  }

  function syncAllAccounts() {
    getAllCloudAccountsAPI()
      .then(res => {
        if (res.success) {
          accounts.value = res.data;
        } else {
          message(res.msg, { type: "error" });
        }
      })
      .catch(error => {
        message(error, { type: "error" });
      });
  }

  function syncAllDatacenters() {
    getAllDatacentersAPI()
      .then(res => {
        if (res.success) {
          datacenters.value = res.data;
        } else {
          message(res.msg, { type: "error" });
        }
      })
      .catch(error => {
        message(error, { type: "error" });
      });
  }

  const resetForm = formEl => {
    if (!formEl) return;
    formEl.resetFields();
    onSearch();
  };

  onMounted(() => {
    onSearch();
    syncAllAccounts();
    syncAllDatacenters();
  });

  return {
    form,
    loading,
    columns,
    dataList,
    pagination,
    onSearch,
    resetForm,
    handleSizeChange,
    handleCurrentChange,
    accounts,
    syncAllAccounts,
    datacenters,
    syncAllDatacenters
  };
}
