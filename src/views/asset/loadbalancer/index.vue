<template>
  <div class="main">
    <el-card class="search-card">
      <el-form
        ref="formRef"
        :inline="true"
        :model="form"
        class="search-form"
        @submit.prevent
      >
        <el-form-item label="账号" prop="account_id">
          <el-select
            v-model="form.account_id"
            class="search-select"
            filterable
            clearable
            @change="onSearch"
          >
            <el-option
              v-for="(item, index) in accounts"
              :key="index"
              :label="item.name"
              :value="item.id"
            >
              {{ item.name }}
            </el-option>
          </el-select>
          <el-button
            class="sync-button"
            :icon="useRenderIcon(Refresh)"
            @click="syncAllAccounts"
          />
        </el-form-item>
        <el-form-item label="数据中心" prop="datacenter_id">
          <el-select
            v-model="form.datacenter_id"
            class="search-select"
            filterable
            clearable
            @change="onSearch"
          >
            <el-option
              v-for="(item, index) in datacenters"
              :key="index"
              :label="item.name"
              :value="item.id"
            >
              {{ item.name }}
            </el-option>
          </el-select>
          <el-button
            class="sync-button"
            :icon="useRenderIcon(Refresh)"
            @click="syncAllDatacenters"
          />
        </el-form-item>
        <el-form-item label="负载均衡类型" prop="loadbalancer_type">
          <el-select
            v-model="form.loadbalancer_type"
            class="search-select"
            filterable
            clearable
            @change="onSearch"
          >
            <el-option label="NLB" value="nlb">NLB</el-option>
            <el-option label="ALB" value="alb">ALB</el-option>
            <el-option label="SLB" value="slb">SLB</el-option>
            <el-option label="ELB" value="elb">ELB</el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="负载均衡ID" prop="loadbalancer_id">
          <el-input
            v-model="form.loadbalancer_id"
            placeholder="请输入负载均衡ID"
            clearable
            @keyup.enter="onSearch"
          />
        </el-form-item>
        <el-form-item label="地址" prop="host">
          <el-input
            v-model="form.host"
            placeholder="请输入地址"
            clearable
            @keyup.enter="onSearch"
          />
        </el-form-item>
        <el-form-item label="关键字" prop="keyword">
          <el-input
            v-model="form.keyword"
            placeholder="请输入关键字"
            clearable
            @keyup.enter="onSearch"
          />
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            :icon="useRenderIcon('ri:search-line')"
            :loading="loading"
            class="search-button"
            style="
              background: linear-gradient(135deg, #409eff 0%, #4facff 100%);
              border: none;
              box-shadow: 0 4px 12px rgb(64 158 255 / 30%);
              transition: all 0.3s ease;
            "
            @click="onSearch"
          >
            搜索
          </el-button>
          <el-button
            :icon="useRenderIcon(Refresh)"
            class="reset-button"
            style="
              color: #606266;
              border-color: #dcdfe6;
              transition: all 0.3s ease;
            "
            @click="resetForm(formRef)"
          >
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <PureTableBar title="负载均衡" :columns="columns" @refresh="onSearch">
      <template v-slot="{ size, dynamicColumns }">
        <pure-table
          ref="tableRef"
          row-key="id"
          align-whole="left"
          table-layout="auto"
          :loading="loading"
          :size="size"
          :minHeight="500"
          :data="dataList"
          :columns="dynamicColumns"
          :pagination="{ ...pagination, size }"
          :header-cell-style="{
            color: '#303133',
            fontWeight: '600',
            borderBottom: '2px solid #e4e7ed'
          }"
          :row-style="{
            cursor: 'pointer',
            transition: 'background-color 0.2s ease'
          }"
          :cell-style="{
            padding: '12px 10px'
          }"
          border
          stripe
          highlight-current-row
          @page-size-change="handleSizeChange"
          @page-current-change="handleCurrentChange"
        >
          <template #empty>
            <el-empty
              description="暂无数据"
              :image-size="120"
              style="padding: 40px 0"
            />
          </template>
        </pure-table>
      </template>
    </PureTableBar>
  </div>
</template>

<script setup lang="ts">
import { ref, onBeforeMount } from "vue";
import { useRole } from "./hook";
import { PureTableBar } from "@/components/RePureTableBar";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import type { TableInstance } from "element-plus";

import Refresh from "@iconify-icons/ep/refresh";
import { useRoute } from "vue-router";
defineOptions({
  name: "Loadbanacer"
});

const formRef = ref<TableInstance>();
const tableRef = ref();

const {
  form,
  loading,
  columns,
  dataList,
  pagination,
  onSearch,
  resetForm,
  handleSizeChange,
  handleCurrentChange,
  syncAllAccounts,
  accounts,
  datacenters,
  syncAllDatacenters
} = useRole();

onBeforeMount(() => {
  const route = useRoute();
  const host = route.query.host as string;
  if (host) {
    form.host = host;
  }
});
</script>

<style scoped lang="scss">
.main {
  padding: 20px;
  border-radius: 12px;
}

.search-card {
  margin-bottom: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 8%);
  transition: box-shadow 0.3s ease;

  &:hover {
    box-shadow: 0 6px 20px rgb(0 0 0 / 12%);
  }
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;

  :deep(.el-form-item) {
    margin-bottom: 0;
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: #606266;
  }

  .el-input,
  .el-select {
    width: 220px;

    :deep(input),
    :deep(.el-input__wrapper) {
      border-radius: 8px;

      &:focus {
        border-color: #409eff;
        box-shadow: 0 0 0 1px rgb(64 158 255 / 20%);
      }
    }
  }

  .sync-button {
    margin-left: 8px;
    color: #409eff;
    transition: color 0.3s ease;

    &:hover {
      color: #0056b3;
    }
  }
}

.search-button,
.reset-button {
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
    transform: translateY(-2px);
  }
}

.search-button {
  color: white;
  background-color: #409eff;
  border-color: #409eff;
}

:deep(.pure-table) {
  overflow: hidden;
  border: 1px solid #e4e7ed;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 4%);
}
</style>
