<template>
  <div>
    <el-card shadow="hover" class="form-card">
      <el-form
        ref="ruleFormRef"
        label-width="auto"
        style="max-width: 600px"
        :model="newFormInline.form"
        size="large"
        status-icon
        label-position="top"
        class="form-layout"
      >
        <el-form-item
          label="标签名称"
          prop="key"
          :rules="[
            { required: true, message: '请输入标签名称', trigger: 'blur' },
            { max: 255, message: '请输入最大255', trigger: 'blur' }
          ]"
        >
          <el-input
            v-model="newFormInline.form.key"
            maxlength="255"
            show-word-limit
            placeholder="请输入标签名称"
            class="input-field"
          />
        </el-form-item>
        <el-form-item
          label="标签值"
          prop="value"
          :rules="[
            { required: true, message: '请输入标签值', trigger: 'blur' },
            { max: 255, message: '请输入最大255', trigger: 'blur' }
          ]"
        >
          <el-input
            v-model="newFormInline.form.value"
            maxlength="255"
            show-word-limit
            placeholder="请输入标签值"
            class="input-field"
          />
        </el-form-item>
        <el-form-item
          label="备注"
          prop="remark"
          :rules="[{ max: 255, message: '请输入最大255', trigger: 'blur' }]"
        >
          <el-input
            v-model="newFormInline.form.remark"
            type="textarea"
            placeholder="请输入备注"
            maxlength="255"
            show-word-limit
            class="textarea-field"
          />
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { defineProps, ref } from "vue";
import type { FormInstance } from "element-plus";
import type { TagForm } from "@/api/asset/tag";

export interface FormProps {
  formInline: {
    form: TagForm;
  };
}

const props = withDefaults(defineProps<FormProps>(), {
  formInline: () => ({ row: undefined, form: undefined })
});

const newFormInline = ref(props.formInline);
const ruleFormRef = ref<FormInstance>();
defineExpose({ ruleFormRef });
</script>

<style scoped lang="scss">
.form-card {
  padding: 20px; /* 内边距 */
  border-radius: 12px; /* 圆角 */
  box-shadow: 0 4px 16px rgb(0 0 0 / 8%); /* 阴影效果 */
  transition: box-shadow 0.3s ease;

  &:hover {
    box-shadow: 0 6px 20px rgb(0 0 0 / 12%);
  }
}

.form-layout {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.input-field {
  border-radius: 8px; /* 输入框圆角 */
  transition: border-color 0.3s ease;

  &:focus-within {
    border-color: var(--el-color-primary);
    box-shadow: 0 0 0 1px rgb(64 158 255 / 20%);
  }
}

.textarea-field {
  border-radius: 8px; /* 输入框圆角 */
  transition: border-color 0.3s ease;

  &:focus-within {
    border-color: var(--el-color-primary);
    box-shadow: 0 0 0 1px rgb(64 158 255 / 20%);
  }
}

.el-form-item {
  margin-bottom: 20px; /* 表单项底部间距 */
}

.el-input:hover {
  border-color: var(--el-color-primary); /* 悬停时边框颜色 */
}

.el-input.is-focus {
  border-color: var(--el-color-primary); /* 聚焦时边框颜色 */
}
</style>
