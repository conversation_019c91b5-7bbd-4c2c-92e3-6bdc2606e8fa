import dayjs from "dayjs";
import type { PaginationProps } from "@pureadmin/table";
import { reactive, ref, onMounted, h } from "vue";
import { message } from "@/utils/message";
import {
  getPrivateDomainsAPI,
  type PrivateDomainRecord,
  type PrivateDomain
} from "@/api/asset/private-dns";
import {
  getAllCloudAccountsAPI,
  type CloudAccount
} from "@/api/asset/cloud-account";
import { getAllDatacentersAPI, type Datacenter } from "@/api/asset/datacenter";
import { CloudTypes, CloudTypesColors } from "@/config/enum";
import { addDrawer } from "@/components/ReDrawer";
import Record from "./Record.vue";

export function useRole() {
  const form = reactive({
    account_id: undefined,
    keyword: undefined,
    ip: undefined
  });
  const dataList = ref<PrivateDomain[] | PrivateDomainRecord[]>([]);
  const loading = ref<boolean>(true);
  const accounts = ref<CloudAccount[]>([]);
  const datacenters = ref<Datacenter[]>([]);
  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true,
    pageSizes: [10, 20, 30, 40, 50, 100]
  });

  const recordColumns: TableColumnList = [
    {
      label: "主机",
      prop: "rr",
      cellRenderer: ({ row }) => (
        <el-text type="primary" style="font-weight: bold; font-size: 14px;">
          {row.rr}
        </el-text>
      )
    },
    {
      label: "解析值",
      prop: "value",
      minWidth: 120,
      cellRenderer: ({ row }) => (
        <el-text style="color: #409EFF; font-size: 14px;">{row.value}</el-text>
      )
    },
    {
      label: "状态",
      prop: "status",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <el-tag type={row.status === "active" ? "success" : "danger"}>
          {row.status}
        </el-tag>
      )
    },
    {
      label: "解析类型",
      prop: "type",
      minWidth: 80
    },
    {
      label: "TTL",
      prop: "ttl",
      minWidth: 80
    },
    {
      label: "线路",
      prop: "line",
      minWidth: 80
    },
    {
      label: "记录ID",
      prop: "record_id",
      minWidth: 100
    },
    {
      label: "备注",
      prop: "remark",
      minWidth: 150,
      cellRenderer: ({ row }) => (
        <el-tooltip content={row.remark} placement="top">
          <el-text>
            {row.remark.length > 20
              ? row.remark.substring(0, 20) + "..."
              : row.remark}
          </el-text>
        </el-tooltip>
      )
    },
    {
      label: "创建时间",
      prop: "created_at",
      minWidth: 120,
      cellRenderer: ({ row }) => {
        return dayjs(row?.created_at).format("YYYY-MM-DD HH:mm:ss");
      }
    },
    {
      label: "同步时间",
      prop: "sync_time",
      minWidth: 120,
      cellRenderer: ({ row }) => {
        return dayjs(row?.sync_time).format("YYYY-MM-DD HH:mm:ss");
      }
    }
  ];

  const columns: TableColumnList = [
    {
      label: "账户",
      prop: "account",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <div style="white-space: pre-line; line-height: 1.5;">
          <p>
            <el-text type="primary" style="margin: 10px;">
              <b>{row.account}</b>
            </el-text>
          </p>
          <p>
            <el-text
              style="margin: 10px;"
              type={CloudTypesColors.get(row.cloud_type)}
            >
              {CloudTypes.get(row.cloud_type)}
            </el-text>
            <el-text type="info" style="margin: 10px;">
              <b>{row.datacenter}</b>
            </el-text>
          </p>
        </div>
      )
    },
    {
      label: "名称",
      prop: "name",
      cellRenderer: ({ row }) => (
        <div style="white-space: pre-line;">
          名称：
          <el-text type="primary">
            <b>{row.name}</b>
          </el-text>
          <p>ID：{row.domain_id}</p>
        </div>
      )
    },
    {
      label: "记录数",
      prop: "record_count",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <div>
          <el-button
            onClick={() => {
              showRecords(row);
            }}
            link
            type="primary"
            size="large"
            style="text-decoration: underline; margin: 10px;"
          >
            <b>{row.record_count}</b> 条记录
            <iconify-icon-online icon="ep:link" style="margin:10px" />
          </el-button>
        </div>
      )
    },
    {
      label: "备注",
      prop: "remark",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <div style="white-space: pre-line;">
          <p>{row.remark}</p>
        </div>
      )
    },
    {
      label: "时间",
      prop: "sync_time",
      minWidth: 80,
      cellRenderer: ({ row }) => (
        <div style="white-space: normal;">
          <p>创建时间：{dayjs(row.created_at).format("YYYY-MM-DD HH:mm:ss")}</p>
          <p>
            同步时间：
            <b>{dayjs(row.sync_time).format("YYYY-MM-DD HH:mm:ss")}</b>
          </p>
        </div>
      )
    }
  ];

  function showRecords(row: PrivateDomain) {
    addDrawer({
      headerRenderer: ({ titleId, titleClass }) => (
        <div class="flex flex-row justify-between">
          <h4 id={titleId} class={titleClass}>
            {row.name} 解析记录
          </h4>
        </div>
      ),
      size: "80%",
      hideFooter: true,
      contentRenderer: () =>
        h(Record, {
          formInline: {
            domain_id: row.domain_id
          }
        })
    });
  }

  function handleSizeChange(val: number) {
    pagination.pageSize = val;
    onSearch();
  }

  function handleCurrentChange(val: number) {
    pagination.currentPage = val;
    onSearch();
  }

  async function onSearch() {
    loading.value = true;
    getPrivateDomainsAPI({
      page: pagination.currentPage,
      limit: pagination.pageSize,
      keyword: form.keyword,
      account_id: form.account_id,
      ip: form.ip
    })
      .then(res => {
        if (res.success) {
          dataList.value = res.data;
          pagination.total = res.count;
          pagination.pageSize = pagination.pageSize;
          pagination.currentPage = pagination.currentPage;
        } else {
          dataList.value = [];
          pagination.total = 0;
          pagination.pageSize = pagination.pageSize;
          pagination.currentPage = pagination.currentPage;
        }
      })
      .catch(() => {
        message("请求失败", { type: "error" });
      })
      .finally(() => {
        loading.value = false;
      });
  }

  function syncAllAccounts() {
    getAllCloudAccountsAPI()
      .then(res => {
        if (res.success) {
          accounts.value = res.data;
        } else {
          message(res.msg, { type: "error" });
        }
      })
      .catch(error => {
        message(error, { type: "error" });
      });
  }

  function syncAllDatacenters() {
    getAllDatacentersAPI()
      .then(res => {
        if (res.success) {
          datacenters.value = res.data;
        } else {
          message(res.msg, { type: "error" });
        }
      })
      .catch(error => {
        message(error, { type: "error" });
      });
  }

  const resetForm = formEl => {
    if (!formEl) return;
    formEl.resetFields();
    onSearch();
  };

  onMounted(() => {
    onSearch();
    syncAllAccounts();
    syncAllDatacenters();
  });

  return {
    form,
    loading,
    recordColumns,
    columns,
    dataList,
    pagination,
    onSearch,
    resetForm,
    handleSizeChange,
    handleCurrentChange,
    accounts,
    syncAllAccounts,
    datacenters,
    syncAllDatacenters
  };
}
