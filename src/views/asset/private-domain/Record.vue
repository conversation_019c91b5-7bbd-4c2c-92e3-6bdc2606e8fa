<script setup lang="ts">
import { ref, onMounted, reactive } from "vue";
import { PureTableBar } from "@/components/RePureTableBar";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import type { TableInstance } from "element-plus";
import Refresh from "@iconify-icons/ep/refresh";
import type { PaginationProps } from "@pureadmin/table";
import { message } from "@/utils/message";
import { useRole } from "./hook";
import {
  type PrivateDomainRecord,
  type PrivateDomain,
  getDomainRecordsAPI
} from "@/api/asset/private-dns";

interface FormProps {
  formInline: {
    domain_id: string;
  };
}
const props = withDefaults(defineProps<FormProps>(), {
  formInline: () => ({ domain_id: "" })
});
const newFormInline = ref(props.formInline);

const { recordColumns } = useRole();

const formRef = ref<TableInstance>();
const tableRef = ref();
const form = reactive({
  account_id: undefined,
  keyword: undefined
});
const dataList = ref<PrivateDomain[] | PrivateDomainRecord[]>([]);
const loading = ref<boolean>(true);
const pagination = reactive<PaginationProps>({
  total: 0,
  pageSize: 10,
  currentPage: 1,
  background: true,
  pageSizes: [10, 20, 30, 40, 50, 100]
});

function handleSizeChange(val: number) {
  pagination.pageSize = val;
  onSearch();
}

function handleCurrentChange(val: number) {
  pagination.currentPage = val;
  onSearch();
}

async function onSearch() {
  loading.value = true;
  getDomainRecordsAPI(newFormInline.value.domain_id, {
    page: pagination.currentPage,
    limit: pagination.pageSize,
    keyword: form.keyword
  })
    .then(res => {
      if (res.success) {
        dataList.value = res.data;
        pagination.total = res.count;
      } else {
        dataList.value = [];
        pagination.total = 0;
      }
    })
    .catch(() => {
      message("请求失败", { type: "error" });
    })
    .finally(() => {
      loading.value = false;
    });
}

const resetForm = formEl => {
  if (!formEl) return;
  formEl.resetFields();
  onSearch();
};

onMounted(() => {
  onSearch();
});
</script>

<template>
  <div class="main">
    <el-card class="search-card">
      <el-form
        ref="formRef"
        :inline="true"
        :model="form"
        class="search-form"
        @submit.prevent
      >
        <el-form-item label="关键字" prop="keyword">
          <el-input
            v-model="form.keyword"
            placeholder="请输入关键字，rr:前缀精准匹配rr记录"
            clearable
            @keyup.enter="onSearch"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            :icon="useRenderIcon('ri:search-line')"
            :loading="loading"
            class="search-button"
            style="
              background: linear-gradient(135deg, #409eff 0%, #4facff 100%);
              border: none;
              box-shadow: 0 4px 12px rgb(64 158 255 / 30%);
              transition: all 0.3s ease;
            "
            @click="onSearch"
          >
            搜索
          </el-button>
          <el-button
            :icon="useRenderIcon(Refresh)"
            class="reset-button"
            style="
              color: #606266;
              border-color: #dcdfe6;
              transition: all 0.3s ease;
            "
            @click="resetForm(formRef)"
          >
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
    <PureTableBar title="解析记录" :columns="recordColumns" @refresh="onSearch">
      <template v-slot="{ size, dynamicColumns }">
        <pure-table
          ref="tableRef"
          row-key="id"
          align-whole="left"
          table-layout="auto"
          :loading="loading"
          :size="size"
          :minHeight="500"
          :data="dataList"
          :columns="dynamicColumns"
          :pagination="{ ...pagination, size }"
          :header-cell-style="{
            color: '#303133',
            fontWeight: '600',
            borderBottom: '2px solid #e4e7ed'
          }"
          :row-style="{
            cursor: 'pointer',
            transition: 'background-color 0.2s ease'
          }"
          :cell-style="{
            padding: '12px 10px'
          }"
          border
          stripe
          highlight-current-row
          @page-size-change="handleSizeChange"
          @page-current-change="handleCurrentChange"
        >
          <template #empty>
            <el-empty
              description="暂无数据"
              :image-size="120"
              style="padding: 40px 0"
            />
          </template>
        </pure-table>
      </template>
    </PureTableBar>
  </div>
</template>

<style scoped lang="scss">
.main {
  padding: 20px;
  border-radius: 12px;
}

.search-card {
  margin-bottom: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 8%);
  transition: box-shadow 0.3s ease;

  &:hover {
    box-shadow: 0 6px 20px rgb(0 0 0 / 12%);
  }
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;

  :deep(.el-form-item) {
    margin-bottom: 0;
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: #606266;
  }

  .el-input {
    width: 350px;

    :deep(input) {
      border-color: #e4e7ed;
      border-radius: 8px;

      &:focus {
        border-color: #409eff;
        box-shadow: 0 0 0 1px rgb(64 158 255 / 20%);
      }
    }
  }
}

.search-button,
.reset-button {
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
    transform: translateY(-2px);
  }
}

.search-button {
  color: white;
  background-color: #409eff;
  border-color: #409eff;
}

:deep(.pure-table) {
  overflow: hidden;
  border: 1px solid #e4e7ed;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 4%);
}
</style>
