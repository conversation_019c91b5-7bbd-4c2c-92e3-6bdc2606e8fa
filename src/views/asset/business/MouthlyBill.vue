<template>
  <div>
    <div class="date-picker-container">
      <el-date-picker
        v-model="form.date_range"
        type="monthrange"
        format="YYYY-MM"
        value-format="YYYY-MM"
        placeholder="选择一段时间"
        clearable
        range-separator="至"
        start-placeholder="开始月份"
        end-placeholder="结束月份"
      />
    </div>
    <div v-if="loading" class="loading-message">加载中...</div>
    <div v-if="!billCycles.length && !loading" class="no-data-message">
      没有数据
    </div>
    <div ref="chartRef" class="chart-container" />
  </div>
</template>

<script setup lang="ts">
import { ref, onBeforeMount, reactive, watch } from "vue";
import * as echarts from "echarts";
import type { getMonthlyBillAmountParams } from "@/api/asset/bill";
import { message } from "@/utils/message";
import dayjs from "dayjs";
import { getMonthlyBillAmountAPI } from "@/api/asset/bussiness";

const props = defineProps<{
  business_id: number;
  business_name: string | undefined;
  resource_id: string | undefined;
}>();

const form = reactive<getMonthlyBillAmountParams>({
  date_range: [
    dayjs().subtract(12, "month").format("YYYY-MM"),
    dayjs().format("YYYY-MM")
  ],
  resource_id: undefined
});

const chartRef = ref<HTMLDivElement | null>(null);
const billCycles = ref<string[]>([]);
const amounts = ref<number[]>([]);
const loading = ref(false);

const getMonthlyBillAmount = async () => {
  loading.value = true;
  try {
    const [start_time, end_time] = form.date_range;
    const res = await getMonthlyBillAmountAPI(props.business_id, {
      start_time,
      end_time,
      resource_id: props.resource_id
    });
    if (res?.success && res?.data) {
      billCycles.value = res?.data?.map(item => item.bill_cycle);
      amounts.value = res?.data?.map(item => item.amount);
      updateChart();
    } else {
      message(res?.msg || "获取数据失败", { type: "error" });
    }
  } catch (error) {
    message(error.message || "获取数据失败", { type: "error" });
  } finally {
    loading.value = false;
  }
};

const updateChart = () => {
  if (chartRef.value) {
    const myChart = echarts.init(chartRef.value);
    const option = {
      title: {
        text: props.business_name
          ? `${props.business_name} 月度账单`
          : "业务月度账单",
        left: "center",
        textStyle: {
          fontSize: 20,
          fontWeight: "bold",
          color: "#333"
        }
      },
      tooltip: {
        trigger: "item",
        formatter: "{a} <br/>{b}: {c}元"
      },
      xAxis: {
        type: "category",
        data: billCycles.value,
        axisLabel: {
          rotate: 45,
          margin: 10,
          textStyle: {
            color: "#666"
          }
        }
      },
      yAxis: {
        type: "value",
        axisLabel: {
          formatter: "{value} 元",
          textStyle: {
            color: "#666"
          }
        }
      },
      series: [
        {
          name: "金额",
          type: "bar",
          data: amounts.value.map(amount => amount.toFixed(2)),
          itemStyle: {
            color: "#4CAF50"
          },
          label: {
            show: true,
            position: "top",
            formatter: "{c}元"
          }
        },
        {
          name: "趋势线",
          type: "line",
          data: amounts.value.map(amount => amount.toFixed(2)),
          itemStyle: {
            color: "#FF5733"
          },
          label: {
            show: false
          },
          smooth: true
        }
      ]
    };
    myChart.setOption(option);
    window.addEventListener("resize", () => {
      myChart.resize();
    });
  }
};

onBeforeMount(() => {
  getMonthlyBillAmount();
});

watch(
  () => form.date_range,
  () => {
    getMonthlyBillAmount();
  }
);
</script>

<style scoped>
.date-picker-container {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.chart-container {
  width: 100%;
  height: 400px;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgb(0 0 0 / 10%);
}

.no-data-message {
  margin-top: 20px;
  color: #999;
  text-align: center;
}

.loading-message {
  margin-top: 20px;
  font-size: 16px;
  color: #666;
  text-align: center;
}
</style>
