<script setup lang="ts">
import { ref } from "vue";
import { useRole } from "./hook";
import { PureTableBar } from "@/components/RePureTableBar";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";

import Refresh from "@iconify-icons/ep/refresh";
import Plus from "@iconify-icons/ep/plus";

defineOptions({
  name: "Business"
});

const formRef = ref();
const tableRef = ref();

const {
  form,
  loading,
  columns,
  dataList,
  pagination,
  onSearch,
  resetForm,
  handleSizeChange,
  handleCurrentChange,
  addFunc
} = useRole();
</script>

<template>
  <div class="main">
    <el-card class="search-card">
      <el-form
        ref="formRef"
        :inline="true"
        :model="form"
        class="search-form"
        @submit.prevent
      >
        <el-form-item label="关键字" prop="keyword">
          <el-input
            v-model="form.keyword"
            placeholder="请输入关键字"
            clearable
            class="!w-[150px]"
            @keyup.enter="onSearch"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            :icon="useRenderIcon('ri:search-line')"
            :loading="loading"
            class="search-button"
            style="
              background: linear-gradient(135deg, #409eff 0%, #4facff 100%);
              border: none;
              box-shadow: 0 4px 12px rgb(64 158 255 / 30%);
              transition: all 0.3s ease;
            "
            @click="onSearch"
          >
            搜索
          </el-button>
          <el-button
            :icon="useRenderIcon(Refresh)"
            class="reset-button"
            style="
              color: #606266;
              border-color: #dcdfe6;
              transition: all 0.3s ease;
            "
            @click="resetForm(formRef)"
          >
            重置
          </el-button>
          <el-button
            type="primary"
            link
            :icon="useRenderIcon(Plus)"
            class="add-button"
            style="color: #409eff; transition: all 0.3s ease"
            @click="addFunc"
          >
            添加
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <PureTableBar title="业务列表" :columns="columns" @refresh="onSearch">
      <template v-slot="{ size, dynamicColumns }">
        <div class="table-wrapper">
          <pure-table
            ref="tableRef"
            row-key="id"
            align-whole="left"
            table-layout="auto"
            :loading="loading"
            :size="size"
            :minHeight="500"
            :data="dataList"
            :columns="dynamicColumns"
            :pagination="{ ...pagination, size }"
            :header-cell-style="{
              fontWeight: '600'
            }"
            :row-style="{
              cursor: 'pointer',
              transition: 'background-color 0.2s ease'
            }"
            :cell-style="{
              padding: '12px 10px'
            }"
            border
            stripe
            highlight-current-row
            @page-size-change="handleSizeChange"
            @page-current-change="handleCurrentChange"
          >
            <template #empty>
              <el-empty
                description="暂无数据"
                :image-size="120"
                style="padding: 40px 0"
              />
            </template>
          </pure-table>
        </div>
      </template>
    </PureTableBar>
  </div>
</template>

<style scoped lang="scss">
.main {
  padding: 20px;
  border-radius: 12px;
}

.search-card {
  margin-bottom: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 8%);
  transition: box-shadow 0.3s ease;

  &:hover {
    box-shadow: 0 6px 20px rgb(0 0 0 / 12%);
  }
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;

  :deep(.el-form-item) {
    margin-bottom: 0;
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: #606266;
  }

  .el-input {
    width: 100%;
    max-width: 150px;

    :deep(input) {
      border-color: #e4e7ed;
      border-radius: 8px;

      &:focus {
        border-color: #409eff;
        box-shadow: 0 0 0 1px rgb(64 158 255 / 20%);
      }
    }
  }
}

.table-wrapper {
  overflow-x: auto;
}

.search-button,
.reset-button,
.add-button {
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
    transform: translateY(-2px);
  }
}

.search-button {
  color: white;
  background-color: #409eff;
  border-color: #409eff;
}

.reset-button {
  margin-left: 12px;
  color: #606266;
  border-color: #dcdfe6;
}

.add-button {
  margin-left: 12px;
  color: #409eff;
  border: none;
}

:deep(.pure-table) {
  overflow: hidden;
  border: 1px solid #e4e7ed;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 4%);

  .el-table__header {
    background: linear-gradient(135deg, #f5f7fa 0%, #f8f9fb 100%) !important;
  }

  .el-table__row {
    transition: all 0.2s ease;

    &:hover {
      background-color: #f5f7fa !important;
      box-shadow: 0 2px 8px rgb(0 0 0 / 6%);
    }
  }

  .el-table__cell {
    padding: 12px 10px !important;
  }

  .el-table__expand-icon {
    color: #409eff;
    transition: transform 0.2s ease;

    &:hover {
      transform: scale(1.2);
    }
  }
}
</style>
