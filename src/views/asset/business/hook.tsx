import {
  getBusinessesAPI,
  addBusinessAPI,
  updateBusinessAPI,
  deleteBusinessAPI,
  type Business,
  type BusinessForm,
  type SetResourceGroupForm,
  setResourceGroupAPI
} from "@/api/asset/bussiness";
import type { PaginationProps } from "@pureadmin/table";
import { reactive, ref, onMounted, h } from "vue";
import { message } from "@/utils/message";
import { addDrawer } from "@/components/ReDrawer";
import { addDialog } from "@/components/ReDialog";
import Uform from "./Uform.vue";
import SetResourceGroup from "./SetResourceGroup.vue";
import Detail from "./Detail.vue";
import MouthlyBill from "./MouthlyBill.vue";

export function useRole() {
  const form = reactive({
    keyword: undefined
  });
  const dataList = ref<Business[]>([]);
  const loading = ref<boolean>(true);
  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true,
    pageSizes: [10, 20, 30, 40, 50, 100]
  });
  const columns: TableColumnList = [
    {
      label: "ID",
      prop: "id",
      minWidth: 80,
      sortable: true
    },
    {
      label: "名称",
      prop: "name",
      minWidth: 120,
      showOverflowTooltip: true
    },
    {
      prop: "resource_groups",
      label: "自定义关联资源组(默认关联名称一致的资源组)",
      minWidth: 150,
      showOverflowTooltip: true,
      cellRenderer: ({ row }) => (
        <el-text style={{ display: "flex", flexWrap: "wrap" }}>
          {row.resource_groups?.length > 0 ? (
            row.resource_groups.map(item => (
              <el-tag
                key={item.id}
                size="large"
                type="primary"
                effect="plain"
                style={{ marginRight: "4px", marginBottom: "4px" }}
              >
                {item.group_name + "(" + item.group_display_name + ")"}
              </el-tag>
            ))
          ) : (
            <span>-</span>
          )}
        </el-text>
      )
    },
    {
      prop: "cloud_accounts",
      label: "云账号",
      minWidth: 150,
      showOverflowTooltip: true,
      cellRenderer: ({ row }) => (
        <el-text style={{ display: "flex", flexWrap: "wrap" }}>
          {row.cloud_accounts?.length > 0 ? (
            row.cloud_accounts.map(item => (
              <el-tag
                key={item.id}
                type="primary"
                effect="plain"
                size="large"
                style={{ marginRight: "4px", marginBottom: "4px" }}
              >
                {item.name}
              </el-tag>
            ))
          ) : (
            <span>-</span>
          )}
        </el-text>
      )
    },
    {
      prop: "host_count",
      label: "主机数量",
      minWidth: 150,
      showOverflowTooltip: true,
      cellRenderer: ({ row }) => (
        <el-text
          type="primary"
          size="large"
          style={{
            fontWeight: 600,
            padding: "4px 8px",
            borderRadius: "4px"
          }}
        >
          {row.host_count}
        </el-text>
      )
    },
    {
      label: "备注",
      prop: "remark",
      minWidth: 150,
      showOverflowTooltip: true
    },
    {
      label: "操作",
      slot: "operation",
      minWidth: 280,
      cellRenderer: ({ row }) => {
        return (
          <div>
            <el-button
              type="primary"
              link
              onClick={() => handleBusinessMonthlyBill(row.id, row.name)}
              style="margin-right: 8px; border-radius: 8px; transition: background-color 0.3s;"
              class="action-button"
            >
              查看月度账单
            </el-button>
            <el-button
              type="primary"
              link
              onClick={() => openDetail(row)}
              style="margin-right: 8px; border-radius: 8px; transition: background-color 0.3s;"
              class="action-button"
            >
              查看详情
            </el-button>
            <el-button
              type="success"
              link
              class="action-button"
              onClick={() => setResourceGroup(row)}
              style="margin-right: 8px; border-radius: 8px; transition: background-color 0.3s;"
            >
              设置资源组/云账号
            </el-button>
            <el-button
              type="warning"
              link
              class="action-button"
              onClick={() => editBusiness(row)}
              style="margin-right: 8px; border-radius: 8px; transition: background-color 0.3s;"
            >
              编辑
            </el-button>
            <el-button
              type="danger"
              link
              class="action-button"
              onClick={() => deleteBusiness(row)}
              style="border-radius: 8px; transition: background-color 0.3s;"
            >
              删除
            </el-button>
          </div>
        );
      }
    }
  ];
  const editForm = ref<BusinessForm>();
  const childrenRef = ref(null);
  function editBusiness(row: Business) {
    editForm.value = {
      name: row.name,
      remark: row.remark
    };
    addDrawer({
      headerRenderer: ({ titleId, titleClass }) => (
        // jsx 语法
        <div class="flex flex-row justify-between">
          <h4 id={titleId} class={titleClass}>
            编辑业务
            <b>{row.name}</b>
          </h4>
        </div>
      ),
      contentRenderer: () =>
        h(Uform, {
          formInline: {
            form: editForm.value
          },
          ref: childrenRef
        }),
      beforeSure: done => {
        if (childrenRef.value?.ruleFormRef) {
          childrenRef.value.ruleFormRef
            .validate((valid: boolean) => {
              if (valid) {
                updateBusinessAPI(row.id, editForm.value)
                  .then(res => {
                    if (res.success) {
                      message(res.msg, { type: "success" });
                      done();
                      editForm.value = undefined;
                      onSearch();
                    } else {
                      message(res.msg, { type: "error" });
                    }
                  })
                  .catch(error => {
                    message(error, { type: "error" });
                  });
              } else {
                message("请检查表单数据", { type: "error" });
              }
            })
            .catch(error => {
              message("请检查表单数据:" + error, { type: "error" });
            });
        }
      }
    });
  }

  function addFunc() {
    editForm.value = {
      name: "",
      remark: ""
    };
    addDrawer({
      headerRenderer: ({ titleId, titleClass }) => (
        <div class="flex flex-row justify-between">
          <h4 id={titleId} class={titleClass}>
            添加业务
          </h4>
        </div>
      ),
      contentRenderer: () =>
        h(Uform, {
          formInline: {
            form: editForm.value
          },
          ref: childrenRef
        }),
      beforeSure: done => {
        if (childrenRef.value?.ruleFormRef) {
          childrenRef.value.ruleFormRef.validate((valid: boolean) => {
            if (valid) {
              addBusinessAPI(editForm.value)
                .then(res => {
                  if (res.success) {
                    message(res.msg, { type: "success" });
                    editForm.value = undefined;
                    onSearch();
                    done();
                  } else {
                    message(res.msg, { type: "error" });
                  }
                })
                .catch(error => {
                  message(error, { type: "error" });
                });
            } else {
              message("请检查表单数据", { type: "error" });
            }
          });
        } else {
          message("请检查表单", { type: "error" });
        }
      }
    });
  }
  const setResourceGroupForm = reactive<SetResourceGroupForm>({
    resource_group_ids: [],
    cloud_account_ids: []
  });
  function setResourceGroup(row: Business) {
    setResourceGroupForm.resource_group_ids = row.resource_groups?.map(
      item => item.id
    );
    setResourceGroupForm.cloud_account_ids = row.cloud_accounts?.map(
      item => item.id
    );
    addDrawer({
      headerRenderer: ({ titleId, titleClass }) => (
        <div class="flex flex-row justify-between">
          <h4 id={titleId} class={titleClass}>
            设置资源组/云账号
          </h4>
        </div>
      ),
      contentRenderer: () =>
        h(SetResourceGroup, {
          formInline: {
            row: row,
            form: setResourceGroupForm
          },
          ref: childrenRef
        }),
      beforeSure: done => {
        if (childrenRef.value?.ruleFormRef) {
          childrenRef.value.ruleFormRef.validate((valid: boolean) => {
            if (valid) {
              setResourceGroupAPI(row.id, setResourceGroupForm)
                .then(res => {
                  if (res.success) {
                    message(res.msg, { type: "success" });
                    setResourceGroupForm.resource_group_ids = [];
                    onSearch();
                    done();
                  } else {
                    message(res.msg, { type: "error" });
                  }
                })
                .catch(error => {
                  message(error, { type: "error" });
                });
            } else {
              message("请检查表单数据", { type: "error" });
            }
          });
        } else {
          message("请检查表单", { type: "error" });
        }
      }
    });
  }

  function deleteBusiness(row: Business) {
    addDialog({
      title: "",
      width: 500,
      sureBtnLoading: true,
      contentRenderer: () => (
        <p>
          确认删除：
          <b style="color:red">{row.name}</b>
        </p>
      ),
      beforeSure: done => {
        deleteBusinessAPI(row.id)
          .then(res => {
            if (res.success) {
              done();
              message(res.msg, { type: "success" });
              onSearch();
            } else {
              message(res.msg, { type: "error" });
            }
          })
          .catch(error => {
            message(error, { type: "error" });
          });
      }
    });
  }

  function handleSizeChange(val: number) {
    pagination.pageSize = val;
    onSearch();
  }

  function handleCurrentChange(val: number) {
    pagination.currentPage = val;
    onSearch();
  }

  async function onSearch() {
    loading.value = true;
    getBusinessesAPI({
      page: pagination.currentPage,
      limit: pagination.pageSize,
      keyword: form.keyword
    })
      .then(res => {
        if (res.success) {
          dataList.value = res.data.map(item => ({
            ...item,
            rowStyle: {
              backgroundColor: "#ffffff",
              transition: "background-color 0.2s ease",
              cursor: "pointer"
            }
          }));
          pagination.total = res.count;
        } else {
          dataList.value = [];
          pagination.total = 0;
        }
      })
      .catch(() => {
        message("请求失败", { type: "error" });
      })
      .finally(() => {
        loading.value = false;
      });
  }

  const resetForm = formEl => {
    if (!formEl) return;
    formEl.resetFields();
    onSearch();
  };

  const handleBusinessMonthlyBill = (
    business_id: number,
    business_name: string
  ) => {
    addDialog({
      title: "业务月度账单",
      hideFooter: true,
      draggable: true,
      contentRenderer: () =>
        h(MouthlyBill, {
          business_id: business_id,
          business_name: business_name,
          resource_id: "business"
        })
    });
  };
  function openDetail(business: Business) {
    addDrawer({
      headerRenderer: ({ titleId, titleClass }) => (
        <div class="flex flex-row justify-between">
          <h4 id={titleId} class={titleClass}>
            业务详情
          </h4>
        </div>
      ),
      contentRenderer: () => h(Detail, { business: business })
    });
  }

  onMounted(() => {
    onSearch();
  });

  return {
    form,
    loading,
    columns,
    dataList,
    pagination,
    onSearch,
    resetForm,
    handleSizeChange,
    handleCurrentChange,
    addFunc,
    openDetail
  };
}
