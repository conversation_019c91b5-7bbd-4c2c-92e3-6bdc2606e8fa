<template>
  <div class="detail-container">
    <el-card
      shadow="hover"
      class="detail-card"
      :body-style="{
        padding: '30px',
        borderRadius: '12px'
      }"
    >
      <el-descriptions
        :column="1"
        size="large"
        border
        :label-style="{
          fontWeight: '600',
          color: '#303133',
          padding: '12px 20px'
        }"
        :content-style="{
          padding: '12px 20px'
        }"
      >
        <el-descriptions-item label="业务名称">
          <el-text class="business-name">{{ businessDetail.name }}</el-text>
        </el-descriptions-item>
        <el-descriptions-item label="备注">
          <el-text type="info" class="remark">
            {{ businessDetail.remark || "暂无备注" }}
          </el-text>
        </el-descriptions-item>
        <el-descriptions-item label="关联主机数">
          <el-text type="primary" class="host-count">
            {{ businessDetail.host_count || 0 }}
          </el-text>
        </el-descriptions-item>
        <el-descriptions-item label="资源组">
          <div class="resource-groups">
            <el-tag
              v-for="group in businessDetail.resource_groups"
              :key="group.id"
              class="resource-tag"
              type="primary"
              effect="light"
            >
              <span class="group-name">{{ group.group_name }}</span>
              <span class="group-display-name"
                >({{ group.group_display_name }})</span
              >
            </el-tag>
            <span
              v-if="!businessDetail.resource_groups?.length"
              class="no-data"
            >
              -
            </span>
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="云账号">
          <div class="resource-groups">
            <el-tag
              v-for="account in businessDetail.cloud_accounts"
              :key="account.id"
              class="resource-tag"
              type="primary"
              effect="light"
            >
              <span class="group-name">{{ account.name }}</span>
            </el-tag>
            <span v-if="!businessDetail.cloud_accounts?.length" class="no-data">
              -
            </span>
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          <el-text type="info" class="time-info">
            {{ dayjs(businessDetail.created_at).format("YYYY-MM-DD HH:mm:ss") }}
          </el-text>
        </el-descriptions-item>
        <el-descriptions-item label="更新时间">
          <el-text type="info" class="time-info">
            {{ dayjs(businessDetail.updated_at).format("YYYY-MM-DD HH:mm:ss") }}
          </el-text>
        </el-descriptions-item>
      </el-descriptions>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import type { Business } from "@/api/asset/bussiness";
import { ref } from "vue";
import dayjs from "dayjs";

interface DetailProps {
  business: Business;
}
const props = defineProps<DetailProps>();
const businessDetail = ref<Business>(props.business);
</script>

<style scoped lang="scss">
.detail-container {
  padding: 20px;
}

.detail-card {
  border-radius: 12px;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 8px 24px rgb(0 0 0 / 10%);
    transform: translateY(-2px);
  }
}

.business-name {
  font-size: 16px;
  font-weight: 600;
}

.host-count {
  padding: 4px 12px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 4px;
}

.resource-groups {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.resource-tag {
  display: inline-flex;
  align-items: center;
  padding: 6px 10px;
  border-radius: 6px;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 2px 8px rgb(64 158 255 / 20%);
    transform: translateY(-2px);
  }

  .group-name {
    font-weight: 500;
  }

  .group-display-name {
    margin-left: 4px;
    opacity: 0.8;
  }
}

.no-data {
  font-size: 14px;
  font-style: italic;
  color: #909399;
}

:deep(.el-descriptions) {
  .el-descriptions__body {
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgb(0 0 0 / 5%);
  }

  .el-descriptions__label {
    position: relative;

    &::after {
      position: absolute;
      bottom: -1px;
      left: 0;
      width: 100%;
      height: 2px;
      content: "";
      background: linear-gradient(90deg, #409eff 0%, transparent 100%);
      opacity: 0;
      transition: opacity 0.3s ease;
    }
  }

  .el-descriptions__cell:hover {
    .el-descriptions__label::after {
      opacity: 1;
    }
  }
}
</style>
