<template>
  <div class="set-resource-container">
    <el-card shadow="hover" class="form-card" :body-style="{ padding: '30px' }">
      <el-form
        ref="ruleFormRef"
        :model="newFormInline.form"
        size="large"
        status-icon
        label-position="top"
        class="custom-form"
        @submit.prevent
      >
        <el-form-item label="业务名称">
          <el-input
            v-model="newFormInline.row.name"
            disabled
            class="input-field"
          >
            <template #prefix>
              <iconify-icon-online
                icon="ep:office-building"
                width="18"
                height="18"
                style="color: #409eff"
              />
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="关联主机数">
          <el-text type="info">
            {{ newFormInline.row.host_count || 0 }}
          </el-text>
        </el-form-item>
        <el-form-item label="备注">
          <el-text type="info">{{ newFormInline.row.remark }}</el-text>
        </el-form-item>
        <el-form-item label="资源组">
          <div class="resource-group-wrapper">
            <el-select
              v-model="newFormInline.form.resource_group_ids"
              multiple
              filterable
              clearable
              placeholder="请选择资源组"
              class="resource-select !w-full"
              :popper-class="'resource-select-dropdown'"
              style="width: 100%"
            >
              <el-option
                v-for="item in resourceGroup"
                :key="item.id"
                :label="`${item.group_name}(${item.group_display_name} - ${item.account_name})`"
                :value="item.id"
              >
                <div class="resource-option">
                  <span class="group-name">{{ item.group_name }}</span>
                  <span class="group-display-name">
                    ({{ item.group_display_name }} - {{ item.account_name }})
                  </span>
                </div>
              </el-option>
            </el-select>

            <div
              v-if="newFormInline.form.resource_group_ids?.length > 0"
              class="selected-resources"
            >
              <div class="selected-title">
                <iconify-icon-online
                  icon="ep:connection"
                  width="16"
                  height="16"
                />
                <span>已选择的资源组</span>
              </div>
              <div class="selected-tags">
                <el-tag
                  v-for="id in newFormInline.form.resource_group_ids"
                  :key="id"
                  closable
                  type="success"
                  class="resource-tag"
                  @close="handleRemoveTag(id)"
                >
                  {{ getResourceGroupName(id) }}
                </el-tag>
              </div>
            </div>

            <div v-else class="warning-alert">
              <el-alert
                title="提示：不选择任何资源组,将清空该业务的资源组关联"
                type="warning"
                :closable="false"
                show-icon
              />
            </div>
          </div>
        </el-form-item>

        <el-form-item label="云账号">
          <div class="resource-group-wrapper">
            <el-select
              v-model="newFormInline.form.cloud_account_ids"
              multiple
              filterable
              clearable
              placeholder="请选择云账号"
              class="resource-select !w-full"
              :popper-class="'resource-select-dropdown'"
              style="width: 100%"
            >
              <el-option
                v-for="item in cloudAccounts"
                :key="item.id"
                :label="`${item.name}`"
                :value="item.id"
              >
                <div class="resource-option">
                  <span class="group-name">{{ item.name }}</span>
                </div>
              </el-option>
            </el-select>

            <div
              v-if="newFormInline.form.cloud_account_ids?.length > 0"
              class="selected-resources"
            >
              <div class="selected-title">
                <iconify-icon-online
                  icon="ep:connection"
                  width="16"
                  height="16"
                />
                <span>已选择的云账号</span>
              </div>
              <div class="selected-tags">
                <el-tag
                  v-for="id in newFormInline.form.cloud_account_ids"
                  :key="id"
                  closable
                  type="success"
                  class="resource-tag"
                  @close="handleRemoveTag(id)"
                >
                  {{ getCloudAccountName(id) }}
                </el-tag>
              </div>
            </div>

            <div v-else class="warning-alert">
              <el-alert
                title="提示：不选择任何云账号,将清空该业务的云账号关联"
                type="warning"
                :closable="false"
                show-icon
              />
            </div>
          </div>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { defineProps, onMounted, ref } from "vue";
import { IconifyIconOnline } from "@/components/ReIcon";
import type { FormInstance } from "element-plus";
import type { Business, SetResourceGroupForm } from "@/api/asset/bussiness";
import {
  getAllResourceGroupesAPI,
  type ResourceGroup
} from "@/api/asset/resource-group";
import { message } from "@/utils/message";
import {
  type CloudAccount,
  getAllCloudAccountsAPI
} from "@/api/asset/cloud-account";
// 定义 Props 接口
export interface FormProps {
  formInline: {
    row: Business;
    form: SetResourceGroupForm;
  };
}

// Props 默认值
const props = withDefaults(defineProps<FormProps>(), {
  formInline: () => ({
    row: undefined,
    form: undefined
  })
});

// 响应式数据
const resourceGroup = ref<ResourceGroup[]>([]);
const newFormInline = ref(props.formInline);
const ruleFormRef = ref<FormInstance>();

// 获取资源组名称
const getResourceGroupName = (id: number) => {
  const group = resourceGroup.value.find(item => item.id === id);

  return group
    ? `${group.group_name}(${group.group_display_name} - ${group.account_name})`
    : "";
};

// 获取云账号名称
const getCloudAccountName = (id: number) => {
  const account = cloudAccounts.value.find(item => item.id === id);

  return account ? `${account.name}` : "";
};

// 移除标签处理
const handleRemoveTag = (id: number) => {
  newFormInline.value.form.resource_group_ids =
    newFormInline.value.form.resource_group_ids.filter(item => item !== id);
};

// 获取所有资源组
const getAllResourceGroupes = () => {
  getAllResourceGroupesAPI()
    .then(res => {
      if (res.success) {
        resourceGroup.value = res.data;
      } else {
        message(res.msg, { type: "error" });
      }
    })
    .catch(() => {
      message("请求失败", { type: "error" });
    });
};
const cloudAccounts = ref<CloudAccount[]>([]);
// 获取所有云账号
const getAllCloudAccounts = () => {
  getAllCloudAccountsAPI().then(res => {
    if (res.success) {
      cloudAccounts.value = res.data;
    } else {
      message(res.msg, { type: "error" });
    }
  });
};

// 生命周期钩子
onMounted(() => {
  getAllResourceGroupes();
  getAllCloudAccounts();
});

defineExpose({ ruleFormRef });
</script>

<style scoped lang="scss">
.set-resource-container {
  padding: 24px;

  // 表单卡片样式
  .form-card {
    background-color: var(--el-bg-color);
    border: 1px solid var(--el-border-color);
    border-radius: 16px;
    transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);

    &:hover {
      box-shadow: 0 12px 24px var(--el-box-shadow-lighter);
      transform: translateY(-3px);
    }

    :deep(.el-form-item__label) {
      color: var(--el-text-color-primary);
    }
  }

  // 输入框样式
  .input-field {
    :deep(.el-input__wrapper) {
      background-color: var(--el-fill-color-blank);
      border-radius: 10px;
      transition: all 0.3s ease;

      &:hover {
        background-color: var(--el-fill-color-light);
      }

      &.is-focus {
        box-shadow: 0 0 0 2px var(--el-color-primary-light-8);
      }
    }
  }

  // 资源组区域样式
  .resource-group-wrapper {
    display: flex;
    flex-direction: column;
    gap: 16px;

    .resource-select {
      :deep(.el-input__wrapper) {
        background-color: var(--el-fill-color-blank);

        &:hover {
          background-color: var(--el-fill-color-light);
        }
      }
    }
  }

  // 已选择资源组样式
  .selected-resources {
    padding: 20px;
    margin-top: 16px;
    background-color: var(--el-color-primary-light-9);
    border: 1px dashed var(--el-color-primary-light-5);
    border-radius: 14px;

    .selected-title {
      display: flex;
      gap: 8px;
      align-items: center;
      margin-bottom: 16px;
      font-size: 0.95rem;
      font-weight: 600;
      color: var(--el-color-primary);
    }

    .selected-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;
    }

    html.dark & {
      background-color: var(--el-color-primary-dark-9);
      border-color: var(--el-color-primary-dark-5);
    }
  }

  // 资源标签样式
  .resource-tag {
    padding: 8px 14px;
    color: var(--el-color-success-dark-2);
    background-color: var(--el-color-success-light-9);
    border: 1px solid var(--el-color-success-light-5);
    border-radius: 8px;
    transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);

    &:hover {
      box-shadow: 0 6px 12px -2px var(--el-box-shadow-lighter);
      transform: translateY(-2px);
    }

    html.dark & {
      color: var(--el-color-success-light-3);
      background-color: var(--el-color-success-dark-9);
      border-color: var(--el-color-success-dark-5);
    }
  }

  // 警告提示样式
  .warning-alert {
    :deep(.el-alert) {
      margin-top: 16px;
      background-color: var(--el-color-warning-light-9);
      border: 1px solid var(--el-color-warning-light-5);
      border-radius: 12px;

      .el-alert__title {
        font-size: 14px;
        font-weight: 500;
        color: var(--el-color-warning-dark-2);
      }

      html.dark & {
        background-color: var(--el-color-warning-dark-9);
        border-color: var(--el-color-warning-dark-5);

        .el-alert__title {
          color: var(--el-color-warning-light-3);
        }
      }
    }
  }

  // 下拉选择框样式
  :deep(.resource-select-dropdown) {
    background-color: var(--el-bg-color);
    border: 1px solid var(--el-border-color);
    border-radius: 12px;

    .resource-option {
      padding: 10px 14px;

      .group-name {
        font-weight: 500;
        color: var(--el-text-color-primary);
      }

      .group-display-name {
        font-size: 13px;
        color: var(--el-text-color-secondary);
      }
    }

    .el-select-dropdown__item.selected {
      color: var(--el-color-success);
      background-color: var(--el-color-success-light-9);
    }

    .el-select-dropdown__item:hover {
      background-color: var(--el-fill-color-light);
    }

    html.dark & {
      .el-select-dropdown__item.selected {
        background-color: var(--el-color-success-dark-9);
      }
    }
  }
}
</style>
