<template>
  <div>
    <el-card
      shadow="hover"
      class="form-card"
      :body-style="{
        padding: '30px',
        borderRadius: '12px'
      }"
    >
      <el-form
        ref="ruleFormRef"
        :model="newFormInline.form"
        size="large"
        status-icon
        label-position="top"
        class="custom-form"
        :label-style="{
          color: '#303133',
          fontWeight: '600',
          marginBottom: '12px'
        }"
        @submit.prevent
      >
        <el-form-item
          label="名称"
          prop="name"
          :rules="[
            { required: true, message: '请输入名称', trigger: 'blur' },
            { max: 255, message: '请输入最大255', trigger: 'blur' }
          ]"
        >
          <el-input
            v-model="newFormInline.form.name"
            placeholder="请输入名称"
            maxlength="255"
            show-word-limit
            class="input-field"
            :style="{
              borderRadius: '8px',
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)'
            }"
          >
            <template #prefix>
              <iconify-icon-online
                icon="ep:document"
                width="16"
                height="16"
                style="color: #a8abb2"
              />
            </template>
          </el-input>
        </el-form-item>
        <el-form-item
          label="备注"
          prop="remark"
          :rules="[{ max: 255, message: '请输入最大255', trigger: 'blur' }]"
        >
          <el-input
            v-model="newFormInline.form.remark"
            type="textarea"
            placeholder="请输入备注"
            :rows="4"
            maxlength="255"
            show-word-limit
            class="input-field"
            :style="{
              borderRadius: '8px',
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)'
            }"
          >
            <template #prefix>
              <iconify-icon-online
                icon="ep:edit"
                width="16"
                height="16"
                style="color: #a8abb2"
              />
            </template>
          </el-input>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { defineProps, ref } from "vue";
import { IconifyIconOnline } from "@/components/ReIcon";
import type { FormInstance } from "element-plus";
import type { BusinessForm } from "@/api/asset/bussiness";

export interface FormProps {
  formInline: {
    form: BusinessForm;
  };
}

const props = withDefaults(defineProps<FormProps>(), {
  formInline: () => ({ row: undefined, form: undefined })
});

const newFormInline = ref(props.formInline);
const ruleFormRef = ref<FormInstance>();
defineExpose({ ruleFormRef });
</script>

<style scoped lang="scss">
.form-card {
  border-radius: 12px;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 8px 24px rgb(0 0 0 / 10%);
    transform: translateY(-4px);
  }
}

.custom-form {
  .el-form-item {
    margin-bottom: 20px;

    &__label {
      margin-bottom: 12px;
      font-weight: 600;
      color: #303133;
      transition: color 0.3s ease;
    }
  }
}

.input-field {
  border-radius: 8px;
  transition: all 0.3s ease;

  :deep(input),
  :deep(textarea) {
    border-color: #e4e7ed;
    border-radius: 8px;

    &:focus {
      border-color: #409eff;
      box-shadow: 0 0 0 1px rgb(64 158 255 / 20%);
    }
  }

  :deep(.el-input__prefix-inner) {
    display: flex;
    align-items: center;
    color: #a8abb2;
    transition: color 0.3s ease;
  }

  &:focus-within {
    :deep(.el-input__prefix-inner) {
      color: #409eff;
    }
  }
}
</style>
