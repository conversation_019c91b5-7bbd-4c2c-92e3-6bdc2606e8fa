import dayjs from "dayjs";
import {
  type DatacenterForm,
  getDatacenterList,
  type Datacenter,
  addDataceterAPI,
  updateDatacenterAPI,
  deleteDatacenterAPI
} from "@/api/asset/datacenter";
import type { PaginationProps } from "@pureadmin/table";
import { reactive, ref, onMounted, h } from "vue";
import { message } from "@/utils/message";
import { CloudTypes, CloudTypesColors } from "@/config/enum";
import { addDrawer } from "@/components/ReDrawer";
import Uform from "./Uform.vue";
import { addDialog } from "@/components/ReDialog";
import { RouterLink } from "vue-router";

export function useRole() {
  const form = reactive({
    keyword: undefined,
    cloud_type: undefined
  });
  const dataList = ref<Datacenter[]>([]);
  const loading = ref(true);

  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true,
    pageSizes: [10, 20, 30, 40, 50, 100]
  });

  const columns: TableColumnList = [
    {
      label: "名称",
      prop: "name",
      minWidth: 150,
      cellRenderer: ({ row }) => <div class="datacenter-name">{row.name}</div>
    },
    {
      label: "Region ID",
      prop: "code",
      minWidth: 100,
      cellRenderer: ({ row }) => <div class="datacenter-code">{row.code}</div>
    },
    {
      label: "类型",
      prop: "cloud_type",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <div class="datacenter-type">
          <el-text type={CloudTypesColors.get(row.cloud_type)}>
            {CloudTypes.get(row.cloud_type)}
          </el-text>
        </div>
      )
    },
    {
      label: "主机数量",
      prop: "host_total",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <RouterLink
          to={{ name: "主机列表", query: { datacenter_id: row.id } }}
          style={{ color: "#409eff" }}
        >
          <span>{row.host_total}</span>
        </RouterLink>
      )
    },
    {
      label: "备注",
      prop: "remark",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <div class="datacenter-remark">{row.remark}</div>
      )
    },
    {
      label: "时间信息",
      prop: "time",
      minWidth: 240,
      cellRenderer: ({ row }) => (
        <div class="time-info">
          <div class="time-item">
            <span class="label">创建时间:</span>
            <span class="value">
              {dayjs(row.created_at).format("YYYY-MM-DD HH:mm:ss")}
            </span>
          </div>
          <div class="time-item">
            <span class="label">更新时间:</span>
            <span class="value">
              {dayjs(row.updated_at).format("YYYY-MM-DD HH:mm:ss")}
            </span>
          </div>
        </div>
      )
    },
    {
      label: "操作",
      prop: "action",
      minWidth: 150,
      cellRenderer: ({ row }) => (
        <div class="datacenter-actions">
          <el-button type="primary" link onClick={() => updateFunc(row)}>
            编辑
          </el-button>
          <el-button type="danger" link onClick={() => deleteFunc(row)}>
            删除
          </el-button>
        </div>
      )
    }
  ];

  const editForm = ref<DatacenterForm | null>(null);
  const childrenRef = ref<any>(null);

  function addFunc() {
    editForm.value = {
      name: "",
      code: "",
      remark: ""
    };
    addDrawer({
      headerRenderer: ({ titleId, titleClass }) => (
        <div class="flex flex-row justify-between">
          <h4 id={titleId} class={titleClass}>
            添加数据中心
          </h4>
        </div>
      ),
      contentRenderer: () =>
        h(Uform, {
          formInline: {
            form: editForm.value
          },
          ref: childrenRef
        }),
      beforeSure: done => {
        if (childrenRef.value.ruleFormRef) {
          childrenRef.value.ruleFormRef
            .validate((valid: boolean) => {
              if (valid) {
                addDataceterAPI(editForm.value)
                  .then(res => {
                    if (res.success) {
                      message(res.msg, { type: "success" });
                      done();
                      onSearch();
                      editForm.value = undefined;
                    } else {
                      message(res.msg, { type: "error" });
                    }
                  })
                  .catch(error => {
                    message(error, { type: "error" });
                  });
              } else {
                message("请检查表单数据", { type: "error" });
              }
            })
            .catch(error => {
              message(error, { type: "error" });
            });
        }
      }
    });
  }
  function updateFunc(row: Datacenter) {
    editForm.value = {
      name: row.name,
      code: row.code,
      remark: row.remark
    };
    addDrawer({
      headerRenderer: ({ titleId, titleClass }) => (
        <div class="flex flex-row justify-between">
          <h4 id={titleId} class={titleClass}>
            修改数据中心 {row.name}
          </h4>
        </div>
      ),
      contentRenderer: () =>
        h(Uform, {
          formInline: {
            form: editForm.value
          },
          ref: childrenRef
        }),
      beforeSure: done => {
        if (childrenRef.value.ruleFormRef) {
          childrenRef.value.ruleFormRef
            .validate((valid: boolean) => {
              if (valid) {
                updateDatacenterAPI(row.id, editForm.value)
                  .then(res => {
                    if (res.success) {
                      message(res.msg, { type: "success" });
                      done();
                      onSearch();
                      editForm.value = undefined;
                    } else {
                      message(res.msg, { type: "error" });
                    }
                  })
                  .catch(error => {
                    message(error, { type: "error" });
                  });
              } else {
                message("请检查表单数据", { type: "error" });
              }
            })
            .catch(error => {
              message(error, { type: "error" });
            });
        }
      }
    });
  }
  function deleteFunc(row: Datacenter) {
    addDialog({
      title: "",
      width: 500,
      sureBtnLoading: true,
      contentRenderer: () => (
        <p>
          确认删除：<b style="color:red"> {row.name}</b>
        </p>
      ),
      beforeSure: done => {
        deleteDatacenterAPI(row.id)
          .then(res => {
            if (res.success) {
              message(res.msg, { type: "success" });
              onSearch();
              done();
            } else {
              message(res.msg, { type: "error" });
            }
          })
          .catch(error => {
            message("请求失败" + error, { type: "error" });
          })
          .finally(() => {
            loading.value = false;
          });
      }
    });
  }

  function handleSizeChange(val: number) {
    pagination.pageSize = val;
    onSearch();
  }

  function handleCurrentChange(val: number) {
    pagination.currentPage = val;
    onSearch();
  }

  async function onSearch() {
    loading.value = true;
    getDatacenterList({
      page: pagination.currentPage,
      limit: pagination.pageSize,
      keyword: form.keyword,
      cloud_type: form.cloud_type
    })
      .then(res => {
        if (res.success) {
          dataList.value = res.data;
          pagination.total = res.count;
          pagination.pageSize = pagination.pageSize;
          pagination.currentPage = pagination.currentPage;
        } else {
          dataList.value = [];
          pagination.total = 0;
          pagination.pageSize = pagination.pageSize;
          pagination.currentPage = pagination.currentPage;
        }
      })
      .catch(() => {
        message("请求失败", { type: "error" });
      })
      .finally(() => {
        loading.value = false;
      });
  }

  const resetForm = formEl => {
    if (!formEl) return;
    formEl.resetFields();
    onSearch();
  };

  onMounted(() => {
    onSearch();
  });

  return {
    form,
    loading,
    columns,
    dataList,
    pagination,
    onSearch,
    resetForm,
    handleSizeChange,
    handleCurrentChange,
    addFunc
  };
}
