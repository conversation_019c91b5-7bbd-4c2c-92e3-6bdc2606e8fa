<template>
  <div>
    <el-card shadow="hover" class="form-card">
      <el-form
        ref="ruleFormRef"
        label-width="auto"
        style="max-width: 600px"
        :model="newFormInline.form"
        size="large"
        status-icon
        label-position="top"
        class="custom-form"
      >
        <el-form-item
          label="名称"
          prop="name"
          :rules="[formRules.required, formRules.maxLength]"
        >
          <el-input
            v-model="newFormInline.form.name"
            placeholder="请输入名称"
            show-word-limit
            maxlength="255"
            class="custom-input"
          />
        </el-form-item>
        <el-form-item
          label="Code"
          prop="code"
          :rules="[{ max: 255, message: '请输入最大255', trigger: 'blur' }]"
        >
          <el-input
            v-model="newFormInline.form.code"
            placeholder="请输入region id"
            show-word-limit
            maxlength="255"
            class="custom-input"
          />
        </el-form-item>
        <el-form-item
          label="备注"
          prop="remark"
          :rules="[{ max: 255, message: '请输入最大255', trigger: 'blur' }]"
        >
          <el-input
            v-model="newFormInline.form.remark"
            type="textarea"
            show-word-limit
            maxlength="255"
            class="custom-textarea"
            :autosize="{ minRows: 3, maxRows: 6 }"
          />
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { defineProps, ref } from "vue";
import type { FormInstance } from "element-plus";
import type { DatacenterForm } from "@/api/asset/datacenter";

// 统一的表单验证规则
const formRules = {
  required: { required: true, message: "请输入名称", trigger: "blur" },
  maxLength: { max: 255, message: "最大长度255个字符", trigger: "blur" }
} as const;

// 完善 Props 类型定义
interface FormProps {
  formInline: {
    form: DatacenterForm;
  };
}

const props = withDefaults(defineProps<FormProps>(), {
  formInline: () => ({
    form: {} as DatacenterForm
  })
});

const newFormInline = ref(props.formInline);
const ruleFormRef = ref<FormInstance>();

defineExpose({ ruleFormRef });
</script>

<style scoped lang="scss">
.form-card {
  border-radius: 12px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 8%);
  transition: box-shadow 0.3s ease;

  &:hover {
    box-shadow: 0 6px 20px rgb(0 0 0 / 12%);
  }
}

.custom-form {
  :deep(.el-form-item) {
    margin-bottom: 16px;
  }

  :deep(.el-form-item__label) {
    margin-bottom: 8px;
    font-weight: 600;
    color: #303133;
  }

  .custom-input,
  .custom-textarea {
    :deep(.el-input__wrapper) {
      border-color: #e4e7ed;
      border-radius: 8px;
      transition: all 0.3s ease;

      &:hover {
        border-color: #409eff;
        box-shadow: 0 0 0 1px rgb(64 158 255 / 20%);
      }

      &.is-focus {
        border-color: #409eff;
        box-shadow: 0 0 0 1px rgb(64 158 255 / 40%);
      }
    }

    :deep(input),
    :deep(textarea) {
      font-size: 14px;
    }
  }

  .custom-textarea {
    :deep(.el-textarea__inner) {
      min-height: 100px;
      border-radius: 8px;
    }
  }
}
</style>
