import dayjs from "dayjs";
import {
  getDDosProtectionListAPI,
  type DDosProtection
} from "@/api/asset/ddos";
import type { PaginationProps } from "@pureadmin/table";
import { reactive, ref, onMounted } from "vue";
import { message } from "@/utils/message";

import {
  getAllCloudAccountsAPI,
  type CloudAccount
} from "@/api/asset/cloud-account";

export function useRole() {
  const form = reactive({
    keyword: undefined,
    account_id: undefined
  });
  const dataList = ref<DDosProtection[]>([]);
  const loading = ref(true);
  const accounts = ref<CloudAccount[]>([]);
  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true,
    pageSizes: [10, 20, 30, 40, 50, 100]
  });

  const columns: TableColumnList = [
    {
      label: "ID",
      prop: "id",
      minWidth: 50
    },
    {
      label: "账户",
      prop: "account",
      minWidth: 80
    },
    {
      label: "实例ID",
      prop: "instance_id",
      minWidth: 120
    },
    {
      label: "状态",
      prop: "status",
      minWidth: 80,
      cellRenderer: ({ row }) => (
        <el-tag type={row.status === 1 ? "success" : "danger"}>
          {row.status === 1 ? "正常" : "过期"}
        </el-tag>
      )
    },
    {
      label: "启用状态",
      prop: "enabled",
      minWidth: 80,
      cellRenderer: ({ row }) => (
        <el-tag type={row.enabled ? "success" : "danger"}>
          {row.enabled ? "正常" : "停用"}
        </el-tag>
      )
    },
    {
      label: "IP地址",
      prop: "ip",
      minWidth: 120
    },
    {
      label: "IP版本",
      prop: "ip_version",
      minWidth: 80
    },
    {
      label: "IP模式",
      prop: "ip_mode",
      minWidth: 80
    },
    {
      label: "过期时间",
      prop: "expire_time",
      minWidth: 160,
      formatter: row => dayjs(row.expire_time).format("YYYY-MM-DD HH:mm:ss")
    },
    {
      label: "备注",
      prop: "remark",
      minWidth: 120
    },
    {
      label: "创建时间",
      prop: "create_time",
      minWidth: 160,
      formatter: row => dayjs(row.create_time).format("YYYY-MM-DD HH:mm:ss")
    },
    {
      label: "同步时间",
      prop: "sync_time",
      minWidth: 160,
      formatter: row => dayjs(row.sync_time).format("YYYY-MM-DD HH:mm:ss")
    }
  ];

  function handleSizeChange(val: number) {
    pagination.pageSize = val;
    onSearch();
  }

  function handleCurrentChange(val: number) {
    pagination.currentPage = val;
    onSearch();
  }

  async function onSearch() {
    loading.value = true;
    getDDosProtectionListAPI({
      page: pagination.currentPage,
      limit: pagination.pageSize,
      keyword: form.keyword,
      account_id: form.account_id
    })
      .then(res => {
        if (res.success) {
          dataList.value = res.data;
          pagination.total = res.count;
        } else {
          dataList.value = [];
          pagination.total = 0;
        }
      })
      .catch(() => {
        message("请求失败", { type: "error" });
      })
      .finally(() => {
        loading.value = false;
      });
  }

  const resetForm = formEl => {
    if (!formEl) return;
    formEl.resetFields();
    onSearch();
  };

  onMounted(() => {
    onSearch();
  });
  function getAllCloudAccounts() {
    getAllCloudAccountsAPI()
      .then(res => {
        if (res.success) {
          accounts.value = res.data;
        } else {
          message(res.msg, { type: "error" });
        }
      })
      .catch(error => {
        message(error, { type: "error" });
      });
  }

  onMounted(() => {
    onSearch();
    getAllCloudAccounts();
  });

  return {
    form,
    loading,
    columns,
    dataList,
    pagination,
    onSearch,
    handleSizeChange,
    handleCurrentChange,
    resetForm,
    getAllCloudAccounts,
    accounts
  };
}
