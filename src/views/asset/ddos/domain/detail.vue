<template>
  <div class="detail-container">
    <div class="detail-header">
      <div class="domain-title">
        <el-text type="primary" style="font-size: 20px; font-weight: bold">
          {{ data.domain }}
        </el-text>
        <el-tag :type="data.http2_enable ? 'success' : 'info'" class="ml-2">
          {{ data.http2_enable ? "HTTP2 已启用" : "HTTP2 未启用" }}
        </el-tag>
      </div>
      <div class="domain-info">
        <el-text type="info">账户：{{ data.account }}</el-text>
        <el-text type="info" class="ml-4"
          >转发端口：{{ data.proxy_types }}</el-text
        >
        <el-text type="info" class="ml-4">
          同步时间：{{ dayjs(data.sync_time).format("YYYY-MM-DD HH:mm:ss") }}
        </el-text>
      </div>
    </div>

    <div class="detail-content">
      <div class="section-card">
        <div class="section-header">
          <el-icon><Monitor /></el-icon>
          <span class="section-title"
            >后端服务地址 ({{ data.real_servers?.length || 0 }})</span
          >
        </div>
        <div class="server-list">
          <div
            v-for="(server, index) in data.real_servers"
            :key="index"
            class="server-item"
          >
            <el-icon><Connection /></el-icon>
            <el-text type="primary">{{ server }}</el-text>
          </div>
        </div>
      </div>

      <div class="section-card">
        <div class="section-header">
          <el-icon><Monitor /></el-icon>
          <span class="section-title"
            >后端服务主机 ({{ data.hosts?.length || 0 }})</span
          >
        </div>
        <div class="host-list">
          <div v-for="host in data.hosts" :key="host.id" class="host-item">
            <div class="host-info">
              <div class="host-name">
                <el-icon><Monitor /></el-icon>
                <el-link
                  type="primary"
                  :href="`/asset/host?ip=${host.ip}`"
                  target="_blank"
                >
                  {{ host.name }}
                </el-link>
              </div>
              <div class="host-ips">
                <el-tag type="info" class="host-tag">{{ host.ip }}</el-tag>
                <el-tag type="success" class="host-tag">{{
                  host.public_ip
                }}</el-tag>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="action-buttons">
      <el-button type="primary" @click="handleViewBps">
        <el-icon><DataLine /></el-icon>
        查看带宽
      </el-button>
      <el-button type="success" @click="handleSync">
        <el-icon><Refresh /></el-icon>
        同步带宽
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { h } from "vue";
import dayjs from "dayjs";
import type { DDosDomain } from "@/api/asset/ddos";
import Bps from "./bps.vue";
import Sync from "./sync.vue";
import { addDialog } from "@/components/ReDialog";
import {
  Monitor,
  Connection,
  DataLine,
  Refresh
} from "@element-plus/icons-vue";

const props = defineProps<{
  data: DDosDomain;
}>();

const handleViewBps = () => {
  addDialog({
    title: `DDoS域名 - ${props.data.domain} - 带宽统计`,
    hideFooter: true,
    contentRenderer: () => h(Bps, { id: props.data.id })
  });
};

const handleSync = () => {
  addDialog({
    title: `DDoS域名 - ${props.data.domain} - 带宽同步`,
    hideFooter: true,
    width: "30%",
    contentRenderer: () => h(Sync, { id: props.data.id })
  });
};
</script>

<style scoped lang="scss">
.detail-container {
  min-height: 100%;
  padding: 24px;
  background-color: #f5f7fa;

  .detail-header {
    padding: 24px;
    margin-bottom: 24px;
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 12px 0 rgb(0 0 0 / 5%);

    .domain-title {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
    }

    .domain-info {
      display: flex;
      align-items: center;
      font-size: 14px;
      color: #606266;
    }
  }

  .detail-content {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  .section-card {
    padding: 24px;
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 12px 0 rgb(0 0 0 / 5%);

    .section-header {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      font-size: 16px;
      font-weight: 600;
      color: #303133;

      .el-icon {
        margin-right: 8px;
        font-size: 18px;
        color: #409eff;
      }
    }
  }

  .server-list {
    display: flex;
    flex-direction: column;
    gap: 12px;

    .server-item {
      display: flex;
      gap: 8px;
      align-items: center;
      padding: 12px;
      background-color: #f8f9fa;
      border-radius: 8px;
      transition: all 0.3s ease;

      &:hover {
        background-color: #f0f2f5;
      }

      .el-icon {
        color: #409eff;
      }
    }
  }

  .host-list {
    display: flex;
    flex-direction: column;
    gap: 12px;

    .host-item {
      padding: 16px;
      background-color: #f8f9fa;
      border-radius: 8px;
      transition: all 0.3s ease;

      &:hover {
        background-color: #f0f2f5;
      }

      .host-info {
        display: flex;
        flex-direction: column;
        gap: 12px;

        .host-name {
          display: flex;
          gap: 8px;
          align-items: center;

          .el-icon {
            color: #409eff;
          }
        }

        .host-ips {
          display: flex;
          gap: 8px;
        }
      }
    }
  }

  .host-tag {
    height: 28px;
    padding: 0 12px;
    margin: 0;
    font-size: 13px;
    line-height: 26px;
    border-radius: 4px;
  }

  .action-buttons {
    display: flex;
    gap: 16px;
    justify-content: center;
    margin-top: 32px;

    .el-button {
      padding: 12px 24px;
      font-size: 14px;

      .el-icon {
        margin-right: 8px;
      }
    }
  }
}
</style>
