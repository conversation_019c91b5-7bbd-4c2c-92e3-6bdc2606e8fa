<template>
  <div class="batch-sync-container">
    <div class="sync-header">
      <el-alert
        v-if="domains.length > 0"
        type="info"
        :closable="false"
        show-icon
      >
        <template #title>
          <span class="alert-title">{{ domains.length }} 个域名已选中</span>
        </template>
        <div class="alert-content">
          选择日期后点击“开始同步”按钮，将为所有选中的域名同步带宽使用情况
        </div>
      </el-alert>
    </div>
    
    <el-form :model="form" label-width="100px" class="sync-form">
      <el-form-item label="同步日期" class="date-form-item">
        <div class="date-container">
          <el-date-picker
            v-model="form.syncDate"
            type="date"
            placeholder="选择日期"
            :disabled="loading"
            value-format="YYYY-MM-DD"
            :clearable="false"
            :disabled-date="disabledDate"
            style="width: 100%"
            @change="resetSyncStatus"
          />
          <div v-if="form.syncDate" class="date-info">
            <el-icon><Calendar /></el-icon>
            <span>您选择的同步日期为 {{ formatDate(form.syncDate) }}</span>
          </div>
        </div>
      </el-form-item>

      <div class="domains-section">
        <div class="domains-header">
          <div class="domains-title">
            <el-icon><Connection /></el-icon>
            <span>选中的域名</span>
          </div>
          <div class="domains-stat">
            <el-tag v-if="successCount > 0" type="success" size="small" class="stat-tag">
              成功: {{ successCount }}
            </el-tag>
            <el-tag v-if="errorCount > 0" type="danger" size="small" class="stat-tag">
              失败: {{ errorCount }}
            </el-tag>
            <el-tag v-if="processedCount < totalCount" type="info" size="small" class="stat-tag">
              待同步: {{ totalCount - processedCount }}
            </el-tag>
          </div>
        </div>
        
        <div class="selected-domains">
          <div
            v-for="(domain, index) in domains"
            :id="'domain-item-'+domain.id"
            :key="index"
            :ref="el => { if(el) domainRefs[domain.id] = el as HTMLElement }"
            class="domain-item"
          >
            <div class="domain-info">
              <el-tag size="small" type="info" class="domain-tag">
                {{ domain.domain }}
              </el-tag>
              <span v-if="domain.cname" class="domain-cname">
                {{ domain.cname }}
              </span>
            </div>
            <div class="domain-status">
              <el-tag
                v-if="syncStatus[domain.id] === 'success'"
                size="small"
                type="success"
                effect="light"
              >
                <el-icon class="status-icon"><Check /></el-icon> 同步成功
              </el-tag>
              <el-tag
                v-else-if="syncStatus[domain.id] === 'error'"
                size="small"
                type="danger"
                effect="light"
              >
                <el-icon class="status-icon"><Close /></el-icon> 同步失败
              </el-tag>
              <el-tag
                v-else-if="syncStatus[domain.id] === 'loading'"
                size="small"
                type="warning"
                effect="light"
              >
                <el-icon class="status-icon"><Loading /></el-icon> 同步中...
              </el-tag>
              <el-tag v-else size="small" type="info" effect="light">
                <el-icon class="status-icon"><Timer /></el-icon> 待同步
              </el-tag>
            </div>
          </div>
        </div>
      </div>

      <div v-if="totalCount > 0 && processedCount > 0" class="progress-section">
        <el-progress
          :percentage="progressPercentage"
          :status="progressStatus"
          :stroke-width="10"
          :format="format => `${processedCount}/${totalCount} (${format}%)`"
          class="sync-progress"
        />
      </div>

      <div class="actions-section">
        <el-button
          type="primary"
          :loading="loading"
          :disabled="domains.length === 0"
          class="sync-button"
          :icon="loading ? 'Loading' : 'Connection'"
          @click="handleBatchSync"
        >
          {{ loading ? "同步中..." : "开始同步" }}
        </el-button>
      </div>
    </el-form>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, nextTick } from "vue";
import { ElMessage } from "element-plus";
import { syncDDosDomainBpsAPI, type DDosDomain } from "@/api/asset/ddos";
import { Calendar, Connection, Check, Close, Loading, Timer } from "@element-plus/icons-vue";

const loading = ref(false);

const form = reactive({
  syncDate: new Date().toISOString().split("T")[0] // 默认今天
});

// 禁用未来日期
const disabledDate = (time: Date) => {
  return time.getTime() > Date.now();
};

const props = defineProps<{
  domains: DDosDomain[];
  onSuccess?: () => void;
  onError?: (error: any) => void;
}>();

const emit = defineEmits(["update:status"]);

// 跟踪每个域名的同步状态
const syncStatus = reactive<Record<number, "pending" | "loading" | "success" | "error">>({});

// 保存域名元素引用，用于滚动定位
const domainRefs = reactive<Record<number, HTMLElement | null>>({});

// 初始化同步状态为待处理
for (const domain of props.domains) {
  syncStatus[domain.id] = "pending";
}

// 统计数据
const totalCount = computed(() => props.domains.length);
const processedCount = ref(0);
const successCount = ref(0);
const errorCount = ref(0);

// 计算进度百分比
const progressPercentage = computed(() => {
  if (totalCount.value === 0) return 0;
  return Math.round((processedCount.value / totalCount.value) * 100);
});

// 计算进度条状态
const progressStatus = computed(() => {
  if (errorCount.value > 0) {
    return progressPercentage.value === 100 ? "exception" : "";
  }
  return progressPercentage.value === 100 ? "success" : "";
});

// 格式化日期显示
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  const today = new Date();
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);
  
  // 格式化日期
  const options: Intl.DateTimeFormatOptions = { year: 'numeric', month: '2-digit', day: '2-digit', weekday: 'long' };
  return new Intl.DateTimeFormat('zh-CN', options).format(date);
};

// 批量同步函数
// 重置所有域名的同步状态
const resetSyncStatus = () => {
  if (loading.value) return; // 如果正在同步中，不重置
  
  // 重置处理计数器
  processedCount.value = 0;
  successCount.value = 0;
  errorCount.value = 0;
  
  // 重置所有域名状态为待处理
  for (const domain of props.domains) {
    syncStatus[domain.id] = "pending";
  }
  
  // 移除所有高亮
  removeAllHighlights();
  
  // 通知状态更新
  emit("update:status", { ...syncStatus });
};

// 更新高亮样式
const updateHighlight = (domainId: number) => {
  // 先移除所有高亮
  removeAllHighlights();
  
  // 为当前域名添加高亮
  const element = domainRefs[domainId];
  if (element && element instanceof HTMLElement) {
    element.classList.add('domain-item-syncing');
  }
};

// 移除所有高亮
const removeAllHighlights = () => {
  // 移除所有之前的高亮
  Object.values(domainRefs).forEach(el => {
    if (el && el instanceof HTMLElement) {
      el.classList.remove('domain-item-syncing');
    }
  });
};

// 滚动到指定域名
const scrollToDomain = async (domainId: number) => {
  await nextTick();
  const element = domainRefs[domainId];
  if (element && element instanceof HTMLElement) {
    // 获取滚动容器
    const container = element.closest('.selected-domains');
    if (container) {
      // 计算元素相对于容器的位置
      const containerRect = container.getBoundingClientRect();
      const elementRect = element.getBoundingClientRect();
      const relativeTop = elementRect.top - containerRect.top;
      
      // 判断元素是否在可视区域内
      const isInView = 
        relativeTop >= 0 && 
        relativeTop <= containerRect.height - elementRect.height;
      
      // 如果不在可视区域，则滚动到该元素
      if (!isInView) {
        element.scrollIntoView({ behavior: "smooth", block: "center" });
      }
    } else {
      element.scrollIntoView({ behavior: "smooth", block: "center" });
    }
  }
};

const handleBatchSync = async () => {
  if (props.domains.length === 0) {
    ElMessage.warning("请选择要同步的域名");
    return;
  }

  loading.value = true;
  processedCount.value = 0;
  successCount.value = 0;
  errorCount.value = 0;

  // 统计结果
  const results: Array<{ domain: string; success: boolean; error?: string }> = [];

  try {
    // 逐个同步域名带宽信息
    for (const domain of props.domains) {
      // 更新状态为同步中
      syncStatus[domain.id] = "loading";
      emit("update:status", { ...syncStatus });
      
      // 更新高亮样式
      updateHighlight(domain.id);
      
      // 滚动到当前处理的域名
      await scrollToDomain(domain.id);

      try {
        const res = await syncDDosDomainBpsAPI(domain.id, form.syncDate);
        if (res.success) {
          syncStatus[domain.id] = "success";
          successCount.value++;
          results.push({ domain: domain.domain, success: true });
          await scrollToDomain(domain.id);
        } else {
          syncStatus[domain.id] = "error";
          errorCount.value++;
          results.push({
            domain: domain.domain,
            success: false,
            error: res.msg || "同步失败"
          });
          await scrollToDomain(domain.id);
        }
      } catch (error: any) {
        syncStatus[domain.id] = "error";
        errorCount.value++;
        results.push({
          domain: domain.domain,
          success: false,
          error: error.message || "同步失败"
        });
        await scrollToDomain(domain.id);
      }

      // 更新进度
      processedCount.value++;
    }

    // 显示最终结果
    if (errorCount.value === 0) {
      ElMessage.success(`所有 ${successCount.value} 个域名带宽同步成功！`);
      props.onSuccess?.();
    } else if (successCount.value === 0) {
      ElMessage.error(`所有 ${errorCount.value} 个域名带宽同步失败！`);
      props.onError?.(new Error("所有域名同步失败"));
    } else {
      ElMessage.warning(
        `同步完成: ${successCount.value} 个成功，${errorCount.value} 个失败`
      );
    }
  } catch (error: any) {
    ElMessage.error("批量同步过程中发生错误");
    props.onError?.(error);
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.batch-sync-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 20px;
}

.sync-header {
  margin-bottom: 8px;
}

.alert-title {
  font-size: 14px;
  font-weight: 600;
}

.alert-content {
  margin-top: 4px;
  font-size: 12px;
  color: #67798e;
}

.sync-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.date-form-item {
  margin-bottom: 0;
}

.date-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.date-info {
  display: flex;
  gap: 6px;
  align-items: center;
  padding: 4px 8px;
  font-size: 12px;
  color: #67798e;
  background-color: #f0f9ff;
  border-radius: 4px;
}

.domains-section {
  margin-bottom: 16px;
  overflow: hidden;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 3%);
}

.domains-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
}

.domains-title {
  display: flex;
  gap: 8px;
  align-items: center;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.domains-stat {
  display: flex;
  gap: 8px;
  align-items: center;
}

.stat-tag {
  font-size: 11px;
}

.selected-domains {
  max-height: 280px;
  padding: 8px 16px;
  overflow-y: auto;
  overscroll-behavior: contain; /* 防止滚动传播 */
  background-color: #fff;
  scroll-behavior: smooth;
}

.domain-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 6px;
  border-bottom: 1px solid #ebeef5;
  transition: all 0.3s ease;
}

.domain-item:last-child {
  border-bottom: none;
}

.domain-info {
  display: flex;
  gap: 8px;
  align-items: center;
  overflow: hidden;
}

.domain-tag {
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
}

.domain-cname {
  max-width: 200px;
  overflow: hidden;
  font-size: 11px;
  color: #909399;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.domain-status {
  flex-shrink: 0;
}

.status-icon {
  margin-right: 4px;
  font-size: 12px;
}

/* 添加当前处理项的高亮效果 */
.domain-item-syncing {
  padding-left: 10px;
  background-color: rgb(247 186 85 / 5%);
  border-left: 3px solid #e6a23c;
}

.progress-section {
  padding: 8px 16px;
  margin-bottom: 16px;
  background-color: #f5f7fa;
  border-radius: 8px;
}

.sync-progress {
  margin: 4px 0;
}

.actions-section {
  display: flex;
  justify-content: center;
  margin-top: 8px;
}

.sync-button {
  min-width: 120px;
  font-weight: 500;
  box-shadow: 0 2px 5px rgb(0 0 0 / 10%);
  transition: all 0.3s;
}

.sync-button:hover {
  box-shadow: 0 4px 8px rgb(0 0 0 / 15%);
  transform: translateY(-2px);
}
</style>
