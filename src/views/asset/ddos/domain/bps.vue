<template>
  <div>
    <div class="top-container">
      <div class="date-picker-container">
        <el-date-picker
          v-model="form.date_range"
          type="daterange"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          placeholder="选择时间范围"
          clearable
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          :shortcuts="[
          {
            text: '最近7天',
            value: () => {
              const end = dayjs();
              const start = dayjs().subtract(6, 'day');
              return [start.format('YYYY-MM-DD'), end.format('YYYY-MM-DD')];
            }
          },
          {
            text: '最近30天',
            value: () => {
              const end = dayjs();
              const start = dayjs().subtract(29, 'day');
              return [start.format('YYYY-MM-DD'), end.format('YYYY-MM-DD')];
            }
          },
          {
            text: '最近90天',
            value: () => {
              const end = dayjs();
              const start = dayjs().subtract(89, 'day');
              return [start.format('YYYY-MM-DD'), end.format('YYYY-MM-DD')];
            }
          }
        ]"
        />
      </div>
    </div>
    <div v-if="!timePoints.length" class="no-data-message">没有数据</div>
    <div v-show="timePoints.length" ref="chartRef" class="chart-container" />
  </div>
</template>

<script setup lang="ts">
import { ref, onBeforeMount, reactive, watch, onUnmounted } from "vue";
import * as echarts from "echarts";
import {
  getDDosDomainBpsListAPI,
  getAllDDosDomainBpsListAPI
} from "@/api/asset/ddos";
import { message } from "@/utils/message";
import dayjs from "dayjs";
import { debounce } from 'lodash-es'; // 添加导入debounce函数

const props = defineProps<{
  id?: number;
  isAllDomains?: boolean;
}>();

const form = reactive<{
  date_range: [string, string];
}>({
  date_range: [
    dayjs().subtract(30, "day").format("YYYY-MM-DD"),
    dayjs().format("YYYY-MM-DD")
  ]
});

const chartRef = ref<HTMLDivElement | null>(null);
const timePoints = ref<string[]>([]);
const inBps = ref<any[]>([]);
const outBps = ref<any[]>([]);
const domainData = ref<Record<string, { in: number[]; out: number[] }>>({});

const getDomainBpsData = async () => {
  const [start_time, end_time] = form.date_range;
  try {
    let res;
    if (props.isAllDomains) {
      res = await getAllDDosDomainBpsListAPI({
        start_time,
        end_time
      });
    } else {
      res = await getDDosDomainBpsListAPI(props.id!, {
        start_time,
        end_time
      });
    }

    if (res.success && res.data) {
      if (props.isAllDomains) {
        // 处理所有域名的数据
        const allData = res.data;
        
        // 收集所有日期并排序
        const dates = new Set<string>();
        allData.forEach(item => {
          const date = dayjs(item.stat_date).format("YYYY-MM-DD");
          dates.add(date);
        });
        const sortedDates = Array.from(dates).sort();
        timePoints.value = sortedDates;
        
        // 创建每个域名的数据结构，为每个日期初始化数据点
        const domainMap: Record<string, { in: number[]; out: number[] }> = {};
        
        // 提取所有唯一域名
        const uniqueDomains = [...new Set(allData.map(item => item.domain))] as string[];
        
        // 限制最多显示10个域名，避免图表过于复杂
        const limitedDomains = uniqueDomains.length > 10 ? uniqueDomains.slice(0, 10) : uniqueDomains;
        
        // 为每个域名初始化数据数组，用0填充所有日期位置
        limitedDomains.forEach(domain => {
          domainMap[domain] = {
            in: new Array(sortedDates.length).fill(0),
            out: new Array(sortedDates.length).fill(0)
          };
        });
        
        // 填充实际数据
        allData.forEach(item => {
          const date = dayjs(item.stat_date).format("YYYY-MM-DD");
          const dateIndex = sortedDates.indexOf(date);
          const domain = item.domain as string;
          if (dateIndex !== -1 && domainMap[domain]) {
            domainMap[domain].in[dateIndex] = item.in_bps;
            domainMap[domain].out[dateIndex] = item.out_bps;
          }
        });
        
        domainData.value = domainMap;
      } else {
        // 处理单个域名的数据
        if (!res.data || !Array.isArray(res.data) || res.data.length === 0) {
          timePoints.value = [];
          inBps.value = [];
          outBps.value = [];
          return;
        }
        
        // 按日期排序
        const sortedData = [...res.data].sort((a, b) => {
          return dayjs(a.stat_date).diff(dayjs(b.stat_date));
        });
        
        // 提取日期点和对应的流量数据
        timePoints.value = sortedData.map(item => dayjs(item.stat_date).format("YYYY-MM-DD"));
        inBps.value = sortedData.map((item, index) => [timePoints.value[index], item.in_bps || 0]);
        outBps.value = sortedData.map((item, index) => [timePoints.value[index], item.out_bps || 0]);
        
        console.log("单域名数据处理结果:", {
          timePoints: timePoints.value,
          inBps: inBps.value,
          outBps: outBps.value
        });
      }
      updateChart();
    } else {
      message(res.msg || "获取数据失败", { type: "error" });
    }
  } catch (error) {
    message(error.message || "获取数据失败", { type: "error" });
  }
};

const formatBandwidth = (value: number) => {
  if (value >= 1000000000) {
    return (value / 1000000000).toFixed(2) + " Gbps";
  } else if (value >= 1000000) {
    return (value / 1000000).toFixed(2) + " Mbps";
  } else if (value >= 1000) {
    return (value / 1000).toFixed(2) + " Kbps";
  }
  return value + " bps";
};

// 格式化带宽值，带单位的同时显示原始值
// 例如: "123.45 Mbps (123456789 bps)"
const formatBandwidthWithOriginal = (value: number) => {
  const formatted = formatBandwidth(value);
  // 原始值使用逗号分隔的数字格式，更易读
  const originalFormatted = value.toLocaleString();
  return `${formatted} (${originalFormatted} bps)`;
};

const updateChart = () => {
  if (!chartRef.value) return;
  
  // 在图表初始化前先设置父容器样式
  const container = chartRef.value.parentElement;
  if (container) {
    container.style.width = "100%";
    container.style.height = "100%";
  }

  // 销毁现有图表实例，避免重新渲染时的内存泄漏
  if (chartRef.value) {
    echarts.dispose(chartRef.value);
  }
  
  const chart = echarts.init(chartRef.value);
  
  // 检查数据是否为空
  if ((!props.isAllDomains && (!inBps.value.length || !outBps.value.length)) ||
      (props.isAllDomains && Object.keys(domainData.value).length === 0)) {
    chart.setOption({
      title: {
        text: '没有数据',
        left: 'center',
        top: 'center',
        textStyle: {
          fontSize: 20,
          color: '#999'
        }
      }
    });
    return;
  }
  
  // 设置ECharts图表选项与数据
  const option = {
    title: {
      text: props.isAllDomains
        ? "所有域名带宽使用情况"
        : "域名带宽使用情况",
      subtext: `统计时间: ${form.date_range[0]} 至 ${form.date_range[1]}`,
      left: "center",
      textStyle: {
        fontSize: 18,
        fontWeight: "bold",
        color: "#333"
      },
      subtextStyle: {
        fontSize: 13,
        color: "#999"
      }
    },
    tooltip: {
      trigger: "axis",
      confine: true, // 限制tooltip在图表区域内
      enterable: true, // 允许鼠标进入tooltip
      axisPointer: {
        type: "cross",
        label: {
          backgroundColor: "#6a7985"
        }
      },
      formatter: function (params: any[]) {
        const date = dayjs(params[0].axisValue).format("YYYY-MM-DD");
        let result = `<div style="font-weight:bold;margin-bottom:8px;font-size:14px;color:#333;">${date}</div>`;
        
        if (props.isAllDomains) {
          // 按域名分组显示
          const domainGroups: Record<string, any[]> = {};
          params.forEach(param => {
            const parts = param.seriesName.split(" - ");
            const domain = parts[0];
            if (!domainGroups[domain]) {
              domainGroups[domain] = [];
            }
            domainGroups[domain].push(param);
          });
          
          Object.entries(domainGroups).forEach(([domain, items]) => {
            result += `<div style="margin:8px 0;padding:8px;background-color:#f8f9fa;border-radius:4px;">
              <div style="font-weight:bold;color:#409EFF;margin-bottom:4px;">${domain}</div>`;
            
            items.forEach(param => {
              const color = param.color;
              const marker = `<span style="display:inline-block;margin-right:5px;border-radius:50%;width:8px;height:8px;background-color:${color};"></span>`;
              // 显示方向（入向/出向）
              const direction = param.seriesName.split(" - ")[1];
              // 使用格式化函数处理带宽值，显示格式化的带宽和原始数值
              const formattedValue = formatBandwidthWithOriginal(param.value[1]);
              result += `<div style="margin:4px 0 4px 10px;">${marker}${direction}: ${formattedValue}</div>`;
            });

            result += `</div>`;
          });
        } else {
          // 单个域名的情况
          params.forEach(param => {
            const color = param.color;
            const marker = `<span style="display:inline-block;margin-right:5px;border-radius:50%;width:8px;height:8px;background-color:${color};"></span>`;
            // 使用格式化函数处理带宽值，显示格式化的带宽和原始数值
            const formattedValue = formatBandwidthWithOriginal(param.value[1]);
            result += `<div style="margin:6px 0;font-size:13px;">${marker}${param.seriesName}: ${formattedValue}</div>`;
          });
        }
        
        return result;
      }
    },
    legend: {
      type: props.isAllDomains && Object.keys(domainData.value).length > 5 ? 'scroll' : 'plain',
      bottom: "bottom",
      padding: [20, 10],
      textStyle: {
        color: "#666"
      },
      pageIconColor: "#409eff",
      pageIconInactiveColor: "#ccc",
      pageTextStyle: {
        color: "#666"
      },
      selected: {}, // 自动保持全部选中状态
      formatter: name => {
        // 如果名称太长，截断显示
        if (name.length > 25) {
          return name.substring(0, 25) + '...';
        }
        return name;
      }
    },
    grid: {
      left: "10%",
      right: "5%",
      bottom: props.isAllDomains && Object.keys(domainData.value).length > 5 ? "15%" : "10%",
      top: "15%",
      containLabel: true,
      width: 'auto', // 自适应宽度
      height: 'auto' // 自适应高度
    },
    xAxis: {
      type: "time",
      // 设置合理的时间范围，避免图表两侧大量空白
      min: timePoints.value.length > 0 ? timePoints.value[0] : undefined,
      max: timePoints.value.length > 0 ? timePoints.value[timePoints.value.length - 1] : undefined,
      boundaryGap: ['5%', '5%'],
      axisLabel: {
        formatter: (value: string) => {
          const date = dayjs(value);
          return date.format("MM-DD");
        },
        textStyle: {
          color: "#666",
          fontSize: 12
        },
        rotate: timePoints.value.length > 30 ? 45 : 0,
      },
      axisLine: {
        lineStyle: {
          color: "#ddd"
        }
      },
      axisTick: {
        alignWithLabel: true,
        lineStyle: {
          color: "#ddd"
        }
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "#eee",
          type: "dashed"
        }
      }
    },
    yAxis: {
      type: "value",
      name: "带宽 (bps)",
      nameTextStyle: {
        color: "#666",
        fontSize: 14,
        padding: [0, 0, 0, 50]
      },
      // 设置最小值为0，使数据从底部开始展示，并加入自动调整最大值
      min: 0,
      // 根据数据调整最大值，避免大量上部空白
      max: function(value) {
        // 给最大值增加10%的空间，减少空白区域
        return value.max > 0 ? Math.ceil(value.max * 1.1) : 100;
      },
      // 添加分割线数量，使图表更易读
      splitNumber: 5,
      // 设置缩放区间，确保数据合理展示
      scale: true,
      axisLabel: {
        formatter: (value: number) => formatBandwidth(value),
        textStyle: {
          color: "#666",
          fontSize: 12
        }
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: "#ddd"
        }
      },
      splitLine: {
        lineStyle: {
          color: "#eee",
          type: "dashed"
        }
      }
    },
    series: props.isAllDomains
      ? Object.entries(domainData.value).flatMap(([domain, data], index) => {
          // 使用预定义的颜色列表，循环使用
          const colors = [
            ['#5470c6', '#91cc75'],
            ['#ee6666', '#fac858'],
            ['#73c0de', '#3ba272'],
            ['#fc8452', '#9a60b4'],
            ['#ea7ccc', '#5470c6']
          ];
          const colorPair = colors[index % colors.length];
          
          return [
            {
              name: `${domain} - 入向`,
              type: "line",
              data: data.in.map((value, i) => [timePoints.value[i], value]),
              smooth: true,
              symbol: 'circle',
              symbolSize: 6,
              // 只有数据点多于30个时才采样，否则显示全部点
              sampling: timePoints.value.length > 30 ? 'lttb' : undefined,
              itemStyle: {
                color: colorPair[0]
              },
              lineStyle: {
                width: 2,
                color: colorPair[0]
              },
              areaStyle: {
                opacity: 0.15,
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: colorPair[0] },
                  { offset: 1, color: 'rgba(255, 255, 255, 0.2)' }
                ])
              }
            },
            {
              name: `${domain} - 出向`,
              type: "line",
              data: data.out.map((value, i) => [timePoints.value[i], value]),
              smooth: true,
              symbol: 'circle',
              symbolSize: 6,
              // 只有数据点多于30个时才采样，否则显示全部点
              sampling: timePoints.value.length > 30 ? 'lttb' : undefined,
              itemStyle: {
                color: colorPair[1]
              },
              lineStyle: {
                width: 2,
                color: colorPair[1]
              },
              areaStyle: {
                opacity: 0.15,
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: colorPair[1] },
                  { offset: 1, color: 'rgba(255, 255, 255, 0.2)' }
                ])
              }
            }
          ];
        })
      : [
          {
            name: "入向",
            type: "line",
            data: inBps.value,
            smooth: true,
            symbol: 'circle',
            symbolSize: 6,
            lineStyle: {
              width: 3,
              color: '#5470c6'
            },
            itemStyle: {
              color: '#5470c6'
            },
            areaStyle: {
              opacity: 0.15,
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#5470c6' },
                { offset: 1, color: 'rgba(84, 112, 198, 0.1)' }
              ])
            }
          },
          {
            name: "出向",
            type: "line",
            data: outBps.value,
            smooth: true,
            symbol: 'circle',
            symbolSize: 6,
            lineStyle: {
              width: 3,
              color: '#91cc75'
            },
            itemStyle: {
              color: '#91cc75'
            },
            areaStyle: {
              opacity: 0.15,
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#91cc75' },
                { offset: 1, color: 'rgba(145, 204, 117, 0.1)' }
              ])
            }
          }
        ]
  };

  chart.setOption(option);

  // 使用函数引用和防抖，避免频繁触发resize事件
  const handleResize = debounce(() => {
    if (chart) chart.resize();
  }, 200);
  window.addEventListener("resize", handleResize);
  
  // 组件卸载时清理事件监听器
  onUnmounted(() => {
    window.removeEventListener("resize", handleResize);
    if (chartRef.value) {
      echarts.dispose(chartRef.value);
    }
  });
  
  // 手动触发一次resize事件确保图表在初始化后正确渲染
  setTimeout(() => {
    handleResize();
  }, 300);
};

watch(
  () => form.date_range,
  () => {
    if (form.date_range[0] && form.date_range[1]) {
      getDomainBpsData();
    }
  }
);

onBeforeMount(() => {
  getDomainBpsData();
});
</script>

<style scoped>
.top-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  margin-bottom: 10px;
}

.date-picker-container {
  display: flex;
  justify-content: center;
  padding: 0;
}

:deep(.el-date-editor) {
  width: 380px !important;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 4%);
  transition: all 0.3s ease;
}

:deep(.el-date-editor:hover) {
  box-shadow: 0 4px 12px rgb(0 0 0 / 8%);
}

:deep(.el-date-editor .el-range-input) {
  font-size: 14px;
}

:deep(.el-date-editor .el-range__icon) {
  color: #409eff;
}

:deep(.el-picker-panel) {
  border-radius: 8px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 12%);
}

:deep(.el-picker-panel .el-picker-panel__sidebar) {
  background-color: #f8f9fb;
  border-right: 1px solid #ebeef5;
}

:deep(.el-picker-panel .el-picker-panel__shortcut) {
  padding: 12px 16px;
  font-size: 14px;
  color: #606266;
}

:deep(.el-picker-panel .el-picker-panel__shortcut:hover) {
  color: #409eff;
  background-color: #ecf5ff;
}

.chart-container {
  width: 100%;
  height: 650px; /* 增加高度，改善图表展示 */
  padding: 20px;
  margin-top: 20px;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 8%);
  transition: all 0.3s ease;
}

.chart-container:hover {
  box-shadow: 0 6px 20px rgb(0 0 0 / 12%);
}

.no-data-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 600px;
  font-size: 16px;
  color: #999;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgb(0 0 0 / 10%);
}
</style>
