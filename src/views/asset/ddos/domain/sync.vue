<template>
  <div class="sync-container">
    <el-form :model="form" label-width="120px">
      <el-form-item label="同步日期">
        <el-date-picker
          v-model="form.syncDate"
          type="date"
          placeholder="选择日期"
          :disabled="loading"
          value-format="YYYY-MM-DD"
          :clearable="false"
          :disabled-date="disabledDate"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" :loading="loading" @click="handleSync">
          同步
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from "vue";
import { ElMessage } from "element-plus";
import { syncDDosDomainBpsAPI } from "@/api/asset/ddos";

const loading = ref(false);

const form = reactive({
  syncDate: new Date().toISOString().split("T")[0] // 默认今天
});

// 禁用未来日期
const disabledDate = (time: Date) => {
  return time.getTime() > Date.now();
};

const props = defineProps<{
  id: number;
  onSuccess?: () => void;
  onError?: (error: any) => void;
}>();
const handleSync = async () => {
  if (!props.id) {
    ElMessage.error("域名ID不能为空");
    return;
  }
  loading.value = true;
  try {
    const res = await syncDDosDomainBpsAPI(props.id, form.syncDate);
    if (res.success) {
      ElMessage.success("同步成功");
      props.onSuccess?.();
    } else {
      const errorMsg = res.msg || "同步失败";
      ElMessage.error(errorMsg);
      props.onError?.(new Error(errorMsg));
    }
  } catch (error: any) {
    const errorMsg = error.message || "同步失败";
    ElMessage.error(errorMsg);
    props.onError?.(errorMsg);
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.sync-container {
  padding: 20px;
}
</style>
