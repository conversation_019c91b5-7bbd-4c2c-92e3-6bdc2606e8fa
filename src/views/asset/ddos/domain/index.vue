<template>
  <div class="main">
    <el-card class="search-card">
      <el-form
        ref="formRef"
        :inline="true"
        :model="form"
        class="search-form"
        @submit.prevent
      >
        <div class="flex items-center gap-2">
          <el-form-item label="账户" prop="account_id" class="!mb-0">
            <div class="flex items-center gap-1">
              <el-select
                v-model="form.account_id"
                style="width: 350px"
                filterable
                clearable
                placeholder="请选择账户"
                @change="onSearch"
              >
                <el-option
                  v-for="account in accounts"
                  :key="account.id"
                  :label="account.name"
                  :value="account.id"
                />
              </el-select>
              <el-button
                class="!ml-0 flex-none"
                :icon="useRenderIcon('ep:refresh')"
                @click="getAllCloudAccounts"
              />
            </div>
          </el-form-item>

          <el-form-item label="关键字" prop="keyword" class="!mb-0">
            <el-input
              v-model="form.keyword"
              style="width: 240px"
              placeholder="请输入实例ID、IP等关键字"
              clearable
              @keyup.enter="onSearch"
            />
          </el-form-item>

          <el-form-item class="!mb-0">
            <div class="flex items-center gap-2">
              <el-button
                type="primary"
                :icon="useRenderIcon('ri:search-line')"
                :loading="loading"
                class="search-button !ml-0"
                @click="onSearch"
              >
                搜索
              </el-button>
              <el-button
                :icon="useRenderIcon(Refresh)"
                class="reset-button"
                @click="resetForm(formRef)"
              >
                重置
              </el-button>
              <el-button
                type="success"
                :icon="useRenderIcon('ri:line-chart-line')"
                class="all-bps-button"
                @click="showAllBps"
              >
                查看所有域名流量
              </el-button>
              <el-button
                type="warning"
                :icon="useRenderIcon('ri:refresh-line')"
                class="batch-sync-button"
                :loading="loading"
                @click="handleBatchSync"
              >
                批量同步带宽
              </el-button>
            </div>
          </el-form-item>
        </div>
      </el-form>
    </el-card>

    <PureTableBar title="DDos 防护域名" :columns="columns" @refresh="onSearch">
      <template v-slot="{ size, dynamicColumns }">
        <pure-table
          ref="tableRef"
          row-key="id"
          align-whole="left"
          table-layout="auto"
          :loading="loading"
          :size="size"
          :minHeight="500"
          :data="dataList"
          :columns="dynamicColumns"
          :pagination="{ ...pagination, size }"
          :header-cell-style="{
            fontWeight: '600'
          }"
          :row-style="{
            cursor: 'pointer',
            transition: 'background-color 0.2s ease'
          }"
          :cell-style="{
            padding: '12px 10px'
          }"
          border
          stripe
          highlight-current-row
          @selection-change="(val) => selectedRows = val"
          @page-size-change="handleSizeChange"
          @page-current-change="handleCurrentChange"
        >
          <template #empty>
            <el-empty
              description="暂无数据"
              :image-size="120"
              style="padding: 40px 0"
            />
          </template>
        </pure-table>
      </template>
    </PureTableBar>
  </div>
</template>

<script setup lang="ts">
import { onBeforeMount, ref, h } from "vue";
import { useRole } from "./hook";
import { PureTableBar } from "@/components/RePureTableBar";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { addDialog } from "@/components/ReDialog";
import Bps from "./bps.vue";

import Refresh from "@iconify-icons/ep/refresh";
import { useRoute } from "vue-router";

defineOptions({
  name: "DDosDomain"
});

const formRef = ref();
const tableRef = ref();

const {
  form,
  loading,
  columns,
  dataList,
  pagination,
  resetForm,
  onSearch,
  handleSizeChange,
  handleCurrentChange,
  getAllCloudAccounts,
  accounts,
  selectedRows,
  handleBatchSync
} = useRole();

onBeforeMount(() => {
  const router = useRoute();
  const accountId = router.query.account_id as string;
  if (accountId) {
    form.account_id = accountId;
  }
  const keyword = router.query.keyword as string;
  if (keyword) {
    form.keyword = keyword;
  }
});

function showAllBps() {
  addDialog({
    title: "所有域名流量统计",
    width: "80%",
    hideFooter: true,
    contentRenderer: () => h(Bps, { isAllDomains: true })
  });
}
</script>

<style scoped lang="scss">
.main {
  padding: 20px;
  border-radius: 12px;
}

.search-card {
  margin-bottom: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 8%);
  transition: box-shadow 0.3s ease;

  &:hover {
    box-shadow: 0 6px 20px rgb(0 0 0 / 12%);
  }
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;

  :deep(.el-form-item) {
    margin-bottom: 0;
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: #606266;
  }

  .el-input {
    width: 220px;

    :deep(.el-input__wrapper) {
      border-radius: 8px;

      &:focus {
        border-color: #409eff;
        box-shadow: 0 0 0 1px rgb(64 158 255 / 20%);
      }
    }
  }
}

.search-button,
.reset-button,
.add-button {
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.search-button {
  background-color: #409eff;
  border-color: #409eff;
}

:deep(.pure-table) {
  overflow: hidden;
  border: 1px solid #e4e7ed;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 4%);
}

:deep(.host-list) {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

:deep(.host-item) {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 8px;
  background-color: #f8f9fa;
  border-radius: 8px;
  transition: all 0.3s ease;

  &:hover {
    background-color: #f0f2f5;
  }
}

:deep(.host-main) {
  display: flex;
  align-items: center;
}

:deep(.host-tags) {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

:deep(.host-tag) {
  height: 28px;
  padding: 0 12px;
  margin: 0;
  font-size: 13px;
  line-height: 26px;
  border-radius: 4px;
}

:deep(.domain-column) {
  .el-link {
    font-weight: 500;
    transition: all 0.3s ease;

    &:hover {
      opacity: 0.8;
    }
  }
}

.all-bps-button {
  background-color: #67c23a;
  border-color: #67c23a;

  &:hover {
    background-color: #85ce61;
    border-color: #85ce61;
  }
}
</style>
