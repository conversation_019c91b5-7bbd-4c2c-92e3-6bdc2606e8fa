import dayjs from "dayjs";
import { getDDosDomainListAPI, type DDosDomain } from "@/api/asset/ddos";
import type { PaginationProps } from "@pureadmin/table";
import { reactive, ref, onMounted, h } from "vue";
import { message } from "@/utils/message";
import Bps from "./bps.vue";
import Sync from "./sync.vue";
import BatchSync from "./batch-sync.vue";

import {
  getAllCloudAccountsAPI,
  type CloudAccount
} from "@/api/asset/cloud-account";
import { addDialog } from "@/components/ReDialog";
import Detail from "./detail.vue";

export function useRole() {
  const selectedRows = ref<DDosDomain[]>([]);

  const form = reactive({
    keyword: undefined,
    account_id: undefined
  });
  const dataList = ref<DDosDomain[]>([]);
  const loading = ref(true);
  const accounts = ref<CloudAccount[]>([]);
  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true,
    pageSizes: [10, 20, 30, 40, 50, 100]
  });

  const columns: TableColumnList = [
    {
      type: 'selection',
      width: 55,
      align: 'center',
      fixed: 'left'
    },
    {
      label: "域名信息",
      prop: "domain",
      minWidth: 400,
      align: "left",
      cellRenderer: ({ row }) => (
        <div
          class="domain-info"
          style="display: flex; flex-direction: column; gap: 4px;"
        >
          <div style="display: flex; align-items: center; gap: 8px;">
            <el-text type="info" class="label" style="width: 80px;">
              账号：
            </el-text>
            <el-text>{row.account}</el-text>
          </div>
          <div style="display: flex; align-items: center; gap: 8px;">
            <el-text type="info" class="label" style="width: 80px;">
              域名：
            </el-text>
            <el-tooltip content={row.domain} placement="top">
              <el-link
                type="primary"
                style="font-size: 13px;"
                onClick={() => showBps(row)}
              >
                {row.domain}
              </el-link>
            </el-tooltip>
          </div>
          <div style="display: flex; align-items: center; gap: 8px;">
            <el-text type="info" class="label" style="width: 80px;">
              CNAME：
            </el-text>
            <el-tooltip content={row.cname} placement="top">
              <el-text type="info">{row.cname || "-"}</el-text>
            </el-tooltip>
          </div>
          <div style="display: flex; align-items: center; gap: 8px;">
            <el-text type="info" class="label" style="width: 80px;">
              状态：
            </el-text>
            <div style="display: flex; gap: 8px;">
              <el-tag size="small" type={row.http2_enable ? "success" : "info"}>
                {row.http2_enable ? "HTTP2启用" : "HTTP2未启用"}
              </el-tag>
              <el-tag size="small" type="warning">
                {row.proxy_types}
              </el-tag>
            </div>
          </div>
        </div>
      )
    },
    {
      label: "服务地址数",
      prop: "real_servers",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <el-tag type="info" size="small">
          {row.real_servers?.length || 0}
        </el-tag>
      )
    },
    {
      label: "后端服务地址",
      prop: "real_servers",
      minWidth: 300,
      cellRenderer: ({ row }) => (
        <div
          class="host-list"
          style="display: flex; flex-direction: column; gap: 4px;"
        >
          {row.hosts?.length > 0
            ? row.hosts.map(host => (
                <div class="host-item" style="padding: 2px 8px;">
                  <div class="host-main">
                    <el-tooltip
                      content={host.name}
                      placement="top"
                      effect="light"
                    >
                      <el-link
                        type="primary"
                        href={`/asset/host?ip=${host.ip}`}
                        target="_blank"
                        style="font-weight: bold; font-size: 13px;"
                      >
                        {host.public_ip || host.ip}
                      </el-link>
                    </el-tooltip>
                  </div>
                  <div class="host-tags" style="margin-top: 2px;">
                    <el-tag
                      size="small"
                      type="info"
                      class="host-tag"
                      style="margin-right: 4px;"
                    >
                      {host.ip}
                    </el-tag>
                  </div>
                </div>
              ))
            : row.real_servers?.map(server => (
                <div class="host-item" style="padding: 2px 8px;">
                  <div class="host-main">
                    <el-text
                      type="primary"
                      style="font-weight: bold; font-size: 13px;"
                    >
                      {server}
                    </el-text>
                  </div>
                </div>
              ))}
        </div>
      )
    },
    {
      label: "更新信息",
      prop: "sync_time",
      minWidth: 160,
      cellRenderer: ({ row }) => (
        <el-text type="info">
          {dayjs(row.sync_time).format("YYYY-MM-DD HH:mm:ss")}
        </el-text>
      )
    },
    {
      label: "操作",
      prop: "id",
      minWidth: 180,
      cellRenderer: ({ row }) => (
        <div class="cell-item domain-column">
          <el-link type="primary" onClick={() => showBps(row)}>
            查看带宽
          </el-link>
          <el-divider direction="vertical" />
          <el-link type="primary" onClick={() => handleSync(row)}>
            同步带宽
          </el-link>
          <el-divider direction="vertical" />
          <el-link type="primary" onClick={() => showDetail(row)}>
            查看详情
          </el-link>
        </div>
      )
    }
  ];

  function handleSizeChange(val: number) {
    pagination.pageSize = val;
    onSearch();
  }

  function handleCurrentChange(val: number) {
    pagination.currentPage = val;
    onSearch();
  }

  function showBps(row: any) {
    addDialog({
      title: `DDoS域名 - ${row.domain} - 带宽统计`,
      hideFooter: true,
      contentRenderer: () => h(Bps, { id: row.id })
    });
  }

  function handleSync(row: any) {
    addDialog({
      title: `DDoS域名 - ${row.domain} - 带宽同步`,
      hideFooter: true,
      width: "30%",
      contentRenderer: () => h(Sync, { id: row.id })
    });
  }

  function showDetail(row: DDosDomain) {
    addDialog({
      title: `DDoS域名详情 - ${row.domain}`,
      width: "60%",
      hideFooter: true,
      contentRenderer: () => h(Detail, { data: row })
    });
  }

  async function onSearch() {
    loading.value = true;
    getDDosDomainListAPI({
      page: pagination.currentPage,
      limit: pagination.pageSize,
      keyword: form.keyword,
      account_id: form.account_id
    })
      .then(res => {
        if (res.success) {
          dataList.value = res.data;
          pagination.total = res.count;
        } else {
          dataList.value = [];
          pagination.total = 0;
        }
      })
      .catch(() => {
        message("请求失败", { type: "error" });
      })
      .finally(() => {
        loading.value = false;
      });
  }

  const resetForm = formEl => {
    if (!formEl) return;
    formEl.resetFields();
    onSearch();
  };

  onMounted(() => {
    onSearch();
  });
  function getAllCloudAccounts() {
    getAllCloudAccountsAPI()
      .then(res => {
        if (res.success) {
          accounts.value = res.data;
        } else {
          message(res.msg, { type: "error" });
        }
      })
      .catch(error => {
        message(error, { type: "error" });
      });
  }

  onMounted(() => {
    onSearch();
    getAllCloudAccounts();
  });

  function handleBatchSync() {
    if (selectedRows.value.length === 0) {
      message("请选择要同步的域名", { type: "warning" });
      return;
    }
    
    // 使用单个对话框包含批量同步组件
    addDialog({
      title: `批量同步域名带宽使用情况 (${selectedRows.value.length} 个域名)`,
      hideFooter: true,
      width: "50%",
      contentRenderer: () => h(BatchSync, { domains: selectedRows.value })
    });
  }

  return {
    form,
    loading,
    columns,
    dataList,
    pagination,
    onSearch,
    handleSizeChange,
    handleCurrentChange,
    resetForm,
    getAllCloudAccounts,
    accounts,
    showDetail,
    selectedRows,
    handleBatchSync
  };
}
