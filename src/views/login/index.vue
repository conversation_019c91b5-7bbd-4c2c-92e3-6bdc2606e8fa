<script setup lang="ts">
import Motion from "./utils/motion";
import { useRouter } from "vue-router";
import { message } from "@/utils/message";
import { loginRules } from "./utils/rule";
import { useNav } from "@/layout/hooks/useNav";
import type { FormInstance } from "element-plus";
import { useLayout } from "@/layout/hooks/useLayout";
import { useUserStoreHook } from "@/store/modules/user";
import { initRouter, getTopMenu } from "@/router/utils";
import { bg, avatar, illustration } from "./utils/static";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { ref, reactive, toRaw, onMounted, onBeforeUnmount } from "vue";
import { useDataThemeChange } from "@/layout/hooks/useDataThemeChange";

import dayIcon from "@/assets/svg/day.svg?component";
import darkIcon from "@/assets/svg/dark.svg?component";
import Lock from "@iconify-icons/ri/lock-fill";
import User from "@iconify-icons/ri/user-3-fill";

defineOptions({
  name: "Login"
});
const router = useRouter();
const loading = ref(false);
const ruleFormRef = ref<FormInstance>();
const activeTab = ref("oa"); // 默认使用OA登录

const { initStorage } = useLayout();
initStorage();

const { dataTheme, overallStyle, dataThemeChange } = useDataThemeChange();
dataThemeChange(overallStyle.value);
const { title } = useNav();

const ruleForm = reactive({
  username: "",
  password: ""
});

const onLogin = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate(valid => {
    if (valid) {
      loading.value = true;
      useUserStoreHook()
        .loginByUsername({
          username: ruleForm.username,
          password: ruleForm.password
        })
        .then(res => {
          if (res.success) {
            return initRouter().then(() => {
              router.push(getTopMenu(true).path).then(() => {
                message("登录成功", { type: "success" });
              });
            });
          }
        })
        .finally(() => (loading.value = false));
    }
  });
};

const loginByOAGateway = () => {
  useUserStoreHook()
    .loginByOAGateway()
    .then(res => {
      if (res.success) {
        return initRouter().then(() => {
          router.push(getTopMenu(true).path).then(() => {
            message("IAM登录成功", { type: "success" });
          });
        });
      }
    });
};
const oaLogin = () => {
  var domain = window.location.protocol + "//" + document.domain;
  var port = window.location.port;
  if (port != "") {
    domain += ":" + port;
  }
  let url =
    "https://oa-service.meiyou.com/qrcode?redirect_url=" +
    domain +
    "/oalogin&client_id=d90c989884e2459bab4ef1ea38225b94&response_type=code&state=1";
  window.open(url, "_self");
};

function onkeypress({ code }: KeyboardEvent) {
  if (["Enter", "NumpadEnter"].includes(code)) {
    activeTab.value === "oa" ? oaLogin() : onLogin(ruleFormRef.value);
  }
}

onMounted(() => {
  loginByOAGateway();
  window.document.addEventListener("keypress", onkeypress);
});

onBeforeUnmount(() => {
  window.document.removeEventListener("keypress", onkeypress);
});
</script>

<template>
  <div class="select-none">
    <img :src="bg" class="wave" />
    <div class="flex-c absolute right-5 top-3">
      <el-switch
        v-model="dataTheme"
        inline-prompt
        :active-icon="dayIcon"
        :inactive-icon="darkIcon"
        @change="dataThemeChange"
      />
    </div>
    <div class="login-container">
      <div class="img">
        <component :is="toRaw(illustration)" />
      </div>
      <div class="login-box">
        <div class="login-form">
          <avatar class="avatar" />
          <Motion>
            <h2 class="outline-none">{{ title }}</h2>
          </Motion>

          <el-tabs v-model="activeTab" class="tabs" type="card">
            <el-tab-pane label="OA登录" name="oa">
              <Motion>
                <el-button
                  class="w-full mt-4 oa-button"
                  size="default"
                  type="primary"
                  :loading="loading"
                  @click="oaLogin()"
                >
                  OA登录
                </el-button>
              </Motion>
            </el-tab-pane>
            <el-tab-pane label="用户名登录" name="username">
              <el-form
                ref="ruleFormRef"
                :model="ruleForm"
                :rules="loginRules"
                size="large"
                class="form"
              >
                <Motion :delay="100">
                  <el-form-item
                    :rules="[
                      {
                        required: true,
                        message: '请输入账号',
                        trigger: 'blur'
                      }
                    ]"
                    prop="username"
                  >
                    <el-input
                      v-model="ruleForm.username"
                      clearable
                      placeholder="账号"
                      :prefix-icon="useRenderIcon(User)"
                      class="input-field"
                    />
                  </el-form-item>
                </Motion>
                <Motion :delay="150">
                  <el-form-item prop="password">
                    <el-input
                      v-model="ruleForm.password"
                      clearable
                      show-password
                      placeholder="密码"
                      :prefix-icon="useRenderIcon(Lock)"
                      class="input-field"
                    />
                  </el-form-item>
                </Motion>

                <Motion :delay="250">
                  <el-button
                    class="w-full mt-4 login-button"
                    size="default"
                    type="primary"
                    :loading="loading"
                    @click="onLogin(ruleFormRef)"
                  >
                    登录
                  </el-button>
                </Motion>
              </el-form>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
@import url("@/style/login.css");

.login-container {
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 8%);
  transition: box-shadow 0.3s ease;

  &:hover {
    box-shadow: 0 6px 20px rgb(0 0 0 / 12%);
  }
}

.input-field {
  border-radius: 8px;
  transition: border-color 0.3s ease;

  &:focus-within {
    border-color: #409eff;
    box-shadow: 0 0 0 1px rgb(64 158 255 / 20%);
  }
}

.oa-button,
.login-button {
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
    transform: translateY(-2px);
  }
}

.oa-button {
  color: white;
  background-color: #409eff;
  border-color: #409eff;
}

.login-button {
  color: white;
  background-color: #67c23a;
  border-color: #67c23a;
}
</style>
