<template>
  <div>
    <el-form
      ref="ruleFormRef"
      :model="form"
      label-width="120px"
      label-position="top"
      @submit.prevent
    >
      <el-form-item label="MySQL存储空间总量(byte)" prop="mysql_storage_total">
        <el-input-number v-model="form.mysql_storage_total">
          <template #suffix>
            <span>byte</span>
          </template>
        </el-input-number>
      </el-form-item>
      <el-form-item label="Tidb存储空间总量(byte)" prop="tidb_storage_total">
        <el-input-number v-model="form.tidb_storage_total">
          <template #suffix>
            <span>byte</span>
          </template>
        </el-input-number>
      </el-form-item>
      <el-form-item
        label="MongoDB存储空间总量(byte)"
        prop="mongodb_storage_total"
      >
        <el-input-number v-model="form.mongodb_storage_total">
          <template #suffix>
            <span>byte</span>
          </template>
        </el-input-number>
      </el-form-item>
      <el-form-item
        label="ClickHouse存储空间总量(byte)"
        prop="clickhouse_storage_total"
      >
        <el-input-number v-model="form.clickhouse_storage_total">
          <template #suffix>
            <span>byte</span>
          </template>
        </el-input-number>
      </el-form-item>
      <el-form-item
        label="Starrocks存储空间总量(byte)"
        prop="starrocks_storage_total"
      >
        <el-input-number v-model="form.starrocks_storage_total">
          <template #suffix>
            <span>byte</span>
          </template>
        </el-input-number>
      </el-form-item>
      <el-form-item label="DLI 数据仓库(byte)" prop="dli_storage_total">
        <el-input-number v-model="form.dli_storage_total">
          <template #suffix>
            <span>byte</span>
          </template>
        </el-input-number>
      </el-form-item>
      <el-form-item label="OSS存储空间总量(byte)" prop="oss_storage_total">
        <el-input-number v-model="form.oss_storage_total">
          <template #suffix>
            <span>byte</span>
          </template>
        </el-input-number>
      </el-form-item>
      <el-form-item label="NAS存储空间总量(byte)" prop="nas_storage_total">
        <el-input-number v-model="form.nas_storage_total">
          <template #suffix>
            <span>byte</span>
          </template>
        </el-input-number>
      </el-form-item>
      <el-form-item style="padding-right: 20px; text-align: right">
        <el-button type="warning" @click="onSubmit">保存修改</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import type { DataAssets } from "@/api/statistics/asset";
import { updateDataAssetAPI } from "@/api/statistics/asset";
import { message } from "@/utils/message";
import { ref } from "vue";

const props = defineProps<{
  data: DataAssets;
}>();

const form = ref<DataAssets>(props.data);

const onSubmit = () => {
  updateDataAssetAPI(props.data.id, form.value)
    .then(res => {
      if (res.success) {
        message("提交成功", { type: "success" });
      } else {
        message(res.msg || "提交失败", { type: "error" });
      }
    })
    .catch(error => {
      message(error, { type: "error" });
    });
};
</script>

<style lang="scss" scoped>
.el-input-number {
  width: 80%;
}
</style>
