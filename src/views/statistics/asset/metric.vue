<template>
  <div class="metric-container">
    <div class="date-picker-container">
      <el-date-picker
        v-model="dateRange"
        type="monthrange"
        format="YYYY-MM"
        value-format="YYYY-MM"
        placeholder="选择一段时间"
        clearable
        range-separator="至"
        start-placeholder="开始月份"
        end-placeholder="结束月份"
        :disabled="loading"
      />
      <el-button
        type="primary"
        :icon="Refresh"
        circle
        :loading="loading"
        @click="fetchDataAndRenderChart"
      />
    </div>

    <div v-if="loading" class="loading-container">
      <el-icon class="loading-icon"><Loading /></el-icon>
      <span>正在加载数据...</span>
    </div>

    <div v-else-if="!chartData.length" class="no-data-message">
      <el-icon :size="32" color="#999"><DataLine /></el-icon>
      <p>没有找到相关数据</p>
      <p>请尝试选择其他时间范围</p>
    </div>

    <div v-show="!loading && chartData.length > 0" class="chart-wrapper">
      <div ref="chartRef" class="chart-container" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, watch, onBeforeUnmount } from "vue";
import * as echarts from "echarts";
import { message } from "@/utils/message";
import dayjs from "dayjs";
import { formatStorage, formatThousands } from "@/utils/format";
import { getMonthMetricsAPI } from "@/api/statistics/asset";
import { Refresh, Loading, DataLine } from "@element-plus/icons-vue";

// 图表相关的响应式引用
const chartRef = ref<HTMLDivElement | null>(null);
const chartInstance = ref<echarts.ECharts | null>(null);

// 数据相关的响应式引用
const loading = ref(false);
const chartData = ref<any[]>([]);

const props = defineProps<{
  assetType: string;
  metricType: string;
  title: string;
  unit: string;
  name: string;
}>();

const { assetType, metricType } = props;

const dateRange = ref([
  dayjs().subtract(3, "month").format("YYYY-MM"),
  dayjs().format("YYYY-MM")
]);

// 初始化图表实例
const initChart = async () => {
  try {
    // 确保 DOM 已经渲染完成
    await nextTick();

    // 等待父容器完全挂载
    await new Promise(resolve => setTimeout(resolve, 100));

    const chartContainer = chartRef.value;
    if (!chartContainer) {
      console.warn("Chart container not found, retrying...");
      return null;
    }

    // 确保容器尺寸正确
    if (chartContainer.clientWidth === 0 || chartContainer.clientHeight === 0) {
      console.warn("Container size is 0, waiting for layout...");
      return null;
    }

    // 销毁现有实例
    if (chartInstance.value) {
      chartInstance.value.dispose();
    }

    // 创建新实例
    const instance = echarts.init(chartContainer);
    chartInstance.value = instance;

    return instance;
  } catch (error) {
    return null;
  }
};

// 处理图表大小调整
const handleResize = () => {
  if (chartInstance.value) {
    chartInstance.value.resize();
  }
};

// 生成图表配置
const generateChartOption = (months: string[], metrics: number[]) => {
  return {
    title: {
      text: props.title,
      subtext: `${dateRange.value[0]} 至 ${dateRange.value[1]}`,
      left: "center",
      textStyle: {
        fontSize: 22,
        fontWeight: "bold",
        color: "#333"
      },
      subtextStyle: {
        fontSize: 14,
        color: "#666"
      }
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "line",
        lineStyle: {
          color: "rgba(0,0,0,0.2)",
          width: 1,
          type: "dashed"
        }
      },
      backgroundColor: "rgba(255,255,255,0.9)",
      borderWidth: 1,
      borderColor: "#ccc",
      padding: [8, 12],
      textStyle: {
        color: "#333"
      },
      formatter: function (params: any) {
        const month = params[0].axisValue;
        const metric = params[0].data;
        if (metricType === "memory_mb_total") {
          return `${month}<br/>${formatStorage(metric * 1024 * 1024)}`;
        } else if (assetType === "data") {
          return `${month}<br/>${formatStorage(metric)}`;
        } else {
          return `${month}<br/>${formatThousands(metric)}`;
        }
      }
    },
    grid: {
      top: 100,
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: months,
      axisLine: {
        lineStyle: {
          color: "#888"
        }
      },
      axisLabel: {
        rotate: 45,
        color: "#666",
        margin: 16
      }
    },
    yAxis: {
      type: "value",
      axisLine: {
        show: true,
        lineStyle: {
          color: "#888"
        }
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: "dashed",
          color: "#eee"
        }
      },
      axisLabel: {
        color: "#666",
        margin: 16,
        formatter: value => {
          if (metricType === "memory_mb_total") {
            return formatStorage(value * 1024 * 1024);
          }
          if (assetType === "data") {
            return formatStorage(value);
          } else {
            return formatThousands(value);
          }
        }
      }
    },
    series: [
      {
        data: metrics,
        type: "line",
        smooth: true,
        lineStyle: {
          color: "#409EFF",
          width: 3,
          shadowColor: "rgba(64, 158, 255, 0.3)",
          shadowBlur: 10,
          shadowOffsetY: 8
        },
        itemStyle: {
          color: "#409EFF",
          borderWidth: 2
        },
        emphasis: {
          focus: "series",
          itemStyle: {
            borderWidth: 3,
            shadowBlur: 10,
            shadowColor: "rgba(64, 158, 255, 0.5)"
          }
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: "rgba(64, 158, 255, 0.5)"
            },
            {
              offset: 1,
              color: "rgba(64, 158, 255, 0.1)"
            }
          ])
        },
        symbol: "circle",
        symbolSize: 8,
        label: {
          show: true,
          position: "top",
          distance: 10,
          backgroundColor: "rgba(255,255,255,0.7)",
          borderRadius: 3,
          padding: [2, 4],
          formatter: params => {
            if (metricType === "memory_mb_total") {
              return formatStorage(params.value * 1024 * 1024);
            }
            if (assetType === "data") {
              return formatStorage(params.value);
            } else {
              return formatThousands(params.value);
            }
          }
        }
      }
    ]
  };
};

// 渲染图表
const renderChart = async (months: string[], metrics: number[]) => {
  try {
    let instance = chartInstance.value;
    let retryCount = 0;
    const maxRetries = 3;

    while (!instance && retryCount < maxRetries) {
      instance = await initChart();
      if (!instance) {
        retryCount++;
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    if (!instance) {
      console.warn("Failed to create chart instance after retries");
      return;
    }

    const option = generateChartOption(months, metrics);
    instance.setOption(option, true);
    instance.resize();
  } catch (error) {
    console.error("Failed to render chart:", error);
  }
};

// 获取数据并渲染图表
const fetchDataAndRenderChart = async () => {
  loading.value = true;
  try {
    const [startTime, endTime] = dateRange.value;
    const response = await getMonthMetricsAPI({
      start: startTime,
      end: endTime,
      asset_type: assetType,
      metric_type: metricType,
      name: props.name
    });

    if (response.success && response.data) {
      chartData.value = response.data;
      const months = response.data.map(item => item.month);
      const metrics = response.data.map(item => item.metric);

      // 确保在数据加载完成后重新尝试渲染
      await nextTick();
      await new Promise(resolve => setTimeout(resolve, 300));
      await renderChart(months, metrics);
    } else {
      chartData.value = [];
    }
  } catch (error) {
    message("获取数据失败：" + error, { type: "error" });
    console.error("Error fetching data:", error);
    chartData.value = [];
  } finally {
    loading.value = false;
  }
};

// 生命周期钩子
onMounted(async () => {
  // 等待对话框动画完成
  await new Promise(resolve => setTimeout(resolve, 100));
  await fetchDataAndRenderChart();
});

onBeforeUnmount(() => {
  window.removeEventListener("resize", handleResize);
  if (chartInstance.value) {
    chartInstance.value.dispose();
    chartInstance.value = null;
  }
});

// 监听数据变化
watch(dateRange, async () => {
  await fetchDataAndRenderChart();
});

// 监听容器尺寸变化
const resizeObserver = new ResizeObserver(async entries => {
  const entry = entries[0];
  if (entry.contentRect.width > 0 && entry.contentRect.height > 0) {
    if (chartInstance.value) {
      chartInstance.value.resize();
    } else if (chartData.value.length > 0) {
      const months = chartData.value.map(item => item.month);
      const metrics = chartData.value.map(item => item.metric);
      await renderChart(months, metrics);
    }
  }
});

onMounted(() => {
  if (chartRef.value) {
    resizeObserver.observe(chartRef.value);
  }
});

onBeforeUnmount(() => {
  resizeObserver.disconnect();
});
</script>

<style scoped>
.metric-container {
  box-sizing: border-box;
  width: 100%;
  min-height: 500px;
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
}

.date-picker-container {
  display: flex;
  gap: 12px;
  align-items: center;
  margin-bottom: 24px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #909399;
}

.loading-icon {
  margin-bottom: 12px;
  font-size: 32px;
  animation: rotate 1s linear infinite;
}

.no-data-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #909399;
}

.no-data-message p {
  margin: 8px 0;
  font-size: 14px;
}

.chart-wrapper {
  position: relative;
  width: 100%;
  height: 400px;
  min-height: 400px;
  margin-top: 20px;
  background-color: #fff;
}

.chart-container {
  position: absolute;
  inset: 0;
  min-width: 200px !important;
  min-height: 200px !important;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}
</style>
