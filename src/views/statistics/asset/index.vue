<template>
  <div class="main">
    <el-card class="filter-card">
      <div class="filter-container">
        <el-form :inline="true" class="filter-form">
          <el-form-item label="选择月份">
            <el-date-picker
              v-model="selectedMonth"
              type="month"
              format="YYYY-MM"
              value-format="YYYY-MM"
              placeholder="选择月份"
              :disabled-date="disabledDate"
              @change="handleMonthChange"
            />
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              :icon="Refresh"
              class="refresh-btn"
              @click="fetchData"
            >
              刷新
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <!-- 计算资源统计 -->
    <el-card v-loading="loading" class="stat-card">
      <template #header>
        <div class="card-header">
          <h3>
            <iconify-icon-online
              icon="carbon:cloud-service-management"
              style="font-size: 22px"
            />计算资源统计
          </h3>
        </div>
      </template>
      <el-row :gutter="24">
        <el-col
          v-for="(item, index) in computeResourceItems"
          :key="index"
          :span="4"
        >
          <div class="stat-item">
            <div class="stat-icon-wrapper">
              <iconify-icon-online :icon="item.icon" style="font-size: 36px" />
            </div>
            <div class="stat-title">{{ item.title }}</div>
            <div class="stat-value">
              {{ item.value }}
              <span
                v-if="item.change !== undefined"
                class="change-indicator"
                :class="{
                  increase: item.change > 0,
                  decrease: item.change < 0,
                  'no-change': item.change === 0
                }"
              >
                <iconify-icon-online
                  v-if="item.change !== 0"
                  :icon="item.change > 0 ? 'ep:top' : 'ep:bottom'"
                  style="margin-right: 2px"
                />
                <span v-else style="margin-right: 2px">-</span>
                {{
                  item.change === 0
                    ? "-"
                    : item.isMemory
                      ? formatStorage(Math.abs(item.change) * 1024 * 1024)
                      : formatNumberWithCommas(Math.abs(item.change)) +
                        (item.unit ? " " + item.unit : "")
                }}
              </span>
              <el-button
                type="primary"
                size="small"
                link
                style="padding: 4px 8px"
                @click="
                  showMetric(
                    item.assetType,
                    item.metricType,
                    '计算资源统计' + item.title,
                    item.unit,
                    ''
                  )
                "
              >
                <el-icon><TrendCharts /></el-icon>
                <span style="margin-left: 4px">趋势图</span>
              </el-button>
            </div>
          </div>
        </el-col>
      </el-row>

      <el-divider content-position="left">区域资源分布</el-divider>
      <el-row :gutter="24">
        <el-col v-for="(item, index) in regionStats" :key="index" :span="12">
          <el-card shadow="hover" class="region-card">
            <div class="region-title">{{ item.region_name }}</div>
            <div class="region-stats">
              <div class="region-stat-item">
                <div class="stat-label">
                  <iconify-icon-online
                    icon="grommet-icons:host"
                    style="font-size: 20px"
                  />主机数
                </div>
                <div class="stat-value">
                  {{ formatNumberWithCommas(item.host_total || 0) }} 台
                  <span
                    v-if="item.host_total_change !== undefined"
                    class="change-indicator"
                    :class="{
                      increase: item.host_total_change > 0,
                      decrease: item.host_total_change < 0,
                      'no-change': item.host_total_change === 0
                    }"
                  >
                    <iconify-icon-online
                      v-if="item.host_total_change !== 0"
                      :icon="
                        item.host_total_change > 0 ? 'ep:top' : 'ep:bottom'
                      "
                      style="margin-right: 2px"
                    />
                    <span v-else style="margin-right: 2px">-</span>
                    {{
                      item.host_total_change === 0
                        ? "-"
                        : formatNumberWithCommas(
                            Math.abs(item.host_total_change)
                          ) + " 台"
                    }}
                  </span>
                  <el-button
                    type="primary"
                    size="small"
                    link
                    style="padding: 4px 8px; margin-left: 8px"
                    @click="
                      showMetric(
                        'region',
                        'host_total',
                        '区域资源统计' + item.name,
                        '台',
                        item.name
                      )
                    "
                  >
                    <el-icon><TrendCharts /></el-icon>
                    <span style="margin-left: 4px">趋势图</span>
                  </el-button>
                </div>
              </div>
              <div class="region-stat-item">
                <div class="stat-label">
                  <iconify-icon-online
                    icon="ep:cpu"
                    style="font-size: 20px"
                  />CPU核数
                </div>
                <div class="stat-value">
                  {{ formatNumberWithCommas(item.cpu_total || 0) }} 核
                  <span
                    v-if="item.cpu_total_change !== undefined"
                    class="change-indicator"
                    :class="{
                      increase: item.cpu_total_change > 0,
                      decrease: item.cpu_total_change < 0,
                      'no-change': item.cpu_total_change === 0
                    }"
                  >
                    <iconify-icon-online
                      v-if="item.cpu_total_change !== 0"
                      :icon="item.cpu_total_change > 0 ? 'ep:top' : 'ep:bottom'"
                      style="margin-right: 2px"
                    />
                    <span v-else style="margin-right: 2px">-</span>
                    {{
                      item.cpu_total_change === 0
                        ? "-"
                        : formatNumberWithCommas(
                            Math.abs(item.cpu_total_change)
                          ) + " 核"
                    }}
                  </span>
                  <el-button
                    type="primary"
                    size="small"
                    link
                    style="padding: 4px 8px; margin-left: 8px"
                    @click="
                      showMetric(
                        'region',
                        'gpu_total',
                        '区域资源统计' + item.name,
                        '卡',
                        item.name
                      )
                    "
                  >
                    <el-icon><TrendCharts /></el-icon>
                    <span style="margin-left: 4px">趋势图</span>
                  </el-button>
                </div>
              </div>
              <div class="region-stat-item">
                <div class="stat-label">
                  <iconify-icon-online
                    icon="material-symbols:memory"
                    style="font-size: 20px"
                  />内存
                </div>
                <div class="stat-value">
                  {{ formatStorage(item.memory_mb_total * 1024 * 1024 || 0) }}
                  <span
                    v-if="item.memory_mb_total_change !== undefined"
                    class="change-indicator"
                    :class="{
                      increase: item.memory_mb_total_change > 0,
                      decrease: item.memory_mb_total_change < 0,
                      'no-change': item.memory_mb_total_change === 0
                    }"
                    @click="
                      showMetric(
                        'region',
                        'memory_mb_total',
                        '区域资源统计' + item.name,
                        'byte',
                        item.name
                      )
                    "
                  >
                    <iconify-icon-online
                      v-if="item.memory_mb_total_change !== 0"
                      :icon="
                        item.memory_mb_total_change > 0 ? 'ep:top' : 'ep:bottom'
                      "
                      style="margin-right: 2px"
                    />
                    <span v-else style="margin-right: 2px">-</span>
                    {{
                      item.memory_mb_total_change === 0
                        ? "-"
                        : formatStorage(
                            Math.abs(item.memory_mb_total_change) * 1024 * 1024
                          )
                    }}
                  </span>
                  <el-button
                    type="primary"
                    size="small"
                    link
                    style="padding: 4px 8px; margin-left: 8px"
                    @click="
                      showMetric(
                        'region',
                        'gpu_total',
                        '区域资源统计' + item.name,
                        '卡',
                        item.name
                      )
                    "
                  >
                    <el-icon><TrendCharts /></el-icon>
                    <span style="margin-left: 4px">趋势图</span>
                  </el-button>
                </div>
              </div>
              <div class="region-stat-item">
                <div class="stat-label">
                  <iconify-icon-online
                    icon="bi:gpu-card"
                    style="font-size: 20px"
                  />GPU卡数
                </div>
                <div class="stat-value">
                  {{ formatNumberWithCommas(item.gpu_total || 0) }} 卡
                  <span
                    v-if="item.gpu_total_change !== undefined"
                    class="change-indicator"
                    :class="{
                      increase: item.gpu_total_change > 0,
                      decrease: item.gpu_total_change < 0,
                      'no-change': item.gpu_total_change === 0
                    }"
                    @click="
                      showMetric(
                        'region',
                        'gpu_total',
                        '区域资源统计' + item.name,
                        '卡',
                        item.name
                      )
                    "
                  >
                    <iconify-icon-online
                      v-if="item.gpu_total_change !== 0"
                      :icon="item.gpu_total_change > 0 ? 'ep:top' : 'ep:bottom'"
                      style="margin-right: 2px"
                    />
                    <span v-else style="margin-right: 2px">-</span>
                    {{
                      item.gpu_total_change === 0
                        ? "-"
                        : formatNumberWithCommas(
                            Math.abs(item.gpu_total_change)
                          ) + " 卡"
                    }}
                  </span>
                  <el-button
                    type="primary"
                    size="small"
                    link
                    style="padding: 4px 8px; margin-left: 8px"
                    @click="
                      showMetric(
                        'region',
                        'gpu_total',
                        '区域资源统计' + item.name,
                        '卡',
                        item.name
                      )
                    "
                  >
                    <el-icon><TrendCharts /></el-icon>
                    <span style="margin-left: 4px">趋势图</span>
                  </el-button>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <el-divider content-position="left">云服务商资源分布</el-divider>
      <el-row :gutter="24">
        <el-col v-for="(item, index) in cloudStats" :key="index" :span="12">
          <el-card shadow="hover" class="cloud-card">
            <div class="cloud-title">{{ item.name }}</div>
            <div class="cloud-stats">
              <div class="cloud-stat-item">
                <div class="stat-label">
                  <iconify-icon-online
                    icon="grommet-icons:host"
                    style="font-size: 20px"
                  />主机数
                </div>
                <div class="stat-value">
                  {{ formatNumberWithCommas(item.host_total || 0) }} 台
                  <span
                    v-if="item.host_total_change !== undefined"
                    class="change-indicator"
                    :class="{
                      increase: item.host_total_change > 0,
                      decrease: item.host_total_change < 0,
                      'no-change': item.host_total_change === 0
                    }"
                    @click="
                      showMetric(
                        'cloud',
                        'host_total',
                        '云服务商资源统计' + item.name,
                        '台',
                        item.name
                      )
                    "
                  >
                    <iconify-icon-online
                      v-if="item.host_total_change !== 0"
                      :icon="
                        item.host_total_change > 0 ? 'ep:top' : 'ep:bottom'
                      "
                      style="margin-right: 2px"
                    />
                    <span v-else style="margin-right: 2px">-</span>
                    {{
                      item.host_total_change === 0
                        ? "-"
                        : formatNumberWithCommas(
                            Math.abs(item.host_total_change)
                          ) + " 台"
                    }}
                  </span>
                  <el-button
                    type="primary"
                    size="small"
                    link
                    style="padding: 4px 8px; margin-left: 8px"
                    @click="
                      showMetric(
                        'region',
                        'gpu_total',
                        '区域资源统计' + item.name,
                        '卡',
                        item.name
                      )
                    "
                  >
                    <el-icon><TrendCharts /></el-icon>
                    <span style="margin-left: 4px">趋势图</span>
                  </el-button>
                </div>
              </div>
              <div class="cloud-stat-item">
                <div class="stat-label">
                  <iconify-icon-online
                    icon="ep:cpu"
                    style="font-size: 20px"
                  />CPU核数
                </div>
                <div class="stat-value">
                  {{ formatNumberWithCommas(item.cpu_total || 0) }} 核
                  <span
                    v-if="item.cpu_total_change !== undefined"
                    class="change-indicator"
                    :class="{
                      increase: item.cpu_total_change > 0,
                      decrease: item.cpu_total_change < 0,
                      'no-change': item.cpu_total_change === 0
                    }"
                    @click="
                      showMetric(
                        'cloud',
                        'cpu_total',
                        '云服务商资源统计' + item.name,
                        '核',
                        item.name
                      )
                    "
                  >
                    <iconify-icon-online
                      v-if="item.cpu_total_change !== 0"
                      :icon="item.cpu_total_change > 0 ? 'ep:top' : 'ep:bottom'"
                      style="margin-right: 2px"
                    />
                    <span v-else style="margin-right: 2px">-</span>
                    {{
                      item.cpu_total_change === 0
                        ? "-"
                        : formatNumberWithCommas(
                            Math.abs(item.cpu_total_change)
                          ) + " 核"
                    }}
                  </span>
                  <el-button
                    type="primary"
                    size="small"
                    link
                    style="padding: 4px 8px; margin-left: 8px"
                    @click="
                      showMetric(
                        'region',
                        'gpu_total',
                        '区域资源统计' + item.name,
                        '卡',
                        item.name
                      )
                    "
                  >
                    <el-icon><TrendCharts /></el-icon>
                    <span style="margin-left: 4px">趋势图</span>
                  </el-button>
                </div>
              </div>
              <div class="cloud-stat-item">
                <div class="stat-label">
                  <iconify-icon-online
                    icon="material-symbols:memory"
                    style="font-size: 20px"
                  />内存
                </div>
                <div class="stat-value">
                  {{ formatStorage(item.memory_mb_total * 1024 * 1024 || 0) }}
                  <span
                    v-if="item.memory_mb_total_change !== undefined"
                    class="change-indicator"
                    :class="{
                      increase: item.memory_mb_total_change > 0,
                      decrease: item.memory_mb_total_change < 0,
                      'no-change': item.memory_mb_total_change === 0
                    }"
                    @click="
                      showMetric(
                        'cloud',
                        'memory_mb_total',
                        '云服务商资源统计' + item.name,
                        'byte',
                        item.name
                      )
                    "
                  >
                    <iconify-icon-online
                      v-if="item.memory_mb_total_change !== 0"
                      :icon="
                        item.memory_mb_total_change > 0 ? 'ep:top' : 'ep:bottom'
                      "
                      style="margin-right: 2px"
                    />
                    <span v-else style="margin-right: 2px">-</span>
                    {{
                      item.memory_mb_total_change === 0
                        ? "-"
                        : formatStorage(
                            Math.abs(item.memory_mb_total_change) * 1024 * 1024
                          )
                    }}
                  </span>
                  <el-button
                    type="primary"
                    size="small"
                    link
                    style="padding: 4px 8px; margin-left: 8px"
                    @click="
                      showMetric(
                        'region',
                        'gpu_total',
                        '区域资源统计' + item.name,
                        '卡',
                        item.name
                      )
                    "
                  >
                    <el-icon><TrendCharts /></el-icon>
                    <span style="margin-left: 4px">趋势图</span>
                  </el-button>
                </div>
              </div>
              <div class="cloud-stat-item">
                <div class="stat-label">
                  <iconify-icon-online
                    icon="bi:gpu-card"
                    style="font-size: 20px"
                  />GPU卡数
                </div>
                <div class="stat-value">
                  {{ formatNumberWithCommas(item.gpu_total || 0) }} 卡
                  <span
                    v-if="item.gpu_total_change !== undefined"
                    class="change-indicator"
                    :class="{
                      increase: item.gpu_total_change > 0,
                      decrease: item.gpu_total_change < 0,
                      'no-change': item.gpu_total_change === 0
                    }"
                    @click="
                      showMetric(
                        'cloud',
                        'gpu_total',
                        '云服务商资源统计' + item.name,
                        '卡',
                        item.name
                      )
                    "
                  >
                    <iconify-icon-online
                      v-if="item.gpu_total_change !== 0"
                      :icon="item.gpu_total_change > 0 ? 'ep:top' : 'ep:bottom'"
                      style="margin-right: 2px"
                    />
                    <span v-else style="margin-right: 2px">-</span>
                    {{
                      item.gpu_total_change === 0
                        ? "-"
                        : formatNumberWithCommas(
                            Math.abs(item.gpu_total_change)
                          ) + " 卡"
                    }}
                  </span>
                  <el-button
                    type="primary"
                    size="small"
                    link
                    style="padding: 4px 8px; margin-left: 8px"
                    @click="
                      showMetric(
                        'region',
                        'gpu_total',
                        '区域资源统计' + item.name,
                        '卡',
                        item.name
                      )
                    "
                  >
                    <el-icon><TrendCharts /></el-icon>
                    <span style="margin-left: 4px">趋势图</span>
                  </el-button>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </el-card>

    <!-- 可优化资源统计 -->
    <el-card v-loading="loading" class="stat-card">
      <template #header>
        <div class="card-header">
          <h3>
            <iconify-icon-online
              icon="carbon:optimize-server"
              style="font-size: 22px"
            />可优化资源统计
          </h3>
        </div>
      </template>
      <el-row :gutter="24">
        <el-col :span="8">
          <div class="stat-item optimize-item">
            <div class="stat-icon-wrapper">
              <iconify-icon-online
                icon="lineicons:amd"
                style="font-size: 36px"
              />
            </div>
            <div class="stat-title">AMD占比</div>
            <div class="progress-container">
              <el-progress
                type="dashboard"
                :percentage="
                  Number((optimizableData.amd_percent || 0).toFixed(2))
                "
                :color="customColors"
                :stroke-width="10"
              />
            </div>
            <div class="stat-value">
              {{ Number((optimizableData.amd_percent || 0).toFixed(2)) }}%
              <span
                v-if="optimizableData.amd_percent_change !== undefined"
                class="change-indicator"
                :class="{
                  increase: optimizableData.amd_percent_change > 0,
                  decrease: optimizableData.amd_percent_change < 0,
                  'no-change': optimizableData.amd_percent_change === 0
                }"
              >
                <iconify-icon-online
                  v-if="optimizableData.amd_percent_change !== 0"
                  :icon="
                    optimizableData.amd_percent_change > 0
                      ? 'ep:top'
                      : 'ep:bottom'
                  "
                  style="margin-right: 2px"
                />
                <span v-else style="margin-right: 2px">-</span>
                {{
                  optimizableData.amd_percent_change === 0
                    ? "-"
                    : Math.abs(
                        Number(optimizableData.amd_percent_change.toFixed(2))
                      ) + "%"
                }}
              </span>
              <el-button
                type="primary"
                size="small"
                link
                style="padding: 4px 8px; margin-left: 8px"
                @click="
                  showMetric(
                    'optimizable',
                    'amd_percent',
                    '可优化资源统计',
                    '%',
                    'AMD占比'
                  )
                "
              >
                <el-icon><TrendCharts /></el-icon>
                <span style="margin-left: 4px">趋势图</span>
              </el-button>
            </div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="stat-item optimize-item">
            <div class="stat-icon-wrapper">
              <iconify-icon-online icon="ep:cpu" style="font-size: 36px" />
            </div>
            <div class="stat-title">CPU使用率</div>
            <div class="progress-container">
              <el-progress
                type="dashboard"
                :percentage="
                  Number((optimizableData.cpu_usage || 0).toFixed(2))
                "
                :color="customColors"
                :stroke-width="10"
              />
            </div>
            <div class="stat-value">
              {{ Number((optimizableData.cpu_usage || 0).toFixed(2)) }}%
              <span
                v-if="optimizableData.cpu_usage_change !== undefined"
                class="change-indicator"
                :class="{
                  increase: optimizableData.cpu_usage_change > 0,
                  decrease: optimizableData.cpu_usage_change < 0,
                  'no-change': optimizableData.cpu_usage_change === 0
                }"
              >
                <iconify-icon-online
                  v-if="optimizableData.cpu_usage_change !== 0"
                  :icon="
                    optimizableData.cpu_usage_change > 0
                      ? 'ep:top'
                      : 'ep:bottom'
                  "
                  style="margin-right: 2px"
                />
                <span v-else style="margin-right: 2px">-</span>
                {{
                  optimizableData.cpu_usage_change === 0
                    ? "-"
                    : Math.abs(
                        Number(optimizableData.cpu_usage_change.toFixed(2))
                      ) + "%"
                }}
              </span>
              <el-button
                type="primary"
                size="small"
                link
                style="padding: 4px 8px; margin-left: 8px"
                @click="
                  showMetric(
                    'optimizable',
                    'cpu_usage',
                    '可优化资源统计',
                    '%',
                    'CPU使用率'
                  )
                "
              >
                <el-icon><TrendCharts /></el-icon>
                <span style="margin-left: 4px">趋势图</span>
              </el-button>
            </div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="stat-item optimize-item">
            <div class="stat-icon-wrapper">
              <iconify-icon-online
                icon="material-symbols:memory"
                style="font-size: 36px"
              />
            </div>
            <div class="stat-title">内存使用率</div>
            <div class="progress-container">
              <el-progress
                type="dashboard"
                :percentage="
                  Number((optimizableData.memory_usage || 0).toFixed(2))
                "
                :color="customColors"
                :stroke-width="10"
              />
            </div>
            <div class="stat-value">
              {{ Number((optimizableData.memory_usage || 0).toFixed(2)) }}%
              <span
                v-if="optimizableData.memory_usage_change !== undefined"
                class="change-indicator"
                :class="{
                  increase: optimizableData.memory_usage_change > 0,
                  decrease: optimizableData.memory_usage_change < 0,
                  'no-change': optimizableData.memory_usage_change === 0
                }"
              >
                <iconify-icon-online
                  v-if="optimizableData.memory_usage_change !== 0"
                  :icon="
                    optimizableData.memory_usage_change > 0
                      ? 'ep:top'
                      : 'ep:bottom'
                  "
                  style="margin-right: 2px"
                />
                <span v-else style="margin-right: 2px">-</span>
                {{
                  optimizableData.memory_usage_change === 0
                    ? "-"
                    : Math.abs(
                        Number(optimizableData.memory_usage_change.toFixed(2))
                      ) + "%"
                }}
              </span>

              <el-button
                type="primary"
                size="small"
                link
                style="padding: 4px 8px; margin-left: 8px"
                @click="
                  showMetric(
                    'optimizable',
                    'memory_usage',
                    '可优化资源统计',
                    '%',
                    '内存使用率'
                  )
                "
              >
                <el-icon><TrendCharts /></el-icon>
                <span style="margin-left: 4px">趋势图</span>
              </el-button>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 数据资源统计 -->
    <el-card v-loading="loading" class="stat-card">
      <template #header>
        <div class="card-header">
          <h3>
            <iconify-icon-online
              icon="carbon:data-base"
              style="font-size: 22px"
            />数据资源统计
          </h3>
        </div>
      </template>
      <el-row :gutter="24" :style="{ marginBottom: '24px' }">
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-icon-wrapper">
              <iconify-icon-online
                icon="lineicons:mysql"
                style="font-size: 36px"
              />
            </div>
            <div class="stat-title">MySQL存储</div>
            <div class="stat-value">
              {{ formatStorage(dataAssetsData.mysql_storage_total || 0) }}
              <span
                v-if="dataAssetsData.mysql_storage_total_change !== undefined"
                class="change-indicator"
                :class="{
                  increase: dataAssetsData.mysql_storage_total_change > 0,
                  decrease: dataAssetsData.mysql_storage_total_change < 0,
                  'no-change': dataAssetsData.mysql_storage_total_change === 0
                }"
              >
                <iconify-icon-online
                  v-if="dataAssetsData.mysql_storage_total_change !== 0"
                  :icon="
                    dataAssetsData.mysql_storage_total_change > 0
                      ? 'ep:top'
                      : 'ep:bottom'
                  "
                  style="margin-right: 2px"
                />
                <span v-else style="margin-right: 2px">-</span>
                {{
                  dataAssetsData.mysql_storage_total_change === 0
                    ? ""
                    : formatStorage(
                        Math.abs(dataAssetsData.mysql_storage_total_change)
                      )
                }}
              </span>
              <el-button
                type="primary"
                size="small"
                link
                style="padding: 4px 8px; margin-left: 8px"
                @click="
                  showMetric(
                    'data',
                    'mysql_storage_total',
                    '数据资源统计',
                    'byte',
                    'MySQL存储'
                  )
                "
              >
                <el-icon><TrendCharts /></el-icon>
                <span style="margin-left: 4px">趋势图</span>
              </el-button>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-icon-wrapper">
              <iconify-icon-online
                icon="fluent:document-database-20-regular"
                style="font-size: 36px"
              />
            </div>
            <div class="stat-title">TiDB存储</div>
            <div class="stat-value">
              {{ formatStorage(dataAssetsData.tidb_storage_total || 0) }}
              <span
                v-if="dataAssetsData.tidb_storage_total_change !== undefined"
                class="change-indicator"
                :class="{
                  increase: dataAssetsData.tidb_storage_total_change > 0,
                  decrease: dataAssetsData.tidb_storage_total_change < 0,
                  'no-change': dataAssetsData.tidb_storage_total_change === 0
                }"
              >
                <iconify-icon-online
                  v-if="dataAssetsData.tidb_storage_total_change !== 0"
                  :icon="
                    dataAssetsData.tidb_storage_total_change > 0
                      ? 'ep:top'
                      : 'ep:bottom'
                  "
                  style="margin-right: 2px"
                />
                <span v-else style="margin-right: 2px">-</span>
                {{
                  dataAssetsData.tidb_storage_total_change === 0
                    ? ""
                    : formatStorage(
                        Math.abs(dataAssetsData.tidb_storage_total_change)
                      )
                }}
              </span>
              <el-button
                type="primary"
                size="small"
                link
                style="padding: 4px 8px; margin-left: 8px"
                @click="
                  showMetric(
                    'data',
                    'tidb_storage_total',
                    '数据资源统计',
                    'byte',
                    'TiDB存储'
                  )
                "
              >
                <el-icon><TrendCharts /></el-icon>
                <span style="margin-left: 4px">趋势图</span>
              </el-button>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-icon-wrapper">
              <iconify-icon-online
                icon="lineicons:mongodb"
                style="font-size: 36px"
              />
            </div>
            <div class="stat-title">MongoDB存储</div>
            <div class="stat-value">
              {{ formatStorage(dataAssetsData.mongodb_storage_total || 0) }}
              <span
                v-if="dataAssetsData.mongodb_storage_total_change !== undefined"
                class="change-indicator"
                :class="{
                  increase: dataAssetsData.mongodb_storage_total_change > 0,
                  decrease: dataAssetsData.mongodb_storage_total_change < 0,
                  'no-change': dataAssetsData.mongodb_storage_total_change === 0
                }"
              >
                <iconify-icon-online
                  v-if="dataAssetsData.mongodb_storage_total_change !== 0"
                  :icon="
                    dataAssetsData.mongodb_storage_total_change > 0
                      ? 'ep:top'
                      : 'ep:bottom'
                  "
                  style="margin-right: 2px"
                />
                <span v-else style="margin-right: 2px">-</span>
                {{
                  dataAssetsData.mongodb_storage_total_change === 0
                    ? ""
                    : formatStorage(
                        Math.abs(dataAssetsData.mongodb_storage_total_change)
                      )
                }}
              </span>
              <el-button
                type="primary"
                size="small"
                link
                style="padding: 4px 8px; margin-left: 8px"
                @click="
                  showMetric(
                    'data',
                    'mongodb_storage_total',
                    '数据资源统计',
                    'byte',
                    'MongoDB存储'
                  )
                "
              >
                <el-icon><TrendCharts /></el-icon>
                <span style="margin-left: 4px">趋势图</span>
              </el-button>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-icon-wrapper">
              <iconify-icon-online
                icon="devicon-plain:clickhouse"
                style="font-size: 36px"
              />
            </div>
            <div class="stat-title">ClickHouse存储</div>
            <div class="stat-value">
              {{ formatStorage(dataAssetsData.clickhouse_storage_total || 0) }}
              <span
                v-if="
                  dataAssetsData.clickhouse_storage_total_change !== undefined
                "
                class="change-indicator"
                :class="{
                  increase: dataAssetsData.clickhouse_storage_total_change > 0,
                  decrease: dataAssetsData.clickhouse_storage_total_change < 0,
                  'no-change':
                    dataAssetsData.clickhouse_storage_total_change === 0
                }"
              >
                <iconify-icon-online
                  v-if="dataAssetsData.clickhouse_storage_total_change !== 0"
                  :icon="
                    dataAssetsData.clickhouse_storage_total_change > 0
                      ? 'ep:top'
                      : 'ep:bottom'
                  "
                  style="margin-right: 2px"
                />
                <span v-else style="margin-right: 2px">-</span>
                {{
                  dataAssetsData.clickhouse_storage_total_change === 0
                    ? ""
                    : formatStorage(
                        Math.abs(dataAssetsData.clickhouse_storage_total_change)
                      )
                }}
              </span>
              <el-button
                type="primary"
                size="small"
                link
                style="padding: 4px 8px; margin-left: 8px"
                @click="
                  showMetric(
                    'data',
                    'clickhouse_storage_total',
                    '数据资源统计',
                    'byte',
                    'ClickHouse存储'
                  )
                "
              >
                <el-icon><TrendCharts /></el-icon>
                <span style="margin-left: 4px">趋势图</span>
              </el-button>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-icon-wrapper">
              <iconify-icon-online
                icon="iconoir:database-star"
                style="font-size: 36px"
              />
            </div>
            <div class="stat-title">Starrocks存储</div>
            <div class="stat-value">
              {{ formatStorage(dataAssetsData.starrocks_storage_total || 0) }}
              <span
                v-if="
                  dataAssetsData.starrocks_storage_total_change !== undefined
                "
                class="change-indicator"
                :class="{
                  increase: dataAssetsData.starrocks_storage_total_change > 0,
                  decrease: dataAssetsData.starrocks_storage_total_change < 0,
                  'no-change':
                    dataAssetsData.starrocks_storage_total_change === 0
                }"
              >
                <iconify-icon-online
                  v-if="dataAssetsData.starrocks_storage_total_change !== 0"
                  :icon="
                    dataAssetsData.starrocks_storage_total_change > 0
                      ? 'ep:top'
                      : 'ep:bottom'
                  "
                  style="margin-right: 2px"
                />
                <span v-else style="margin-right: 2px">-</span>
                {{
                  dataAssetsData.starrocks_storage_total_change === 0
                    ? ""
                    : formatStorage(
                        Math.abs(dataAssetsData.starrocks_storage_total_change)
                      )
                }}
              </span>
              <el-button
                type="primary"
                size="small"
                link
                style="padding: 4px 8px; margin-left: 8px"
                @click="
                  showMetric(
                    'data',
                    'starrocks_storage_total',
                    '数据资源统计',
                    'byte',
                    'Starrocks存储'
                  )
                "
              >
                <el-icon><TrendCharts /></el-icon>
                <span style="margin-left: 4px">趋势图</span>
              </el-button>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-icon-wrapper">
              <iconify-icon-online
                icon="foundation:database"
                style="font-size: 36px"
              />
            </div>
            <div class="stat-title">DLI 数据仓库</div>
            <div class="stat-value">
              {{ formatStorage(dataAssetsData.dli_storage_total || 0) }}
              <span
                v-if="dataAssetsData.dli_storage_total_change !== undefined"
                class="change-indicator"
                :class="{
                  increase: dataAssetsData.dli_storage_total_change > 0,
                  decrease: dataAssetsData.dli_storage_total_change < 0,
                  'no-change': dataAssetsData.dli_storage_total_change === 0
                }"
              >
                <iconify-icon-online
                  v-if="dataAssetsData.dli_storage_total_change !== 0"
                  :icon="
                    dataAssetsData.dli_storage_total_change > 0
                      ? 'ep:top'
                      : 'ep:bottom'
                  "
                  style="margin-right: 2px"
                />
                <span v-else style="margin-right: 2px">-</span>
                {{
                  dataAssetsData.dli_storage_total_change === 0
                    ? ""
                    : formatStorage(
                        Math.abs(dataAssetsData.dli_storage_total_change)
                      )
                }}
              </span>
              <el-button
                type="primary"
                size="small"
                link
                style="padding: 4px 8px; margin-left: 8px"
                @click="
                  showMetric(
                    'data',
                    'dli_storage_total',
                    '数据资源统计',
                    'byte',
                    'DLI存储'
                  )
                "
              >
                <el-icon><TrendCharts /></el-icon>
                <span style="margin-left: 4px">趋势图</span>
              </el-button>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-icon-wrapper">
              <iconify-icon-online
                icon="tdesign:object-storage"
                style="font-size: 36px"
              />
            </div>
            <div class="stat-title">OSS存储</div>
            <div class="stat-value">
              {{ formatStorage(dataAssetsData.oss_storage_total || 0) }}
              <span
                v-if="dataAssetsData.oss_storage_total_change !== undefined"
                class="change-indicator"
                :class="{
                  increase: dataAssetsData.oss_storage_total_change > 0,
                  decrease: dataAssetsData.oss_storage_total_change < 0,
                  'no-change': dataAssetsData.oss_storage_total_change === 0
                }"
              >
                <iconify-icon-online
                  v-if="dataAssetsData.oss_storage_total_change !== 0"
                  :icon="
                    dataAssetsData.oss_storage_total_change > 0
                      ? 'ep:top'
                      : 'ep:bottom'
                  "
                  style="margin-right: 2px"
                />
                <span v-else style="margin-right: 2px">-</span>
                {{
                  dataAssetsData.oss_storage_total_change === 0
                    ? ""
                    : formatStorage(
                        Math.abs(dataAssetsData.oss_storage_total_change)
                      )
                }}
              </span>
              <el-button
                type="primary"
                size="small"
                link
                style="padding: 4px 8px; margin-left: 8px"
                @click="
                  showMetric(
                    'data',
                    'oss_storage_total',
                    '数据资源统计',
                    'byte',
                    'OSS存储'
                  )
                "
              >
                <el-icon><TrendCharts /></el-icon>
                <span style="margin-left: 4px">趋势图</span>
              </el-button>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-icon-wrapper">
              <iconify-icon-online icon="mdi:nas" style="font-size: 36px" />
            </div>
            <div class="stat-title">NAS存储</div>
            <div class="stat-value">
              {{ formatStorage(dataAssetsData.nas_storage_total || 0) }}
              <span
                v-if="dataAssetsData.nas_storage_total_change !== undefined"
                class="change-indicator"
                :class="{
                  increase: dataAssetsData.nas_storage_total_change > 0,
                  decrease: dataAssetsData.nas_storage_total_change < 0,
                  'no-change': dataAssetsData.nas_storage_total_change === 0
                }"
              >
                <iconify-icon-online
                  v-if="dataAssetsData.nas_storage_total_change !== 0"
                  :icon="
                    dataAssetsData.nas_storage_total_change > 0
                      ? 'ep:top'
                      : 'ep:bottom'
                  "
                  style="margin-right: 2px"
                />
                <span v-else style="margin-right: 2px">-</span>
                {{
                  dataAssetsData.nas_storage_total_change === 0
                    ? ""
                    : formatStorage(
                        Math.abs(dataAssetsData.nas_storage_total_change)
                      )
                }}
              </span>
              <el-button
                type="primary"
                size="small"
                link
                style="padding: 4px 8px; margin-left: 8px"
                @click="
                  showMetric(
                    'data',
                    'nas_storage_total',
                    '数据资源统计',
                    'byte',
                    'NAS存储'
                  )
                "
              >
                <el-icon><TrendCharts /></el-icon>
                <span style="margin-left: 4px">趋势图</span>
              </el-button>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 域名带宽统计 -->
    <el-card v-loading="loading" class="stat-card">
      <template #header>
        <div class="card-header">
          <h3>
            <iconify-icon-online
              icon="carbon:network-4"
              style="font-size: 22px"
            />域名带宽统计
          </h3>
          <div class="chart-actions">
            <el-tooltip content="刷新数据" placement="top">
              <el-button type="primary" link @click="fetchData">
                <iconify-icon-online
                  icon="mdi:refresh"
                  style="font-size: 18px"
                />
              </el-button>
            </el-tooltip>
          </div>
        </div>
      </template>
      <div class="domain-bandwidth-total">
        <div class="total-item">
          <span class="total-label">入站带宽</span>
          <span class="total-value">{{ formatBandwidth(totalAllInBps) }}</span>
        </div>
        <div class="total-item">
          <span class="total-label">出站带宽</span>
          <span class="total-value">{{ formatBandwidth(totalAllOutBps) }}</span>
        </div>
        <div class="total-item">
          <span class="total-label">总带宽</span>
          <span class="total-value highlight">{{
            formatBandwidth(totalAllBps)
          }}</span>
        </div>
      </div>
      <div class="table-container">
        <el-table
          :data="domainBpsTableData"
          style="width: 100%"
          border
          stripe
          :header-cell-style="{
            backgroundColor: '#f5f7fa',
            color: '#606266',
            fontWeight: 'bold'
          }"
        >
          <el-table-column prop="domain" label="域名" min-width="180" />
          <el-table-column label="入站带宽" min-width="180">
            <template #default="scope">
              <div class="bandwidth-cell">
                <span>{{ formatBandwidth(scope.row.in_bps) }}</span>
                <el-progress
                  :percentage="scope.row.in_percent"
                  :color="getBandwidthColor(scope.row.in_percent)"
                  :show-text="false"
                  :stroke-width="8"
                  class="bandwidth-progress"
                />
                <span class="percent-text"
                  >{{ scope.row.in_percent.toFixed(2) }}%</span
                >
              </div>
            </template>
          </el-table-column>
          <el-table-column label="出站带宽" min-width="180">
            <template #default="scope">
              <div class="bandwidth-cell">
                <span>{{ formatBandwidth(scope.row.out_bps) }}</span>
                <el-progress
                  :percentage="scope.row.out_percent"
                  :color="getBandwidthColor(scope.row.out_percent)"
                  :show-text="false"
                  :stroke-width="8"
                  class="bandwidth-progress"
                />
                <span class="percent-text"
                  >{{ scope.row.out_percent.toFixed(2) }}%</span
                >
              </div>
            </template>
          </el-table-column>
          <el-table-column label="总带宽" min-width="180">
            <template #default="scope">
              <div class="bandwidth-cell">
                <span>{{ formatBandwidth(scope.row.total_bps) }}</span>
                <el-progress
                  :percentage="scope.row.total_percent"
                  :color="getBandwidthColor(scope.row.total_percent)"
                  :show-text="false"
                  :stroke-width="8"
                  class="bandwidth-progress"
                />
                <span class="percent-text"
                  >{{ scope.row.total_percent.toFixed(2) }}%</span
                >
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, nextTick, h } from "vue";
import {
  type ComputerAssets,
  type DataAssets,
  getMonthAPI,
  type GetMonthParmas
} from "@/api/statistics/asset";
import { ElMessage } from "element-plus";
import { formatStorage, formatBandwidth } from "@/utils/format";
import { getLastMonth } from "@/utils/date";
import { formatNumberWithCommas } from "@/utils/locale";

import { Refresh, TrendCharts } from "@element-plus/icons-vue";
import Metric from "./metric.vue";
import { addDialog } from "@/components/ReDialog";

// 数据加载状态
const loading = ref(false);

// 选择的月份默认为上个月份
const selectedMonth = ref(getLastMonth());

// 统计数据
const computeResourceData = ref<ComputerAssets>({});
const dataAssetsData = ref<DataAssets>({});
const domainBpsData = ref<any[]>([]);
const domainBpsTableData = ref<any[]>([]);
const optimizableData = ref<any>({});
const cloudStats = ref<any[]>([]);
const regionStats = ref<any[]>([]);

// 进度条颜色
const customColors = [
  { color: "#f56c6c", percentage: 20 },
  { color: "#e6a23c", percentage: 40 },
  { color: "#5cb87a", percentage: 60 },
  { color: "#1989fa", percentage: 80 },
  { color: "#6f7ad3", percentage: 100 }
];

// 计算资源统计项
const computeResourceItems = computed(() => {
  return [
    {
      title: "主机总数",
      value:
        formatNumberWithCommas(computeResourceData.value?.host_total || 0) +
        " 台",
      icon: "grommet-icons:host",
      change: computeResourceData.value?.host_total_change,
      unit: "台",
      metricType: "host_total",
      assetType: "computer"
    },
    {
      title: "CPU总数",
      value:
        formatNumberWithCommas(computeResourceData.value?.cpu_total || 0) +
        " 核",
      icon: "ep:cpu",
      change: computeResourceData.value?.cpu_total_change,
      unit: "核",
      metricType: "cpu_total",
      assetType: "computer"
    },
    {
      title: "内存总量",
      value: formatStorage(
        computeResourceData.value?.memory_mb_total * 1024 * 1024 || 0
      ),
      icon: "material-symbols:memory",
      change: computeResourceData.value?.memory_mb_total_change,
      isMemory: true,
      unit: "byte",
      metricType: "memory_mb_total",
      assetType: "computer"
    },
    {
      title: "GPU总数",
      value:
        formatNumberWithCommas(computeResourceData.value?.gpu_total || 0) +
        " 卡",
      icon: "bi:gpu-card",
      change: computeResourceData.value?.gpu_total_change,
      unit: "卡",
      metricType: "gpu_total",
      assetType: "computer"
    },
    {
      title: "DLI CU",
      value:
        formatNumberWithCommas(computeResourceData.value?.dli_cpu_total || 0) +
        " CU",
      icon: "token:data",
      change: computeResourceData.value?.dli_cpu_total_change,
      unit: "CU",
      metricType: "dli_cpu_total",
      assetType: "computer"
    }
  ];
});

const disabledDate = (time: Date) => {
  const date = new Date();
  date.setDate(1); // 设为当前月的第一天
  date.setMonth(date.getMonth() - 1); // 减去一个月
  return time.getTime() > date.getTime();
};

// 月份选择器变化处理
const handleMonthChange = () => {
  if (!selectedMonth.value) {
    ElMessage.warning("请选择月份");
    return;
  }

  try {
    fetchData();
  } catch (error) {
    console.error("获取数据失败:", error);
    ElMessage.error("获取数据失败，请稍后重试");
  }
};

// 获取带宽进度条颜色
const getBandwidthColor = (percent: number) => {
  if (percent < 20) return "#67C23A";
  if (percent < 50) return "#409EFF";
  if (percent < 80) return "#E6A23C";
  return "#F56C6C";
};

// 获取数据
const fetchData = async () => {
  if (!selectedMonth.value) {
    ElMessage.warning("请选择月份");
    return;
  }

  loading.value = true;
  try {
    const params: GetMonthParmas = {
      month: selectedMonth.value
    };

    const res = await getMonthAPI(params);

    if (res.success && res.data) {
      const data = res.data;

      // 更新数据
      computeResourceData.value = data.computer_assets || {};
      dataAssetsData.value = data.data_assets || {};
      domainBpsData.value = data.domain_bps || [];
      optimizableData.value = data.optimizable_asset || {};
      cloudStats.value =
        data.cloud_stats || computeResourceData.value?.cloud_stats || [];
      regionStats.value =
        data.region_stats || computeResourceData.value?.region_stats || [];

      // 处理域名带宽数据，计算百分比
      processDomainBpsData();

      // 渲染图表
      nextTick(() => {
        try {
          renderDataAssetsChart();
        } catch (error) {
          console.error("渲染图表失败:", error);
        }
      });
    } else {
      ElMessage.error(res.msg || "获取数据失败");
    }
  } catch (error) {
    console.error("获取数据失败", error);
    ElMessage.error("获取数据失败");
  } finally {
    loading.value = false;
  }
};
const totalAllBps = ref(0);
const totalAllInBps = ref(0);
const totalAllOutBps = ref(0);
// 处理域名带宽数据，计算百分比
const processDomainBpsData = () => {
  if (!domainBpsData.value || domainBpsData.value.length === 0) return;

  // 计算总带宽
  let totalInBps = 0;
  let totalOutBps = 0;

  domainBpsData.value.forEach(item => {
    totalInBps += item.in_bps || 0;
    totalOutBps += item.out_bps || 0;
  });
  // 计算每个域名的百分比
  domainBpsTableData.value = domainBpsData.value.map(item => {
    const in_bps = item.in_bps || 0;
    const out_bps = item.out_bps || 0;
    const total_bps = in_bps + out_bps;
    totalAllBps.value = totalAllBps.value + total_bps;
    totalAllInBps.value = totalAllInBps.value + in_bps;
    totalAllOutBps.value = totalAllOutBps.value + out_bps;
    return {
      domain: item.domain,
      in_bps,
      out_bps,
      total_bps,
      in_percent: totalInBps ? (in_bps / totalInBps) * 100 : 0,
      out_percent: totalOutBps ? (out_bps / totalOutBps) * 100 : 0,
      total_percent:
        totalInBps + totalOutBps
          ? (total_bps / (totalInBps + totalOutBps)) * 100
          : 0
    };
  });

  // 按总带宽排序
  domainBpsTableData.value.sort((a, b) => b.total_bps - a.total_bps);
};

const showMetric = (assetType, metricType, title, unit, name) => {
  addDialog({
    title: `${title} 统计趋势分析`,
    width: "80%",
    hideFooter: true,
    contentRenderer: () =>
      h(Metric, {
        assetType,
        metricType,
        title,
        unit,
        name
      })
  });
};
// 渲染数据资源统计图表
const renderDataAssetsChart = () => {
  const data = dataAssetsData.value;
  if (!data) return;
};

// 页面加载时获取数据
onMounted(() => {
  fetchData();
});
</script>

<style scoped>
/* 添加响应式调整 */
@media (width <= 1200px) {
  .cloud-stat-item,
  .region-stat-item {
    width: 100%;
  }
}

@media (width <= 768px) {
  .stat-item {
    margin-bottom: 16px;
  }
}

/* 响应式调整 */
@media (width <= 1400px) {
  .stat-icon-wrapper {
    width: 60px;
    height: 60px;
  }
}

.main {
  min-height: 100vh;
  padding: 20px;
  background-color: #f5f7fa;
}

.filter-card,
.stat-card {
  margin-bottom: 24px;
  overflow: hidden;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 8%);
  transition: all 0.3s ease;
}

.stat-card:hover {
  box-shadow: 0 6px 20px rgb(0 0 0 / 12%);
  transform: translateY(-3px);
}

.filter-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background-color: #fafafa;
  border-bottom: 1px solid #ebeef5;
}

.card-header h3 {
  display: flex;
  gap: 8px;
  align-items: center;
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.card-header h3::before {
  display: block;
  width: 4px;
  height: 18px;
  content: "";
  background: linear-gradient(to bottom, #409eff, #53a8ff);
  border-radius: 2px;
}

.stat-item {
  height: 100%;
  padding: 14px;
  text-align: center;
  background-color: #fff;
  border: 1px solid #ebeef5;
  border-radius: 10px;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 5%);
  transition: all 0.3s ease;
}

.stat-item:hover {
  border-color: #d0e1fd;
  box-shadow: 0 8px 16px rgb(0 0 0 / 10%);
  transform: translateY(-5px);
}

.stat-title {
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: center;
  margin-bottom: 12px;
  font-size: 16px;
  font-weight: 600;
  color: #606266;
}

.stat-title > :deep(iconify-icon-online) {
  display: flex;
  align-items: center;
  color: #409eff;
}

.stat-value {
  margin-top: 8px;
  font-size: 28px;
  font-weight: bold;
  color: transparent;
  background: linear-gradient(45deg, #409eff, #53a8ff);
  background-clip: text;
}

.stat-unit {
  padding: 2px 4px;
  font-size: 14px;
}

.chart {
  width: 100%;
  height: 600px;
  transition: height 0.3s ease;
}

.stat-label {
  display: flex;
  gap: 8px;
  align-items: center;
  margin-bottom: 6px;
  font-size: 14px;
  color: #606266;
}

.stat-label > :deep(iconify-icon-online) {
  color: #409eff;
}

.cloud-card,
.region-card {
  margin-bottom: 16px;
  border: none;
  border-radius: 10px;
  transition: all 0.3s ease;
}

.cloud-card:hover,
.region-card:hover {
  box-shadow: 0 8px 16px rgb(0 0 0 / 10%);
  transform: translateY(-3px);
}

.cloud-title,
.region-title {
  position: relative;
  padding-bottom: 10px;
  margin-bottom: 16px;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  border-bottom: 1px solid #ebeef5;
}

.cloud-title::after,
.region-title::after {
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 40px;
  height: 3px;
  content: "";
  background: linear-gradient(to right, #409eff, #53a8ff);
  border-radius: 3px;
}

.cloud-stats,
.region-stats {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.cloud-stat-item,
.region-stat-item {
  width: 45%;
  padding: 10px;
  margin-bottom: 16px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.cloud-stat-item:hover,
.region-stat-item:hover {
  background-color: #f5f7fa;
}

.el-divider {
  margin: 32px 0;
}

.el-divider__text {
  font-size: 16px;
  font-weight: 600;
  color: #606266;
  background-color: #fff;
}

/* 自定义日期选择器样式 */
:deep(.el-date-editor) {
  border-radius: 8px;
}

:deep(.el-button) {
  border-radius: 8px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

.stat-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 70px;
  height: 70px;
  margin: 0 auto 16px;
  color: #409eff;
  background: linear-gradient(135deg, #f0f5ff, #e6f7ff);
  border-radius: 50%;
  box-shadow: 0 4px 12px rgb(0 149 255 / 15%);
  transition: all 0.3s ease;
}

.stat-item:hover .stat-icon-wrapper {
  box-shadow: 0 6px 16px rgb(0 149 255 / 25%);
  transform: scale(1.1);
}

.optimize-item .progress-container {
  margin: 16px auto;
}

.refresh-btn {
  border-radius: 8px;
  transition: all 0.3s ease;
}

.refresh-btn:hover {
  background-color: #337ecc;
}

.edit-btn {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.edit-btn:hover {
  box-shadow: 0 4px 12px rgb(0 0 0 / 10%);
  transform: translateY(-2px);
}

.chart-actions {
  display: flex;
  gap: 8px;
}

/* 表格容器样式 */
.table-container {
  width: 100%;
  padding: 0 16px;
  margin-top: 16px;
}

/* 带宽单元格样式 */
.bandwidth-cell {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.bandwidth-progress {
  margin: 4px 0;
}

.percent-text {
  font-size: 16px;
  color: #909399;
  text-align: right;
}

/* 表格样式优化 */
:deep(.el-table) {
  overflow: hidden;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 5%);
}

:deep(.el-table__row) {
  transition: all 0.3s;
}

:deep(.el-table__row:hover) {
  background-color: #f0f7ff !important;
}

:deep(.el-progress-bar__inner) {
  transition: all 0.6s ease-in-out;
}

:deep(.el-progress-bar__outer) {
  background-color: #ebeef5;
  border-radius: 4px;
}

.change-indicator {
  display: inline-flex;
  align-items: center;
  padding: 2px 6px;
  margin-left: 8px;
  font-size: 16px;
  border-radius: 4px;
}

.domain-bandwidth-total {
  display: flex;
  gap: 48px;
  padding: 24px;
  margin-bottom: 16px;
  background: #f5f7fa;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 5%);
}

.total-item {
  display: flex;
  flex: 1;
  flex-direction: column;
  gap: 12px;
  align-items: center;
  padding: 16px;
  background: white;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.total-item:hover {
  box-shadow: 0 4px 16px 0 rgb(0 0 0 / 10%);
  transform: translateY(-2px);
}

.total-label {
  font-size: 14px;
  font-weight: 500;
  color: #909399;
}

.total-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.total-value.highlight {
  font-size: 28px;
  color: #409eff;
}

.increase {
  color: #f56c6c;
  background-color: #fef0f0;
}

.decrease {
  color: #67c23a;
  background-color: #f0f9eb;
}

.no-change {
  color: #909399;
  background-color: #f4f4f5;
}
</style>
