import dayjs from "dayjs";
import type { PaginationProps } from "@pureadmin/table";
import { reactive, ref, onMounted } from "vue";
import { message } from "@/utils/message";
import {
  checkTodyBackupAPI,
  getBackupsAPI,
  type Backup
} from "@/api/database/mysql/backup";

export function useRole() {
  const form = reactive({
    keyword: undefined,
    host: undefined
  });
  const dataList = ref<Backup[]>([]);
  const loading = ref(true);
  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true,
    pageSizes: [10, 20, 30, 40, 50, 100]
  });
  const columns: TableColumnList = [
    {
      label: "项目",
      prop: "project"
    },
    {
      label: "主机",
      prop: "host"
    },
    {
      label: "端口",
      prop: "port"
    },
    {
      label: "文件名",
      prop: "filename"
    },
    {
      label: "文件大小",
      prop: "filesize",
      minWidth: 50,
      cellRenderer: ({ row }) => {
        const size = row.filesize;
        let display = "";
        if (size >= 1024 * 1024 * 1024) {
          display = (size / (1024 * 1024 * 1024)).toFixed(2) + " GB";
        } else if (size >= 1024 * 1024) {
          display = (size / (1024 * 1024)).toFixed(2) + " MB";
        } else if (size >= 1024) {
          display = (size / 1024).toFixed(2) + " KB";
        } else {
          display = size + " B";
        }
        return (
          <div style="white-space: pre-line;">
            <p>{display}</p>
          </div>
        );
      }
    },
    {
      label: "状态",
      prop: "status",
      cellRenderer: ({ row }) => (
        <div style="white-space: pre-line;">
          {row.status === "ok" ? (
            <el-text type="success"> {row.status}</el-text>
          ) : (
            <el-text type="danger">{row.status}</el-text>
          )}
        </div>
      )
    },
    {
      label: "备份时间",
      prop: "backup_time",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <div style="white-space: normal;">
          <el-text type="primary">
            {dayjs(row.created_at).format("YYYY-MM-DD HH:mm:ss")}
          </el-text>
        </div>
      )
    },
    {
      label: "创建时间",
      prop: "backup_time",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <div style="white-space: normal;">
          <p>{dayjs(row.created_at).format("YYYY-MM-DD HH:mm:ss")}</p>
        </div>
      )
    }
  ];

  function handleSizeChange(val: number) {
    pagination.pageSize = val;
    onSearch();
  }

  function handleCurrentChange(val: number) {
    pagination.currentPage = val;
    onSearch();
  }

  async function onSearch() {
    loading.value = true;
    getBackupsAPI({
      page: pagination.currentPage,
      limit: pagination.pageSize,
      keyword: form.keyword,
      host: form.host
    })
      .then(res => {
        if (res.success) {
          dataList.value = res.data;
          pagination.total = res.count;
          pagination.pageSize = pagination.pageSize;
          pagination.currentPage = pagination.currentPage;
        } else {
          dataList.value = [];
          pagination.total = 0;
          pagination.pageSize = pagination.pageSize;
          pagination.currentPage = pagination.currentPage;
        }
      })
      .catch(() => {
        message("请求失败", { type: "error" });
      })
      .finally(() => {
        loading.value = false;
      });
  }

  const checkTodyBackup = () => {
    checkTodyBackupAPI()
      .then(res => {
        if (res.success) {
          message("检查成功", { type: "success" });
        } else {
          message("检查失败", { type: "error" });
        }
      })
      .catch(() => {
        message("请求失败", { type: "error" });
      });
  };

  const resetForm = formEl => {
    if (!formEl) return;
    formEl.resetFields();
    onSearch();
  };

  onMounted(() => {
    onSearch();
  });

  return {
    form,
    loading,
    columns,
    dataList,
    pagination,
    onSearch,
    resetForm,
    handleSizeChange,
    handleCurrentChange,
    checkTodyBackup
  };
}
