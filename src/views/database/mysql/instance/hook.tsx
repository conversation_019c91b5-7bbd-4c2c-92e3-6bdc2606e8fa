import dayjs from "dayjs";
import type { PaginationProps } from "@pureadmin/table";
import { reactive, ref, onMounted, h } from "vue";
import { message } from "@/utils/message";

import {
  deleteInstanceAPI,
  getInstancesAPI,
  type InstanceForm,
  syncInstanceMetadata,
  type Instance,
  createInstanceAPI,
  updateInstanceAPI,
  tagInstances,
  type BatTagFrom
} from "@/api/database/mysql/instance";
import { addDrawer } from "@/components/ReDrawer";
import Table from "./Table.vue";
import { addDialog } from "@/components/ReDialog";
import Uform from "./Uform.vue";
import DataLength from "./DataLength.vue";
import TagForm from "./TagForm.vue";
import { MySQLInstanceTypes } from "@/config/enum";
import { ElButton, ElMessage } from "element-plus";
import { useClipboard } from "@vueuse/core";

export function useRole() {
  const form = reactive({
    keyword: undefined
  });
  const dataList = ref<Instance[]>([]);
  const loading = ref<boolean>(true);
  const selectedRows = ref<Instance[]>([]);
  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true,
    pageSizes: [10, 20, 30, 40, 50, 100]
  });
  const columns: TableColumnList = [
    {
      type: "selection",
      width: 55,
      align: "left",
      hide: () => false
    },
    {
      label: "名称",
      prop: "name",
      minWidth: 120,
      cellRenderer: ({ row }) => (
        <div class="text-sm">
          <p>
            名称： <el-text type="info">{row.name}</el-text>
          </p>
          <p style="margin-top: 10px;margin-bottom: 10px;">
            版本：
            <el-text type="info">
              {MySQLInstanceTypes.get(row.instance_type)}
              {row.version ? ` (${row.version}) ` : ""}
            </el-text>
          </p>
          <p>
            从节点数：{" "}
            <el-text type="info" style="font-weight: bold;">
              {row.replica_count}
            </el-text>
          </p>
        </div>
      )
    },
    {
      label: "连接信息",
      prop: "connection",
      minWidth: 200,
      cellRenderer: ({ row }) => (
        <div class="text-sm">
          <p>
            地址：
            <el-text type="info" style="margin-right: 10px;">
              {row.host}
            </el-text>
          </p>
          <p style="margin-top: 10px;margin-bottom: 10px;">
            端口：
            <el-text type="info" style="margin-right: 10px;">
              {row.port}
            </el-text>
          </p>
          <p>
            用户：
            <el-text type="info" style="margin-right: 10px;">
              {row.username}
            </el-text>
          </p>
        </div>
      )
    },
    {
      label: "CI地址",
      prop: "ci_address",
      minWidth: 150,
      cellRenderer: ({ row }) => (
        <div class="url-column">
          {row.ci_address ? (
            <div class="url-content">
              <el-text type="warning" style="margin-right: 10px;">
                {row.ci_address}
              </el-text>
              <ElButton
                class="copy-button"
                type="primary"
                link
                onClick={() => handleCopy(row.ci_address, "CI")}
              >
                复制
              </ElButton>
            </div>
          ) : (
            <el-text type="info"> 无 </el-text>
          )}
        </div>
      )
    },
    {
      label: "标签",
      prop: "tags",
      minWidth: 150,
      cellRenderer: ({ row }) => (
        <div class="flex flex-wrap gap-1">
          {row.tags?.length ? (
            row.tags.map(tag => (
              <el-tag
                size="large"
                effect="plain"
                type="primary"
                class="mb-1 break-all"
                style="max-width: 100%; word-break: break-word;"
              >
                {tag.key}={tag.value}
              </el-tag>
            ))
          ) : (
            <span class="text-gray-400">-</span>
          )}
        </div>
      )
    },
    {
      label: "关联业务域名",
      prop: "domains",
      minWidth: 150,
      cellRenderer: ({ row }) => (
        <div class="flex flex-wrap gap-1">
          {row.domains?.length ? (
            row.domains.map(domain => (
              <el-tag
                size="large"
                effect="plain"
                type="primary"
                class="mb-1 break-all"
                style="max-width: 100%; word-break: break-word;"
              >
                {domain.name +
                  (domain.domain_type === "public" ? "（公网）" : "（私网）")}
              </el-tag>
            ))
          ) : (
            <span class="text-gray-400">-</span>
          )}
        </div>
      )
    },
    {
      label: "备注",
      prop: "remark",
      minWidth: 150,
      cellRenderer: ({ row }) => (
        <div class="text-sm" style="white-space: pre-line;">
          {row.remark}
        </div>
      )
    },
    {
      label: "时间",
      prop: "created_at",
      minWidth: 180,
      cellRenderer: ({ row }) => (
        <div class="text-sm">
          <p>
            创建：
            <el-text type="info" style="margin-right: 10px;">
              {dayjs(row.created_at).format("YYYY-MM-DD HH:mm:ss")}
            </el-text>
          </p>
          <p style="margin-top: 10px;margin-bottom: 10px;">
            更新：
            <el-text type="info" style="margin-right: 10px;">
              {dayjs(row.updated_at).format("YYYY-MM-DD HH:mm:ss")}
            </el-text>
          </p>
        </div>
      )
    },
    {
      label: "操作",
      prop: "action",
      minWidth: 240,
      cellRenderer: ({ row }) => (
        <div>
          <div class="flex items-center flex-wrap gap-1">
            <el-button-group class="mr-1">
              <el-button type="primary" link onClick={() => showTables(row)}>
                <iconify-icon-online
                  icon="material-symbols:table-outline"
                  class="mr-1"
                />
                查看表
              </el-button>
              <el-button
                type="primary"
                link
                onClick={() => showDataLength(row)}
                style="margin-right: 10px; margin-left: 10px;"
              >
                <iconify-icon-online icon="ep:document" class="mr-1" />
                数据统计
              </el-button>
              <el-button type="warning" link onClick={() => syncFunc(row)}>
                <iconify-icon-online icon="ep:refresh" class="mr-1" />
                同步
              </el-button>
            </el-button-group>
          </div>
          <div class="flex-1" style="margin-top: 10px;">
            <el-button-group>
              <el-button type="primary" link onClick={() => editFunc(row)}>
                <iconify-icon-online icon="ep:edit" class="mr-1" />
                编辑
              </el-button>
              <el-button
                type="danger"
                style="margin-right: 10px; margin-left: 10px;"
                link
                onClick={() => deleteFunc(row)}
              >
                <iconify-icon-online icon="ep:delete" class="mr-1" />
                删除
              </el-button>
            </el-button-group>
          </div>
        </div>
      )
    }
  ];

  function syncFunc(row: Instance) {
    syncInstanceMetadata(row.id)
      .then(res => {
        if (res.success) {
          message(res.msg, { type: "success" });
        } else {
          message(res.msg, { type: "error" });
        }
      })
      .catch(errror => {
        message("同步失败：" + errror, { type: "error" });
      });
  }
  function deleteFunc(row: Instance) {
    addDialog({
      title: "删除确认",
      width: 480,
      draggable: true,
      closeOnClickModal: false,
      contentRenderer: () => (
        <div class="flex flex-col gap-2">
          <p class="text-lg mb-4">您确定要删除以下MySQL实例吗？</p>
          <el-descriptions border column={1}>
            <el-descriptions-item
              label="实例名称"
              label-class-name="font-bold w-[100px]"
            >
              <span class="text-red-500 font-bold">{row.name}</span>
            </el-descriptions-item>
            <el-descriptions-item label="连接地址" label-class-name="font-bold">
              {row.host}:{row.port}
            </el-descriptions-item>
            <el-descriptions-item label="版本" label-class-name="font-bold">
              {row.version}
            </el-descriptions-item>
          </el-descriptions>
          <div class="bg-[#fff3f3] text-red-500 p-3 rounded mt-4">
            <i class="el-icon-warning mr-2" />
            警告：此操作将永久删除该实例及其相关数据，且无法恢复！
          </div>
        </div>
      ),
      beforeSure: done => {
        deleteInstanceAPI(row.id)
          .then(res => {
            if (res.success) {
              message(res.msg, { type: "success" });
              onSearch();
              done();
            } else {
              message(res.msg, { type: "error" });
            }
          })
          .catch(error => {
            message("请求失败：" + error, { type: "error" });
          })
          .finally(() => {
            loading.value = false;
          });
      }
    });
  }
  const editForm = ref<InstanceForm>();
  const childrenRef = ref(null);
  const { copy: copyToClipboard } = useClipboard();

  const handleCopy = async (text: string, type: string) => {
    try {
      await copyToClipboard(text);
      ElMessage.success(`${type} URL 已复制到剪贴板`);
    } catch (error) {
      ElMessage.error("复制失败");
    }
  };

  function addFunc() {
    editForm.value = {
      name: "",
      instance_type: "mysql",
      host: "",
      port: 3306,
      username: "",
      password: "",
      remark: "",
      replica_count: 0,
      ci_address: ""
    };
    addDrawer({
      headerRenderer: ({ titleId, titleClass }) => (
        <div class="flex flex-row justify-between">
          <h4 id={titleId} class={titleClass}>
            添加实例
          </h4>
        </div>
      ),
      contentRenderer: () =>
        h(Uform, {
          formInline: {
            form: editForm.value
          },
          ref: childrenRef
        }),
      beforeSure: done => {
        if (childrenRef.value.ruleFormRef) {
          childrenRef.value.ruleFormRef.validate(valid => {
            if (valid) {
              createInstanceAPI(editForm.value)
                .then(res => {
                  if (res.success) {
                    message(res.msg, { type: "success" });
                    done();
                    // 重置表单数据
                    editForm.value = undefined;
                    onSearch();
                  } else {
                    message(res.msg, { type: "error" });
                  }
                })
                .catch(error => {
                  message(error, { type: "error" });
                });
            } else {
              message("请检查表单", { type: "error" });
            }
          });
        } else {
          message("请检查表单", { type: "error" });
        }
      }
    });
  }
  function editFunc(row: Instance) {
    editForm.value = {
      name: row.name,
      instance_type: row.instance_type,
      host: row.host,
      port: row.port,
      username: row.username,
      password: "",
      remark: row.remark,
      replica_count: row.replica_count,
      business_domain_ids: row.business_domain_ids,
      ci_address: row.ci_address
    };
    addDrawer({
      headerRenderer: ({ titleId, titleClass }) => (
        <div class="flex flex-row justify-between">
          <h4 id={titleId} class={titleClass}>
            更新实例 {row.name}
          </h4>
        </div>
      ),
      contentRenderer: () =>
        h(Uform, {
          formInline: {
            form: editForm.value
          },
          ref: childrenRef
        }),
      beforeSure: done => {
        if (childrenRef.value.ruleFormRef) {
          childrenRef.value.ruleFormRef.validate(valid => {
            if (valid) {
              updateInstanceAPI(row.id, editForm.value)
                .then(res => {
                  if (res.success) {
                    message(res.msg, { type: "success" });
                    done();
                    // 重置表单数据
                    editForm.value = undefined;
                    onSearch();
                  } else {
                    message(res.msg, { type: "error" });
                  }
                })
                .catch(error => {
                  message(error, { type: "error" });
                });
            } else {
              message("请检查表单", { type: "error" });
            }
          });
        } else {
          message("请检查表单", { type: "error" });
        }
      }
    });
  }
  function showTables(row: Instance) {
    addDrawer({
      headerRenderer: ({ titleId, titleClass }) => (
        <div class="flex flex-row justify-between">
          <h4 id={titleId} class={titleClass}>
            {row.name} 表信息
          </h4>
        </div>
      ),
      size: "100%",
      hideFooter: true,
      contentRenderer: () =>
        h(Table, {
          formInline: {
            instance_id: row.id
          }
        })
    });
  }

  const showDataLength = (row: Instance) => {
    addDialog({
      title: `${row.name} - 实例数据统计`,
      hideFooter: true,
      contentRenderer: () =>
        h(DataLength, {
          instanceId: row.id,
          schemaName: undefined,
          tableName: undefined
        })
    });
  };

  function handleSizeChange(val: number) {
    pagination.pageSize = val;
    onSearch();
  }

  function handleCurrentChange(val: number) {
    pagination.currentPage = val;
    onSearch();
  }

  async function onSearch() {
    loading.value = true;
    getInstancesAPI({
      page: pagination.currentPage,
      limit: pagination.pageSize,
      keyword: form.keyword
    })
      .then(res => {
        if (res.success) {
          dataList.value = res.data;
          pagination.total = res.count;
          pagination.pageSize = pagination.pageSize;
          pagination.currentPage = pagination.currentPage;
        } else {
          dataList.value = [];
          pagination.total = 0;
          pagination.pageSize = pagination.pageSize;
          pagination.currentPage = pagination.currentPage;
        }
      })
      .catch(() => {
        message("请求失败", { type: "error" });
      })
      .finally(() => {
        loading.value = false;
      });
  }

  const resetForm = formEl => {
    if (!formEl) return;
    formEl.resetFields();
    onSearch();
  };

  // 批量设置标签
  function batchSetTags() {
    if (selectedRows.value.length === 0) {
      message("请选择要设置标签的实例", { type: "warning" });
      return;
    }

    let tagFormRef: any = null;

    addDialog({
      title: "批量设置标签",
      width: 600,
      draggable: true,
      closeOnClickModal: false,
      contentRenderer: () =>
        h(TagForm, {
          ref: ref => {
            tagFormRef = ref;
          },
          selectedRows: selectedRows.value,
          onSuccess: () => {
            onSearch();
          }
        }),
      beforeSure: async done => {
        try {
          if (
            tagFormRef.operationType !== "clear" &&
            !tagFormRef.selectedTags?.length
          ) {
            message("请选择要设置的标签", { type: "warning" });
            return;
          }

          const data: BatTagFrom = {
            instance_ids: selectedRows.value.map(row => row.id),
            tag_ids: tagFormRef.selectedTags || [],
            op: tagFormRef.operationType
          };
          const res = await tagInstances(data);
          if (res.success) {
            message("设置标签成功", { type: "success" });
            onSearch();
            done();
          } else {
            message(res.msg || "设置标签失败", { type: "error" });
          }
        } catch (error) {
          message("设置标签失败：" + error, { type: "error" });
        }
      }
    });
  }

  onMounted(() => {
    onSearch();
  });

  return {
    form,
    loading,
    columns,
    dataList,
    pagination,
    selectedRows,
    onSearch,
    resetForm,
    handleSizeChange,
    handleCurrentChange,
    addFunc,
    batchSetTags
  };
}
