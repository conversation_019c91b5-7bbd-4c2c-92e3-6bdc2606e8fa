import { ref, reactive, onMounted, h } from "vue";
import type { TableInstance } from "element-plus";
import type { PaginationProps } from "@pureadmin/table";
import { getTablesAPI, type Table } from "@/api/database/mysql/instance";
import { message } from "@/utils/message";
import dayjs from "dayjs";
import { addDialog } from "@/components/ReDialog";
import DataLength from "./DataLength.vue";
import { formatStorage } from "@/utils/format";

interface FormProps {
  formInline: {
    instance_id?: number;
  };
}

const newFormInline = ref<FormProps["formInline"]>({ instance_id: undefined });
const formRef = ref<TableInstance>();
const tableRef = ref();
const form = reactive({
  schema: undefined,
  keyword: undefined
});
const dataList = ref<Table[]>([]);
const loading = ref<boolean>(true);
const pagination = reactive<PaginationProps>({
  total: 0,
  pageSize: 20,
  currentPage: 1,
  background: true,
  pageSizes: [10, 20, 30, 40, 50, 100]
});

const handleSizeChange = (val: number) => {
  pagination.pageSize = val;
  onSearch();
};

const handleCurrentChange = (val: number) => {
  pagination.currentPage = val;
  onSearch();
};

const onSearch = async () => {
  loading.value = true;
  try {
    const res = await getTablesAPI(newFormInline.value.instance_id, {
      keyword: form.keyword,
      page: pagination.currentPage,
      limit: pagination.pageSize,
      schema: form.schema
    });
    if (res.success) {
      dataList.value = res.data;
      pagination.total = res.count;
    } else {
      dataList.value = [];
      pagination.total = 0;
      message(res.msg, { type: "error" });
    }
  } catch (error) {
    message("请求失败" + error, { type: "error" });
  } finally {
    loading.value = false;
  }
};

const resetForm = (formEl: any) => {
  if (!formEl) return;
  formEl.resetFields();
  onSearch();
};

const showDataLength = (
  instanceName: string,
  instanceID: number,
  schemaName: string,
  tableName: string
) => {
  addDialog({
    title: `${instanceName} - 数据大小`,
    hideFooter: true,
    contentRenderer: () =>
      h(DataLength, {
        instanceId: instanceID,
        schemaName: schemaName,
        tableName: tableName
      })
  });
};

const columns: TableColumnList = [
  {
    label: "库名",
    prop: "table_schema",
    cellRenderer: ({ row }) => {
      return (
        <el-button
          type="primary"
          link
          icon="el-icon-document"
          onClick={() =>
            showDataLength(
              row.table_schema + "库",
              row.instance_id,
              row.table_schema,
              undefined
            )
          }
        >
          {row.table_schema}
        </el-button>
      );
    }
  },
  {
    label: "表名",
    prop: "name",
    cellRenderer: ({ row }) => {
      return (
        <el-button
          type="primary"
          link
          onClick={() =>
            showDataLength(
              row.table_schema + " 库 " + row.name + " 表",
              row.instance_id,
              row.table_schema,
              row.name
            )
          }
        >
          {row.name}
        </el-button>
      );
    }
  },
  {
    label: "表类型",
    prop: "table_type"
  },
  {
    label: "引擎",
    prop: "table_engine"
  },
  {
    label: "行格式",
    prop: "row_format"
  },
  {
    label: "字符集",
    prop: "collation"
  },
  {
    label: "行数",
    prop: "table_rows"
  },
  {
    label: "数据大小",
    prop: "data_length",
    cellRenderer: ({ row }) => {
      return formatStorage(row.data_length);
    }
  },
  {
    label: "索引大小",
    prop: "index_length",
    cellRenderer: ({ row }) => {
      return formatStorage(row.index_length);
    }
  },
  {
    label: "选项",
    prop: "create_options"
  },
  {
    label: "备注",
    prop: "remark"
  },
  {
    label: "创建时间",
    prop: "create_time",
    cellRenderer: ({ row }) => {
      return dayjs(row?.create_time).format("YYYY-MM-DD HH:mm:ss");
    }
  },
  {
    label: "更新时间",
    prop: "update_time",
    cellRenderer: ({ row }) => {
      return (
        row?.update_time &&
        dayjs(row?.update_time).format("YYYY-MM-DD HH:mm:ss")
      );
    }
  },
  {
    label: "同步时间",
    prop: "sync_time",
    minWidth: 80,
    cellRenderer: ({ row }) => {
      return (
        row?.sync_time && dayjs(row?.sync_time).format("YYYY-MM-DD HH:mm:ss")
      );
    }
  }
];
onMounted(() => {
  onSearch();
});

export {
  newFormInline,
  formRef,
  tableRef,
  form,
  dataList,
  loading,
  pagination,
  columns,
  handleSizeChange,
  handleCurrentChange,
  onSearch,
  resetForm
};
