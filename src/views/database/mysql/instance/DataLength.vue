<template>
  <el-card class="box-card">
    <div class="clearfix" style="margin-bottom: 20px">
      <span class="card-title">数据统计趋势分析</span>
    </div>
    <div style="display: flex; align-items: center; margin-bottom: 20px">
      <el-date-picker
        v-model="dateRange"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        value-format="YYYY-MM-DD"
        :default-time="[new Date(), new Date()]"
        style="margin-right: 10px"
        @change="fetchDataAndRenderChart"
      />
      <el-button link type="primary" @click="fetchDataAndRenderChart">
        刷新数据
      </el-button>
    </div>
    <div ref="chart" style="width: 100%; height: 400px" />
  </el-card>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import * as echarts from "echarts";
import {
  getInstanceSchemaTableLengthAPI,
  getInstanceSchemaLengthAPI,
  getInstanceLengthAPI
} from "@/api/database/mysql/instance";
import { message } from "@/utils/message";
import dayjs from "dayjs";
import { formatStorage } from "@/utils/format";

const chart = ref(null);

const props = defineProps<{
  instanceId: number;
  schemaName: string | undefined;
  tableName: string | undefined;
}>();

const { instanceId, schemaName, tableName } = props;

const dateRange = ref([
  dayjs().subtract(3, "month").format("YYYY-MM-DD"),
  dayjs().format("YYYY-MM-DD")
]);

const fetchDataAndRenderChart = async () => {
  const [startTime, endTime] = dateRange.value;
  try {
    let response;
    if (schemaName && !tableName) {
      response = await getInstanceSchemaLengthAPI(instanceId, schemaName, {
        start_time: startTime,
        end_time: endTime
      });
    } else if (!schemaName && !tableName) {
      response = await getInstanceLengthAPI(instanceId, {
        start_time: startTime,
        end_time: endTime
      });
    } else {
      response = await getInstanceSchemaTableLengthAPI(
        instanceId,
        schemaName,
        tableName,
        {
          start_time: startTime,
          end_time: endTime
        }
      );
    }

    if (response.success && response.data) {
      const dates = response.data.map(item => item.date);
      const lengths = response.data.map(item => item.length);

      const chartInstance = echarts.init(chart.value);
      const option = {
        title: {
          text: `${schemaName ? " 库：" + schemaName : ""} ${tableName ? " 表：" + tableName : ""}`,
          left: "center",
          textStyle: {
            color: "#333",
            fontSize: 16
          }
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow"
          },
          formatter: function (params) {
            const date = params[0].axisValue;
            const length = params[0].data;
            return `${date}<br/>: ${formatStorage(length)}`;
          }
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true
        },
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: dates,
          axisLine: {
            lineStyle: {
              color: "#888"
            }
          },
          axisLabel: {
            rotate: 45
          }
        },
        yAxis: {
          type: "value",
          axisLine: {
            show: true,
            lineStyle: {
              color: "#888"
            }
          },
          splitLine: {
            show: true,
            lineStyle: {
              type: "dashed"
            }
          },
          axisLabel: {
            formatter: value => `${formatStorage(value)}`
          }
        },
        series: [
          {
            data: lengths,
            type: "line",
            smooth: true,
            lineStyle: {
              color: "#409EFF",
              width: 3
            },
            itemStyle: {
              color: "#409EFF"
            },
            areaStyle: {
              color: "rgba(64, 158, 255, 0.2)"
            },
            symbol: "circle",
            symbolSize: 6
          }
        ]
      };
      chartInstance.setOption(option);
    }
  } catch (error) {
    message("获取数据失败" + error, { type: "error" });
    console.error("Error fetching data:", error);
  }
};

onMounted(() => {
  fetchDataAndRenderChart();
});
</script>

<style scoped>
.box-card {
  padding: 20px;
}

.card-title {
  font-size: 18px;
  font-weight: bold;
}
</style>
