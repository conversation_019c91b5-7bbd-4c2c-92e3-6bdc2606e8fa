<template>
  <div>
    <el-card shadow="never" :form="newFormInline.form">
      <el-form
        ref="ruleFormRef"
        label-width="auto"
        style="max-width: 600px"
        :model="newFormInline.form"
        label-position="top"
        status-icon
      >
        <el-form-item
          label="名称"
          prop="name"
          show-word-limit
          maxlength="255"
          :rules="[
            { required: true, message: '请输入名称', trigger: 'blur' },
            { max: 255, message: '名称长度不能超过255个字符', trigger: 'blur' }
          ]"
        >
          <el-input v-model="newFormInline.form.name" />
        </el-form-item>
        <el-form-item
          label="类型"
          prop="instance_type"
          :rules="[{ required: true, message: '请选择类型', trigger: 'blur' }]"
        >
          <el-select
            v-model="newFormInline.form.instance_type"
            placeholder="请选择类型"
          >
            <el-option label="MySQL" value="mysql" />
            <el-option label="Tidb" value="tidb" />
          </el-select>
        </el-form-item>
        <el-form-item
          label="地址"
          prop="host"
          show-word-limit
          maxlength="255"
          :rules="[
            { required: true, message: '请输入地址', trigger: 'blur' },
            { max: 255, message: '名称长度不能超过255个字符', trigger: 'blur' }
          ]"
        >
          <el-input v-model="newFormInline.form.host" />
        </el-form-item>
        <el-form-item label="端口" prop="port">
          <el-input-number
            v-model="newFormInline.form.port"
            step-strictly
            size="large"
          />
        </el-form-item>
        <el-form-item
          label="CI地址"
          prop="ci_address"
          show-word-limit
          maxlength="255"
          :rules="[
            {
              max: 255,
              message: 'CI地址长度不能超过255个字符',
              trigger: 'blur'
            }
          ]"
        >
          <el-input v-model="newFormInline.form.ci_address" />
        </el-form-item>
        <el-form-item label="从节点数" prop="replica_count">
          <el-input-number
            v-model="newFormInline.form.replica_count"
            step-strictly
            size="large"
          />
        </el-form-item>

        <el-form-item
          label="管理用户名"
          :rule="[
            { required: true, message: '请输入名称', trigger: 'blur' },
            { max: 255, message: '名称长度不能超过255个字符', trigger: 'blur' }
          ]"
        >
          <el-input
            v-model="newFormInline.form.username"
            placeholder="管理用户名"
            show-word-limit
            maxlength="255"
          />
        </el-form-item>
        <el-form-item
          label="管理密码"
          :rule="[
            { max: 255, message: '名称长度不能超过255个字符', trigger: 'blur' }
          ]"
        >
          <el-input
            v-model="newFormInline.form.password"
            type="password"
            show-password
            placeholder="留空表示不修改"
          />
        </el-form-item>
        <el-button type="warning" @click="testConnection(newFormInline.form)"
          >测试连接</el-button
        >
        <!-- 关联业务域名 -->
        <el-form-item label="关联业务域名" prop="business_domain_ids">
          <el-select
            v-model="newFormInline.form.business_domain_ids"
            multiple
            clearable
            filterable
            placeholder="请选择要关联的业务域名"
            style="width: 100%"
            
          >
            <el-option
              v-for="domain in domainOptions"
              :key="domain.id"
              :label="domain.name + (domain.domain_type === 'public' ? '（公网）' : '（私网）')"
              :value="domain.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item
          label="备注"
          prop="remark"
          :rules="[
            { max: 255, message: '名称长度不能超过255个字符', trigger: 'blur' }
          ]"
        >
          <el-input
            v-model="newFormInline.form.remark"
            type="textarea"
            show-word-limit
            maxlength="255"
          />
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from "vue";
import { getAllDomainsAPI, type Domain } from "@/api/appops/domain";
import { defineProps, ref } from "vue";
import {
  testConnectionAPI,
  type InstanceForm as Form
} from "@/api/database/mysql/instance";
import type { FormInstance } from "element-plus";
import { message } from "@/utils/message";
export interface FormProps {
  formInline: {
    form: Form;
  };
}
const props = withDefaults(defineProps<FormProps>(), {
  formInline: () => ({ form: undefined })
});

const newFormInline = ref(props.formInline);
const ruleFormRef = ref<FormInstance>();
defineExpose({ ruleFormRef });

// 业务域名选项
const domainOptions = ref<Domain[]>([]);

onMounted(async () => {
  try {
    const res = await getAllDomainsAPI();
    if (res.success && res.data) {
      domainOptions.value = res.data;
    }
  } catch (e) {
    // 可根据需要添加错误提示
  }
});

function testConnection(form: Form) {
  testConnectionAPI(form).then(res => {
    if (res.success) {
      message(res.msg, { type: "success" });
    } else {
      message(res.msg, { type: "error" });
    }
  });
}
</script>
