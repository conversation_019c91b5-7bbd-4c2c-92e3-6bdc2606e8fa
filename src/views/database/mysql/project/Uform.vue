<template>
  <div>
    <el-card shadow="never" :form="newFormInline.form">
      <el-form
        ref="ruleFormRef"
        label-width="auto"
        style="max-width: 600px; margin: auto"
        :model="newFormInline.form"
        label-position="top"
        status-icon
      >
        <el-form-item
          label="名称"
          prop="name"
          show-word-limit
          maxlength="255"
          :rules="[
            { required: true, message: '请输入名称', trigger: 'blur' },
            { max: 255, message: '名称长度不能超过255个字符', trigger: 'blur' }
          ]"
        >
          <el-input v-model="newFormInline.form.name" />
        </el-form-item>
        <el-form-item
          label="主机"
          prop="link"
          show-word-limit
          label-width="auto"
          maxlength="255"
          :rules="[
            { required: true, message: '请输入主机', trigger: 'blur' },
            { max: 255, message: '名称长度不能超过255个字符', trigger: 'blur' }
          ]"
        >
          <el-input v-model="newFormInline.form.link" />
        </el-form-item>
        <el-form-item>
          <el-form-item label="端口" prop="port" style="margin-right: 20px">
            <el-input-number
              v-model="newFormInline.form.port"
              step-strictly
              size="large"
              :min="1"
              :max="65535"
            />
          </el-form-item>
          <el-form-item label="环境" prop="env" style="margin-right: 20px">
            <el-select
              v-model="newFormInline.form.env"
              placeholder="请选择环境"
              style="width: 120px"
            >
              <el-option
                v-for="(item, index) in ENVs"
                :key="index"
                :label="item[1]"
                :value="item[0]"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="是否是敏感" prop="is_sensitive">
            <el-switch v-model="newFormInline.form.is_sensitive" />
          </el-form-item>
        </el-form-item>
        <el-form-item label="自定义库" prop="custom_dbs">
          <el-select
            v-model="newFormInline.form.custom_dbs"
            multiple
            filterable
            clearable
            allow-create
            default-first-option
            :reserve-keyword="false"
            placeholder="请输入自定义库"
            size="large"
            tag-type="warning"
          />
        </el-form-item>
        <el-form-item
          label="管理用户名"
          :rules="[
            { required: true, message: '请输入名称', trigger: 'blur' },
            { max: 255, message: '名称长度不能超过255个字符', trigger: 'blur' }
          ]"
        >
          <el-input
            v-model="newFormInline.form.admin_user"
            placeholder="管理用户名"
            show-word-limit
            maxlength="255"
          />
        </el-form-item>
        <el-form-item
          label="管理密码"
          :rules="[
            { max: 255, message: '名称长度不能超过255个字符', trigger: 'blur' }
          ]"
        >
          <el-input
            v-model="newFormInline.form.admin_password"
            type="password"
            show-password
            placeholder="留空表示不修改"
          />
        </el-form-item>
        <el-button type="warning" @click="testConnection()">测试连接</el-button>
        <el-form-item
          label="扩展链接"
          prop="ext_link"
          show-word-limit
          maxlength="255"
          :rules="[
            { max: 255, message: '名称长度不能超过255个字符', trigger: 'blur' }
          ]"
        >
          <el-input v-model="newFormInline.form.ext_link" />
        </el-form-item>
        <el-form-item
          label="备注"
          prop="remark"
          :rules="[
            { max: 255, message: '名称长度不能超过255个字符', trigger: 'blur' }
          ]"
        >
          <el-input
            v-model="newFormInline.form.remark"
            type="textarea"
            show-word-limit
            maxlength="255"
            :rows="3"
          />
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { defineProps, ref } from "vue";
import type { ProjectForm as Form } from "@/api/database/mysql/project";
import type { FormInstance } from "element-plus";
import { ENVs } from "@/config/enum";
import { testConnectionAPI } from "@/api/database/mysql/instance";
import { message } from "@/utils/message";

export interface FormProps {
  formInline: {
    form: Form;
  };
}

const props = withDefaults(defineProps<FormProps>(), {
  formInline: () => ({ form: undefined })
});

const newFormInline = ref(props.formInline);
const ruleFormRef = ref<FormInstance>();
defineExpose({ ruleFormRef });

const testConnection = () => {
  testConnectionAPI({
    host: newFormInline.value.form.link,
    port: newFormInline.value.form.port,
    username: newFormInline.value.form.admin_user,
    password: newFormInline.value.form.admin_password,
    name: newFormInline.value.form.name,
    instance_type: "mysql",
    replica_count: 0,
    ci_address: "",
    remark: newFormInline.value.form.remark
  }).then(res => {
    if (res.success) {
      message(res.msg, { type: "success" });
    } else {
      message(res.msg, { type: "error" });
    }
  });
};
</script>

<style scoped>
.el-card {
  padding: 20px;
  border-radius: 8px;
}

.el-form-item {
  margin-bottom: 16px;
}
</style>
