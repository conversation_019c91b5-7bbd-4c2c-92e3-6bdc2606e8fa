import dayjs from "dayjs";
import type { PaginationProps } from "@pureadmin/table";
import { reactive, ref, onMounted, h } from "vue";
import { message } from "@/utils/message";

import { addDrawer } from "@/components/ReDrawer";
import { addDialog } from "@/components/ReDialog";
import Uform from "./Uform.vue";
import {
  createProjectAPI,
  deleteProjectAPI,
  type ProjectForm,
  updateProjectAPI,
  type Project,
  getProjectsAPI,
  syncProjectDBs
} from "@/api/database/mysql/project";
import { ENVColors, ENVs } from "@/config/enum";

export function useRole() {
  const form = reactive({
    keyword: undefined,
    env: undefined
  });
  const dataList = ref<Project[]>([]);
  const loading = ref<boolean>(true);
  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true,
    pageSizes: [10, 20, 30, 40, 50, 100]
  });
  const columns: TableColumnList = [
    {
      label: "名称",
      prop: "name",
      minWidth: 100
    },
    {
      label: "主机:端口",
      prop: "link",
      cellRenderer: ({ row }) => (
        <div style="white-space: nowrap;">
          {row.link}:{row.port}
        </div>
      )
    },
    {
      label: "扩展链接",
      prop: "ext_link",
      minWidth: 150
    },
    {
      label: "环境",
      prop: "env",
      cellRenderer: ({ row }) => (
        <div style="white-space: nowrap;">
          <el-text style="margin: 5px;" type={ENVColors.get(row.env)}>
            {ENVs.get(row.env)}
          </el-text>
        </div>
      )
    },
    {
      label: "是否是敏感库",
      prop: "is_sensitive",
      cellRenderer: ({ row }) => (
        <div style="white-space: nowrap;">
          <el-text type={row.is_sensitive ? "danger" : "success"}>
            {row.is_sensitive ? "敏感" : "非敏感"}
          </el-text>
        </div>
      )
    },
    {
      label: "用户名",
      prop: "admin_user",
      minWidth: 100
    },
    {
      label: "库名",
      prop: "dbs",
      minWidth: 150,
      cellRenderer: ({ row }) => (
        <div style="white-space: pre-line;">
          {row.dbs?.map((item: string) => (
            <el-tag key={item} type="primary" style="margin: 3px;" size="small">
              {item}
            </el-tag>
          ))}
        </div>
      )
    },
    {
      label: "自定义库名",
      prop: "custom_dbs",
      minWidth: 150,
      cellRenderer: ({ row }) => (
        <div style="white-space: pre-line;">
          {row.custom_dbs?.map((item: string) => (
            <el-tag key={item} type="warning" style="margin: 3px;" size="small">
              {item}
            </el-tag>
          ))}
        </div>
      )
    },
    {
      label: "备注",
      prop: "remark",
      minWidth: 100,
      cellRenderer: ({ row }) => (
        <div style="white-space: normal;">
          <p>{row.remark || "无"}</p>
        </div>
      )
    },
    {
      label: "时间",
      prop: "created_at",
      width: 280,
      cellRenderer: ({ row }) => (
        <div style="white-space: normal;">
          <p>创建：{dayjs(row.created_at).format("YYYY-MM-DD HH:mm:ss")}</p>
          <p>
            更新：
            <b>{dayjs(row.updated_at).format("YYYY-MM-DD HH:mm:ss")}</b>
          </p>
        </div>
      )
    },
    {
      label: "操作",
      prop: "action",
      minWidth: 120,
      cellRenderer: ({ row }) => (
        <div style="display: flex; align-items: center; gap: 5px;">
          <el-button type="warning" link onClick={() => editFunc(row)}>
            <iconify-icon-online icon="ep:edit"></iconify-icon-online>
            编辑
          </el-button>
          <el-button type="danger" link onClick={() => deleteFunc(row)}>
            <iconify-icon-online icon="ep:delete" />
            删除
          </el-button>
        </div>
      )
    }
  ];

  function syncFunc() {
    syncProjectDBs()
      .then(res => {
        if (res.success) {
          message(res.msg, { type: "success" });
        } else {
          message(res.msg, { type: "error" });
        }
      })
      .catch(errror => {
        message("同步失败：" + errror, { type: "error" });
      });
  }
  function deleteFunc(row: Project) {
    addDialog({
      title: "",
      width: 500,
      sureBtnLoading: true,
      contentRenderer: () => (
        <p>
          确认删除：<b style="color:red"> {row.name}</b>
        </p>
      ),
      beforeSure: done => {
        deleteProjectAPI(row.id)
          .then(res => {
            if (res.success) {
              message(res.msg, { type: "success" });
              onSearch();
              done();
            } else {
              message(res.msg, { type: "error" });
            }
          })
          .catch(error => {
            message("请求失败" + error, { type: "error" });
          })
          .finally(() => {
            loading.value = false;
          });
      }
    });
  }
  const editForm = ref<ProjectForm>();
  const childrenRef = ref(null);
  function addFunc() {
    editForm.value = {
      name: "",
      link: "",
      ext_link: "",
      port: 3306,
      env: 0,
      admin_user: "",
      admin_password: "",
      dba_group_id: undefined,
      is_sensitive: false,
      custom_dbs: [],
      remark: ""
    };
    addDrawer({
      headerRenderer: ({ titleId, titleClass }) => (
        <div class="flex flex-row justify-between">
          <h4 id={titleId} class={titleClass}>
            添加项目
          </h4>
        </div>
      ),
      contentRenderer: () =>
        h(Uform, {
          formInline: {
            form: editForm.value
          },
          ref: childrenRef
        }),
      beforeSure: done => {
        if (childrenRef.value.ruleFormRef) {
          childrenRef.value.ruleFormRef.validate(valid => {
            if (valid) {
              createProjectAPI(editForm.value)
                .then(res => {
                  if (res.success) {
                    message(res.msg, { type: "success" });
                    done();
                    editForm.value = undefined;
                    onSearch();
                  } else {
                    message(res.msg, { type: "error" });
                  }
                })
                .catch(error => {
                  message(error, { type: "error" });
                });
            } else {
              message("请检查表单", { type: "error" });
            }
          });
        } else {
          message("请检查表单", { type: "error" });
        }
      }
    });
  }
  function editFunc(row: Project) {
    editForm.value = {
      name: row.name,
      link: row.link,
      ext_link: row.ext_link,
      port: row.port,
      env: row.env,
      admin_user: row.admin_user,
      admin_password: "",
      dba_group_id: row.dba_group_id,
      is_sensitive: row.is_sensitive,
      custom_dbs: row.custom_dbs ? row.custom_dbs : [],
      remark: row.remark
    };
    addDrawer({
      headerRenderer: ({ titleId, titleClass }) => (
        <div class="flex flex-row justify-between">
          <h4 id={titleId} class={titleClass}>
            更新项目 {row.name}
          </h4>
        </div>
      ),
      contentRenderer: () =>
        h(Uform, {
          formInline: {
            form: editForm.value
          },
          ref: childrenRef
        }),
      beforeSure: done => {
        if (childrenRef.value.ruleFormRef) {
          childrenRef.value.ruleFormRef.validate(valid => {
            if (valid) {
              updateProjectAPI(row.id, editForm.value)
                .then(res => {
                  if (res.success) {
                    message(res.msg, { type: "success" });
                    done();
                    // 重置表单数据
                    editForm.value = undefined;
                    onSearch();
                  } else {
                    message(res.msg, { type: "error" });
                  }
                })
                .catch(error => {
                  message(error, { type: "error" });
                });
            } else {
              message("请检查表单", { type: "error" });
            }
          });
        } else {
          message("请检查表单", { type: "error" });
        }
      }
    });
  }
  function handleSizeChange(val: number) {
    pagination.pageSize = val;
    onSearch();
  }

  function handleCurrentChange(val: number) {
    pagination.currentPage = val;
    onSearch();
  }

  async function onSearch() {
    loading.value = true;
    getProjectsAPI({
      page: pagination.currentPage,
      limit: pagination.pageSize,
      keyword: form.keyword,
      env: form.env
    })
      .then(res => {
        if (res.success) {
          dataList.value = res.data;
          pagination.total = res.count;
          pagination.pageSize = pagination.pageSize;
          pagination.currentPage = pagination.currentPage;
        } else {
          dataList.value = [];
          pagination.total = 0;
          pagination.pageSize = pagination.pageSize;
          pagination.currentPage = pagination.currentPage;
        }
      })
      .catch(() => {
        message("请求失败", { type: "error" });
      })
      .finally(() => {
        loading.value = false;
      });
  }

  const resetForm = formEl => {
    if (!formEl) return;
    formEl.resetFields();
    onSearch();
  };

  onMounted(() => {
    onSearch();
  });

  return {
    form,
    loading,
    columns,
    dataList,
    pagination,
    onSearch,
    resetForm,
    handleSizeChange,
    handleCurrentChange,
    addFunc,
    syncFunc
  };
}
