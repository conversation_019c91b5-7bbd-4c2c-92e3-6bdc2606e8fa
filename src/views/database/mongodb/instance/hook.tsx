import dayjs from "dayjs";
import type { PaginationProps } from "@pureadmin/table";
import { reactive, ref, onMounted, h } from "vue";
import { message } from "@/utils/message";
import type { FormInstance } from "element-plus";

import {
  deleteInstanceAPI,
  getInstancesAPI,
  type InstanceForm,
  type Instance,
  createInstanceAPI,
  updateInstanceAPI
} from "@/api/database/mongodb/instance";
import { addDrawer } from "@/components/ReDrawer";
import { addDialog } from "@/components/ReDialog";
import Uform from "./Uform.vue";

export function useRole() {
  const form = reactive({
    keyword: undefined
  });
  const dataList = ref<Instance[]>([]);
  const loading = ref<boolean>(true);
  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true,
    pageSizes: [10, 20, 30, 40, 50, 100]
  });
  const columns: TableColumnList = [
    {
      label: "名称",
      prop: "name",
      minWidth: 120,
      cellRenderer: ({ row }) => (
        <div class="flex items-center">
          <el-icon class="mr-2 text-[#00ED64]">
            <iconify-icon-online icon="logos:mongodb-icon" />
          </el-icon>
          <el-text class="font-medium text-[15px]">{row.name}</el-text>
        </div>
      )
    },
    {
      label: "连接信息",
      prop: "host",
      minWidth: 200,
      cellRenderer: ({ row }) => (
        <div class="flex flex-col gap-2">
          <div class="flex items-center">
            <el-icon class="mr-2 text-[var(--el-color-info)]">
              <iconify-icon-online icon="ep:monitor" />
            </el-icon>
            <el-text class="font-mono">{row.host}</el-text>
          </div>
          <div class="flex items-center">
            <el-icon class="mr-2 text-[var(--el-color-info)]">
              <iconify-icon-online icon="ep:switch" />
            </el-icon>
            <el-text type="info" class="font-mono">
              {row.port}
            </el-text>
          </div>
        </div>
      )
    },
    {
      label: "环境/版本",
      prop: "env",
      minWidth: 180,
      cellRenderer: ({ row }) => (
        <div class="flex items-center gap-3">
          <div class="flex flex-col items-center bg-[var(--el-fill-color-light)] rounded-lg px-3 py-2">
            <div class="flex items-center gap-1.5">
              <span
                class={[
                  "font-medium",
                  row.env === 1
                    ? "text-[#f56c6c]"
                    : row.env === 2
                      ? "text-[#e6a23c]"
                      : row.env === 3
                        ? "text-[#67c23a]"
                        : "text-[#909399]"
                ]}
              >
                {row.env === 1
                  ? "生产环境"
                  : row.env === 2
                    ? "预发布"
                    : row.env === 3
                      ? "测试环境"
                      : "开发环境"}
              </span>
            </div>
            <div class="flex items-center gap-1.5 mt-1">
              <span class="text-[var(--el-text-color-secondary)] text-[13px]">
                MongoDB {row.version}
              </span>
            </div>
          </div>
        </div>
      )
    },
    {
      label: "连接地址",
      prop: "link",
      minWidth: 280,
      cellRenderer: ({ row }) => (
        <div class="flex items-center bg-[var(--el-fill-color-light)] rounded-lg px-3 py-2">
          <el-text class="font-mono text-[13px] mr-2" truncated>
            {row.link}
          </el-text>
          <el-button
            link
            type="primary"
            class="!ml-auto"
            onClick={e => {
              e.stopPropagation();
              navigator.clipboard
                .writeText(row.link)
                .then(() => {
                  message("复制成功", { type: "success" });
                })
                .catch(() => {
                  message("复制失败", { type: "error" });
                });
            }}
          >
            <el-icon>
              <iconify-icon-online icon="ep:document-copy" />
            </el-icon>
          </el-button>
        </div>
      )
    },
    {
      label: "管理员",
      prop: "admin_user",
      minWidth: 150,
      cellRenderer: ({ row }) => (
        <div class="flex items-center">
          <el-icon class="mr-2 text-[var(--el-color-info)]">
            <iconify-icon-online icon="ep:user" />
          </el-icon>
          <el-text class="text-[14px]">{row.admin_user}</el-text>
        </div>
      )
    },
    {
      label: "备注",
      prop: "remark",
      minWidth: 150,
      cellRenderer: ({ row }) => (
        <div class="py-1">
          {row.remark ? (
            <el-text class="text-[13px] leading-normal">{row.remark}</el-text>
          ) : (
            <el-text class="text-[13px] text-[var(--el-text-color-disabled)] italic">
              暂无备注信息
            </el-text>
          )}
        </div>
      )
    },
    {
      label: "时间信息",
      prop: "created_at",
      minWidth: 200,
      cellRenderer: ({ row }) => (
        <div class="flex flex-col gap-2">
          <div class="flex items-center text-[var(--el-text-color-secondary)]">
            <el-icon class="mr-1.5">
              <iconify-icon-online icon="ep:calendar" />
            </el-icon>
            <el-text class="text-[13px]">
              创建于 {dayjs(row.created_at).format("YYYY-MM-DD HH:mm")}
            </el-text>
          </div>
          <div class="flex items-center text-[var(--el-text-color-secondary)]">
            <el-icon class="mr-1.5">
              <iconify-icon-online icon="ep:refresh" />
            </el-icon>
            <el-text class="text-[13px]">
              更新于 {dayjs(row.updated_at).format("YYYY-MM-DD HH:mm")}
            </el-text>
          </div>
        </div>
      )
    },
    {
      label: "操作",
      width: 180,
      cellRenderer: ({ row }) => (
        <div class="flex items-center justify-center gap-2">
          <el-button
            type="primary"
            link
            class="flex items-center !px-2"
            onClick={() => editFunc(row)}
          >
            <el-icon>
              <iconify-icon-online icon="ep:edit" />
            </el-icon>
            <span class="ml-1">编辑</span>
          </el-button>
          <el-divider direction="vertical" />
          <el-button
            type="danger"
            link
            class="flex items-center !px-2"
            onClick={() => deleteFunc(row)}
          >
            <el-icon>
              <iconify-icon-online icon="ep:delete" />
            </el-icon>
            <span class="ml-1">删除</span>
          </el-button>
        </div>
      )
    }
  ];

  function deleteFunc(row: Instance) {
    addDialog({
      title: "删除实例",
      width: 480,
      draggable: true,
      closeOnClickModal: false,
      contentRenderer: () => (
        <div class="flex flex-col gap-3">
          <div class="flex items-center gap-2">
            <el-icon class="text-[var(--el-color-danger)] text-xl">
              <iconify-icon-online icon="ep:warning-filled" />
            </el-icon>
            <span class="text-[var(--el-text-color-regular)] text-[15px]">
              删除后数据将无法恢复，请谨慎操作！
            </span>
          </div>
          <div class="flex flex-col gap-2 bg-[var(--el-fill-color-light)] p-4 rounded-lg">
            <div class="flex items-center gap-2">
              <el-icon class="text-[#00ED64] text-lg">
                <iconify-icon-online icon="logos:mongodb-icon" />
              </el-icon>
              <span class="font-medium text-[15px]">{row.name}</span>
            </div>
            <div class="flex items-center gap-2 text-[var(--el-text-color-regular)] text-[13px]">
              <el-icon>
                <iconify-icon-online icon="ep:connection" />
              </el-icon>
              <span class="font-mono">
                {row.host}:{row.port}
              </span>
            </div>
            {row.remark && (
              <div class="flex items-start gap-2 text-[var(--el-text-color-secondary)] text-[13px] mt-1">
                <el-icon style="margin-top: 3px">
                  <iconify-icon-online icon="ep:info-filled" />
                </el-icon>
                <span class="break-all">{row.remark}</span>
              </div>
            )}
          </div>
        </div>
      ),
      beforeSure: done => {
        loading.value = true;
        deleteInstanceAPI(row.id)
          .then(res => {
            if (res.success) {
              message(res.msg, { type: "success" });
              onSearch();
              done();
            } else {
              message(res.msg, { type: "error" });
            }
          })
          .catch(error => {
            message("删除失败：" + error, { type: "error" });
          })
          .finally(() => {
            loading.value = false;
          });
      }
    });
  }
  const editForm = ref<InstanceForm>();
  const childrenRef = ref(null);
  function addFunc() {
    editForm.value = {
      name: "",
      host: "",
      port: 27017,
      link: "",
      admin_user: "",
      admin_password: "",
      version: "",
      env: 3,
      remark: ""
    };
    addDrawer({
      headerRenderer: ({ titleId, titleClass }) => (
        <div class="flex flex-row justify-between">
          <h4 id={titleId} class={titleClass}>
            添加实例
          </h4>
        </div>
      ),
      contentRenderer: () =>
        h(Uform, {
          formInline: {
            form: editForm.value
          },
          ref: childrenRef
        }),
      beforeSure: done => {
        if (childrenRef.value.ruleFormRef) {
          childrenRef.value.ruleFormRef.validate(valid => {
            if (valid) {
              createInstanceAPI(editForm.value)
                .then(res => {
                  if (res.success) {
                    message(res.msg, { type: "success" });
                    done();
                    // 重置表单数据
                    editForm.value = undefined;
                    onSearch();
                  } else {
                    message(res.msg, { type: "error" });
                  }
                })
                .catch(error => {
                  message(error, { type: "error" });
                });
            } else {
              message("请检查表单", { type: "error" });
            }
          });
        } else {
          message("请检查表单", { type: "error" });
        }
      }
    });
  }
  function editFunc(row: Instance) {
    editForm.value = {
      name: row.name,
      host: row.host,
      port: row.port,
      link: row.link,
      admin_user: row.admin_user,
      admin_password: "",
      version: row.version,
      env: row.env,
      remark: row.remark
    };
    addDrawer({
      headerRenderer: ({ titleId, titleClass }) => (
        <div class="flex flex-row justify-between">
          <h4 id={titleId} class={titleClass}>
            更新实例 {row.name}
          </h4>
        </div>
      ),
      contentRenderer: () =>
        h(Uform, {
          formInline: {
            form: editForm.value
          },
          ref: childrenRef
        }),
      beforeSure: done => {
        if (childrenRef.value.ruleFormRef) {
          childrenRef.value.ruleFormRef.validate(valid => {
            if (valid) {
              updateInstanceAPI(row.id, editForm.value)
                .then(res => {
                  if (res.success) {
                    message(res.msg, { type: "success" });
                    done();
                    // 重置表单数据
                    editForm.value = undefined;
                    onSearch();
                  } else {
                    message(res.msg, { type: "error" });
                  }
                })
                .catch(error => {
                  message(error, { type: "error" });
                });
            } else {
              message("请检查表单", { type: "error" });
            }
          });
        } else {
          message("请检查表单", { type: "error" });
        }
      }
    });
  }

  function handleSizeChange(val: number) {
    pagination.pageSize = val;
    onSearch();
  }

  function handleCurrentChange(val: number) {
    pagination.currentPage = val;
    onSearch();
  }

  async function onSearch() {
    loading.value = true;
    getInstancesAPI({
      page: pagination.currentPage,
      limit: pagination.pageSize,
      keyword: form.keyword
    })
      .then(res => {
        if (res.success) {
          dataList.value = res.data;
          pagination.total = res.count;
          pagination.pageSize = pagination.pageSize;
          pagination.currentPage = pagination.currentPage;
        } else {
          dataList.value = [];
          pagination.total = 0;
          pagination.pageSize = pagination.pageSize;
          pagination.currentPage = pagination.currentPage;
        }
      })
      .catch(() => {
        message("请求失败", { type: "error" });
      })
      .finally(() => {
        loading.value = false;
      });
  }

  const resetForm = (formEl: FormInstance | undefined) => {
    if (!formEl) return;
    formEl.resetFields();
    onSearch();
  };

  onMounted(() => {
    onSearch();
  });

  return {
    form,
    loading,
    columns,
    dataList,
    pagination,
    onSearch,
    resetForm,
    handleSizeChange,
    handleCurrentChange,
    addFunc
  };
}
