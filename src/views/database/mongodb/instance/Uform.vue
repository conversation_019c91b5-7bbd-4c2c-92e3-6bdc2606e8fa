<template>
  <div>
    <el-card shadow="never" :form="newFormInline.form">
      <el-form
        ref="ruleFormRef"
        label-width="auto"
        style="max-width: 600px"
        :model="newFormInline.form"
        label-position="top"
        status-icon
      >
        <el-form-item
          label="名称"
          prop="name"
          show-word-limit
          maxlength="255"
          :rules="[
            { required: true, message: '请输入名称', trigger: 'blur' },
            { max: 255, message: '名称长度不能超过255个字符', trigger: 'blur' }
          ]"
        >
          <el-input v-model="newFormInline.form.name" />
        </el-form-item>
        <el-form-item
          label="地址"
          prop="host"
          show-word-limit
          maxlength="255"
          :rules="[
            { required: true, message: '请输入地址', trigger: 'blur' },
            { max: 255, message: '名称长度不能超过255个字符', trigger: 'blur' }
          ]"
        >
          <el-input v-model="newFormInline.form.host" />
        </el-form-item>
        <el-form-item label="端口" prop="port">
          <el-input-number
            v-model="newFormInline.form.port"
            step-strictly
            size="large"
          />
        </el-form-item>
        <el-form-item
          label="环境"
          prop="env"
          :rules="[
            {
              required: true,
              message: '请选择环境',
              trigger: 'change'
            }
          ]"
        >
          <el-select v-model="newFormInline.form.env" placeholder="请选择环境">
            <el-option label="生产" :value="1" />
            <el-option label="预发布" :value="2" />
            <el-option label="测试" :value="3" />
            <el-option label="开发" :value="4" />
          </el-select>
        </el-form-item>
        <el-form-item
          label="管理用户名"
          prop="admin_user"
          :rules="[
            {
              required: true,
              message: '请输入用户名',
              trigger: 'blur'
            },
            {
              max: 255,
              message: '用户名长度不能超过255个字符',
              trigger: 'blur'
            }
          ]"
        >
          <el-input
            v-model="newFormInline.form.admin_user"
            placeholder="管理用户名"
            show-word-limit
            maxlength="255"
          />
        </el-form-item>
        <el-form-item
          label="管理密码"
          prop="admin_password"
          :rules="[
            { max: 255, message: '密码长度不能超过255个字符', trigger: 'blur' }
          ]"
        >
          <el-input
            v-model="newFormInline.form.admin_password"
            type="password"
            show-password
            placeholder="留空表示不修改"
          />
        </el-form-item>
        <el-form-item
          label="备注"
          prop="remark"
          :rules="[
            { max: 255, message: '名称长度不能超过255个字符', trigger: 'blur' }
          ]"
        >
          <el-input
            v-model="newFormInline.form.remark"
            type="textarea"
            show-word-limit
            maxlength="255"
          />
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { defineProps, ref } from "vue";
import type { InstanceForm as Form } from "@/api/database/mongodb/instance";
import type { FormInstance } from "element-plus";
export interface FormProps {
  formInline: {
    form: Form;
  };
}
const props = withDefaults(defineProps<FormProps>(), {
  formInline: () => ({ form: undefined })
});

const newFormInline = ref(props.formInline);
const ruleFormRef = ref<FormInstance>();
defineExpose({ ruleFormRef });
</script>
