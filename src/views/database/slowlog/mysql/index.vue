<template>
  <div class="main">
    <el-card class="search-card">
      <el-form
        ref="formRef"
        :inline="true"
        :model="form"
        class="search-form"
        @submit.prevent
      >
        <el-form-item label="存储" prop="id">
          <el-select
            v-model="form.id"
            placeholder="请选择存储"
            clearable
            :loading="storeLoading"
            @change="onSearch"
          >
            <el-option
              v-for="item in storeOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
              <div class="store-option">
                <span>{{ item.name }}</span>
                <span class="store-option-info">
                  {{ item.host }}:{{ item.port }} / {{ item.db }}
                </span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="关键字" prop="keyword">
          <el-input
            v-model="form.keyword"
            placeholder="请输入关键字"
            clearable
            @keyup.enter="onSearch"
          />
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            :icon="useRenderIcon('ri:search-line')"
            :loading="slowLogLoading || statisticsLoading || dmlLoading"
            class="search-button"
            @click="onSearch"
          >
            搜索
          </el-button>
          <el-button
            :icon="useRenderIcon(Refresh)"
            class="reset-button"
            @click="resetForm(formRef)"
          >
            重置
          </el-button>
        </el-form-item>
        <el-form-item>
          <el-button :icon="useRenderIcon('ep:view')" @click="handleStatistics">
            查看统计趋势
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-tabs
      v-model="activeTab"
      class="mysql-tabs"
      @tab-change="handleTabChange"
    >
      <!-- 慢查询日志标签页 -->
      <el-tab-pane label="慢查询日志" name="slowLog">
        <PureTableBar
          title="MySQL 慢查询日志列表"
          :columns="slowLogColumns"
          @refresh="onSearch"
        >
          <template v-slot="{ size, dynamicColumns }">
            <pure-table
              row-key="id"
              align-whole="left"
              table-layout="auto"
              :loading="slowLogLoading"
              :size="size"
              :minHeight="500"
              :data="slowLogDataList"
              :columns="dynamicColumns"
              :pagination="{ ...slowLogPagination, size }"
              :header-cell-style="{
                fontWeight: '600'
              }"
              :row-style="{
                cursor: 'pointer',
                transition: 'background-color 0.2s ease'
              }"
              :cell-style="{
                padding: '12px 10px'
              }"
              border
              stripe
              highlight-current-row
              @page-size-change="handleSlowLogSizeChange"
              @page-current-change="handleSlowLogCurrentChange"
            >
              <template #empty>
                <el-empty
                  description="暂无数据"
                  :image-size="120"
                  style="padding: 40px 0"
                />
              </template>
            </pure-table>
          </template>
        </PureTableBar>
      </el-tab-pane>

      <!-- DML 日志标签页 -->
      <el-tab-pane label="DML 日志" name="dml">
        <PureTableBar
          title="MySQL DML 日志列表"
          :columns="dmlColumns"
          @refresh="onSearch"
        >
          <template v-slot="{ size, dynamicColumns }">
            <pure-table
              row-key="id"
              align-whole="left"
              table-layout="auto"
              :loading="dmlLoading"
              :size="size"
              :minHeight="500"
              :data="dmlDataList"
              :columns="dynamicColumns"
              :pagination="{ ...dmlPagination, size }"
              :header-cell-style="{
                fontWeight: '600'
              }"
              :row-style="{
                cursor: 'pointer',
                transition: 'background-color 0.2s ease'
              }"
              :cell-style="{
                padding: '12px 10px'
              }"
              border
              stripe
              highlight-current-row
              @page-size-change="handleDmlSizeChange"
              @page-current-change="handleDmlCurrentChange"
            >
              <template #empty>
                <el-empty
                  description="暂无数据"
                  :image-size="120"
                  style="padding: 40px 0"
                />
              </template>
            </pure-table>
          </template>
        </PureTableBar>
      </el-tab-pane>
      <!-- 慢查询统计标签页 -->
      <el-tab-pane label="慢查询统计" name="statistics">
        <PureTableBar
          title="MySQL 慢查询统计列表"
          :columns="statisticsColumns"
          @refresh="onSearch"
        >
          <template v-slot="{ size, dynamicColumns }">
            <pure-table
              row-key="id"
              align-whole="left"
              table-layout="auto"
              :loading="statisticsLoading"
              :size="size"
              :minHeight="500"
              :data="statisticsDataList"
              :columns="dynamicColumns"
              :pagination="{ ...statisticsPagination, size }"
              :header-cell-style="{
                fontWeight: '600'
              }"
              :row-style="{
                cursor: 'pointer',
                transition: 'background-color 0.2s ease'
              }"
              :cell-style="{
                padding: '12px 10px'
              }"
              border
              stripe
              highlight-current-row
              @page-size-change="handleStatisticsSizeChange"
              @page-current-change="handleStatisticsCurrentChange"
            >
              <template #empty>
                <el-empty
                  description="暂无数据"
                  :image-size="120"
                  style="padding: 40px 0"
                />
              </template>
            </pure-table>
          </template>
        </PureTableBar>
      </el-tab-pane>
    </el-tabs>

    <!-- 自定义回到顶部按钮，不使用 ElBacktop -->
    <div
      v-show="showBackTop"
      class="back-to-top"
      title="回到顶部"
      @click="scrollToTop"
    >
      <el-icon><ArrowUp /></el-icon>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import useHook from "./hook";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { PureTableBar } from "@/components/RePureTableBar";
import Refresh from "@iconify-icons/ep/refresh";
import { ArrowUp } from "@element-plus/icons-vue";
import { addDialog } from "@/components/ReDialog";
import { h } from "vue";
import StatDBType from "../StatDBType.vue";

defineOptions({
  name: "SlowlogMySQL"
});

const formRef = ref();
const showBackTop = ref(false);

const handleStatistics = () => {
  addDialog({
    title: "查看 MySQL 慢查询统计趋势",
    width: "80%",
    hideFooter: true,
    contentRenderer: () =>
      h(StatDBType, {
        id: form.id,
        dbType: "mysql"
      })
  });
};

const {
  form,
  storeOptions,
  storeLoading,
  activeTab,
  // 慢查询日志
  slowLogDataList,
  slowLogLoading,
  slowLogPagination,
  slowLogColumns,
  handleSlowLogSizeChange,
  handleSlowLogCurrentChange,
  // 慢查询统计
  statisticsDataList,
  statisticsLoading,
  statisticsPagination,
  statisticsColumns,
  handleStatisticsSizeChange,
  handleStatisticsCurrentChange,
  // DML 日志
  dmlDataList,
  dmlLoading,
  dmlPagination,
  dmlColumns,
  handleDmlSizeChange,
  handleDmlCurrentChange,
  // 共用方法
  onSearch,
  resetForm,
  handleTabChange
} = useHook();

// 监听滚动事件，控制回到顶部按钮的显示
const handleScroll = () => {
  showBackTop.value = window.scrollY > 300;
};

// 回到顶部
const scrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: "smooth"
  });
};

onMounted(() => {
  window.addEventListener("scroll", handleScroll);
});

onUnmounted(() => {
  window.removeEventListener("scroll", handleScroll);
});
</script>

<style scoped lang="scss">
.main {
  position: relative;
  padding: 20px;
  border-radius: 12px;
}

.search-card {
  margin-bottom: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 8%);
  transition: box-shadow 0.3s ease;

  &:hover {
    box-shadow: 0 6px 20px rgb(0 0 0 / 12%);
  }
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;

  :deep(.el-form-item) {
    margin-bottom: 0;
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: #606266;
  }

  .el-input {
    width: 350px;

    :deep(input),
    :deep(.el-input__wrapper) {
      border-radius: 8px;

      &:focus {
        border-color: #409eff;
        box-shadow: 0 0 0 1px rgb(64 158 255 / 20%);
      }
    }
  }

  .el-select {
    width: 350px;

    :deep(.el-input__wrapper) {
      border-radius: 8px;

      &:focus {
        border-color: #409eff;
        box-shadow: 0 0 0 1px rgb(64 158 255 / 20%);
      }
    }
  }
}

.store-option {
  display: flex;
  flex-direction: column;

  .store-option-info {
    margin-top: 4px;
    font-size: 12px;
    color: #909399;
  }
}

.search-button,
.reset-button {
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
    transform: translateY(-2px);
  }
}

.search-button {
  color: white;
  background: linear-gradient(135deg, #409eff 0%, #4facff 100%);
  border: none;
  box-shadow: 0 4px 12px rgb(64 158 255 / 30%);
}

.reset-button {
  color: #606266;
  border-color: #dcdfe6;
}

.mysql-tabs {
  :deep(.el-tabs__header) {
    margin-bottom: 20px;
  }

  :deep(.el-tabs__item) {
    height: 40px;
    padding: 0 20px;
    font-size: 16px;
    font-weight: 500;
    line-height: 40px;
    transition: all 0.3s ease;

    &.is-active {
      font-weight: 600;
      color: #409eff;
    }

    &:hover {
      color: #409eff;
    }
  }

  :deep(.el-tabs__active-bar) {
    height: 3px;
    background: linear-gradient(90deg, #409eff 0%, #4facff 100%);
    border-radius: 3px;
  }
}

.back-to-top {
  position: fixed;
  right: 40px;
  bottom: 40px;
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  color: white;
  cursor: pointer;
  background-color: #409eff;
  border-radius: 50%;
  box-shadow: 0 2px 12px rgb(0 0 0 / 15%);
  transition: all 0.3s ease;

  &:hover {
    background-color: #66b1ff;
    box-shadow: 0 4px 16px rgb(0 0 0 / 20%);
    transform: translateY(-5px);
  }

  .el-icon {
    font-size: 20px;
  }
}
</style>
