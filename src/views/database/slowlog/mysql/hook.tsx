import { reactive, ref, onMounted } from "vue";
import type { FormInstance } from "element-plus";
import {
  getMySQLSlowLogsAPI,
  getMySQLSlowLogStatisticsAPI,
  getMySQLDMLLogsAPI,
  type MySQLSlowLog,
  type MySQLSlowLogStatistics,
  type MySQLDMLLog
} from "@/api/database/slowlog/mysql";
import {
  getAllSlowlogStoresAPI,
  type SlowlogStore
} from "@/api/database/slowlog/store";
import dayjs from "dayjs";
import { addDrawer } from "@/components/ReDrawer";
import { message } from "@/utils/message";

function useHook() {
  // 共用的表单
  const form = reactive({
    id: undefined as number | undefined,
    keyword: undefined as string | undefined
  });

  // 存储列表
  const storeOptions = ref<SlowlogStore[]>([]);
  const storeLoading = ref(false);

  // 获取所有存储选项
  function fetchStoreOptions() {
    storeLoading.value = true;
    getAllSlowlogStoresAPI()
      .then(res => {
        if (res.success && res.data) {
          storeOptions.value = res.data;
          // 如果有数据，默认选择第一个
          if (res.data.length > 0 && form.id === undefined) {
            form.id = res.data[0].id;
            // 初始加载数据
            onSearch();
          }
        }
      })
      .finally(() => {
        storeLoading.value = false;
      });
  }

  // 标签页
  const activeTab = ref("slowLog");

  // 慢查询日志数据
  const slowLogDataList = ref<MySQLSlowLog[]>([]);
  const slowLogLoading = ref(false);
  const slowLogPagination = reactive({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    pageSizes: [10, 20, 50, 100]
  });

  // 慢查询统计数据
  const statisticsDataList = ref<MySQLSlowLogStatistics[]>([]);
  const statisticsLoading = ref(false);
  const statisticsPagination = reactive({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    pageSizes: [10, 20, 50, 100]
  });

  // DML 日志数据
  const dmlDataList = ref<MySQLDMLLog[]>([]);
  const dmlLoading = ref(false);
  const dmlPagination = reactive({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    pageSizes: [10, 20, 50, 100]
  });

  // 慢查询日志表格列定义 - 分类合并后
  const slowLogColumns: TableColumnList = [
    {
      label: "数据库信息",
      prop: "db_info",
      cellRenderer: ({ row }) => (
        <div class="db-info">
          {row.db_instance_id && (
            <div class="flex items-center mb-1">
              <el-tag size="small" type="info" class="mr-1">
                实例ID
              </el-tag>
              <span class="text-[#606266]">{row.db_instance_id}</span>
            </div>
          )}
          {row.idc_type && (
            <div class="flex items-center mb-1">
              <el-tag size="small" type="info" class="mr-1">
                IDC
              </el-tag>
              <span class="text-[#606266]">{row.idc_type}</span>
            </div>
          )}
          <div class="flex items-center mb-1">
            <el-tag size="small" type="success" class="mr-1">
              标题
            </el-tag>
            <span class="text-[#606266]">{row.db_title}</span>
          </div>
          <div class="flex items-center">
            <el-tag size="small" type="warning" class="mr-1">
              项目
            </el-tag>
            <span class="text-[#606266]">{row.db_project_name}</span>
          </div>
        </div>
      )
    },
    {
      label: "连接信息",
      prop: "connection_info",
      cellRenderer: ({ row }) => (
        <div class="connection-info">
          <div class="flex items-center mb-1">
            <el-tag size="small" type="info" class="mr-1">
              主机
            </el-tag>
            <span class="text-[#606266]">
              {row.db_private_ip}:{row.db_port}
            </span>
          </div>
          <div class="flex items-center mb-1">
            <el-tag size="small" type="success" class="mr-1">
              数据库
            </el-tag>
            <span class="text-[#606266]">{row.db_name}</span>
          </div>
          <div class="flex items-center">
            <el-tag size="small" type="warning" class="mr-1">
              客户端
            </el-tag>
            <span class="text-[#606266]">{row.query_client_ip}</span>
          </div>
        </div>
      )
    },
    {
      label: "查询信息",
      prop: "query_info",
      cellRenderer: ({ row }) => (
        <div class="query-info">
          <div class="flex items-center mb-1">
            <el-tag size="small" type="info" class="mr-1">
              类型
            </el-tag>
            <span class="text-[#606266]">{row.query_type}</span>
          </div>
          <div class="flex items-center mb-1">
            <el-tag size="small" type="success" class="mr-1">
              用户
            </el-tag>
            <span class="text-[#606266]">{row.query_user}</span>
          </div>
          <div class="flex items-center">
            <el-tag size="small" type="warning" class="mr-1">
              次数
            </el-tag>
            <span class="text-[#606266]">{row.query_count}</span>
          </div>
        </div>
      )
    },
    {
      label: "性能指标",
      prop: "performance",
      cellRenderer: ({ row }) => (
        <div class="performance-metrics">
          <div class="flex items-center mb-1">
            <el-tag size="small" type="danger" class="mr-1">
              查询时间
            </el-tag>
            <span class="text-[#606266]">{row.query_time}s</span>
          </div>
          <div class="flex items-center mb-1">
            <el-tag size="small" type="warning" class="mr-1">
              锁时间
            </el-tag>
            <span class="text-[#606266]">{row.query_lock_time}</span>
          </div>
          <div class="flex items-center">
            <el-tag size="small" type="info" class="mr-1">
              行数
            </el-tag>
            <span class="text-[#606266]">
              发送: {row.rows_sent} / 扫描: {row.rows_examined}
            </span>
          </div>
        </div>
      )
    },
    {
      label: "SQL信息",
      prop: "sql_info",
      showOverflowTooltip: true,
      cellRenderer: ({ row }) => (
        <div class="sql-info">
          <div class="flex items-center mb-1">
            <el-tag size="small" type="info" class="mr-1">
              模板
            </el-tag>
            <el-tooltip
              class="box-item"
              effect="dark"
              content={row.query_template}
              placement="top"
            >
              <span class="text-[#606266] truncate max-w-[250px]">
                {row.query_template}
              </span>
            </el-tooltip>
          </div>
          <div class="flex items-center">
            <el-tag size="small" type="warning" class="mr-1">
              示例
            </el-tag>
            <el-tooltip
              class="box-item"
              effect="dark"
              content={row.query_sample}
              placement="top"
            >
              <span class="text-[#606266] truncate max-w-[250px]">
                {row.query_sample}
              </span>
            </el-tooltip>
          </div>
        </div>
      )
    },
    {
      label: "时间信息",
      prop: "time_info",
      cellRenderer: ({ row }) => (
        <div class="time-info">
          <div class="flex items-center mb-1">
            <el-tag size="small" type="info" class="mr-1">
              开始
            </el-tag>
            <span class="text-[#606266]">
              {dayjs(row.query_start_time).format("YYYY-MM-DD HH:mm:ss")}
            </span>
          </div>
          <div class="flex items-center">
            <el-tag size="small" type="warning" class="mr-1">
              创建
            </el-tag>
            <span class="text-[#606266]">
              {dayjs(row.create_time).format("YYYY-MM-DD HH:mm:ss")}
            </span>
          </div>
        </div>
      )
    },
    {
      label: "操作",
      prop: "action",
      width: 120,
      cellRenderer: ({ row }) => (
        <div class="action-column">
          <el-button type="primary" link onClick={() => viewSlowLog(row)}>
            <el-icon class="mr-1">
              <el-icon-view />
            </el-icon>
            查看
          </el-button>
        </div>
      )
    }
  ];

  // 查看慢查询日志详情
  function viewSlowLog(row: MySQLSlowLog) {
    addDrawer({
      title: "慢查询日志详情",
      size: "50%",
      contentRenderer: () => {
        return (
          <div class="slow-log-detail p-4">
            <el-descriptions title="数据库信息" column={2} border class="mb-4">
              <el-descriptions-item label="实例ID">
                {row.db_instance_id}
              </el-descriptions-item>
              <el-descriptions-item label="数据库标题">
                {row.db_title}
              </el-descriptions-item>
              <el-descriptions-item label="项目名称">
                {row.db_project_name}
              </el-descriptions-item>
              {row.idc_type && (
                <el-descriptions-item label="IDC类型">
                  {row.idc_type}
                </el-descriptions-item>
              )}
              <el-descriptions-item label="云类型">
                {row.cloud_type}
              </el-descriptions-item>
            </el-descriptions>

            <el-descriptions title="连接信息" column={2} border class="mb-4">
              <el-descriptions-item label="数据库IP">
                {row.db_private_ip}
              </el-descriptions-item>
              <el-descriptions-item label="数据库端口">
                {row.db_port}
              </el-descriptions-item>
              <el-descriptions-item label="数据库名称">
                {row.db_name}
              </el-descriptions-item>
              <el-descriptions-item label="客户端IP">
                {row.query_client_ip}
              </el-descriptions-item>
            </el-descriptions>

            <el-descriptions title="查询信息" column={2} border class="mb-4">
              <el-descriptions-item label="查询类型">
                {row.query_type}
              </el-descriptions-item>
              <el-descriptions-item label="查询用户">
                {row.query_user}
              </el-descriptions-item>
              <el-descriptions-item label="查询次数">
                {row.query_count}
              </el-descriptions-item>
              <el-descriptions-item label="查询开始时间">
                {dayjs(row.query_start_time).format("YYYY-MM-DD HH:mm:ss")}
              </el-descriptions-item>
            </el-descriptions>

            <el-descriptions title="性能指标" column={2} border class="mb-4">
              <el-descriptions-item label="查询时间(秒)">
                <el-tag type="danger">{row.query_time}</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="锁定时间">
                <el-tag type="warning">{row.query_lock_time}</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="发送行数">
                {row.rows_sent}
              </el-descriptions-item>
              <el-descriptions-item label="扫描行数">
                {row.rows_examined}
              </el-descriptions-item>
            </el-descriptions>

            <el-descriptions title="SQL信息" column={1} border class="mb-4">
              <el-descriptions-item label="查询模板" label-align="right">
                <div class="whitespace-pre-wrap break-all">
                  {row.query_template}
                </div>
              </el-descriptions-item>
              <el-descriptions-item label="查询示例" label-align="right">
                <div class="whitespace-pre-wrap break-all">
                  {row.query_sample}
                </div>
              </el-descriptions-item>
            </el-descriptions>

            <el-descriptions title="时间信息" column={2} border>
              <el-descriptions-item label="创建时间">
                {dayjs(row.create_time).format("YYYY-MM-DD HH:mm:ss")}
              </el-descriptions-item>
              <el-descriptions-item label="更新时间">
                {dayjs(row.update_time).format("YYYY-MM-DD HH:mm:ss")}
              </el-descriptions-item>
            </el-descriptions>
          </div>
        );
      }
    });
  }

  // 慢查询统计表格列定义 - 分类合并后
  const statisticsColumns: TableColumnList = [
    {
      label: "数据库信息",
      prop: "db_info",
      cellRenderer: ({ row }) => (
        <div class="db-info">
          {row.db_instance_id && (
            <div class="flex items-center mb-1">
              <el-tag size="small" type="info" class="mr-1">
                实例
              </el-tag>
              <span class="text-[#606266]">{row.db_instance_id}</span>
            </div>
          )}
          {row.idc_type && (
            <div class="flex items-center mb-1">
              <el-tag size="small" type="info" class="mr-1">
                IDC
              </el-tag>
              <span class="text-[#606266]">{row.idc_type}</span>
            </div>
          )}
          <div class="flex items-center mb-1">
            <el-tag size="small" type="success" class="mr-1">
              标题
            </el-tag>
            <span class="text-[#606266]">{row.db_title}</span>
          </div>
          <div class="flex items-center">
            <el-tag size="small" type="warning" class="mr-1">
              项目
            </el-tag>
            <span class="text-[#606266]">{row.db_project_name}</span>
          </div>
        </div>
      )
    },
    {
      label: "连接信息",
      prop: "connection_info",
      cellRenderer: ({ row }) => (
        <div class="connection-info">
          <div class="flex items-center mb-1">
            <el-tag size="small" type="info" class="mr-1">
              主机
            </el-tag>
            <span class="text-[#606266]">
              {row.db_private_ip}:{row.db_port}
            </span>
          </div>
        </div>
      )
    },
    {
      label: "次数",
      prop: "total_count",
      cellRenderer: ({ row }) => (
        <div class="time-info">
          <el-text type="danger" style="font-weight: bold">
            {row.total_count}
          </el-text>
        </div>
      )
    },
    {
      label: "创建时间",
      prop: "create_time",
      cellRenderer: ({ row }) => (
        <div class="time-info">
          <span class="text-[#606266]">
            {dayjs(row.create_time).format("YYYY-MM-DD HH:mm:ss")}
          </span>
        </div>
      )
    },
    {
      label: "操作",
      prop: "action",
      width: 120,
      cellRenderer: ({ row }) => (
        <div class="action-column">
          <el-button type="primary" link onClick={() => viewStatistics(row)}>
            <el-icon class="mr-1">
              <el-icon-view />
            </el-icon>
            查看
          </el-button>
        </div>
      )
    }
  ];

  // 查看慢查询统计详情
  function viewStatistics(row: MySQLSlowLogStatistics) {
    addDrawer({
      title: "慢查询统计详情",
      size: "50%",
      contentRenderer: () => {
        return (
          <div class="statistics-detail p-4">
            <el-descriptions title="数据库信息" column={2} border class="mb-4">
              <el-descriptions-item label="实例ID">
                {row.db_instance_id}
              </el-descriptions-item>
              <el-descriptions-item label="数据库标题">
                {row.db_title}
              </el-descriptions-item>
              <el-descriptions-item label="项目名称">
                {row.db_project_name}
              </el-descriptions-item>
              {row.idc_type && (
                <el-descriptions-item label="IDC类型">
                  {row.idc_type}
                </el-descriptions-item>
              )}
              <el-descriptions-item label="云类型">
                {row.cloud_type}
              </el-descriptions-item>
            </el-descriptions>

            <el-descriptions title="连接信息" column={2} border class="mb-4">
              <el-descriptions-item label="数据库IP">
                {row.db_private_ip}
              </el-descriptions-item>
              <el-descriptions-item label="数据库端口">
                {row.db_port}
              </el-descriptions-item>
              {row.db_name && (
                <el-descriptions-item label="数据库名称">
                  {row.db_name}
                </el-descriptions-item>
              )}
            </el-descriptions>

            <el-descriptions title="查询统计" column={1} border class="mb-4">
              <el-descriptions-item label="查询总次数">
                <el-tag type="danger" size="large">
                  {row.total_count}
                </el-tag>
              </el-descriptions-item>
            </el-descriptions>

            <el-descriptions title="性能统计" column={3} border class="mb-4">
              {row.avg_query_time !== undefined && (
                <el-descriptions-item label="平均查询时间(秒)">
                  <el-tag type="danger">{row.avg_query_time}</el-tag>
                </el-descriptions-item>
              )}
              {row.max_query_time !== undefined && (
                <el-descriptions-item label="最大查询时间(秒)">
                  <el-tag type="danger">{row.max_query_time}</el-tag>
                </el-descriptions-item>
              )}
              {row.min_query_time !== undefined && (
                <el-descriptions-item label="最小查询时间(秒)">
                  <el-tag type="success">{row.min_query_time}</el-tag>
                </el-descriptions-item>
              )}
              {row.avg_lock_time && (
                <el-descriptions-item label="平均锁定时间">
                  <el-tag type="warning">{row.avg_lock_time}</el-tag>
                </el-descriptions-item>
              )}
              {row.avg_rows_sent !== undefined && (
                <el-descriptions-item label="平均发送行数">
                  {row.avg_rows_sent}
                </el-descriptions-item>
              )}
              {row.avg_rows_examined !== undefined && (
                <el-descriptions-item label="平均扫描行数">
                  {row.avg_rows_examined}
                </el-descriptions-item>
              )}
            </el-descriptions>

            {row.query_template && (
              <el-descriptions title="SQL信息" column={1} border class="mb-4">
                <el-descriptions-item label="查询模板" label-align="right">
                  <div class="whitespace-pre-wrap break-all">
                    {row.query_template}
                  </div>
                </el-descriptions-item>
              </el-descriptions>
            )}

            <el-descriptions title="时间信息" column={2} border>
              <el-descriptions-item label="创建时间">
                {dayjs(row.create_time).format("YYYY-MM-DD HH:mm:ss")}
              </el-descriptions-item>
              {row.update_time && (
                <el-descriptions-item label="更新时间">
                  {dayjs(row.update_time).format("YYYY-MM-DD HH:mm:ss")}
                </el-descriptions-item>
              )}
            </el-descriptions>
          </div>
        );
      }
    });
  }

  // DML 日志表格列定义 - 分类合并后
  const dmlColumns: TableColumnList = [
    {
      label: "数据库信息",
      prop: "db_info",
      cellRenderer: ({ row }) => (
        <div class="db-info">
          {row.db_instance_id && (
            <div class="flex items-center mb-1">
              <el-tag size="small" type="info" class="mr-1">
                实例
              </el-tag>
              <span class="text-[#606266]">{row.db_instance_id}</span>
            </div>
          )}
          {row.idc_type && (
            <div class="flex items-center mb-1">
              <el-tag size="small" type="info" class="mr-1">
                IDC
              </el-tag>
              <span class="text-[#606266]">{row.idc_type}</span>
            </div>
          )}
          <div class="flex items-center mb-1">
            <el-tag size="small" type="success" class="mr-1">
              标题
            </el-tag>
            <span class="text-[#606266]">{row.db_title}</span>
          </div>
          <div class="flex items-center">
            <el-tag size="small" type="warning" class="mr-1">
              项目
            </el-tag>
            <span class="text-[#606266]">{row.db_project_name}</span>
          </div>
        </div>
      )
    },
    {
      label: "连接信息",
      prop: "connection_info",
      cellRenderer: ({ row }) => (
        <div class="connection-info">
          <div class="flex items-center mb-1">
            <el-tag size="small" type="info" class="mr-1">
              实例
            </el-tag>
            <span class="text-[#606266]">
              {row.db_private_ip}:{row.db_port}
            </span>
          </div>
          <div class="flex items-center mb-1">
            <el-tag size="small" type="info" class="mr-1">
              离线库
            </el-tag>
            <span class="text-[#606266]">
              {row.offlinedb_ip}:{row.offlinedb_port}
            </span>
          </div>
          <div class="flex items-center mb-1">
            <el-tag size="small" type="success" class="mr-1">
              数据库
            </el-tag>
            <span class="text-[#606266]">{row.db_name}</span>
          </div>

          <div class="flex items-center mb-1">
            <el-tag size="small" type="success" class="mr-1">
              表
            </el-tag>
            <span class="text-[#606266]">{row.tb_name}</span>
          </div>
        </div>
      )
    },
    {
      label: "查询信息",
      prop: "query_info",
      cellRenderer: ({ row }) => (
        <div class="query-info">
          <div class="flex items-center mb-1">
            <el-tag size="small" type="info" class="mr-1">
              类型
            </el-tag>
            <span class="text-[#606266]">{row.query_type}</span>
          </div>
          <div class="flex items-center">
            <el-tag size="small" type="success" class="mr-1">
              用户
            </el-tag>
            <span class="text-[#606266]">{row.query_user}</span>
          </div>
          <div class="flex items-center">
            <el-tag size="small" type="warning" class="mr-1">
              客户端
            </el-tag>
            <span class="text-[#606266]">{row.query_client_ip}</span>
          </div>
        </div>
      )
    },
    {
      label: "性能指标",
      prop: "performance",
      cellRenderer: ({ row }) => (
        <div class="performance-metrics">
          <div class="flex items-center mb-1">
            <el-tag size="small" type="danger" class="mr-1">
              查询时间
            </el-tag>
            <span class="text-[#606266]">{row.query_time}s</span>
          </div>
          <div class="flex items-center mb-1">
            <el-tag size="small" type="warning" class="mr-1">
              锁时间
            </el-tag>
            <span class="text-[#606266]">{row.query_lock_time}</span>
          </div>
          <div class="flex items-center">
            <el-tag size="small" type="info" class="mr-1">
              查询次数/扫描行
            </el-tag>
            <span class="text-[#606266]">
              {row.query_count}/{row.rows_examined}
            </span>
          </div>
        </div>
      )
    },
    {
      label: "SQL信息",
      prop: "sql_info",
      width: 300,
      showOverflowTooltip: true,
      cellRenderer: ({ row }) => (
        <div class="sql-info">
          <div class="flex items-center mb-1">
            <el-tag size="small" type="info" class="mr-1">
              查询语句
            </el-tag>
            <el-tooltip
              class="box-item"
              effect="dark"
              content={row.query_command}
              placement="top"
            >
              <span class="text-[#606266] truncate max-w-[250px]">
                {row.query_command}
              </span>
            </el-tooltip>
          </div>
          <div class="flex items-center">
            <el-tag size="small" type="warning" class="mr-1">
              sql优化建议
            </el-tag>
            <el-tooltip
              class="box-item"
              effect="dark"
              content={row.sql_optimizer}
              placement="top"
            >
              <span class="text-[#606266] truncate max-w-[250px]">
                {row.sql_optimizer}
              </span>
            </el-tooltip>
          </div>
        </div>
      )
    },
    {
      label: "时间信息",
      prop: "time_info",
      cellRenderer: ({ row }) => (
        <div class="time-info">
          {row.query_start_time && (
            <div class="flex items-center mb-1">
              <el-tag size="small" type="info" class="mr-1">
                开始
              </el-tag>
              <span class="text-[#606266]">
                {dayjs(row.query_start_time).format("YYYY-MM-DD HH:mm:ss")}
              </span>
            </div>
          )}
          <div class="flex items-center">
            <el-tag size="small" type="warning" class="mr-1">
              创建
            </el-tag>
            <span class="text-[#606266]">
              {dayjs(row.create_time).format("YYYY-MM-DD HH:mm:ss")}
            </span>
          </div>
          <div class="flex items-center">
            <el-tag size="small" type="warning" class="mr-1">
              更新
            </el-tag>
            <span class="text-[#606266]">
              {dayjs(row.update_time).format("YYYY-MM-DD HH:mm:ss")}
            </span>
          </div>
        </div>
      )
    },
    {
      label: "操作",
      prop: "action",
      width: 120,
      cellRenderer: ({ row }) => (
        <div class="action-column">
          <el-button type="primary" link onClick={() => viewDmlLog(row)}>
            <el-icon class="mr-1">
              <el-icon-view />
            </el-icon>
            查看
          </el-button>
        </div>
      )
    }
  ];

  // 查看 DML 日志详情
  function viewDmlLog(row: MySQLDMLLog) {
    addDrawer({
      title: "DML日志详情",
      size: "50%",
      contentRenderer: () => {
        return (
          <div class="dml-log-detail p-4">
            <el-descriptions title="数据库信息" column={2} border class="mb-4">
              <el-descriptions-item label="实例ID">
                {row.db_instance_id}
              </el-descriptions-item>
              <el-descriptions-item label="数据库标题">
                {row.db_title}
              </el-descriptions-item>
              <el-descriptions-item label="项目名称">
                {row.db_project_name}
              </el-descriptions-item>
              {row.idc_type && (
                <el-descriptions-item label="IDC类型">
                  {row.idc_type}
                </el-descriptions-item>
              )}
              <el-descriptions-item label="云类型">
                {row.cloud_type}
              </el-descriptions-item>
            </el-descriptions>

            <el-descriptions title="连接信息" column={2} border class="mb-4">
              <el-descriptions-item label="数据库IP">
                {row.db_private_ip}
              </el-descriptions-item>
              <el-descriptions-item label="数据库端口">
                {row.db_port}
              </el-descriptions-item>
              <el-descriptions-item label="数据库名称">
                {row.db_name}
              </el-descriptions-item>
              <el-descriptions-item label="表名">
                {row.tb_name}
              </el-descriptions-item>
              <el-descriptions-item label="离线库IP">
                {row.offlinedb_ip}
              </el-descriptions-item>
              <el-descriptions-item label="离线库端口">
                {row.offlinedb_port}
              </el-descriptions-item>
              <el-descriptions-item label="客户端IP">
                {row.query_client_ip}
              </el-descriptions-item>
            </el-descriptions>

            <el-descriptions title="查询信息" column={2} border class="mb-4">
              <el-descriptions-item label="查询类型">
                {row.query_type}
              </el-descriptions-item>
              <el-descriptions-item label="查询用户">
                {row.query_user}
              </el-descriptions-item>
              <el-descriptions-item label="查询次数">
                {row.query_count}
              </el-descriptions-item>
              {row.query_start_time && (
                <el-descriptions-item label="查询开始时间">
                  {dayjs(row.query_start_time).format("YYYY-MM-DD HH:mm:ss")}
                </el-descriptions-item>
              )}
            </el-descriptions>

            <el-descriptions title="性能指标" column={2} border class="mb-4">
              <el-descriptions-item label="查询时间(秒)">
                <el-tag type="danger">{row.query_time}</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="锁定时间">
                <el-tag type="warning">{row.query_lock_time}</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="扫描行数">
                {row.rows_examined}
              </el-descriptions-item>
              <el-descriptions-item label="查询次数">
                <el-tag type="success">{row.query_count}</el-tag>
              </el-descriptions-item>
            </el-descriptions>

            <el-descriptions title="SQL信息" column={1} border class="mb-4">
              <el-descriptions-item label="查询命令" label-align="right">
                <div class="whitespace-pre-wrap break-all">
                  {row.query_command}
                </div>
              </el-descriptions-item>
              <el-descriptions-item label="SQL优化建议" label-align="right">
                <div class="whitespace-pre-wrap break-all">
                  {row.sql_optimizer || "暂无优化建议"}
                </div>
              </el-descriptions-item>
            </el-descriptions>

            <el-descriptions title="时间信息" column={2} border>
              <el-descriptions-item label="创建时间">
                {dayjs(row.create_time).format("YYYY-MM-DD HH:mm:ss")}
              </el-descriptions-item>
              <el-descriptions-item label="更新时间">
                {dayjs(row.update_time).format("YYYY-MM-DD HH:mm:ss")}
              </el-descriptions-item>
            </el-descriptions>
          </div>
        );
      }
    });
  }

  // 处理慢查询日志分页大小变化
  function handleSlowLogSizeChange(val: number) {
    slowLogPagination.pageSize = val;
    slowLogPagination.currentPage = 1;
    onSearchSlowLog();
  }

  // 处理慢查询日志当前页变化
  function handleSlowLogCurrentChange(val: number) {
    slowLogPagination.currentPage = val;
    onSearchSlowLog();
  }

  // 处理慢查询统计分页大小变化
  function handleStatisticsSizeChange(val: number) {
    statisticsPagination.pageSize = val;
    statisticsPagination.currentPage = 1;
    onSearchStatistics();
  }

  // 处理慢查询统计当前页变化
  function handleStatisticsCurrentChange(val: number) {
    statisticsPagination.currentPage = val;
    onSearchStatistics();
  }

  // 处理 DML 日志分页大小变化
  function handleDmlSizeChange(val: number) {
    dmlPagination.pageSize = val;
    dmlPagination.currentPage = 1;
    onSearchDml();
  }

  // 处理 DML 日志当前页变化
  function handleDmlCurrentChange(val: number) {
    dmlPagination.currentPage = val;
    onSearchDml();
  }

  // 处理标签页切换
  function handleTabChange(tab: string) {
    activeTab.value = tab;
    // 切换标签页时，重新加载对应的数据
    onSearch();
  }

  // 搜索
  function onSearch() {
    if (!form.id) {
      return;
    }

    if (activeTab.value === "slowLog") {
      onSearchSlowLog();
    } else if (activeTab.value === "statistics") {
      onSearchStatistics();
    } else if (activeTab.value === "dml") {
      onSearchDml();
    }
  }

  // 重置表单
  function resetForm(formEl: FormInstance | undefined) {
    if (!formEl) return;
    formEl.resetFields();
    // 如果有存储选项，选择第一个
    if (storeOptions.value.length > 0) {
      form.id = storeOptions.value[0].id;
    }
    onSearch();
  }

  // 搜索慢查询日志
  function onSearchSlowLog() {
    if (!form.id) return;

    slowLogLoading.value = true;
    getMySQLSlowLogsAPI(form.id, {
      keyword: form.keyword,
      page: slowLogPagination.currentPage,
      limit: slowLogPagination.pageSize
    })
      .then(res => {
        if (res.success) {
          slowLogDataList.value = res.data || [];
          slowLogPagination.total = res.count || 0;
        } else {
          slowLogDataList.value = [];
          slowLogPagination.total = 0;
          message(res.msg, { type: "error" });
        }
      })
      .finally(() => {
        slowLogLoading.value = false;
      });
  }

  // 搜索慢查询统计
  function onSearchStatistics() {
    if (!form.id) return;

    statisticsLoading.value = true;
    getMySQLSlowLogStatisticsAPI(form.id, {
      keyword: form.keyword,
      page: statisticsPagination.currentPage,
      limit: statisticsPagination.pageSize
    })
      .then(res => {
        if (res.success) {
          statisticsDataList.value = res.data || [];
          statisticsPagination.total = res.count || 0;
        } else {
          statisticsDataList.value = [];
          statisticsPagination.total = 0;
          message(res.msg, { type: "error" });
        }
      })
      .finally(() => {
        statisticsLoading.value = false;
      });
  }

  // 搜索 DML 日志
  function onSearchDml() {
    if (!form.id) return;

    dmlLoading.value = true;
    getMySQLDMLLogsAPI(form.id, {
      keyword: form.keyword,
      page: dmlPagination.currentPage,
      limit: dmlPagination.pageSize
    })
      .then(res => {
        if (res.success) {
          dmlDataList.value = res.data || [];
          dmlPagination.total = res.count || 0;
        } else {
          dmlDataList.value = [];
          dmlPagination.total = 0;
          message(res.msg, { type: "error" });
        }
      })
      .finally(() => {
        dmlLoading.value = false;
      });
  }

  // 组件挂载时获取存储选项
  onMounted(() => {
    fetchStoreOptions();
  });

  return {
    form,
    storeOptions,
    storeLoading,
    activeTab,
    // 慢查询日志
    slowLogDataList,
    slowLogLoading,
    slowLogPagination,
    slowLogColumns,
    handleSlowLogSizeChange,
    handleSlowLogCurrentChange,
    // 慢查询统计
    statisticsDataList,
    statisticsLoading,
    statisticsPagination,
    statisticsColumns,
    handleStatisticsSizeChange,
    handleStatisticsCurrentChange,
    // DML 日志
    dmlDataList,
    dmlLoading,
    dmlPagination,
    dmlColumns,
    handleDmlSizeChange,
    handleDmlCurrentChange,
    // 共用方法
    onSearch,
    resetForm,
    handleTabChange
  };
}

export default useHook;
