<template>
  <div class="main">
    <el-card class="search-card">
      <el-form
        ref="formRef"
        :inline="true"
        :model="form"
        class="search-form"
        @submit.prevent
      >
        <el-form-item label="存储" prop="id">
          <el-select
            v-model="form.id"
            placeholder="请选择存储"
            clearable
            :loading="storeLoading"
            @change="onSearch"
          >
            <el-option
              v-for="item in storeOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
              <div class="store-option">
                <span>{{ item.name }}</span>
                <span class="store-option-info">
                  {{ item.host }}:{{ item.port }} / {{ item.db }}
                </span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="关键字" prop="keyword">
          <el-input
            v-model="form.keyword"
            placeholder="请输入关键字"
            clearable
            @keyup.enter="onSearch"
          />
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            :icon="useRenderIcon('ri:search-line')"
            :loading="slowLogsLoading || statisticsLoading"
            class="search-button"
            @click="onSearch"
          >
            搜索
          </el-button>
          <el-button
            :icon="useRenderIcon(Refresh)"
            class="reset-button"
            @click="resetForm(formRef)"
          >
            重置
          </el-button>
        </el-form-item>
        <el-form-item>
          <el-button :icon="useRenderIcon('ep:view')" @click="handleStatistics">
            查看统计趋势
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-tabs
      v-model="activeTab"
      class="mongodb-tabs"
      @tab-change="handleTabChange"
    >
      <!-- MongoDB 慢查询日志标签页 -->
      <el-tab-pane label="慢查询日志" name="slowLogs">
        <PureTableBar
          title="MongoDB 慢查询日志列表"
          :columns="slowLogsColumns"
          @refresh="onSearch"
        >
          <template v-slot="{ size, dynamicColumns }">
            <pure-table
              row-key="id"
              align-whole="left"
              table-layout="auto"
              :loading="slowLogsLoading"
              :size="size"
              :minHeight="500"
              :data="slowLogsDataList"
              :columns="dynamicColumns"
              :pagination="{ ...slowLogsPagination, size }"
              :header-cell-style="{
                fontWeight: '600'
              }"
              :row-style="{
                cursor: 'pointer',
                transition: 'background-color 0.2s ease'
              }"
              :cell-style="{
                padding: '12px 10px'
              }"
              border
              stripe
              highlight-current-row
              @page-size-change="handleSlowLogsSizeChange"
              @page-current-change="handleSlowLogsCurrentChange"
            >
              <template #empty>
                <el-empty
                  description="暂无数据"
                  :image-size="120"
                  style="padding: 40px 0"
                />
              </template>
            </pure-table>
          </template>
        </PureTableBar>
      </el-tab-pane>

      <!-- MongoDB 慢查询统计标签页 -->
      <el-tab-pane label="慢查询统计" name="statistics">
        <PureTableBar
          title="MongoDB 慢查询统计列表"
          :columns="statisticsColumns"
          @refresh="onSearch"
        >
          <template v-slot="{ size, dynamicColumns }">
            <pure-table
              row-key="id"
              align-whole="left"
              table-layout="auto"
              :loading="statisticsLoading"
              :size="size"
              :minHeight="500"
              :data="statisticsDataList"
              :columns="dynamicColumns"
              :pagination="{ ...statisticsPagination, size }"
              :header-cell-style="{
                fontWeight: '600'
              }"
              :row-style="{
                cursor: 'pointer',
                transition: 'background-color 0.2s ease'
              }"
              :cell-style="{
                padding: '12px 10px'
              }"
              border
              stripe
              highlight-current-row
              @page-size-change="handleStatisticsSizeChange"
              @page-current-change="handleStatisticsCurrentChange"
            >
              <template #empty>
                <el-empty
                  description="暂无数据"
                  :image-size="120"
                  style="padding: 40px 0"
                />
              </template>
            </pure-table>
          </template>
        </PureTableBar>
      </el-tab-pane>
    </el-tabs>

    <!-- 自定义回到顶部按钮，不使用 ElBacktop -->
    <div v-show="showBackTop" class="back-top" @click="scrollToTop">
      <el-icon :size="20">
        <component :is="useRenderIcon('ri:arrow-up-line')" />
      </el-icon>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, h } from "vue";
import useHook from "./hook";
import { PureTableBar } from "@/components/RePureTableBar";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { Refresh } from "@element-plus/icons-vue";
import type { FormInstance } from "element-plus";
import StatDBType from "../StatDBType.vue";
import { addDialog } from "@/components/ReDialog";

// 引入 hook 中的方法和数据
const {
  form,
  storeOptions,
  storeLoading,
  activeTab,
  slowLogsDataList,
  slowLogsLoading,
  slowLogsPagination,
  slowLogsColumns,
  statisticsDataList,
  statisticsLoading,
  statisticsPagination,
  statisticsColumns,
  onSearch,
  handleTabChange,
  handleSlowLogsSizeChange,
  handleSlowLogsCurrentChange,
  handleStatisticsSizeChange,
  handleStatisticsCurrentChange,
  resetForm
} = useHook();

// 表单引用
const formRef = ref<FormInstance>();

// 回到顶部按钮显示控制
const showBackTop = ref(false);

// 监听滚动事件，控制回到顶部按钮的显示
function handleScroll() {
  showBackTop.value = window.scrollY > 300;
}

const handleStatistics = () => {
  addDialog({
    title: "查看 MongoDB 慢日志统计趋势",
    width: "80%",
    hideFooter: true,
    contentRenderer: () =>
      h(StatDBType, {
        id: form.id,
        dbType: "mongodb"
      })
  });
};

// 回到顶部
function scrollToTop() {
  window.scrollTo({
    top: 0,
    behavior: "smooth"
  });
}

onMounted(() => {
  window.addEventListener("scroll", handleScroll);
});

onUnmounted(() => {
  window.removeEventListener("scroll", handleScroll);
});
</script>

<style scoped lang="scss">
.main {
  position: relative;
  padding: 20px;
  border-radius: 12px;
}

.search-card {
  margin-bottom: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 8%);
  transition: box-shadow 0.3s ease;

  &:hover {
    box-shadow: 0 6px 20px rgb(0 0 0 / 12%);
  }
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;

  :deep(.el-form-item) {
    margin-bottom: 0;
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: #606266;
  }

  .el-input {
    width: 350px;

    :deep(input),
    :deep(.el-input__wrapper) {
      border-radius: 8px;

      &:focus {
        border-color: #409eff;
        box-shadow: 0 0 0 1px rgb(64 158 255 / 20%);
      }
    }
  }

  .el-select {
    width: 350px;

    :deep(.el-input__wrapper) {
      border-radius: 8px;

      &:focus {
        border-color: #409eff;
        box-shadow: 0 0 0 1px rgb(64 158 255 / 20%);
      }
    }
  }
}

.search-button {
  margin-right: 8px;
}

.store-option {
  display: flex;
  flex-direction: column;

  .store-option-info {
    margin-top: 4px;
    font-size: 12px;
    color: #909399;
  }
}

.mongodb-tabs {
  margin-top: 16px;
}

.back-top {
  position: fixed;
  right: 40px;
  bottom: 40px;
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  cursor: pointer;
  background-color: #fff;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
  transition: all 0.3s;

  &:hover {
    background-color: #f2f6fc;
  }
}
</style>
