import { reactive, ref, onMounted } from "vue";
import type { FormInstance } from "element-plus";
import {
  getMongoDBSlowLogsAPI,
  getMongoDBSlowLogStatisticsAPI,
  type MongoDBSlowLog,
  type MongoDBSlowLogStatistics
} from "@/api/database/slowlog/mongodb";
import {
  getAllSlowlogStoresAPI,
  type SlowlogStore
} from "@/api/database/slowlog/store";
import dayjs from "dayjs";
import { addDrawer } from "@/components/ReDrawer";
import { message } from "@/utils/message";

function useHook() {
  // 共用的表单
  const form = reactive({
    id: undefined as number | undefined,
    keyword: undefined as string | undefined
  });

  // 存储列表
  const storeOptions = ref<SlowlogStore[]>([]);
  const storeLoading = ref(false);

  // 获取所有存储选项
  function fetchStoreOptions() {
    storeLoading.value = true;
    getAllSlowlogStoresAPI()
      .then(res => {
        if (res.success) {
          storeOptions.value = res.data;
          // 如果有数据，默认选择第一个
          if (res.data.length > 0 && form.id === undefined) {
            form.id = res.data[0].id;
            // 初始加载数据
            onSearch();
          }
        }
      })
      .finally(() => {
        storeLoading.value = false;
      });
  }

  // 标签页
  const activeTab = ref("slowLogs");

  // MongoDB 慢查询日志数据
  const slowLogsDataList = ref<MongoDBSlowLog[]>([]);
  const slowLogsLoading = ref(false);
  const slowLogsPagination = reactive({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    pageSizes: [10, 20, 50, 100]
  });

  // MongoDB 慢查询统计数据
  const statisticsDataList = ref<MongoDBSlowLogStatistics[]>([]);
  const statisticsLoading = ref(false);
  const statisticsPagination = reactive({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    pageSizes: [10, 20, 50, 100]
  });

  // MongoDB 慢查询日志表格列定义
  const slowLogsColumns: TableColumnList = [
    {
      label: "数据库信息",
      prop: "db_info",
      cellRenderer: ({ row }) => (
        <div class="db-info">
          <div class="flex items-center mb-1">
            <el-tag size="small" type="success" class="mr-1">
              标题
            </el-tag>
            <span class="text-[#606266]">{row.db_title}</span>
          </div>
          <div class="flex items-center mb-1">
            <el-tag size="small" type="warning" class="mr-1">
              项目
            </el-tag>
            <span class="text-[#606266]">{row.db_project_name}</span>
          </div>
          <div class="flex items-center">
            <el-tag size="small" type="info" class="mr-1">
              云类型
            </el-tag>
            <span class="text-[#606266]">{row.cloud_type}</span>
          </div>
        </div>
      )
    },
    {
      label: "连接信息",
      prop: "connection_info",
      cellRenderer: ({ row }) => (
        <div class="connection-info">
          <div class="flex items-center mb-1">
            <el-tag size="small" type="info" class="mr-1">
              IP
            </el-tag>
            <span class="text-[#606266]">{row.db_private_ip}</span>
          </div>
          <div class="flex items-center mb-1">
            <el-tag size="small" type="info" class="mr-1">
              端口
            </el-tag>
            <span class="text-[#606266]">{row.db_port}</span>
          </div>
          <div class="flex items-center">
            <el-tag size="small" type="info" class="mr-1">
              数据库
            </el-tag>
            <span class="text-[#606266]">{row.query_database}</span>
          </div>
        </div>
      )
    },
    {
      label: "查询信息",
      prop: "query_info",
      cellRenderer: ({ row }) => (
        <div class="query-info">
          <div class="flex items-center mb-1">
            <el-tag size="small" type="primary" class="mr-1">
              类型
            </el-tag>
            <span class="text-[#606266]">{row.query_type}</span>
          </div>
          <div class="flex items-center mb-1">
            <el-tag size="small" type="danger" class="mr-1">
              耗时
            </el-tag>
            <span class="text-[#606266]">{row.query_time} ms</span>
          </div>
          <div class="flex items-center">
            <el-tag size="small" type="warning" class="mr-1">
              集合
            </el-tag>
            <el-tooltip
              content={row.query_collection}
              placement="top"
              effect="light"
              show-after={200}
            >
              <span class="text-[#606266] truncate max-w-[200px]">
                {row.query_collection}
              </span>
            </el-tooltip>
          </div>
        </div>
      )
    },
    {
      label: "查询时间",
      prop: "query_start_time",
      width: 180,
      formatter: ({ query_start_time }) =>
        dayjs(query_start_time).format("YYYY-MM-DD HH:mm:ss")
    },
    {
      label: "操作",
      width: 120,
      slot: "operation",
      cellRenderer: ({ row }) => (
        <div class="operation-buttons">
          <el-button
            size="small"
            type="primary"
            text
            onClick={() => viewSlowLog(row)}
          >
            查看
          </el-button>
        </div>
      )
    }
  ];

  // MongoDB 慢查询统计表格列定义
  const statisticsColumns: TableColumnList = [
    {
      label: "数据库信息",
      prop: "db_info",
      cellRenderer: ({ row }) => (
        <div class="db-info">
          <div class="flex items-center mb-1">
            <el-tag size="small" type="success" class="mr-1">
              标题
            </el-tag>
            <span class="text-[#606266]">{row.db_title}</span>
          </div>
          <div class="flex items-center mb-1">
            <el-tag size="small" type="warning" class="mr-1">
              项目
            </el-tag>
            <span class="text-[#606266]">{row.db_project_name}</span>
          </div>
        </div>
      )
    },
    {
      label: "连接信息",
      prop: "connection_info",
      cellRenderer: ({ row }) => (
        <div class="connection-info">
          <div class="flex items-center mb-1">
            <el-tag size="small" type="info" class="mr-1">
              IP
            </el-tag>
            <span class="text-[#606266]">{row.db_private_ip}</span>
          </div>
          <div class="flex items-center">
            <el-tag size="small" type="info" class="mr-1">
              端口
            </el-tag>
            <span class="text-[#606266]">{row.db_port}</span>
          </div>
        </div>
      )
    },
    {
      label: "慢查询总数",
      prop: "total_count",
      width: 120
    },
    {
      label: "创建时间",
      prop: "create_time",
      width: 180,
      formatter: ({ create_time }) =>
        dayjs(create_time).format("YYYY-MM-DD HH:mm:ss")
    },
    {
      label: "操作",
      width: 120,
      slot: "operation",
      cellRenderer: ({ row }) => (
        <div class="operation-buttons">
          <el-button
            size="small"
            type="primary"
            text
            onClick={() => viewStatistic(row)}
          >
            查看
          </el-button>
        </div>
      )
    }
  ];

  // 查询数据
  function onSearch() {
    if (form.id === undefined) return;

    switch (activeTab.value) {
      case "slowLogs":
        fetchSlowLogs();
        break;
      case "statistics":
        fetchStatistics();
        break;
    }
  }

  // 获取 MongoDB 慢查询日志数据
  function fetchSlowLogs() {
    if (form.id === undefined) return;

    slowLogsLoading.value = true;
    getMongoDBSlowLogsAPI(form.id, {
      page: slowLogsPagination.currentPage,
      limit: slowLogsPagination.pageSize,
      keyword: form.keyword
    })
      .then(res => {
        if (res.success) {
          slowLogsDataList.value = res.data;
          slowLogsPagination.total = res.count || 0;
        } else {
          slowLogsDataList.value = [];
          slowLogsPagination.total = 0;
          message(res.msg, { type: "error" });
        }
      })
      .finally(() => {
        slowLogsLoading.value = false;
      });
  }

  // 获取 MongoDB 慢查询统计数据
  function fetchStatistics() {
    if (form.id === undefined) return;

    statisticsLoading.value = true;
    getMongoDBSlowLogStatisticsAPI(form.id, {
      page: statisticsPagination.currentPage,
      limit: statisticsPagination.pageSize,
      keyword: form.keyword
    })
      .then(res => {
        if (res.success) {
          statisticsDataList.value = res.data;
          statisticsPagination.total = res.count || 0;
        } else {
          statisticsDataList.value = [];
          statisticsPagination.total = 0;
          message(res.msg, { type: "error" });
        }
      })
      .finally(() => {
        statisticsLoading.value = false;
      });
  }

  // 处理标签页切换
  function handleTabChange(tab: string) {
    activeTab.value = tab;
    onSearch();
  }

  // 处理分页大小变化 - 慢查询日志
  function handleSlowLogsSizeChange(val: number) {
    slowLogsPagination.pageSize = val;
    slowLogsPagination.currentPage = 1;
    fetchSlowLogs();
  }

  // 处理当前页变化 - 慢查询日志
  function handleSlowLogsCurrentChange(val: number) {
    slowLogsPagination.currentPage = val;
    fetchSlowLogs();
  }

  // 处理分页大小变化 - 统计
  function handleStatisticsSizeChange(val: number) {
    statisticsPagination.pageSize = val;
    statisticsPagination.currentPage = 1;
    fetchStatistics();
  }

  // 处理当前页变化 - 统计
  function handleStatisticsCurrentChange(val: number) {
    statisticsPagination.currentPage = val;
    fetchStatistics();
  }

  // 重置表单
  function resetForm(formEl: FormInstance | undefined) {
    if (!formEl) return;
    formEl.resetFields();
    if (storeOptions.value.length > 0) {
      form.id = storeOptions.value[0].id;
    }
    onSearch();
  }

  // 查看 MongoDB 慢查询日志详情
  function viewSlowLog(row: MongoDBSlowLog) {
    addDrawer({
      title: "MongoDB 慢查询日志详情",
      size: "50%",
      contentRenderer: () => {
        return (
          <div class="p-4">
            <el-descriptions title="数据库信息" column={3} border class="mb-4">
              <el-descriptions-item label="数据库标题">
                {row.db_title}
              </el-descriptions-item>
              <el-descriptions-item label="项目名称">
                {row.db_project_name}
              </el-descriptions-item>
              <el-descriptions-item label="云类型">
                {row.cloud_type}
              </el-descriptions-item>
            </el-descriptions>

            <el-descriptions title="连接信息" column={2} border class="mb-4">
              <el-descriptions-item label="数据库IP">
                {row.db_private_ip}
              </el-descriptions-item>
              <el-descriptions-item label="数据库端口">
                {row.db_port}
              </el-descriptions-item>
              <el-descriptions-item label="数据库名称">
                {row.query_database}
              </el-descriptions-item>
              <el-descriptions-item label="集合名称">
                {row.query_collection}
              </el-descriptions-item>
            </el-descriptions>

            <el-descriptions title="查询信息" column={2} border class="mb-4">
              <el-descriptions-item label="查询类型">
                <el-tag type="primary">{row.query_type}</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="查询耗时">
                <el-tag type="danger">{row.query_time} ms</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="扫描文档数">
                <el-tag type="warning">{row.query_docs_examined}</el-tag>
              </el-descriptions-item>
            </el-descriptions>

            <el-descriptions title="查询命令" column={1} border class="mb-4">
              <el-descriptions-item>
                <div class="whitespace-pre-wrap break-all bg-gray-50 p-3 rounded">
                  {row.query_command}
                </div>
              </el-descriptions-item>
            </el-descriptions>

            <el-descriptions title="时间信息" column={2} border>
              <el-descriptions-item label="查询开始时间">
                {dayjs(row.query_start_time).format("YYYY-MM-DD HH:mm:ss")}
              </el-descriptions-item>
              <el-descriptions-item label="创建时间">
                {dayjs(row.create_time).format("YYYY-MM-DD HH:mm:ss")}
              </el-descriptions-item>
            </el-descriptions>
          </div>
        );
      }
    });
  }

  // 查看 MongoDB 慢查询统计详情
  function viewStatistic(row: MongoDBSlowLogStatistics) {
    addDrawer({
      title: "MongoDB 慢查询统计详情",
      size: "50%",
      contentRenderer: () => {
        return (
          <div class="p-4">
            <el-descriptions title="数据库信息" column={2} border class="mb-4">
              <el-descriptions-item label="数据库标题">
                {row.db_title}
              </el-descriptions-item>
              <el-descriptions-item label="项目名称">
                {row.db_project_name}
              </el-descriptions-item>
            </el-descriptions>

            <el-descriptions title="连接信息" column={2} border class="mb-4">
              <el-descriptions-item label="数据库IP">
                {row.db_private_ip}
              </el-descriptions-item>
              <el-descriptions-item label="数据库端口">
                {row.db_port}
              </el-descriptions-item>
            </el-descriptions>

            <el-descriptions title="统计信息" column={1} border class="mb-4">
              <el-descriptions-item label="慢查询总数">
                <el-tag type="danger">{row.total_count}</el-tag>
              </el-descriptions-item>
            </el-descriptions>

            <el-descriptions title="时间信息" column={1} border>
              <el-descriptions-item label="创建时间">
                {dayjs(row.create_time).format("YYYY-MM-DD HH:mm:ss")}
              </el-descriptions-item>
            </el-descriptions>
          </div>
        );
      }
    });
  }

  // 组件挂载时获取存储选项
  onMounted(() => {
    fetchStoreOptions();
  });

  return {
    form,
    storeOptions,
    storeLoading,
    activeTab,
    slowLogsDataList,
    slowLogsLoading,
    slowLogsPagination,
    slowLogsColumns,
    statisticsDataList,
    statisticsLoading,
    statisticsPagination,
    statisticsColumns,
    onSearch,
    handleTabChange,
    handleSlowLogsSizeChange,
    handleSlowLogsCurrentChange,
    handleStatisticsSizeChange,
    handleStatisticsCurrentChange,
    resetForm
  };
}

export default useHook;
