import { reactive, ref, onMounted } from "vue";
import type { FormInstance } from "element-plus";
import {
  getRedisBigKeysAPI,
  getRedisBigKeysStringStatisticAPI,
  type RedisBigKeys,
  type RedisBigKeysStringStatistic
} from "@/api/database/slowlog/redis";
import {
  getAllSlowlogStoresAPI,
  type SlowlogStore
} from "@/api/database/slowlog/store";
import dayjs from "dayjs";
import { addDrawer } from "@/components/ReDrawer";
import { message } from "@/utils/message";

function useHook() {
  // 共用的表单
  const form = reactive({
    id: undefined as number | undefined,
    keyword: undefined as string | undefined
  });

  // 存储列表
  const storeOptions = ref<SlowlogStore[]>([]);
  const storeLoading = ref(false);

  // 获取所有存储选项
  function fetchStoreOptions() {
    storeLoading.value = true;
    getAllSlowlogStoresAPI()
      .then(res => {
        if (res.success && res.data) {
          storeOptions.value = res.data;
          // 如果有数据，默认选择第一个
          if (res.data.length > 0 && form.id === undefined) {
            form.id = res.data[0].id;
            // 初始加载数据
            onSearch();
          }
        }
      })
      .finally(() => {
        storeLoading.value = false;
      });
  }

  // 标签页
  const activeTab = ref("bigKeys");

  // Redis 大键数据
  const bigKeysDataList = ref<RedisBigKeys[]>([]);
  const bigKeysLoading = ref(false);
  const bigKeysPagination = reactive({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    pageSizes: [10, 20, 50, 100]
  });

  // Redis String 大键统计数据
  const stringStatisticsDataList = ref<RedisBigKeysStringStatistic[]>([]);
  const stringStatisticsLoading = ref(false);
  const stringStatisticsPagination = reactive({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    pageSizes: [10, 20, 50, 100]
  });

  // Redis 大键表格列定义
  const bigKeysColumns: TableColumnList = [
    {
      label: "数据库信息",
      prop: "db_info",
      cellRenderer: ({ row }) => (
        <div class="db-info">
          <div class="flex items-center mb-1">
            <el-tag size="small" type="success" class="mr-1">
              标题
            </el-tag>
            <span class="text-[#606266]">{row.db_title}</span>
          </div>
          <div class="flex items-center mb-1">
            <el-tag size="small" type="warning" class="mr-1">
              项目
            </el-tag>
            <span class="text-[#606266]">{row.db_project_name}</span>
          </div>
          <div class="flex items-center">
            <el-tag size="small" type="info" class="mr-1">
              环境
            </el-tag>
            <span class="text-[#606266]">{row.db_env}</span>
          </div>
        </div>
      )
    },
    {
      label: "连接信息",
      prop: "connection_info",
      cellRenderer: ({ row }) => (
        <div class="connection-info">
          <div class="flex items-center mb-1">
            <el-tag size="small" type="info" class="mr-1">
              IP
            </el-tag>
            <span class="text-[#606266]">{row.db_ip}</span>
          </div>
          <div class="flex items-center mb-1">
            <el-tag size="small" type="info" class="mr-1">
              端口
            </el-tag>
            <span class="text-[#606266]">{row.db_port}</span>
          </div>
          <div class="flex items-center">
            <el-tag size="small" type="info" class="mr-1">
              DB
            </el-tag>
            <span class="text-[#606266]">{row.bigkeys_database}</span>
          </div>
        </div>
      )
    },
    {
      label: "键信息",
      prop: "key_info",
      cellRenderer: ({ row }) => (
        <div class="key-info">
          <div class="flex items-center mb-1">
            <el-tag size="small" type="primary" class="mr-1">
              类型
            </el-tag>
            <span class="text-[#606266]">{row.bigkeys_type}</span>
          </div>
          <div class="flex items-center mb-1">
            <el-tag size="small" type="danger" class="mr-1">
              大小
            </el-tag>
            <span class="text-[#606266]">{formatSize(row.bigkeys_size)}</span>
          </div>
          <div class="flex items-center">
            <el-tag size="small" type="warning" class="mr-1">
              键名
            </el-tag>
            <el-tooltip
              content={row.bigkeys_key}
              placement="top"
              effect="light"
              show-after={200}
            >
              <span class="text-[#606266] truncate max-w-[200px]">
                {row.bigkeys_key}
              </span>
            </el-tooltip>
          </div>
        </div>
      )
    },
    {
      label: "创建时间",
      prop: "create_time",
      width: 180,
      formatter: ({ create_time }) =>
        dayjs(create_time).format("YYYY-MM-DD HH:mm:ss")
    },
    {
      label: "操作",
      width: 120,
      slot: "operation",
      cellRenderer: ({ row }) => (
        <div class="operation-buttons">
          <el-button
            size="small"
            type="primary"
            text
            onClick={() => viewBigKey(row)}
          >
            查看
          </el-button>
        </div>
      )
    }
  ];

  // Redis String 大键统计表格列定义
  const stringStatisticsColumns: TableColumnList = [
    {
      label: "项目名称",
      prop: "db_project_name"
    },
    {
      label: "String 键总数",
      prop: "bigkeys_string_count",
      width: 120
    },
    {
      label: ">100KB",
      prop: "bigkeys_string_100kb_count",
      width: 100
    },
    {
      label: ">1MB",
      prop: "bigkeys_string_1mb_count",
      width: 100
    },
    {
      label: ">32MB",
      prop: "bigkeys_string_32mb_count",
      width: 100
    },
    {
      label: ">64MB",
      prop: "bigkeys_string_64mb_count",
      width: 100
    },
    {
      label: ">128MB",
      prop: "bigkeys_string_128mb_count",
      width: 100
    },
    {
      label: ">256MB",
      prop: "bigkeys_string_256mb_count",
      width: 100
    },
    {
      label: "创建时间",
      prop: "create_time",
      width: 280,
      formatter: ({ create_time }) =>
        dayjs(create_time).format("YYYY-MM-DD HH:mm:ss")
    },
    {
      label: "操作",
      width: 120,
      slot: "operation",
      cellRenderer: ({ row }) => (
        <div class="operation-buttons">
          <el-button
            size="small"
            type="primary"
            text
            onClick={() => viewStringStatistic(row)}
          >
            查看
          </el-button>
        </div>
      )
    }
  ];

  // 格式化大小
  function formatSize(size: number): string {
    if (size < 1024) {
      return size + " B";
    } else if (size < 1024 * 1024) {
      return (size / 1024).toFixed(2) + " KB";
    } else if (size < 1024 * 1024 * 1024) {
      return (size / (1024 * 1024)).toFixed(2) + " MB";
    } else {
      return (size / (1024 * 1024 * 1024)).toFixed(2) + " GB";
    }
  }

  // 查询数据
  function onSearch() {
    if (form.id === undefined) return;

    switch (activeTab.value) {
      case "bigKeys":
        fetchBigKeys();
        break;
      case "stringStatistics":
        fetchStringStatistics();
        break;
    }
  }

  // 获取 Redis 大键数据
  function fetchBigKeys() {
    if (form.id === undefined) return;

    bigKeysLoading.value = true;
    getRedisBigKeysAPI(form.id, {
      page: bigKeysPagination.currentPage,
      limit: bigKeysPagination.pageSize,
      keyword: form.keyword
    })
      .then(res => {
        if (res.success) {
          bigKeysDataList.value = res.data;
          bigKeysPagination.total = res.count || 0;
        } else {
          bigKeysDataList.value = [];
          bigKeysPagination.total = 0;
          message(res.msg, { type: "error" });
        }
      })
      .finally(() => {
        bigKeysLoading.value = false;
      });
  }

  // 获取 Redis String 大键统计数据
  function fetchStringStatistics() {
    if (form.id === undefined) return;

    stringStatisticsLoading.value = true;
    getRedisBigKeysStringStatisticAPI(form.id, {
      page: stringStatisticsPagination.currentPage,
      limit: stringStatisticsPagination.pageSize,
      keyword: form.keyword
    })
      .then(res => {
        if (res.success) {
          stringStatisticsDataList.value = res.data;
          stringStatisticsPagination.total = res.count || 0;
        } else {
          stringStatisticsDataList.value = [];
          stringStatisticsPagination.total = 0;
          message(res.msg, { type: "error" });
        }
      })
      .finally(() => {
        stringStatisticsLoading.value = false;
      });
  }

  // 处理标签页切换
  function handleTabChange(tab: string) {
    activeTab.value = tab;
    onSearch();
  }

  // 处理分页大小变化
  function handleBigKeysSizeChange(val: number) {
    bigKeysPagination.pageSize = val;
    bigKeysPagination.currentPage = 1;
    fetchBigKeys();
  }

  // 处理当前页变化
  function handleBigKeysCurrentChange(val: number) {
    bigKeysPagination.currentPage = val;
    fetchBigKeys();
  }

  // 处理分页大小变化 - String 统计
  function handleStringStatisticsSizeChange(val: number) {
    stringStatisticsPagination.pageSize = val;
    stringStatisticsPagination.currentPage = 1;
    fetchStringStatistics();
  }

  // 处理当前页变化 - String 统计
  function handleStringStatisticsCurrentChange(val: number) {
    stringStatisticsPagination.currentPage = val;
    fetchStringStatistics();
  }

  // 重置表单
  function resetForm(formEl: FormInstance | undefined) {
    if (!formEl) return;
    formEl.resetFields();
    if (storeOptions.value.length > 0) {
      form.id = storeOptions.value[0].id;
    }
    onSearch();
  }

  // 查看 Redis 大键详情
  function viewBigKey(row: RedisBigKeys) {
    addDrawer({
      title: "Redis 大键详情",
      size: "50%",
      contentRenderer: () => {
        return (
          <div class="p-4">
            <el-descriptions title="数据库信息" column={3} border class="mb-4">
              <el-descriptions-item label="数据库标题">
                {row.db_title}
              </el-descriptions-item>
              <el-descriptions-item label="项目名称">
                {row.db_project_name}
              </el-descriptions-item>
              <el-descriptions-item label="云类型">
                {row.cloud_type}
              </el-descriptions-item>
              <el-descriptions-item label="环境">
                {row.db_env}
              </el-descriptions-item>
            </el-descriptions>

            <el-descriptions title="连接信息" column={2} border class="mb-4">
              <el-descriptions-item label="数据库IP">
                {row.db_ip}
              </el-descriptions-item>
              <el-descriptions-item label="数据库端口">
                {row.db_port}
              </el-descriptions-item>
              <el-descriptions-item label="数据库索引">
                {row.bigkeys_database}
              </el-descriptions-item>
            </el-descriptions>

            <el-descriptions title="键信息" column={2} border class="mb-4">
              <el-descriptions-item label="键类型">
                <el-tag type="primary">{row.bigkeys_type}</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="键大小">
                <el-tag type="danger">{formatSize(row.bigkeys_size)}</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="键名" span={2}>
                <div class="whitespace-pre-wrap break-all">
                  {row.bigkeys_key}
                </div>
              </el-descriptions-item>
            </el-descriptions>

            <el-descriptions title="时间信息" column={1} border>
              <el-descriptions-item label="创建时间">
                {dayjs(row.create_time).format("YYYY-MM-DD HH:mm:ss")}
              </el-descriptions-item>
            </el-descriptions>
          </div>
        );
      }
    });
  }

  // 查看 Redis String 大键统计详情
  function viewStringStatistic(row: RedisBigKeysStringStatistic) {
    addDrawer({
      title: "Redis String 大键统计详情",
      size: "50%",
      contentRenderer: () => {
        return (
          <div class="p-4">
            <el-descriptions title="项目信息" column={1} border class="mb-4">
              <el-descriptions-item label="项目名称">
                {row.db_project_name}
              </el-descriptions-item>
            </el-descriptions>

            <el-descriptions
              title="String 键统计"
              column={2}
              border
              class="mb-4"
            >
              <el-descriptions-item label="String 键总数">
                <el-tag type="primary">{row.bigkeys_string_count}</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="大于 100KB 的键数">
                <el-tag type="warning">{row.bigkeys_string_100kb_count}</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="大于 1MB 的键数">
                <el-tag type="warning">{row.bigkeys_string_1mb_count}</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="大于 32MB 的键数">
                <el-tag type="danger">{row.bigkeys_string_32mb_count}</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="大于 64MB 的键数">
                <el-tag type="danger">{row.bigkeys_string_64mb_count}</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="大于 128MB 的键数">
                <el-tag type="danger">{row.bigkeys_string_128mb_count}</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="大于 256MB 的键数">
                <el-tag type="danger">{row.bigkeys_string_256mb_count}</el-tag>
              </el-descriptions-item>
            </el-descriptions>

            <el-descriptions title="时间信息" column={1} border>
              <el-descriptions-item label="创建时间">
                {dayjs(row.create_time).format("YYYY-MM-DD HH:mm:ss")}
              </el-descriptions-item>
            </el-descriptions>
          </div>
        );
      }
    });
  }

  // 组件挂载时获取存储选项
  onMounted(() => {
    fetchStoreOptions();
  });

  return {
    form,
    storeOptions,
    storeLoading,
    activeTab,
    bigKeysDataList,
    bigKeysLoading,
    bigKeysPagination,
    bigKeysColumns,
    stringStatisticsDataList,
    stringStatisticsLoading,
    stringStatisticsPagination,
    stringStatisticsColumns,
    onSearch,
    handleTabChange,
    handleBigKeysSizeChange,
    handleBigKeysCurrentChange,
    handleStringStatisticsSizeChange,
    handleStringStatisticsCurrentChange,
    resetForm,
    viewBigKey,
    viewStringStatistic
  };
}

export default useHook;
