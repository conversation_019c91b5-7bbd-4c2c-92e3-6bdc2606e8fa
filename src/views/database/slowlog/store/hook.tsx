import dayjs from "dayjs";
import type { PaginationProps } from "@pureadmin/table";
import { reactive, ref, onMounted, h } from "vue";
import { message } from "@/utils/message";

import { addDrawer } from "@/components/ReDrawer";
import { addDialog } from "@/components/ReDialog";
import Uform from "./Uform.vue";
import {
  getSlowlogStoresAPI,
  addSlowlogStoreAPI,
  updateSlowlogStoreAPI,
  deleteSlowlogStoreAPI,
  type SlowlogStore,
  type SlowlogStoreForm
} from "@/api/database/slowlog/store";

export function useSlowlogStore() {
  const form = reactive({
    keyword: undefined
  });
  const dataList = ref<SlowlogStore[]>([]);
  const loading = ref(false);
  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true,
    pageSizes: [10, 20, 30, 40, 50, 100]
  });
  const columns: TableColumnList = [
    {
      label: "名称",
      prop: "name",
      cellRenderer: ({ row }) => (
        <div class="name-column">
          <span class="font-bold text-[#303133]">{row.name}</span>
        </div>
      )
    },
    {
      label: "连接信息",
      prop: "host",
      cellRenderer: ({ row }) => (
        <div class="connection-info">
          <div class="flex items-center mb-1">
            <el-tag size="small" type="info" class="mr-1">
              主机
            </el-tag>
            <span class="text-[#606266]">
              {row.host}:{row.port}
            </span>
          </div>
          <div class="flex items-center">
            <el-tag size="small" type="success" class="mr-1">
              数据库
            </el-tag>
            <span class="text-[#606266]">{row.db}</span>
          </div>
        </div>
      )
    },
    {
      label: "用户",
      prop: "user",
      cellRenderer: ({ row }) => (
        <div class="user-column">
          <el-tag size="small" effect="plain" class="user-tag">
            {row.user}
          </el-tag>
        </div>
      )
    },
    {
      label: "时间信息",
      prop: "created_at",
      cellRenderer: ({ row }) => (
        <div class="time-info">
          <div class="flex items-center mb-1">
            <el-tag size="small" type="info" class="mr-1">
              创建
            </el-tag>
            <span class="text-[#606266]">
              {dayjs(row.created_at).format("YYYY-MM-DD HH:mm:ss")}
            </span>
          </div>
          <div class="flex items-center">
            <el-tag size="small" type="warning" class="mr-1">
              更新
            </el-tag>
            <span class="text-[#606266]">
              {dayjs(row.updated_at).format("YYYY-MM-DD HH:mm:ss")}
            </span>
          </div>
        </div>
      )
    },
    {
      label: "操作",
      prop: "action",
      cellRenderer: ({ row }) => (
        <div class="action-column">
          <el-button type="primary" link onClick={() => editFunc(row)}>
            <iconify-icon-online icon="ep:edit" class="mr-1" />
            编辑
          </el-button>
          <el-button type="danger" link onClick={() => deleteFunc(row)}>
            <iconify-icon-online icon="ep:delete" class="mr-1" />
            删除
          </el-button>
        </div>
      )
    }
  ];

  function deleteFunc(row: SlowlogStore) {
    addDialog({
      title: "",
      width: 500,
      sureBtnLoading: true,
      contentRenderer: () => (
        <p>
          确认删除：<b style="color:red"> {row.name}</b>
        </p>
      ),
      beforeSure: done => {
        deleteSlowlogStoreAPI(row.id)
          .then(res => {
            if (res.success) {
              message(res.msg, { type: "success" });
              onSearch();
              done();
            } else {
              message(res.msg, { type: "error" });
            }
          })
          .catch(error => {
            message("请求失败" + error, { type: "error" });
          })
          .finally(() => {
            loading.value = false;
          });
      }
    });
  }
  const editForm = ref<SlowlogStoreForm>();
  const childrenRef = ref(null);
  function addFunc() {
    editForm.value = {
      name: "",
      host: "",
      db: "",
      port: 3306,
      user: "",
      password: ""
    };
    addDrawer({
      headerRenderer: ({ titleId, titleClass }) => (
        <div class="flex flex-row justify-between">
          <h4 id={titleId} class={titleClass}>
            添加慢日志存储
          </h4>
        </div>
      ),
      contentRenderer: () =>
        h(Uform, {
          formInline: {
            form: editForm.value
          },
          ref: childrenRef
        }),
      beforeSure: done => {
        if (childrenRef.value.ruleFormRef) {
          childrenRef.value.ruleFormRef.validate(valid => {
            if (valid) {
              addSlowlogStoreAPI(editForm.value)
                .then(res => {
                  if (res.success) {
                    message(res.msg, { type: "success" });
                    done();
                    editForm.value = undefined;
                    onSearch();
                  } else {
                    message(res.msg, { type: "error" });
                  }
                })
                .catch(error => {
                  message(error, { type: "error" });
                });
            } else {
              message("请检查表单", { type: "error" });
            }
          });
        } else {
          message("请检查表单", { type: "error" });
        }
      }
    });
  }
  function editFunc(row: SlowlogStore) {
    editForm.value = {
      name: row.name,
      host: row.host,
      db: row.db,
      port: row.port,
      user: row.user,
      password: "" // 密码不回显，需要用户重新输入
    };
    addDrawer({
      headerRenderer: ({ titleId, titleClass }) => (
        <div class="flex flex-row justify-between">
          <h4 id={titleId} class={titleClass}>
            更新慢日志存储 {row.name}
          </h4>
        </div>
      ),
      contentRenderer: () =>
        h(Uform, {
          formInline: {
            form: editForm.value
          },
          ref: childrenRef
        }),
      beforeSure: done => {
        if (childrenRef.value.ruleFormRef) {
          childrenRef.value.ruleFormRef.validate(valid => {
            if (valid) {
              updateSlowlogStoreAPI(row.id, editForm.value)
                .then(res => {
                  if (res.success) {
                    message(res.msg, { type: "success" });
                    done();
                    // 重置表单数据
                    editForm.value = undefined;
                    onSearch();
                  } else {
                    message(res.msg, { type: "error" });
                  }
                })
                .catch(error => {
                  message(error, { type: "error" });
                });
            } else {
              message("请检查表单", { type: "error" });
            }
          });
        } else {
          message("请检查表单", { type: "error" });
        }
      }
    });
  }
  function handleSizeChange(val: number) {
    pagination.pageSize = val;
    onSearch();
  }

  function handleCurrentChange(val: number) {
    pagination.currentPage = val;
    onSearch();
  }

  async function onSearch() {
    loading.value = true;
    getSlowlogStoresAPI({
      page: pagination.currentPage,
      limit: pagination.pageSize,
      keyword: form.keyword
    })
      .then(res => {
        if (res.success) {
          dataList.value = res.data;
          pagination.total = res.count;
          pagination.pageSize = pagination.pageSize;
          pagination.currentPage = pagination.currentPage;
        } else {
          dataList.value = [];
          pagination.total = 0;
          pagination.pageSize = pagination.pageSize;
          pagination.currentPage = pagination.currentPage;
        }
      })
      .catch(() => {
        message("请求失败", { type: "error" });
      })
      .finally(() => {
        loading.value = false;
      });
  }

  const resetForm = formEl => {
    if (!formEl) return;
    formEl.resetFields();
    onSearch();
  };

  onMounted(() => {
    onSearch();
  });

  return {
    form,
    loading,
    columns,
    dataList,
    pagination,
    onSearch,
    resetForm,
    handleSizeChange,
    handleCurrentChange,
    addFunc
  };
}
