<template>
  <div>
    <el-card shadow="never" :form="newFormInline.form">
      <el-form
        ref="ruleFormRef"
        label-width="auto"
        style="max-width: 600px; margin: auto"
        :model="newFormInline.form"
        label-position="top"
        status-icon
      >
        <el-form-item
          label="名称"
          prop="name"
          show-word-limit
          maxlength="255"
          :rules="[
            { required: true, message: '请输入名称', trigger: 'blur' },
            { max: 255, message: '名称长度不能超过255个字符', trigger: 'blur' }
          ]"
        >
          <el-input v-model="newFormInline.form.name" />
        </el-form-item>
        <el-form-item
          label="主机"
          prop="host"
          show-word-limit
          label-width="auto"
          maxlength="255"
          :rules="[
            { required: true, message: '请输入主机', trigger: 'blur' },
            { max: 255, message: '主机长度不能超过255个字符', trigger: 'blur' }
          ]"
        >
          <el-input v-model="newFormInline.form.host" />
        </el-form-item>
        <el-form-item
          label="端口"
          prop="port"
          :rules="[{ required: true, message: '请输入端口', trigger: 'blur' }]"
        >
          <el-input-number
            v-model="newFormInline.form.port"
            step-strictly
            size="large"
            :min="1"
            :max="65535"
          />
        </el-form-item>
        <el-form-item
          label="数据库"
          prop="db"
          :rules="[
            { required: true, message: '请输入数据库', trigger: 'blur' },
            {
              max: 255,
              message: '数据库长度不能超过255个字符',
              trigger: 'blur'
            }
          ]"
        >
          <el-input v-model="newFormInline.form.db" />
        </el-form-item>
        <el-form-item
          label="用户名"
          prop="user"
          :rules="[
            { required: true, message: '请输入用户名', trigger: 'blur' },
            {
              max: 255,
              message: '用户名长度不能超过255个字符',
              trigger: 'blur'
            }
          ]"
        >
          <el-input
            v-model="newFormInline.form.user"
            placeholder="用户名"
            show-word-limit
            maxlength="255"
          />
        </el-form-item>
        <el-form-item
          label="密码"
          prop="password"
          show-word-limit
          maxlength="255"
          :rules="[
            { max: 255, message: '密码长度不能超过255个字符', trigger: 'blur' }
          ]"
        >
          <el-input
            v-model="newFormInline.form.password"
            type="password"
            show-password
            placeholder="密码"
          />
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { defineProps, ref } from "vue";
import type { SlowlogStoreForm as Form } from "@/api/database/slowlog/store";
import type { FormInstance } from "element-plus";

export interface FormProps {
  formInline: {
    form: Form;
  };
}

const props = withDefaults(defineProps<FormProps>(), {
  formInline: () => ({ form: undefined })
});

const newFormInline = ref(props.formInline);
const ruleFormRef = ref<FormInstance>();
defineExpose({ ruleFormRef });
</script>

<style scoped>
.el-card {
  padding: 20px;
  border-radius: 8px;
}

.el-form-item {
  margin-bottom: 16px;
}
</style>
