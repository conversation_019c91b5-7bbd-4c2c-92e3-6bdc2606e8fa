<template>
  <div class="stat-db-type">
    <div class="time-picker">
      <el-date-picker
        v-model="timeRange"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        :default-time="defaultTime"
        value-format="YYYY-MM-DD"
        @change="handleTimeChange"
      />
    </div>
    <div ref="chartRef" class="chart-container"></div>
    <div v-if="loading" class="loading-container">
      <el-icon class="is-loading"><Loading /></el-icon>
      <span>加载中...</span>
    </div>
    <div v-if="error" class="error-container">
      <el-empty
        description="暂无数据"
        :image-size="120"
        style="padding: 40px 0"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, nextTick } from "vue";
import * as echarts from "echarts";
import type { EChartsOption } from "echarts";
import {
  getProjectStatistics,
  type ProjectStatisticsList
} from "@/api/database/slowlog/staticstic";
import dayjs from "dayjs";
import { Loading } from "@element-plus/icons-vue";

const props = defineProps({
  id: {
    type: Number,
    required: true
  },
  dbType: {
    type: String,
    required: true
  }
});

const chartRef = ref<HTMLElement | null>(null);
let chart: echarts.ECharts | null = null;
const loading = ref(false);
const error = ref("");

const defaultTime: [Date, Date] = [
  new Date(2000, 1, 1, 0, 0, 0),
  new Date(2000, 2, 1, 23, 59, 59)
];

const timeRange = ref<[string, string]>([
  dayjs().subtract(7, "day").format("YYYY-MM-DD"),
  dayjs().format("YYYY-MM-DD")
]);

const handleTimeChange = () => {
  console.log("Time range changed:", timeRange.value);
  fetchData();
};

const resizeHandler = () => {
  if (chart) {
    chart.resize();
  }
};

const fetchData = async () => {
  if (!props.id || !props.dbType) {
    error.value = "缺少必要参数";
    return;
  }

  loading.value = true;
  error.value = "";

  try {
    console.log("Fetching data with params:", {
      id: props.id,
      dbType: props.dbType,
      start_time: timeRange.value[0],
      end_time: timeRange.value[1]
    });

    const response = await getProjectStatistics(props.id, props.dbType, {
      start_time: timeRange.value[0],
      end_time: timeRange.value[1]
    });

    console.log("API Response:", response);

    if (response.data && response.data.length > 0) {
      console.log("Data received:", response.data);
      updateChart(response.data);
    } else {
      error.value = "暂无数据";
      console.warn("No data received from API");
    }
  } catch (err) {
    console.error("Failed to fetch statistics:", err);
    error.value = "获取数据失败";
  } finally {
    loading.value = false;
  }
};

const updateChart = (data: ProjectStatisticsList) => {
  if (!chart) {
    console.error("Chart instance not initialized");
    return;
  }

  console.log("Updating chart with data:", data);

  // Get unique dates and names for the chart
  const dates = [...new Set(data.map(item => item.stat_time))].sort();
  const names = [...new Set(data.map(item => item.name))];

  console.log("Unique dates:", dates);
  console.log("Unique names:", names);

  // Create a color palette for different projects
  const colors = [
    "#5470c6",
    "#91cc75",
    "#fac858",
    "#ee6666",
    "#73c0de",
    "#3ba272",
    "#fc8452",
    "#9a60b4",
    "#ea7ccc",
    "#B5C334",
    "#E87C25"
  ];

  const option: EChartsOption = {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow"
      }
    },
    legend: {
      data: names,
      type: "scroll",
      top: 0,
      left: 0,
      right: 0
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      top: "80px",
      containLabel: true
    },
    xAxis: {
      type: "category",
      data: dates,
      axisLabel: {
        rotate: 45,
        formatter: (value: string): string => dayjs(value).format("MM-DD")
      }
    },
    yAxis: {
      type: "value",
      name: "慢查询数量",
      axisLabel: {
        formatter: (value: number): string => {
          if (value >= 10000) {
            return (value / 10000).toFixed(1) + "w";
          }
          if (value >= 1000) {
            return (value / 1000).toFixed(1) + "k";
          }
          return String(value);
        }
      }
    },
    series: names.map((name, index) => ({
      name,
      type: "line",
      smooth: true,
      symbol: "circle",
      symbolSize: 6,
      color: colors[index % colors.length],
      data: dates.map(date => {
        const item = data.find(d => d.name === name && d.stat_time === date);
        return item ? item.total : 0;
      }),
      emphasis: {
        focus: "series"
      }
    }))
  };

  console.log("Setting chart option:", option);
  // 使用 clear 先清除旧的图表内容，然后重新设置
  chart.clear();
  chart.setOption(option);
};

const initChart = async () => {
  // Wait for DOM to be updated
  await nextTick();

  if (!chartRef.value) {
    console.error("Chart container element not found");
    error.value = "图表容器初始化失败";
    return;
  }

  try {
    // Ensure the container has dimensions
    if (chartRef.value.offsetHeight === 0) {
      chartRef.value.style.height = "500px";
    }

    console.log("Chart container dimensions:", {
      width: chartRef.value.offsetWidth,
      height: chartRef.value.offsetHeight
    });

    // Dispose if already exists
    if (chart) {
      chart.dispose();
    }

    // Initialize chart
    chart = echarts.init(chartRef.value);
    console.log("Chart initialized");

    // Add resize event listener
    window.addEventListener("resize", resizeHandler);

    // Fetch data
    fetchData();
  } catch (err) {
    console.error("Failed to initialize chart:", err);
    error.value = "图表初始化失败";
  }
};

onMounted(() => {
  console.log("Component mounted");
  initChart();
});

onUnmounted(() => {
  console.log("Component unmounting, cleaning up");
  window.removeEventListener("resize", resizeHandler);
  if (chart) {
    chart.dispose();
    chart = null;
  }
});

defineExpose({
  refresh: fetchData
});
</script>

<style scoped>
.stat-db-type {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
  height: 100%;
  min-height: 500px;
}

.time-picker {
  padding: 16px;
}

.chart-container {
  flex: 1;
  width: 100%;
  min-height: 500px;
}

.loading-container {
  position: absolute;
  top: 50%;
  left: 50%;
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
  transform: translate(-50%, -50%);
}

.error-container {
  position: absolute;
  top: 50%;
  left: 50%;
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
  transform: translate(-50%, -50%);
}
</style>
