<script setup lang="ts">
import { ref, onMounted } from "vue";
import type { FormInstance, FormRules } from "element-plus";
import { message } from "@/utils/message";
import {
  createInstanceAPI,
  updateInstanceAPI,
  type Instance,
  testConnectInstanceAPI
} from "@/api/database/redis/instance";
import { type Tag, getAllTagsAPI } from "@/api/asset/tag";
import { closeDrawer } from "@/components/ReDrawer";
import type { InstanceForm } from "@/api/database/redis/instance";

const props = defineProps<{
  onSuccess?: () => void;
  row?: Instance;
}>();

const formRef = ref<FormInstance>();
const loading = ref(false);
const testLoading = ref(false);
const tagOptions = ref<Tag[]>([]);

const form = ref<InstanceForm>({
  host: "",
  port: 6379,
  user: "",
  password: "",
  remark: "",
  tag_ids: []
});

const rules = ref<FormRules>({
  host: [
    { required: true, message: "请输入主机地址", trigger: "blur" },
    {
      max: 255,
      message: "主机地址长度不能超过255个字符",
      trigger: "blur"
    }
  ],
  port: [
    { required: true, message: "请输入端口", trigger: "blur" },
    {
      type: "number",
      min: 1,
      max: 65535,
      message: "端口范围1-65535",
      trigger: "blur"
    }
  ],
  user: [
    {
      max: 255,
      message: "用户名长度不能超过255个字符",
      trigger: "blur"
    }
  ],
  password: [
    { max: 255, message: "密码长度不能超过255个字符", trigger: "blur" }
  ]
});

async function getTagList() {
  try {
    const res = await getAllTagsAPI();
    if (res.success) {
      tagOptions.value = res.data;
    }
  } catch (error) {
    message("获取标签列表失败：" + error, { type: "error" });
  }
}

async function onSubmit() {
  if (!formRef.value) return;
  await formRef.value.validate(async valid => {
    if (valid) {
      loading.value = true;
      try {
        const data: InstanceForm = {
          host: form.value.host,
          port: form.value.port,
          user: form.value.user,
          remark: form.value.remark,
          tag_ids: form.value.tag_ids
        };

        // 只有在密码不为空时才添加密码字段
        if (form.value.password) {
          data.password = form.value.password;
        }

        if (props.row) {
          const res = await updateInstanceAPI(props.row.id, data);
          if (res.success) {
            message("更新成功", { type: "success" });
            props.onSuccess?.();
            closeDrawer({}, 0);
          } else {
            message(res.msg || "更新失败", { type: "error" });
          }
        } else {
          const res = await createInstanceAPI(data);
          if (res.success) {
            message("创建成功", { type: "success" });
            props.onSuccess?.();
            closeDrawer({}, 0);
          } else {
            message(res.msg || "创建失败", { type: "error" });
          }
        }
      } catch (error) {
        message((props.row ? "更新" : "创建") + "失败：" + error, {
          type: "error"
        });
      } finally {
        loading.value = false;
      }
    }
  });
}

// 测试连接
async function testConnection() {
  if (!form.value.host || !form.value.port) {
    message("请填写必要信息后再测试连接", { type: "warning" });
    return;
  }

  testLoading.value = true;
  try {
    const res = await testConnectInstanceAPI({
      host: form.value.host,
      port: form.value.port,
      user: form.value.user,
      password: form.value.password
    });
    if (res.success) {
      message("连接测试成功", { type: "success" });
    } else {
      message(res.msg || "连接测试失败", { type: "error" });
    }
  } catch (error) {
    message("连接测试失败：" + error, { type: "error" });
  } finally {
    testLoading.value = false;
  }
}

onMounted(async () => {
  await getTagList();
  if (props.row) {
    form.value = {
      host: props.row.host,
      port: props.row.port,
      user: props.row.user,
      password: props.row.password || "",
      remark: props.row.remark || "",
      tag_ids: props.row.tags?.map(tag => tag.id) || []
    };
  }
});
</script>

<template>
  <div class="form-container">
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      class="form"
    >
      <div class="form-section">
        <div class="section-title">基本信息</div>
        <el-form-item label="主机地址" prop="host">
          <el-input v-model="form.host" placeholder="请输入主机地址" />
        </el-form-item>
        <el-form-item label="端口" prop="port">
          <el-input-number v-model="form.port" :min="1" :max="65535" />
        </el-form-item>
        <el-form-item label="用户名" prop="user">
          <el-input
            v-model="form.user"
            placeholder="请输入用户名"
            autocomplete="off"
          />
        </el-form-item>
        <el-form-item label="密码">
          <el-input
            v-model="form.password"
            type="password"
            placeholder="请输入密码"
            show-password
            autocomplete="new-password"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            :loading="testLoading"
            class="test-button"
            @click="testConnection"
          >
            测试连接
          </el-button>
        </el-form-item>
      </div>

      <div class="form-section">
        <div class="section-title">其他信息</div>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注"
          />
        </el-form-item>
        <el-form-item label="标签" prop="tag_ids">
          <el-select
            v-model="form.tag_ids"
            multiple
            filterable
            clearable
            default-first-option
            placeholder="请选择标签"
            class="tag-select"
            size="large"
            tag-type="primary"
            tag-effect="plain"
          >
            <el-option
              v-for="item in tagOptions"
              :key="item.id"
              :label="`${item.key}=${item.value}`"
              :value="item.id"
            >
              <div class="tag-option">
                <span class="tag-key">{{ item.key }}</span>
                <span class="tag-separator">=</span>
                <span class="tag-value">{{ item.value }}</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
      </div>

      <div class="form-footer">
        <el-button type="primary" :loading="loading" @click="onSubmit">
          确定
        </el-button>
      </div>
    </el-form>
  </div>
</template>

<style scoped lang="scss">
.form-container {
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgb(0 0 0 / 10%);
}

.form {
  .form-section {
    padding: 20px;
    margin-bottom: 20px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 1px 4px rgb(0 0 0 / 5%);

    .section-title {
      padding-bottom: 10px;
      margin-bottom: 20px;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      border-bottom: 1px solid #ebeef5;
    }
  }

  .el-form-item {
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .el-input,
  .el-input-number,
  .el-select {
    width: 100%;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    transition: all 0.3s ease;

    &:hover {
      border-color: #c0c4cc;
    }

    &:focus {
      border-color: #409eff;
      box-shadow: 0 0 5px rgb(64 158 255 / 50%);
    }
  }

  .el-textarea__inner {
    border-radius: 4px;
    transition: all 0.3s ease;

    &:hover {
      border-color: #c0c4cc;
    }

    &:focus {
      border-color: #409eff;
      box-shadow: 0 0 5px rgb(64 158 255 / 50%);
    }
  }

  .el-select {
    .el-input__wrapper {
      box-shadow: none;
    }
  }

  .el-tag {
    margin-right: 8px;
    margin-bottom: 8px;
  }
}

.form-footer {
  display: flex;
  justify-content: center;
  padding-top: 20px;
  margin-top: 30px;
  border-top: 1px solid #ebeef5;

  .el-button {
    min-width: 120px;
    border-radius: 4px;
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 12px rgb(64 158 255 / 30%);
      transform: translateY(-2px);
    }
  }
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-input-number .el-input__wrapper) {
  box-shadow: none;
}

.tag-select {
  :deep(.el-select__tags) {
    .el-tag {
      height: 24px;
      padding: 0 8px;
      margin-right: 8px;
      margin-bottom: 8px;
      font-size: 12px;
      line-height: 24px;
      color: #409eff;
      background-color: #ecf5ff;
      border-color: #d9ecff;
      border-radius: 4px;
      transition: all 0.3s ease;

      &:hover {
        background-color: #d9ecff;
        border-color: #c6e2ff;
      }

      .el-tag__close {
        margin-left: 4px;
        font-size: 12px;
        color: #409eff;
        transition: all 0.3s ease;

        &:hover {
          color: #66b1ff;
          transform: scale(1.1);
        }
      }
    }
  }
}

.tag-option {
  display: flex;
  gap: 4px;
  align-items: center;
  font-size: 13px;

  .tag-key {
    font-weight: bold;
    color: #409eff;
  }

  .tag-separator {
    color: #909399;
  }

  .tag-value {
    font-weight: bold;
    color: #409eff;
  }
}

.test-button {
  margin-right: 12px;
  color: #409eff;
  background-color: rgb(64 158 255 / 10%);
  border-color: #409eff;

  &:hover {
    background-color: rgb(64 158 255 / 20%);
  }
}
</style>
