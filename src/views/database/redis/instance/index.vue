<script setup lang="ts">
import { ref } from "vue";
import { useInstance } from "./hook";
import { PureTableBar } from "@/components/RePureTableBar";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import Refresh from "@iconify-icons/ep/refresh";
import type { Instance } from "@/api/database/redis/instance";

defineOptions({
  name: "RedisInstance"
});

const tableRef = ref();

const {
  form,
  loading,
  columns,
  dataList,
  pagination,
  onSearch,
  resetForm,
  handleSizeChange,
  handleCurrentChange,
  addFunc,
  batchSetTags,
  selectedRows,
  syncFunc,
  batchDeleteFunc,
  tagOptions
} = useInstance();

function handleSelectionChange(rows: Instance[]) {
  selectedRows.value = rows;
}
</script>

<template>
  <div class="main">
    <el-card class="search-card">
      <el-form
        ref="formRef"
        :inline="true"
        :model="form"
        class="search-form"
        @submit.prevent
      >
        <el-form-item label="关键字" prop="keyword">
          <el-input
            v-model="form.keyword"
            placeholder="请输入关键字"
            clearable
            @keyup.enter="onSearch"
          />
        </el-form-item>

        <el-form-item label="标签" prop="tag_ids">
          <el-select
            v-model="form.tag_ids"
            multiple
            filterable
            clearable
            placeholder="请选择标签"
            class="tag-select"
            @change="onSearch"
          >
            <el-option
              v-for="tag in tagOptions"
              :key="tag.id"
              :label="`${tag.key}=${tag.value}`"
              :value="tag.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            :icon="useRenderIcon('ri:search-line')"
            :loading="loading"
            class="search-button"
            @click="onSearch"
          >
            搜索
          </el-button>
          <el-button
            :icon="useRenderIcon(Refresh)"
            class="reset-button"
            @click="resetForm()"
          >
            重置
          </el-button>
          <el-button
            :icon="useRenderIcon('ep:plus')"
            class="add-button"
            @click="addFunc()"
          >
            添加
          </el-button>
          <el-button
            type="success"
            :icon="useRenderIcon('ep:collection')"
            class="batch-button"
            @click="batchSetTags"
          >
            批量设置标签
          </el-button>
          <el-button
            type="warning"
            :icon="useRenderIcon(Refresh)"
            class="sync-button"
            @click="syncFunc"
          >
            导入实例
          </el-button>
          <el-button
            type="danger"
            :icon="useRenderIcon('ep:delete')"
            class="delete-button"
            @click="batchDeleteFunc"
          >
            批量删除
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <PureTableBar title="实例列表" :columns="columns" @refresh="onSearch">
      <template v-slot="{ size, dynamicColumns }">
        <pure-table
          ref="tableRef"
          row-key="id"
          align-whole="left"
          table-layout="auto"
          :loading="loading"
          :size="size"
          :minHeight="500"
          :data="dataList"
          :columns="dynamicColumns"
          :pagination="{ ...pagination, size }"
          :header-cell-style="{
            fontWeight: '600'
          }"
          :row-style="{
            cursor: 'pointer',
            transition: 'background-color 0.2s ease'
          }"
          :cell-style="{
            padding: '12px 10px'
          }"
          border
          stripe
          highlight-current-row
          :row-class-name="() => ''"
          @page-size-change="handleSizeChange"
          @page-current-change="handleCurrentChange"
          @selection-change="handleSelectionChange"
        >
          <template #empty>
            <el-empty
              description="暂无数据"
              :image-size="120"
              style="padding: 40px 0"
            />
          </template>
        </pure-table>
      </template>
    </PureTableBar>
  </div>
</template>

<style scoped lang="scss">
.main {
  padding: 20px;
  border-radius: 12px;
}

.search-card {
  margin-bottom: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 8%);
  transition: box-shadow 0.3s ease;

  &:hover {
    box-shadow: 0 6px 20px rgb(0 0 0 / 12%);
  }
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;

  :deep(.el-form-item) {
    margin-bottom: 0;
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: #606266;
  }

  .el-input {
    width: 350px;

    :deep(input),
    :deep(.el-input__wrapper) {
      border-radius: 8px;

      &:focus {
        border-color: #409eff;
        box-shadow: 0 0 0 1px rgb(64 158 255 / 20%);
      }
    }
  }
}

.search-button,
.reset-button,
.add-button,
.batch-button,
.sync-button,
.delete-button {
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
    transform: translateY(-2px);
  }
}

.search-button {
  color: white;
  background: linear-gradient(135deg, #409eff 0%, #4facff 100%);
  border: none;
  box-shadow: 0 4px 12px rgb(64 158 255 / 30%);
}

.reset-button {
  color: #606266;
  border-color: #dcdfe6;
}

.add-button {
  color: #409eff;
  background-color: rgb(64 158 255 / 10%);
  border-color: #409eff;
}

.batch-button {
  color: #67c23a;
  background-color: rgb(103 194 58 / 10%);
  border-color: #67c23a;

  &:hover {
    background-color: rgb(103 194 58 / 20%);
  }
}

.sync-button {
  color: #e6a23c;
  background-color: rgb(230 162 60 / 10%);
  border-color: #e6a23c;

  &:hover {
    background-color: rgb(230 162 60 / 20%);
  }
}

.delete-button {
  color: #f56c6c;
  background-color: rgb(245 108 108 / 10%);
  border-color: #f56c6c;

  &:hover {
    background-color: rgb(245 108 108 / 20%);
  }
}

:deep(.pure-table) {
  overflow: hidden;
  border: 1px solid #e4e7ed;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 4%);
}

.tag-select {
  width: 350px;

  :deep(.el-select__wrapper) {
    border-radius: 8px;
    box-shadow: 0 0 0 1px #dcdfe6 inset;

    &:hover {
      box-shadow: 0 0 0 1px #c0c4cc inset;
    }

    &.is-focus {
      box-shadow: 0 0 0 1px #409eff inset;
    }
  }
}
</style>
