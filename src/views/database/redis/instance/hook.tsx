import dayjs from "dayjs";
import type { PaginationProps } from "@pureadmin/table";
import { reactive, ref, onMounted, h } from "vue";
import { message } from "@/utils/message";
import { Edit, Delete, Refresh } from "@element-plus/icons-vue";

import {
  deleteInstanceAPI,
  getInstanceListAPI,
  type Instance,
  tagInstances,
  type BatTagFrom,
  syncConnectInstanceAPI
} from "@/api/database/redis/instance";
import { addDrawer } from "@/components/ReDrawer";
import { addDialog } from "@/components/ReDialog";
import Uform from "./Uform.vue";
import TagForm from "./TagForm.vue";
import { getAllTagsAPI } from "@/api/asset/tag";

export function useInstance() {
  const form = reactive({
    keyword: undefined,
    tag_ids: undefined
  });
  const dataList = ref<Instance[]>([]);
  const loading = ref<boolean>(true);
  const selectedRows = ref<Instance[]>([]);
  const tagOptions = ref([]);
  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true,
    pageSizes: [10, 20, 30, 40, 50, 100]
  });

  // 获取标签列表
  async function getTagList() {
    try {
      const res = await getAllTagsAPI();
      if (res.success) {
        tagOptions.value = res.data;
      }
    } catch (error) {
      message("获取标签列表失败：" + error, { type: "error" });
    }
  }

  // 批量设置标签
  function batchSetTags() {
    if (selectedRows.value.length === 0) {
      message("请选择要设置标签的实例", { type: "warning" });
      return;
    }

    let tagFormRef: any = null;

    addDialog({
      title: "批量设置标签",
      width: 600,
      draggable: true,
      closeOnClickModal: false,
      contentRenderer: () =>
        h(TagForm, {
          ref: ref => {
            tagFormRef = ref;
          },
          selectedRows: selectedRows.value,
          onSuccess: () => {
            onSearch();
          }
        }),
      beforeSure: async done => {
        try {
          if (
            tagFormRef.operationType !== "clear" &&
            !tagFormRef.selectedTags?.length
          ) {
            message("请选择要设置的标签", { type: "warning" });
            return;
          }

          const data: BatTagFrom = {
            instance_ids: selectedRows.value.map(row => row.id),
            tag_ids: tagFormRef.selectedTags || [],
            op: tagFormRef.operationType
          };
          const res = await tagInstances(data);
          if (res.success) {
            message("设置标签成功", { type: "success" });
            onSearch();
            done();
          } else {
            message(res.msg || "设置标签失败", { type: "error" });
          }
        } catch (error) {
          message("设置标签失败：" + error, { type: "error" });
        }
      }
    });
  }

  const columns: TableColumnList = [
    {
      type: "selection",
      width: 55,
      align: "left",
      hide: () => false
    },
    {
      label: "连接信息",
      prop: "connection",
      minWidth: 180,
      cellRenderer: ({ row }) => (
        <div class="text-sm flex flex-col gap-1">
          <div class="flex items-center">
            <span class="w-10 text-gray-500">地址:</span>
            <el-text type="info" class="font-medium">
              {row.host}
            </el-text>
          </div>
          <div class="flex items-center">
            <span class="w-10 text-gray-500">端口:</span>
            <el-text type="info" class="font-medium">
              {row.port}
            </el-text>
          </div>
          {row.user && row.user.trim() !== "" && (
            <div class="flex items-center">
              <span class="w-10 text-gray-500">用户:</span>
              <el-text type="info" class="font-medium">
                {row.user}
              </el-text>
            </div>
          )}
        </div>
      )
    },
    {
      label: "备注",
      prop: "remark",
      minWidth: 120,
      cellRenderer: ({ row }) => <div class="text-sm">{row.remark || "-"}</div>
    },
    {
      label: "标签",
      prop: "tags",
      minWidth: 150,
      cellRenderer: ({ row }) => (
        <div class="flex flex-wrap gap-1">
          {row.tags?.length ? (
            row.tags.map(tag => (
              <el-tag
                size="small"
                effect="plain"
                type="primary"
                class="mb-1 break-all"
                style="max-width: 100%; word-break: break-word;"
              >
                {tag.key}={tag.value}
              </el-tag>
            ))
          ) : (
            <span class="text-gray-400">-</span>
          )}
        </div>
      )
    },
    {
      label: "时间",
      prop: "created_at",
      minWidth: 180,
      cellRenderer: ({ row }) => (
        <div class="text-sm flex flex-col gap-1">
          <div class="flex items-center">
            <span class="w-10 text-gray-500">创建:</span>
            <el-text type="info" class="font-medium">
              {dayjs(row.created_at).format("YYYY-MM-DD HH:mm:ss")}
            </el-text>
          </div>
          <div class="flex items-center">
            <span class="w-10 text-gray-500">更新:</span>
            <el-text type="info" class="font-medium">
              {dayjs(row.updated_at).format("YYYY-MM-DD HH:mm:ss")}
            </el-text>
          </div>
        </div>
      )
    },
    {
      label: "操作",
      prop: "action",
      width: 160,
      fixed: "right",
      cellRenderer: ({ row }) => (
        <div class="flex items-center">
          <el-button-group>
            <el-button
              type="primary"
              size="small"
              onClick={() => editFunc(row)}
            >
              <el-icon>
                <Edit />
              </el-icon>
            </el-button>
            <el-button
              type="danger"
              size="small"
              onClick={() => deleteFunc(row)}
            >
              <el-icon>
                <Delete />
              </el-icon>
            </el-button>
          </el-button-group>
        </div>
      )
    }
  ];

  function deleteFunc(row: Instance) {
    addDialog({
      title: "删除确认",
      width: 480,
      draggable: true,
      closeOnClickModal: false,
      contentRenderer: () => (
        <div class="flex flex-col gap-2">
          <p class="text-lg mb-4">您确定要删除以下Redis实例吗？</p>
          <el-descriptions border column={1}>
            <el-descriptions-item
              label="连接地址"
              label-class-name="font-bold w-[100px]"
            >
              <span class="text-red-500 font-bold">
                {row.host}:{row.port}
              </span>
            </el-descriptions-item>
            {row.user && row.user.trim() !== "" && (
              <el-descriptions-item label="用户" label-class-name="font-bold">
                {row.user}
              </el-descriptions-item>
            )}
          </el-descriptions>
          <div class="bg-[#fff3f3] text-red-500 p-3 rounded mt-4">
            <i class="el-icon-warning mr-2" />
            警告：此操作将永久删除该实例及其相关数据，且无法恢复！
          </div>
        </div>
      ),
      beforeSure: done => {
        deleteInstanceAPI(row.id)
          .then(res => {
            if (res.success) {
              message("删除成功", { type: "success" });
              onSearch();
              done();
            } else {
              message(res.msg || "删除失败", { type: "error" });
            }
          })
          .catch(error => {
            message("删除失败：" + error, { type: "error" });
          });
      }
    });
  }

  function syncFunc() {
    addDialog({
      title: "导入确认",
      width: 480,
      draggable: true,
      closeOnClickModal: false,
      contentRenderer: () => (
        <div class="flex flex-col gap-4">
          <div class="flex items-center gap-2">
            <el-icon class="text-warning text-xl">
              <Refresh />
            </el-icon>
            <p class="text-lg font-medium">您确定要导入监控中的Redis实例吗？</p>
          </div>
          <div class="bg-[#fff9e6] text-warning p-4 rounded-lg">
            <div class="flex items-start gap-2">
              <el-icon class="text-warning text-lg mt-1">
                <Refresh />
              </el-icon>
              <div>
                <p class="font-medium mb-2">导入说明：</p>
                <p class="text-sm leading-relaxed">
                  系统将从Categraf监控的实例中获取Redis实例信息，并导入到当前实例列表中。导入过程可能需要一些时间，请耐心等待。
                </p>
              </div>
            </div>
          </div>
        </div>
      ),
      beforeSure: done => {
        syncConnectInstanceAPI()
          .then(res => {
            if (res.success) {
              message("导入任务已启动", { type: "success" });
              done();
            } else {
              message(res.msg || "导入失败", { type: "error" });
            }
          })
          .catch(error => {
            message("导入失败：" + error, { type: "error" });
          });
      }
    });
  }

  function addFunc() {
    addDrawer({
      title: "新增Redis实例",
      closeOnClickModal: true,
      contentRenderer: () => h(Uform, { onSuccess: onSearch })
    });
  }

  function editFunc(row: Instance) {
    addDrawer({
      title: "编辑Redis实例",
      closeOnClickModal: true,
      contentRenderer: () => h(Uform, { onSuccess: onSearch, row })
    });
  }

  function handleSizeChange(val: number) {
    pagination.pageSize = val;
    onSearch();
  }

  function handleCurrentChange(val: number) {
    pagination.currentPage = val;
    onSearch();
  }

  async function onSearch() {
    loading.value = true;
    try {
      const res = await getInstanceListAPI({
        page: pagination.currentPage,
        limit: pagination.pageSize,
        keyword: form.keyword,
        tag_ids: form.tag_ids?.length ? form.tag_ids.join(",") : undefined
      });
      if (res.success) {
        dataList.value = res.data || [];
        pagination.total = res.count || 0;
      } else {
        message(res.msg || "获取数据失败", { type: "error" });
      }
    } catch (error) {
      message("获取数据失败：" + error, { type: "error" });
    } finally {
      loading.value = false;
    }
  }

  // 批量删除实例
  function batchDeleteFunc() {
    if (selectedRows.value.length === 0) {
      message("请选择要删除的实例", { type: "warning" });
      return;
    }

    addDialog({
      title: "批量删除确认",
      width: 480,
      draggable: true,
      closeOnClickModal: false,
      contentRenderer: () => (
        <div class="flex flex-col gap-2">
          <p class="text-lg mb-4">您确定要删除以下Redis实例吗？</p>
          <el-descriptions border column={1}>
            {selectedRows.value.map(row => (
              <el-descriptions-item
                key={row.id}
                label="连接地址"
                label-class-name="font-bold w-[100px]"
              >
                <span class="text-red-500 font-bold">
                  {row.host}:{row.port}
                </span>
              </el-descriptions-item>
            ))}
          </el-descriptions>
          <div class="bg-[#fff3f3] text-red-500 p-3 rounded mt-4">
            <i class="el-icon-warning mr-2" />
            警告：此操作将永久删除选中的实例及其相关数据，且无法恢复！
          </div>
        </div>
      ),
      beforeSure: async done => {
        try {
          const deletePromises = selectedRows.value.map(row =>
            deleteInstanceAPI(row.id)
              .then(res => {
                if (res.success) {
                  message(`删除实例 ${row.host}:${row.port} 成功`, {
                    type: "success"
                  });
                  return true;
                } else {
                  message(`删除实例 ${row.host}:${row.port} 失败: ${res.msg}`, {
                    type: "error"
                  });
                  return false;
                }
              })
              .catch(error => {
                message(`删除实例 ${row.host}:${row.port} 失败: ${error}`, {
                  type: "error"
                });
                return false;
              })
          );

          const results = await Promise.all(deletePromises);
          const successCount = results.filter(Boolean).length;

          if (successCount > 0) {
            message(`批量删除完成，成功删除 ${successCount} 个实例`, {
              type: "success"
            });
            onSearch();
            done();
          }
        } catch (error) {
          message("批量删除过程中发生错误", { type: "error" });
        }
      }
    });
  }

  onMounted(() => {
    onSearch();
    getTagList();
  });

  return {
    form,
    loading,
    columns,
    dataList,
    pagination,
    selectedRows,
    tagOptions,
    onSearch,
    resetForm: () => {
      form.keyword = undefined;
      form.tag_ids = undefined;
      onSearch();
    },
    handleSizeChange,
    handleCurrentChange,
    addFunc,
    batchSetTags,
    syncFunc,
    batchDeleteFunc
  };
}
