<script setup lang="ts">
import { ref, onMounted, defineExpose } from "vue";
import { message } from "@/utils/message";
import { getAllTagsAPI } from "@/api/asset/tag";
import type { Tag } from "@/api/asset/tag";
import type { Instance } from "@/api/database/redis/instance";

defineProps<{
  selectedRows: Instance[];
  onSuccess?: () => void;
}>();

defineEmits<{
  (e: "success"): void;
}>();

const tagOptions = ref<Tag[]>([]);
const selectedTags = ref<number[]>([]);
const operationType = ref("append");

const operations = {
  append: "追加标签",
  replace: "替换标签",
  delete: "删除标签",
  clear: "清空标签"
};

// 暴露selectedTags和operationType给父组件
defineExpose({
  selectedTags,
  operationType
});

// 获取标签列表
async function getTagList() {
  try {
    const res = await getAllTagsAPI();
    if (res.success) {
      tagOptions.value = res.data;
    }
  } catch (error) {
    message("获取标签列表失败：" + error, { type: "error" });
  }
}

// 标签搜索方法
function filterTags(query: string) {
  if (query !== "") {
    return tagOptions.value.filter(item => {
      return (
        item.key.toLowerCase().includes(query.toLowerCase()) ||
        item.value.toLowerCase().includes(query.toLowerCase())
      );
    });
  } else {
    return tagOptions.value;
  }
}

// 删除标签
function removeTag(tagId: number) {
  selectedTags.value = selectedTags.value.filter(id => id !== tagId);
}

onMounted(async () => {
  await getTagList();
});
</script>

<template>
  <div class="flex flex-col gap-4">
    <div class="bg-[#f5f7fa] p-4 rounded-lg">
      <p class="text-sm text-gray-500 mb-2">
        已选择 {{ selectedRows.length }} 个实例
      </p>
      <el-table :data="selectedRows" style="width: 100%" size="small" border>
        <el-table-column prop="host" label="主机地址" />
        <el-table-column prop="port" label="端口" width="80" />
      </el-table>
    </div>

    <div class="flex flex-col gap-2">
      <div class="flex items-center justify-between">
        <p class="text-sm font-medium text-gray-700">操作类型</p>
        <el-radio-group v-model="operationType" size="small">
          <el-radio-button
            v-for="(label, key) in operations"
            :key="key"
            :label="key"
          >
            {{ label }}
          </el-radio-button>
        </el-radio-group>
      </div>

      <template v-if="operationType !== 'clear'">
        <p class="text-sm font-medium text-gray-700">选择标签</p>
        <el-select
          v-model="selectedTags"
          multiple
          filterable
          allow-create
          default-first-option
          clearable
          placeholder="请选择标签"
          :filter-method="filterTags"
          class="w-full"
        >
          <el-option
            v-for="item in tagOptions"
            :key="item.id"
            :label="`${item.key}=${item.value}`"
            :value="item.id"
          >
            <div class="tag-option">
              <span class="tag-key">{{ item.key }}</span>
              <span class="tag-separator">=</span>
              <span class="tag-value">{{ item.value }}</span>
            </div>
          </el-option>
        </el-select>

        <div class="flex flex-wrap gap-2 mt-2">
          <el-tag
            v-for="tagId in selectedTags"
            :key="tagId"
            closable
            class="tag-item"
            @close="removeTag(tagId)"
          >
            {{ tagOptions.find(t => t.id === tagId)?.key }}={{
              tagOptions.find(t => t.id === tagId)?.value
            }}
          </el-tag>
        </div>
      </template>

      <template v-else>
        <div class="bg-[#fff3f3] text-red-500 p-3 rounded">
          <i class="el-icon-warning mr-2" />
          警告：此操作将清空所有选中实例的标签！
        </div>
      </template>
    </div>
  </div>
</template>

<style scoped lang="scss">
.tag-option {
  display: flex;
  gap: 4px;
  align-items: center;
  font-size: 13px;

  .tag-key {
    font-weight: 500;
    color: #409eff;
  }

  .tag-separator {
    color: #909399;
  }

  .tag-value {
    color: #606266;
  }
}

.tag-item {
  margin-right: 8px;
  margin-bottom: 8px;
  color: #409eff;
  background-color: #ecf5ff;
  border-color: #d9ecff;
  transition: all 0.3s ease;

  &:hover {
    background-color: #d9ecff;
    border-color: #c6e2ff;
  }

  .el-tag__close {
    margin-left: 4px;
    font-size: 12px;
    color: #409eff;
    transition: all 0.3s ease;

    &:hover {
      color: #66b1ff;
      transform: scale(1.1);
    }
  }
}

:deep(.el-radio-button__inner) {
  padding: 8px 16px;
  font-size: 13px;
  border-radius: 4px;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
    transform: translateY(-1px);
  }
}
</style>
