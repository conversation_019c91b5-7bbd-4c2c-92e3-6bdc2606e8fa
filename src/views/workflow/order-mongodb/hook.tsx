import {
  getAllInstancesAPI,
  type Instance
} from "@/api/database/mongodb/instance";
import { getAllMyOrdersAPI } from "@/api/workflow/my-order";
import type { Order } from "@/api/workflow/order";
import { getProcessesAPI, type Process } from "@/api/workflow/process";
import { OrderExtType, OrderType } from "@/config/order-enum";
import { message } from "@/utils/message";
import { ref } from "vue";

export const processes = ref<Process[]>([]);
export const getProcesses = () => {
  getProcessesAPI(OrderType.MongoDBOrderType, undefined)
    .then(res => {
      if (res.success) {
        processes.value = res.data;
      } else {
        message(res.msg, { type: "error" });
      }
    })
    .catch(error => {
      message("请求失败" + error, { type: "error" });
    });
};

export const orders = ref<Order[]>([]);

export const getOrders = () => {
  getAllMyOrdersAPI(OrderExtType.MongoDBOrderType)
    .then(res => {
      if (res.success) {
        orders.value = res.data;
      } else {
        message(res.msg, { type: "error" });
      }
    })
    .catch(error => {
      message("请求失败" + error, { type: "error" });
    });
};

export const instances = ref<Instance[]>([]);

export const getAllInstances = () => {
  getAllInstancesAPI()
    .then(res => {
      if (res.success) {
        instances.value = res.data;
      } else {
        message(res.msg, { type: "error" });
      }
    })
    .catch(error => {
      message("请求失败" + error, { type: "error" });
    });
};
