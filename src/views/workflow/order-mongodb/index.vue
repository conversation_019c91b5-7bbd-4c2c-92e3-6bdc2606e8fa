<template>
  <div>
    <el-row :gutter="20" class="order-container">
      <el-col :span="16">
        <el-card class="main-card" shadow="hover" style="margin-left: 20px">
          <template #header>
            <div class="card-header">
              <h2 class="title">
                <iconify-icon-online icon="simple-icons:mongodb" class="icon" />
                MongoDB账户申请
              </h2>
            </div>
          </template>
          <el-form
            ref="ruleFormRef"
            :model="form"
            class="order-form"
            status-icon
            label-position="top"
            size="large"
          >
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item
                  label="实例"
                  prop="instance_id"
                  :rules="[
                    { required: true, message: '请选择实例', trigger: 'blur' }
                  ]"
                >
                  <el-select
                    v-model="form.instance_id"
                    placeholder="请选择实例"
                    clearable
                    filterable
                    class="custom-select"
                    @change="onInstanceChange"
                  >
                    <el-option
                      v-for="instance in instances"
                      :key="instance.id"
                      :label="instance.name"
                      :value="instance.id"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  label="库名"
                  prop="db_name"
                  :rules="[
                    { required: true, message: '请选择库名', trigger: 'blur' }
                  ]"
                >
                  <el-select
                    v-model="form.db_name"
                    placeholder="请选择库名"
                    clearable
                    filterable
                    class="custom-select"
                  >
                    <el-option
                      v-for="name in dbnames"
                      :key="name"
                      :label="name"
                      :value="name"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item
                  label="角色"
                  prop="role"
                  :rules="[
                    { required: true, message: '请选择角色', trigger: 'blur' }
                  ]"
                >
                  <el-select
                    v-model="form.role"
                    placeholder="请选择角色"
                    class="custom-select"
                  >
                    <el-option label="read (只读权限)" value="read" />
                    <el-option label="readWrite (读写权限)" value="readWrite" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item
              label="需求描述"
              :rules="[
                { required: true, message: '请输入需求描述', trigger: 'blur' },
                {
                  max: 5000,
                  message: '需求描述不能超过5000个字符',
                  trigger: 'blur'
                }
              ]"
              prop="content"
            >
              <el-input
                v-model="form.content"
                type="textarea"
                show-word-limit
                maxlength="5000"
                :autosize="{ minRows: 6, maxRows: 16 }"
                placeholder="请详细描述您的需求..."
              />
            </el-form-item>

            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item
                  label="计划时间"
                  prop="plan_time"
                  :rules="[
                    {
                      required: true,
                      message: '请选择计划时间',
                      trigger: 'change'
                    }
                  ]"
                >
                  <el-date-picker
                    v-model="form.plan_time"
                    type="datetime"
                    class="custom-select"
                    placeholder="选择日期时间"
                    :disabled-date="disabledDateFunction"
                    :default-value="defaultPlanTime"
                    value-on-clear
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="是否紧急" prop="critical">
                  <el-switch v-model="form.critical" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="关联工单" prop="orders">
                  <el-select
                    v-model="form.orders"
                    class="related-orders-select"
                    placeholder="请选择工单"
                    multiple
                    clearable
                    filterable
                    tag-type="primary"
                    tag-size="large"
                  >
                    <el-option
                      v-for="item in orders"
                      :key="item.id"
                      :label="item.title"
                      :value="item.sn"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item>
              <el-button type="primary" @click="onSubmit">
                <iconify-icon-online
                  icon="material-symbols:send"
                  style="margin-right: 4px"
                />提交
              </el-button>
              <RouterLink :to="{ name: '我的工单' }">
                <el-button style="margin-left: 50px">
                  <iconify-icon-online
                    icon="material-symbols:cancel"
                    style="margin-right: 4px"
                  />
                  取消
                </el-button>
              </RouterLink>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
      <el-col :span="8">
        <ApprovalFlow :processes="processes" />
      </el-col>
    </el-row>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref } from "vue";
import { applyOrderAPI, type OrderForm } from "@/api/workflow/order-mongodb";
import { ElMessageBox, type FormInstance } from "element-plus";
import { message } from "@/utils/message";
import {
  getProcesses,
  processes,
  instances,
  getAllInstances,
  orders,
  getOrders
} from "./hook";
import ApprovalFlow from "../components/ApprovalFlow.vue";

const form = ref<OrderForm>({
  content: "",
  db_name: "",
  role: undefined,
  instance_id: undefined,
  plan_time: undefined,
  orders: [],
  env: undefined,
  critical: false
});

const dbnames = ref<string[]>([]);

// MongoDB实例选择改变时的处理函数
const onInstanceChange = () => {
  // 这里可以添加根据实例获取库名的逻辑
  // 此处只是示例，实际实现可能需要调用API
  dbnames.value = [];
  form.value.db_name = "";

  // 模拟从实例获取数据库名称
  if (form.value.instance_id) {
    // 这里应该是实际的API调用
    setTimeout(() => {
      dbnames.value = ["admin", "config", "local", "test"];
    }, 300);
  }
};

const ruleFormRef = ref<FormInstance>();
const onSubmit = async () => {
  const valid = await new Promise(resolve => {
    ruleFormRef.value.validate((valid: boolean) => {
      resolve(valid);
    });
  });
  if (valid) {
    try {
      await ElMessageBox.confirm("是否确认提交工单？", "提交确认", {
        confirmButtonText: "提交",
        cancelButtonText: "取消",
        type: "warning",
        draggable: true,
        center: true,
        customClass: "custom-message-box",
        icon: "Warning"
      });
      const res = await applyOrderAPI(form.value);
      if (res.success) {
        message("提交成功", { type: "success" });
        setTimeout(() => {
          window.location.href = "/workflow/my/order";
        }, 300);
      } else {
        message(res.msg || "提交失败", { type: "error" });
      }
    } catch (error) {
      if (error !== "cancel") {
        message(error.message || "操作取消", { type: "warning" });
      }
    }
  } else {
    message("请检查表单数据", { type: "warning" });
  }
};

const disabledDateFunction = date => {
  const currentDate = new Date();
  const oneWorkingDayLater = getOneWorkingDayLater(currentDate);
  return date < oneWorkingDayLater || isWeekend(date);
};

const defaultPlanTime = ref<Date>(new Date());
// 计算一个工作日之后的日期
const getOneWorkingDayLater = date => {
  let nextDate = new Date(date);
  do {
    nextDate.setDate(nextDate.getDate() + 1);
  } while (isWeekend(nextDate));
  return nextDate;
};

// 判断是否为周末
const isWeekend = date => {
  const dayOfWeek = date.getDay();
  return dayOfWeek === 0 || dayOfWeek === 6;
};

onMounted(() => {
  getProcesses();
  getAllInstances();
  getOrders(); // 获取关联工单列表
  form.value.plan_time = getOneWorkingDayLater(new Date());
});
</script>

<style scoped>
.order-container {
  margin-top: 20px;
}

.main-card {
  margin-left: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 8%);
  transition: all 0.3s ease;
}

.main-card:hover {
  box-shadow: 0 6px 20px rgb(0 0 0 / 12%);
}

.card-header {
  display: flex;
  align-items: center;
  padding: 8px 0;
}

.title {
  display: flex;
  align-items: center;
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.icon {
  margin-right: 12px;
  font-size: 24px;
  color: #4db33d; /* MongoDB Green */
}

.order-form {
  padding: 10px;
  margin-top: 20px;
}

.related-orders-select {
  width: 100%;
}

.custom-select {
  width: 100%;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-select .el-input) {
  transition: all 0.3s;
}

:deep(.el-input__inner) {
  transition: all 0.3s;
}

:deep(.el-input__inner:focus) {
  border-color: #4db33d;
}

:deep(.el-textarea__inner) {
  transition: all 0.3s;
}

:deep(.el-textarea__inner:focus) {
  border-color: #4db33d;
}

:deep(.el-date-editor.el-input) {
  transition: all 0.3s;
}

:deep(.el-button--primary) {
  padding: 12px 24px;
  font-weight: 500;
  transition: all 0.3s;
}

:deep(.el-button--primary:hover) {
  background-color: #4fc08d;
  box-shadow: 0 2px 8px rgb(79 192 141 / 20%);
}

:deep(.el-button--default) {
  padding: 12px 24px;
  font-weight: 500;
  transition: all 0.3s;
}

:deep(.el-button--default:hover) {
  background-color: #f2f6fc;
}

/* 表单提交按钮区域样式 */
:deep(.el-form-item:last-child) {
  padding-top: 20px;
  margin-top: 32px;
  border-top: 1px dashed #ebeef5;
}

:deep(.custom-message-box) {
  padding: 30px 30px 20px;
  border-radius: 8px;
}

:deep(.custom-message-box .el-message-box__status) {
  top: 30px;
  font-size: 24px !important;
}

:deep(.custom-message-box .el-message-box__header) {
  padding: 0 0 10px;
}

:deep(.custom-message-box .el-message-box__title) {
  font-size: 16px;
  font-weight: 500;
}

:deep(.custom-message-box .el-message-box__content) {
  padding: 20px 0;
  font-size: 14px;
}

:deep(.custom-message-box .el-message-box__btns) {
  padding: 10px 0 0;
}

:deep(.custom-message-box .el-message-box__btns button) {
  min-width: 80px;
  padding: 8px 20px;
  margin-left: 12px;
  font-size: 14px;
  border-radius: 4px;
}

:deep(.custom-message-box .el-message-box__btns .el-button--primary) {
  background-color: var(--el-color-warning);
  border-color: var(--el-color-warning);
}

:deep(.custom-message-box .el-message-box__btns .el-button--primary:hover) {
  background-color: var(--el-color-warning-light-3);
  border-color: var(--el-color-warning-light-3);
}
</style>
