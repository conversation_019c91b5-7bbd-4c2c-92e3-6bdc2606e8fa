<template>
  <el-card class="approval-flow-card" shadow="hover">
    <template #header>
      <div class="flow-header">
        <el-icon class="header-icon"><Document /></el-icon>
        <span class="header-title">审批流程</span>
      </div>
    </template>

    <el-timeline class="custom-timeline">
      <el-timeline-item
        v-for="(process, index) in processes"
        :key="index"
        :timestamp="process.node_name"
        placement="top"
        :type="getNodeType(index)"
        :hollow="index !== 0"
        :size="index === 0 ? 'large' : 'normal'"
      >
        <el-card
          class="flow-step-card"
          :class="{ 'is-first': index === 0 }"
          shadow="hover"
        >
          <template v-if="index === 0">
            <div class="flow-info applicant-info">
              <span class="info-label">
                <el-icon><User /></el-icon>
                申请人
              </span>
              <el-tag
                size="small"
                effect="light"
                type="success"
                class="user-tag"
              >
                {{ process.approver.name }}
                <span class="username">({{ process.approver.username }})</span>
              </el-tag>
            </div>
          </template>
          <template v-else>
            <div class="flow-info approver-info">
              <span class="info-label">
                <el-icon><UserFilled /></el-icon>
                审批人
              </span>
              <div class="approver-list">
                <el-tag
                  v-for="approver in process.approvers"
                  :key="approver.id"
                  size="small"
                  effect="light"
                  class="user-tag"
                >
                  {{ approver.name }}
                  <span class="username">({{ approver.username }})</span>
                </el-tag>
              </div>
            </div>
          </template>

          <div v-if="process?.ccs?.length > 0" class="flow-info cc-info">
            <span class="info-label">
              <el-icon><Message /></el-icon>
              抄送
            </span>
            <div class="approver-list">
              <el-tag
                v-for="cc in process.ccs"
                :key="cc.id"
                size="small"
                effect="light"
                type="info"
                class="user-tag"
              >
                {{ cc.name }}
                <span class="username">({{ cc.username }})</span>
              </el-tag>
            </div>
          </div>
        </el-card>
      </el-timeline-item>
    </el-timeline>
  </el-card>
</template>

<script lang="ts" setup>
import { Document, User, UserFilled, Message } from "@element-plus/icons-vue";

defineProps<{
  processes: any[];
}>();

const getNodeType = (index: number) => {
  if (index === 0) return "success";
  return "primary";
};
</script>

<style lang="scss" scoped>
.approval-flow-card {
  background: var(--el-bg-color);
  border-radius: 8px;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 12px rgb(0 0 0 / 10%);
    transform: translateY(-2px);
  }

  .flow-header {
    display: flex;
    gap: 8px;
    align-items: center;
    padding: 4px 0;

    .header-icon {
      font-size: 20px;
      color: var(--el-color-primary);
    }

    .header-title {
      font-size: 16px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }
  }

  .custom-timeline {
    padding: 16px 8px;

    :deep(.el-timeline-item__node) {
      &.is-large {
        width: 16px;
        height: 16px;
      }
    }

    :deep(.el-timeline-item__tail) {
      border-left: 2px solid var(--el-border-color-lighter);
    }

    :deep(.el-timeline-item__timestamp) {
      margin-bottom: 8px;
      font-size: 13px;
      color: var(--el-text-color-secondary);
    }
  }

  .flow-step-card {
    margin-bottom: 8px;
    border: 1px solid var(--el-border-color-lighter);
    border-radius: 8px;
    transition: all 0.3s ease;

    &:hover {
      border-color: var(--el-color-primary-light-7);
      transform: translateX(4px);
    }

    &.is-first {
      border-left: 4px solid var(--el-color-success);
    }

    .flow-info {
      display: flex;
      align-items: flex-start;
      padding: 12px;

      &:not(:last-child) {
        border-bottom: 1px dashed var(--el-border-color-lighter);
      }

      .info-label {
        display: flex;
        flex-shrink: 0;
        gap: 4px;
        align-items: center;
        min-width: 70px;
        margin-right: 12px;
        font-weight: 500;
        color: var(--el-text-color-regular);

        .el-icon {
          font-size: 16px;
        }
      }

      .approver-list {
        display: flex;
        flex: 1;
        flex-wrap: wrap;
        gap: 8px;
      }

      .user-tag {
        display: inline-flex;
        align-items: center;
        height: 24px;
        padding: 0 8px;
        margin: 0;
        border-radius: 4px;
        transition: all 0.2s ease;

        &:hover {
          transform: translateY(-1px);
        }

        .username {
          margin-left: 4px;
          font-size: 12px;
          font-weight: normal;
          color: var(--el-text-color-secondary);
        }
      }
    }

    .applicant-info {
      background-color: var(--el-color-success-light-9);
    }

    .approver-info {
      background-color: var(--el-color-primary-light-9);
    }

    .cc-info {
      background-color: var(--el-color-info-light-9);
    }
  }
}

// 暗色主题适配
html.dark {
  .approval-flow-card {
    .flow-step-card {
      &:hover {
        border-color: var(--el-color-primary);
      }

      .flow-info {
        &.applicant-info {
          background-color: var(--el-color-success-dark-9);
        }

        &.approver-info {
          background-color: var(--el-color-primary-dark-9);
        }

        &.cc-info {
          background-color: var(--el-color-info-dark-9);
        }
      }
    }
  }
}
</style>
