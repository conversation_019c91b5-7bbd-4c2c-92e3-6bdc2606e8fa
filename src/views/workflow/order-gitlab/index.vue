<template>
  <div>
    <el-row :gutter="20" class="order-container">
      <el-col :span="16">
        <el-card class="main-card" shadow="never" style="margin-left: 20px">
          <template #header>
            <div class="card-header">
              <h2 class="title">
                <iconify-icon-online icon="simple-icons:gitlab" class="icon" />
                GitLab项目权限申请
              </h2>
            </div>
          </template>
          <el-form
            ref="ruleFormRef"
            :model="form"
            class="order-form"
            status-icon
            label-position="top"
            size="large"
          >
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item
                  label="仓库"
                  prop="projects"
                  :rules="[
                    {
                      required: true,
                      message:
                        '请输入仓库关键字选择或者直接输入仓库地址，多个地址请回车输入',
                      trigger: 'change'
                    }
                  ]"
                >
                  <div
                    v-if="form.projects.length"
                    class="selected-projects-block"
                  >
                    <div
                      v-for="(proj, idx) in form.projects"
                      :key="proj"
                      class="project-tag-item full-info"
                    >
                      <span class="repo-index">{{ idx + 1 }}.</span>
                      <div class="repo-info-block">
                        <div class="repo-main-full">
                          <iconify-icon-online
                            icon="simple-icons:gitlab"
                            width="18"
                            height="18"
                            style="
                              margin-right: 4px;
                              color: #fc6d26;
                              vertical-align: middle;
                            "
                          />
                          <a
                            :href="proj"
                            target="_blank"
                            rel="noopener"
                            class="repo-link"
                            >{{ proj }}</a
                          >
                          <span v-if="getProjectDesc(proj)" class="repo-desc-inline"
                            >( {{ getProjectDesc(proj) }} )</span
                          >
                        </div>
                      </div>
                      <el-icon
                        class="close-btn"
                        @click.stop="form.projects.splice(idx, 1)"
                        ><close
                      /></el-icon>
                    </div>
                  </div>
                  <el-select
                    v-model="form.projects"
                    multiple
                    placeholder="请输入仓库关键字选择或者直接输入仓库地址，多个地址请回车输入"
                    allow-create
                    filterable
                    tag-type="primary"
                    tag-size="large"
                    tag-effect="plain"
                    clearable
                    remote
                    :remote-method="handleSearch"
                    :loading="loading"
                    :reserve-keyword="false"
                    default-first-option
                    class="custom-select hide-select-tags"
                    collapse-tags-tooltip
                  >
                    <el-option
                      v-for="project in allProjects"
                      :key="project.id"
                      :label="
                        project.http_url + ' (' + project.description + ')'
                      "
                      :value="project.http_url"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item
                  label="申请权限"
                  prop="permission"
                  :rules="[
                    { required: true, message: '请选择权限', trigger: 'change' }
                  ]"
                >
                  <el-select
                    v-model="form.permission"
                    placeholder="请选择权限"
                    class="custom-select"
                  >
                    <el-option label="Maintainer (项目管理员)" :value="40" />
                    <el-option label="Developer (开发人员)" :value="30" />
                    <el-option label="Reporter (报告者)" :value="20" />
                    <el-option label="Guest (访客)" :value="10" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="过期时间" prop="expired_at">
                  <el-date-picker
                    v-model="form.expired_at"
                    type="datetime"
                    placeholder="不选择默认不设置过期时间"
                    :disabled-date="disabledDateFunction"
                    value-on-clear
                    class="custom-select"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item
              label="需求描述"
              :rules="[
                { required: true, message: '请输入需求描述', trigger: 'blur' },
                {
                  max: 5000,
                  message: '需求描述不能超过5000个字符',
                  trigger: 'blur'
                }
              ]"
              prop="content"
            >
              <el-input
                v-model="form.content"
                type="textarea"
                show-word-limit
                maxlength="5000"
                :autosize="{ minRows: 6, maxRows: 16 }"
                placeholder="请详细描述您的需求..."
              />
            </el-form-item>

            <el-form-item>
              <el-button type="primary" @click="onSubmit">
                <iconify-icon-online
                  icon="material-symbols:send"
                  style="margin-right: 4px"
                />提交
              </el-button>
              <RouterLink :to="{ name: '我的工单' }">
                <el-button style="margin-left: 50px">
                  <iconify-icon-online
                    icon="material-symbols:cancel"
                    style="margin-right: 4px"
                  />
                  取消
                </el-button>
              </RouterLink>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
      <el-col :span="8">
        <ApprovalFlow :processes="processes" />
      </el-col>
    </el-row>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref } from "vue";
import { applyOrderAPI, type OrderForm } from "@/api/workflow/order-gitlab";
import { ElMessageBox, type FormInstance } from "element-plus";
import { message } from "@/utils/message";
import {
  getProcesses,
  processes,
  getAllProjects,
  allProjects,
  searchProjects,
  loading
} from "./hook";
import ApprovalFlow from "../components/ApprovalFlow.vue";

const form = ref<OrderForm>({
  content: "",
  projects: [],
  permission: undefined,
  expired_at: undefined,
  plan_time: undefined,
  orders: [],
  env: undefined,
  critical: false
});

const ruleFormRef = ref<FormInstance>();
const onSubmit = async () => {
  const valid = await new Promise(resolve => {
    ruleFormRef.value.validate((valid: boolean) => {
      resolve(valid);
    });
  });
  if (valid) {
    try {
      await ElMessageBox.confirm("是否确认提交工单？", "提交确认", {
        confirmButtonText: "提交",
        cancelButtonText: "取消",
        type: "warning",
        draggable: true,
        center: true,
        customClass: "custom-message-box",
        icon: "Warning"
      });
      const res = await applyOrderAPI(form.value);
      if (res.success) {
        message("提交成功", { type: "success" });
        setTimeout(() => {
          window.location.href = "/workflow/my/order";
        }, 300);
      } else {
        message(res.msg || "提交失败", { type: "error" });
      }
    } catch (error) {
      if (error !== "cancel") {
        message(error.message || "操作取消", { type: "warning" });
      }
    }
  } else {
    message("请检查表单数据", { type: "warning" });
  }
};

const disabledDateFunction = date => {
  const currentDate = new Date();
  const oneWorkingDayLater = getOneWorkingDayLater(currentDate);
  return date < oneWorkingDayLater || isWeekend(date);
};

// 计算一个工作日之后的日期
const getOneWorkingDayLater = date => {
  let nextDate = new Date(date);
  do {
    nextDate.setDate(nextDate.getDate() + 1);
  } while (isWeekend(nextDate));
  return nextDate;
};

// 判断是否为周末
const isWeekend = date => {
  const dayOfWeek = date.getDay();
  return dayOfWeek === 0 || dayOfWeek === 6;
};

// 处理搜索输入
const handleSearch = (query: string) => {
  if (query) {
    searchProjects(query);
  }
};

// 获取项目描述
function getProjectDesc(url: string) {
  const proj = allProjects.value?.find(p => p.http_url === url);
  return proj ? proj.description : "";
}

onMounted(() => {
  getProcesses();
  getAllProjects(); // 初始只加载少量数据
  // 设置默认值
  form.value.plan_time = getOneWorkingDayLater(new Date());
});
</script>

<style scoped>
.order-container {
  margin-top: 20px;
}

.main-card {
  margin-left: 20px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.main-card:hover {
  box-shadow: 0 6px 20px rgb(0 0 0 / 12%);
}

.card-header {
  display: flex;
  align-items: center;
  padding: 8px 0;
}

.title {
  display: flex;
  align-items: center;
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.icon {
  margin-right: 12px;
  font-size: 24px;
  color: #fc6d26; /* GitLab Orange */
}

.order-form {
  padding: 10px;
  margin-top: 20px;
}

.related-orders-select {
  width: 100%;
}

.custom-select {
  width: 100%;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-select .el-input) {
  transition: all 0.3s;
}

:deep(.el-input__inner) {
  transition: all 0.3s;
}

:deep(.el-input__inner:focus) {
  border-color: #fc6d26;
}

:deep(.el-textarea__inner) {
  transition: all 0.3s;
}

:deep(.el-textarea__inner:focus) {
  border-color: #fc6d26;
}

:deep(.el-date-editor.el-input) {
  transition: all 0.3s;
}

:deep(.el-button--primary) {
  padding: 12px 24px;
  font-weight: 500;
  transition: all 0.3s;
}

:deep(.el-button--primary:hover) {
  background-color: #e24329;
  box-shadow: 0 2px 8px rgb(226 67 41 / 20%);
}

:deep(.el-button--default) {
  padding: 12px 24px;
  font-weight: 500;
  transition: all 0.3s;
}

:deep(.el-button--default:hover) {
  background-color: #f2f6fc;
}

/* 表单提交按钮区域样式 */
:deep(.el-form-item:last-child) {
  padding-top: 20px;
  margin-top: 32px;
  border-top: 1px dashed #ebeef5;
}

:deep(.custom-message-box) {
  padding: 30px 30px 20px;
  border-radius: 8px;
}

:deep(.custom-message-box .el-message-box__status) {
  top: 30px;
  font-size: 24px !important;
}

:deep(.custom-message-box .el-message-box__header) {
  padding: 0 0 10px;
}

:deep(.custom-message-box .el-message-box__title) {
  font-size: 16px;
  font-weight: 500;
}

:deep(.custom-message-box .el-message-box__content) {
  padding: 20px 0;
  font-size: 14px;
}

:deep(.custom-message-box .el-message-box__btns) {
  padding: 10px 0 0;
}

:deep(.custom-message-box .el-message-box__btns button) {
  min-width: 80px;
  padding: 8px 20px;
  margin-left: 12px;
  font-size: 14px;
  border-radius: 4px;
}

:deep(.custom-message-box .el-message-box__btns .el-button--primary) {
  background-color: var(--el-color-warning);
  border-color: var(--el-color-warning);
}

:deep(.custom-message-box .el-message-box__btns .el-button--primary:hover) {
  background-color: var(--el-color-warning-light-3);
  border-color: var(--el-color-warning-light-3);
}

.hide-select-tags :deep(.el-select__tags) {
  display: none;
}

.selected-projects-block {
  display: flex;
  flex-direction: column;
  gap: 2px 0;
  padding: 4px 0 2px;
  margin-top: 6px;
  border-radius: 8px;
}

.project-tag-item.full-info {
  position: relative;
  display: flex;
  align-items: flex-start;
  min-height: 28px;
  padding: 4px 8px 4px 6px;
  margin: 0 0 1px;
  font-size: 13px;
  background: none;
  border-radius: 6px;
  box-shadow: none;
  transition: none;
}

.project-tag-item.full-info:hover {
  background: none;
  box-shadow: none;
}

.repo-index {
  min-width: 18px;
  margin-right: 8px;
  font-size: 14px;
  font-weight: bold;
  color: #e24329;
  text-align: right;
}

.repo-info-block {
  display: flex;
  flex: 1;
  flex-direction: column;
  min-width: 0;
}

.repo-main-full {
  display: flex;
  align-items: center;
  margin-bottom: 2px;
  font-size: 14px;
  font-weight: 500;
  color: #e24329;
  word-break: break-all;
}

.repo-link {
  color: #e24329;
  text-decoration: underline dotted;
  word-break: break-all;
  transition: color 0.2s;
}

.repo-link:hover {
  color: #fc6d26;
  text-decoration: underline solid;
}

.repo-desc-inline {
  margin-left: 2px;
  font-size: 12px;
  color: #888;
}

.repo-desc-full {
  margin-top: 1px;
  margin-left: 22px;
  font-size: 12px;
  color: #888;
  word-break: break-all;
}

.close-btn {
  flex-shrink: 0;
  margin-left: 10px;
  font-size: 16px;
  color: #e24329;
  cursor: pointer;
  transition: color 0.2s;
}

.close-btn:hover {
  color: #fc6d26;
}
</style>
