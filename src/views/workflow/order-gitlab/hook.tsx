import { getProcessesAPI, type Process } from "@/api/workflow/process";
import { OrderType } from "@/config/order-enum";
import {
  getProjectListAPI,
  type Project,
  type ProjectListParams
} from "@/api/appops/gitcode/project";
import { message } from "@/utils/message";
import { ref } from "vue";

export const processes = ref<Process[]>([]);
export const getProcesses = () => {
  getProcessesAPI(OrderType.GitlabOrderType, undefined)
    .then(res => {
      if (res.success) {
        processes.value = res.data;
      } else {
        message(res.msg, { type: "error" });
      }
    })
    .catch(error => {
      message("请求失败" + error, { type: "error" });
    });
};

export const allProjects = ref<Project[]>([]);
export const loading = ref<boolean>(false);
export const keyword = ref<string>("");

// 防抖函数
function debounce(fn: Function, delay: number) {
  let timer: NodeJS.Timeout | null = null;
  return function (...args: any[]) {
    if (timer) clearTimeout(timer);
    timer = setTimeout(() => {
      fn.apply(this, args);
    }, delay);
  };
}

// 根据关键词搜索项目
export const searchProjects = debounce(async (search: string) => {
  if (!search || search.length < 2) {
    // 如果搜索词为空或太短，不执行搜索
    allProjects.value = [];
    return;
  }

  loading.value = true;
  try {
    const params: ProjectListParams = {
      keyword: search,
      page: 1,
      limit: 50 // 限制返回数量，提高性能
    };

    const res = await getProjectListAPI(params);
    if (res.success) {
      allProjects.value = res.data || [];
    } else {
      message(res.msg, { type: "error" });
    }
  } catch (error) {
    message("请求失败" + error, { type: "error" });
  } finally {
    loading.value = false;
  }
}, 300); // 300ms的防抖延迟

// 保留原来的函数，但改为只在初始化时获取少量数据
export const getAllProjects = () => {
  loading.value = true;
  // 使用分页API替代获取所有项目
  getProjectListAPI({
    page: 1,
    limit: 1001 // 只获取前20个项目
  })
    .then(res => {
      if (res.success) {
        allProjects.value = res.data || [];
      } else {
        message(res.msg, { type: "error" });
      }
    })
    .catch(error => {
      message("请求失败" + error, { type: "error" });
    })
    .finally(() => {
      loading.value = false;
    });
};
