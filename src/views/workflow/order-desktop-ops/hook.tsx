import { getProcessesAPI, type Process } from "@/api/workflow/process";
import { OrderType } from "@/config/order-enum";
import { message } from "@/utils/message";
import { ref } from "vue";

export const processes = ref<Process[]>([]);
export const applicantID = ref<number | undefined>(undefined);
export const helpApply = ref<boolean>(false);
export const getProcesses = () => {
  getProcessesAPI(OrderType.DesktopOPSOrderType, applicantID.value)
    .then(res => {
      if (res.success) {
        processes.value = res.data;
      } else {
        message(res.msg, { type: "error" });
      }
    })
    .catch(error => {
      message("请求失败" + error, { type: "error" });
    });
};
