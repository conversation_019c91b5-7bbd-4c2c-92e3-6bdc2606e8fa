<template>
  <div class="desktop-support-container">
    <div class="page-header">
      <h1 class="page-title">
        <iconify-icon-online
          icon="material-symbols:computer"
          class="page-icon"
        />
        桌面技术支持
      </h1>
    </div>
    <el-row :gutter="24" class="order-container">
      <el-col :span="16">
        <el-card class="main-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <h2 class="title">
                <iconify-icon-online
                  icon="material-symbols:description-outline-rounded"
                  class="icon"
                />
                <span>工单信息</span>
              </h2>
            </div>
          </template>

          <el-form
            ref="ruleFormRef"
            :model="form"
            class="order-form"
            status-icon
            label-position="top"
            size="large"
          >
            <div
              v-if="hasRole('workflow_admin')"
              class="form-section admin-section"
            >
              <div class="section-header">
                <iconify-icon-online
                  icon="material-symbols:admin-panel-settings-outline"
                  class="section-icon"
                />
                <span class="section-title">管理员设置</span>
              </div>

              <div class="admin-controls-row">
                <el-form-item
                  label="补充申请"
                  prop="help_apply"
                  class="form-item admin-control-item switch-item"
                >
                  <el-switch v-model="form.help_apply" active-color="#409EFF" />
                </el-form-item>

                <el-form-item
                  v-if="form.help_apply"
                  label="申请人"
                  prop="applicant_id"
                  class="form-item admin-control-item applicant-item"
                  :rules="[
                    {
                      required: true,
                      message: '请选择申请人',
                      trigger: 'change'
                    }
                  ]"
                >
                  <div class="applicant-select-container">
                    <el-select
                      v-model="form.applicant_id"
                      placeholder="请选择申请人"
                      class="applicant-select"
                      filterable
                      remote
                      :remote-method="filterUsers"
                      :loading="loading"
                      popper-class="applicant-select-dropdown"
                      :popper-options="{ gpuAcceleration: false }"
                    >
                      <el-option
                        v-for="item in users"
                        :key="item.id"
                        :label="`${item.name} (${item.username})`"
                        :value="item.id"
                      >
                        <div class="applicant-option">
                          <span class="applicant-name">{{ item.name }}</span>
                          <span class="applicant-username">{{
                            item.username
                          }}</span>
                        </div>
                      </el-option>
                    </el-select>
                    <el-button
                      type="primary"
                      class="refresh-button"
                      @click="getUsers"
                    >
                      <iconify-icon-online
                        icon="mdi:refresh"
                        style="margin-right: 4px"
                      />
                      刷新
                    </el-button>
                  </div>
                </el-form-item>
                <el-form-item
                  v-if="form.help_apply && hasRole('workflow_admin')"
                  label="耗时"
                  class="form-item admin-control-item duration-item"
                >
                  <div class="duration-inputs">
                    <el-input-number
                      v-model="service_duration.hours"
                      :min="0"
                      :step="0.1"
                      placeholder="时"
                      class="duration-input"
                      controls-position="right"
                    >
                    </el-input-number>
                    <span>时</span>
                  </div>
                </el-form-item>
              </div>
            </div>

            <div class="form-section">
              <div class="section-header">
                <iconify-icon-online
                  icon="material-symbols:description-outline"
                  class="section-icon"
                />
                <span class="section-title">基本信息</span>
              </div>
              <div class="form-section-row">
                <div class="form-section-col">
                  <el-form-item
                    label="工位"
                    prop="cubicle"
                    class="form-item"
                    :rules="[
                      {
                        required: true,
                        message: '请输入工位',
                        trigger: 'blur'
                      },
                      {
                        max: 255,
                        message: '工位长度不能超过255个字符',
                        trigger: 'blur'
                      }
                    ]"
                  >
                    <el-input
                      v-model="form.cubicle"
                      clearable
                      maxlength="255"
                      show-word-limit
                      placeholder="请输入工位"
                    >
                    </el-input>
                  </el-form-item>
                </div>
                <div class="form-section-col">
                  <el-form-item
                    label="期望完成时间"
                    prop="plan_time"
                    class="form-item"
                    :rules="[
                      {
                        required: true,
                        message: '请选择期望完成时间',
                        trigger: 'change'
                      }
                    ]"
                  >
                    <el-date-picker
                      v-model="form.plan_time"
                      type="datetime"
                      placeholder="选择日期时间"
                      value-on-clear
                      class="date-picker"
                    />
                  </el-form-item>
                </div>
              </div>

              <el-form-item
                label="需求描述"
                prop="content"
                class="form-item"
                :rules="[
                  {
                    required: true,
                    message: '请输入需求描述',
                    trigger: 'blur'
                  },
                  {
                    min: 10,
                    message: '需求描述不能少于10个字符',
                    trigger: 'blur'
                  }
                ]"
              >
                <el-input
                  v-model="form.content"
                  type="textarea"
                  :rows="6"
                  placeholder="请详细描述您的需求"
                  maxlength="2000"
                  show-word-limit
                />
              </el-form-item>
            </div>

            <div class="form-section action-section">
              <div class="action-buttons">
                <el-button
                  type="primary"
                  class="submit-button"
                  @click="onSubmit"
                >
                  <iconify-icon-online
                    icon="material-symbols:send"
                    style="margin-right: 4px"
                  />
                  提交
                </el-button>
                <RouterLink to="/workflow/order-list">
                  <el-button class="cancel-button">
                    <iconify-icon-online
                      icon="material-symbols:cancel"
                      style="margin-right: 4px"
                    />
                    取消
                  </el-button>
                </RouterLink>
              </div>
            </div>
          </el-form>
        </el-card>
      </el-col>
      <el-col :span="8">
        <ApprovalFlow :processes="processes" />
      </el-col>
    </el-row>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, watch, h } from "vue";
import {
  applyOrderAPI,
  type OrderForm
} from "@/api/workflow/order-desktop-ops";
import { ElMessageBox, type FormInstance } from "element-plus";
import { message } from "@/utils/message";
import { getProcesses, processes, applicantID } from "./hook";
import { getAllUsersAPI, type User } from "@/api/auth/user";
import ApprovalFlow from "../components/ApprovalFlow.vue";
import { hasRole } from "@/router/utils";
const form = ref<OrderForm>({
  content: "",
  plan_time: new Date(Date.now() + 24 * 60 * 60 * 1000), // Default to 24 hours from now
  cubicle: undefined,
  applicant_id: undefined,
  service_duration_seconds: 0
});

// 添加计算属性，将天时分秒转换为秒
const calculateTotalSeconds = (): number => {
  const { hours } = service_duration.value;
  return (
    hours * 60 * 60 // 小时转秒
  );
};
const ruleFormRef = ref<FormInstance>();
const onSubmit = async () => {
  const valid = await new Promise(resolve => {
    ruleFormRef.value.validate((valid: boolean) => {
      resolve(valid);
    });
  });
  if (valid) {
    form.value.service_duration_seconds = calculateTotalSeconds();
    try {
      // 使用confirm方法创建确认对话框
      await ElMessageBox.confirm(
        h("div", { class: "custom-confirm-content" }, [
          h("iconify-icon-online", {
            icon: "material-symbols:help-outline-rounded",
            class: "confirm-icon"
          }),
          h("span", null, "确定要提交此工单吗？")
        ]),
        "提交确认",
        {
          confirmButtonClass: "confirm-button-with-icon",
          cancelButtonClass: "cancel-button-with-icon",
          confirmButtonText: "确认提交",
          cancelButtonText: "取消",
          type: "warning",
          draggable: true,
          center: true,
          customClass: "custom-message-box enhanced-dialog",
          showClose: true,
          closeOnClickModal: false,
          closeOnPressEscape: true
        }
      );
      const res = await applyOrderAPI(form.value);
      if (res.success) {
        message("提交成功", { type: "success" });
        setTimeout(() => {
          window.location.href = "/workflow/my/order";
        }, 300);
      } else {
        message(res.msg || "提交失败", { type: "error" });
      }
    } catch (error) {
      if (error !== "cancel") {
        message(error.message || "操作取消", { type: "warning" });
      }
    }
  } else {
    message("请检查表单数据", { type: "warning" });
  }
};

const loading = ref(false);
const allUsers = ref<User[]>([]);
const users = ref<User[]>([]);
const service_duration = ref({
  hours: 0.5
});
const filterUsers = (query: string) => {
  if (query) {
    const lowercaseQuery = query.toLowerCase();
    users.value = allUsers.value.filter(
      user =>
        user.name.toLowerCase().includes(lowercaseQuery) ||
        user.username.toLowerCase().includes(lowercaseQuery)
    );
  } else {
    users.value = [...allUsers.value];
  }
};

// Function to refresh the users list
const getUsers = () => {
  loading.value = true;
  getAllUsersAPI()
    .then(res => {
      if (res.success) {
        allUsers.value = res.data as User[];
        users.value = [...allUsers.value];
      } else {
        message(res.msg || "获取用户列表失败", { type: "error" });
      }
    })
    .catch(error => {
      message("获取用户列表失败: " + error, { type: "error" });
    })
    .finally(() => {
      loading.value = false;
    });
};

// 监听help_apply的变化
watch(
  () => form.value.help_apply,
  () => {
    if (!form.value.help_apply) {
      form.value.applicant_id = undefined;
      applicantID.value = undefined;
    }
  }
);
// 监听applicant_id的变化
watch(
  () => form.value.applicant_id,
  () => {
    if (form.value.applicant_id) {
      applicantID.value = form.value.applicant_id;
      getProcesses();
    }
  }
);

onMounted(() => {
  getProcesses();
  // Initialize users list
  getUsers();
});
</script>

<style scoped>
/* Responsive adjustments */
@media (width <= 1200px) {
  .main-card {
    margin-right: 0;
  }

  .support-form {
    padding: 10px;
  }
}

.desktop-support-container {
  min-height: calc(100vh - 60px);
  padding: 0 0 32px;
  background-color: #f5f7fa;
}

.page-header {
  padding: 16px 24px;
  margin-bottom: 24px;
  background-color: #fff;
  border-bottom: 1px solid #ebeef5;
  box-shadow: 0 2px 8px rgb(0 0 0 / 5%);
}

.page-title {
  display: flex;
  align-items: center;
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.page-icon {
  margin-right: 12px;
  font-size: 24px;
  color: #409eff;
}

.order-container {
  padding: 0 24px;
}

.main-card {
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 8%);
  transition: all 0.3s ease;
}

.main-card:hover {
  box-shadow: 0 6px 20px rgb(0 0 0 / 12%);
}

.section-header {
  display: flex;
  align-items: center;
  padding-bottom: 8px;
  margin-bottom: 12px;
  border-bottom: 1px solid #ebeef5;
}

.section-icon {
  margin-right: 8px;
  font-size: 20px;
  color: #409eff;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.title {
  display: flex;
  align-items: center;
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.icon {
  margin-right: 8px;
  font-size: 18px;
  color: #409eff;
}

.order-form {
  padding: 10px 0;
}

/* 表单项悬停效果 */
.el-form-item {
  padding: 2px 8px;
  margin-bottom: 12px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.el-form-item:hover {
  background-color: rgb(64 158 255 / 5%);
}

/* 表单分区动画效果 */
.form-section-animation {
  transition: all 0.3s ease;
}

.form-section-animation:hover .section-header .section-icon {
  transform: scale(1.1);
}

.admin-controls-row {
  display: flex;
  flex-wrap: nowrap;
  gap: 20px;
  align-items: center;
  width: 100%;
  min-height: 50px;
}

.applicant-select-container {
  display: flex;
  gap: 8px;
  align-items: center;
  width: 100%;
}

.form-section-row {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 10px;
}

.form-section-col {
  flex: 1;
  min-width: 250px;
  padding: 0 10px;
  margin-bottom: 4px;
}

.applicant-select {
  flex: 1;
  min-width: 240px;
}

.applicant-item {
  flex: 2;
  min-width: 280px;
}

.applicant-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.applicant-name {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.applicant-username {
  margin-left: 8px;
  font-size: 13px;
  color: #909399;
}

.duration-inputs {
  display: flex;
  gap: 8px;
  align-items: center;
  width: 100%;
  height: 40px;
}

.duration-item {
  flex: 1;
  min-width: 150px;
}

.duration-input {
  width: 120px;
}

/* 申请人选择下拉框样式 */
:deep(.applicant-select-dropdown) {
  min-width: 280px !important;
  max-width: 400px !important;
  padding: 8px 0;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 10%);
}

:deep(.applicant-select-dropdown .el-select-dropdown__item) {
  padding: 10px 16px;
  line-height: 1.5;
  transition: all 0.2s;
}

:deep(.applicant-select-dropdown .el-select-dropdown__item:hover) {
  background-color: #f5f7fa;
}

:deep(.applicant-select-dropdown .el-select-dropdown__item.selected) {
  font-weight: 500;
  color: #409eff;
  background-color: #ecf5ff;
}

.refresh-button {
  flex-shrink: 0;
  height: 40px;
  padding: 0 12px;
  margin-top: 0;
  margin-bottom: 0;
  transition: all 0.3s ease;
}

.refresh-button:hover {
  box-shadow: 0 4px 12px rgb(64 158 255 / 30%);
  transform: translateY(-2px);
}

.content-textarea {
  border-radius: 4px;
  transition: all 0.3s;
}

.content-textarea:focus {
  box-shadow: 0 0 0 2px rgb(64 158 255 / 20%);
}

.date-picker {
  width: 100%;
  min-width: 240px;
}

.action-buttons {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin-top: 0;
}

.action-section {
  margin-top: 30px;
}

.submit-button {
  min-width: 120px;
  padding: 12px 24px;
  font-weight: 500;
}

.submit-button:hover {
  background-color: #66b1ff;
  box-shadow: 0 2px 8px rgb(64 158 255 / 20%);
}

.cancel-button {
  display: flex;
  align-items: center;
  padding: 12px 24px;
  font-weight: 500;
}

.cancel-button:hover {
  background-color: #f2f6fc;
}

/* Custom message box styling */
:deep(.custom-message-box) {
  border-radius: 8px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 12%);
}

:deep(.custom-message-box .el-message-box__header) {
  padding: 16px 20px;
}

:deep(.custom-message-box .el-message-box__content) {
  padding: 24px 20px;
  font-size: 16px;
}

:deep(.custom-message-box .el-message-box__btns) {
  padding: 12px 20px;
}

/* 添加表单元素优化样式 */
:deep(.el-input__inner) {
  transition: all 0.3s;
}

:deep(.el-input__inner:focus) {
  border-color: #409eff;
}

:deep(.el-textarea__inner) {
  transition: all 0.3s;
}

:deep(.el-textarea__inner:focus) {
  border-color: #409eff;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-select .el-input) {
  transition: all 0.3s;
}

:deep(.el-date-editor.el-input) {
  transition: all 0.3s;
}
</style>

<style scoped>
.icon {
  margin-right: 8px;
}

:deep(.custom-message-box) {
  padding: 30px 30px 20px;
  border-radius: 8px;
}

:deep(.custom-message-box .el-message-box__status) {
  top: 30px;
  font-size: 24px !important;
}

:deep(.custom-message-box .el-message-box__header) {
  padding: 0 0 10px;
}

:deep(.custom-message-box .el-message-box__title) {
  font-size: 16px;
  font-weight: 500;
}

:deep(.custom-message-box .el-message-box__content) {
  padding: 20px 0;
  font-size: 14px;
}

:deep(.custom-message-box .el-message-box__btns) {
  padding: 10px 0 0;
}

:deep(.custom-message-box .el-message-box__btns button) {
  min-width: 80px;
  padding: 8px 20px;
  margin-left: 12px;
  font-size: 14px;
  border-radius: 4px;
}

:deep(.custom-message-box .el-message-box__btns .el-button--primary) {
  background-color: var(--el-color-warning);
  border-color: var(--el-color-warning);
}

:deep(.custom-message-box .el-message-box__btns .el-button--primary:hover) {
  background-color: var(--el-color-warning-light-3);
  border-color: var(--el-color-warning-light-3);
}

/* 增强版对话框样式 */
:deep(.enhanced-dialog) {
  overflow: hidden;
  border: none;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgb(0 0 0 / 15%);
}

:deep(.enhanced-dialog .el-message-box__header) {
  padding: 16px 20px;
  background-color: #f9f9f9;
  border-bottom: 1px solid #eee;
}

:deep(.enhanced-dialog .confirm-title) {
  display: flex;
  gap: 8px;
  align-items: center;
}

:deep(.enhanced-dialog .title-icon) {
  font-size: 20px;
  color: var(--el-color-warning);
}

:deep(.enhanced-dialog .el-message-box__content) {
  padding: 24px 20px;
  font-size: 16px;
}

:deep(.enhanced-dialog .confirm-content) {
  display: flex;
  gap: 10px;
  align-items: center;
  padding: 10px 0;
}

:deep(.enhanced-dialog .confirm-icon) {
  font-size: 24px;
  color: var(--el-color-warning);
}

:deep(.enhanced-dialog .el-message-box__btns) {
  padding: 16px 20px;
  background-color: #f9f9f9;
  border-top: 1px solid #eee;
}

:deep(.enhanced-dialog .el-button) {
  padding: 10px 20px;
  font-weight: 500;
  border-radius: 6px;
  transition: all 0.3s;
}

:deep(.enhanced-dialog .el-button--primary:hover) {
  background-color: var(--el-color-primary-light-3);
  border-color: var(--el-color-primary-light-3);
  box-shadow: 0 4px 12px rgb(64 158 255 / 20%);
  transform: translateY(-2px);
}

:deep(.enhanced-dialog .el-button--default:hover) {
  border-color: #dcdfe6;
  box-shadow: 0 4px 12px rgb(0 0 0 / 10%);
  transform: translateY(-2px);
}

:deep(.enhanced-dialog .confirm-btn-text),
:deep(.enhanced-dialog .cancel-btn-text) {
  display: flex;
  align-items: center;
}

/* 按钮图标样式 */
:deep(.confirm-button-with-icon) {
  position: relative;
  padding-left: 36px !important;
}

:deep(.confirm-button-with-icon)::before {
  position: absolute;
  top: 50%;
  left: 12px;
  width: 16px;
  height: 16px;
  content: "";
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"><path fill="white" d="M9 16.17L4.83 12l-1.42 1.41L9 19L21 7l-1.41-1.41L9 16.17z"/></svg>')
    no-repeat center center;
  transform: translateY(-50%);
}

:deep(.cancel-button-with-icon) {
  position: relative;
  padding-left: 36px !important;
}

:deep(.cancel-button-with-icon)::before {
  position: absolute;
  top: 50%;
  left: 12px;
  width: 16px;
  height: 16px;
  content: "";
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"><path fill="%23606266" d="M19 6.41L17.59 5L12 10.59L6.41 5L5 6.41L10.59 12L5 17.59L6.41 19L12 13.41L17.59 19L19 17.59L13.41 12L19 6.41z"/></svg>')
    no-repeat center center;
  transform: translateY(-50%);
}

/* 自定义确认内容样式 */
:deep(.custom-confirm-content) {
  display: flex;
  gap: 12px;
  align-items: center;
  padding: 10px 0;
  font-size: 16px;
}

:deep(.custom-confirm-content .confirm-icon) {
  font-size: 24px;
  color: var(--el-color-warning);
}

/* 添加标题图标 */
:deep(.enhanced-dialog .el-message-box__title) {
  position: relative;
  padding-left: 30px;
}

:deep(.enhanced-dialog .el-message-box__title)::before {
  position: absolute;
  top: 50%;
  left: 0;
  width: 24px;
  height: 24px;
  content: "";
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path fill="%23E6A23C" d="M4 20q-.825 0-1.413-.588T2 18V6q0-.825.588-1.413T4 4h16q.825 0 1.413.588T22 6v12q0 .825-.588 1.413T20 20H4Zm8-7l8-5V6l-8 5l-8-5v2l8 5Z"/></svg>')
    no-repeat center center;
  transform: translateY(-50%);
}

/* 确认按钮样式 */
:deep(.enhanced-dialog .el-button--primary) {
  position: relative;
  overflow: hidden;
}

:deep(.enhanced-dialog .el-button--primary)::before {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  content: "";
  background: linear-gradient(
    90deg,
    transparent,
    rgb(255 255 255 / 20%),
    transparent
  );
  transition: 0.5s;
}

:deep(.enhanced-dialog .el-button--primary):hover::before {
  left: 100%;
}

/* 表单项标签样式 */
:deep(.el-form-item__label) {
  padding-bottom: 4px;
  font-size: 14px;
  font-weight: 500;
  line-height: 1.3;
  color: #303133;
}

.switch-item {
  flex-shrink: 0;
  width: 120px;
}

/* 表单项内容区域样式 */
:deep(.el-form-item__content) {
  margin-top: 2px;
  line-height: 1.4;
}

/* 动态表单项样式 */
.el-form-item[v-if] {
  animation: fade-in 0.5s ease;
}

:deep(.el-form-item__label-extra) {
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

:deep(.el-form-item) {
  margin-bottom: 24px;
}

/* 输入框样式增强 */
:deep(.el-input__inner) {
  height: 40px;
  line-height: 40px;
  border-radius: 4px;
  transition: all 0.3s;
}

:deep(.el-input__inner:focus) {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgb(64 158 255 / 20%);
}

:deep(.el-textarea__inner) {
  padding: 12px;
  line-height: 1.6;
  border-radius: 4px;
  transition: all 0.3s;
}

:deep(.el-textarea__inner:focus) {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgb(64 158 255 / 20%);
}

:deep(.el-date-editor.el-input) {
  width: 100%;
  transition: all 0.3s;
}
</style>
