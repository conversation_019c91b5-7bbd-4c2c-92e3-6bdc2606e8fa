import dayjs from "dayjs";
import type { PaginationProps } from "@pureadmin/table";
import { reactive, ref, onMounted } from "vue";
import { message } from "@/utils/message";
import { getOrdersAPI } from "@/api/workflow/order";
import {
  ENVColors,
  ENVs,
  OrderExtTypes,
  OrderStatusColors,
  OrderStatuses
} from "@/config/order-enum";
import type { Order } from "@/api/workflow/order";
import { getAllUsersAPI, type User } from "@/api/auth/user";
import { RouterLink } from "vue-router";
import { formatDuration } from "@/utils/date";

export function useRole() {
  const form = reactive({
    keyword: undefined,
    ext_type: undefined,
    status: undefined,
    applicant_id: undefined
  });
  const dataList = ref<Order[]>([]);
  const loading = ref<boolean>(true);
  const users = ref<User[]>([]);

  function getAllUsers() {
    getAllUsersAPI()
      .then(res => {
        if (res.success) {
          users.value = res.data as User[];
        } else {
          message(res.msg, { type: "error" });
        }
      })
      .catch(error => {
        message("请求失败" + error, { type: "error" });
      });
  }

  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true,
    pageSizes: [10, 20, 30, 40, 50, 100]
  });

  const columns: TableColumnList = [
    {
      label: "工单信息",
      prop: "order_info",
      cellRenderer: ({ row }) => {
        const extType = OrderExtTypes.get(row.ext_type);
        const orderType = row.order_type;
        const displayType =
          extType === orderType ? extType : `${extType} - ${orderType}`;
        return (
          <div class="order-info-cell flex flex-col items-start gap-0.5 py-1">
            <div class="order-info-title-row flex items-center gap-2 mb-0.5">
              <el-tooltip content={row.title} placement="top">
                <RouterLink to={{ name: "工单详情", params: { id: row.id } }}>
                  <el-link
                    size="large"
                    type="primary"
                    underline={false}
                    class="order-info-title"
                  >
                    {row.title}
                  </el-link>
                </RouterLink>
              </el-tooltip>
              {row.critical && (
                <el-tag
                  type="danger"
                  size="small"
                  effect="dark"
                  class="ml-1 order-info-critical flex items-center gap-1"
                  style={{
                    fontWeight: 500,
                    padding: "1px 7px",
                    borderRadius: "6px",
                    fontSize: "12px",
                    lineHeight: "18px"
                  }}
                >
                  <i class="iconfont icon-warning text-xs mr-1" />
                  紧急
                </el-tag>
              )}
              {row.env > 0 && (
                <el-tag
                  type={ENVColors.get(row.env)}
                  size="small"
                  effect="light"
                  class="ml-1 order-info-env flex items-center gap-1"
                  style={{
                    fontWeight: 500,
                    padding: "2px 7px",
                    borderRadius: "6px",
                    fontSize: "12px",
                    lineHeight: "18px"
                  }}
                >
                  <i class="iconfont icon-env text-xs mr-1" />
                  {ENVs.get(row.env)}
                </el-tag>
              )}
            </div>
            <div class="order-info-type-row mt-0.5">
              <el-text
                class="order-info-type text-[13px] font-normal tracking-wide"
                style={{ lineHeight: "18px" }}
                type="info"
                size="small"
              >
                <i class="iconfont icon-ticket text-xs mr-1" />
                {displayType}
              </el-text>
            </div>
          </div>
        );
      }
    },
    {
      label: "状态",
      prop: "status",
      width: 150,
      align: "center",
      cellRenderer: ({ row }) => (
        <div style="white-space: nowrap; display: flex; flex-direction: column; align-items: center; justify-content: center;">
          <el-tag
            type={OrderStatusColors.get(row.status)}
            effect="light"
            size="default"
            style={{
              fontWeight: 500,
              padding: "4px 8px",
              borderRadius: "4px"
            }}
          >
            {OrderStatuses.get(row.status)}
          </el-tag>
          {row.status === 2 && row.service_duration_seconds > 0 && (
            <div style="margin-top: 14px;">
              <el-tooltip
                content={`处理耗时：${formatDuration(row.service_duration_seconds)}`}
                placement="top"
              >
                <el-tag type="info" size="small" effect="plain">
                  {formatDuration(row.service_duration_seconds)}
                </el-tag>
              </el-tooltip>
            </div>
          )}
        </div>
      )
    },
    {
      label: "申请人",
      prop: "applicant",
      width: 160,
      align: "center",
      cellRenderer: ({ row }) => (
        <div class="flex items-center">
          <el-avatar
            size={24}
            src={row?.applicant?.avatar || ""}
            class="mr-1"
            style="background: var(--el-color-primary-light-5)"
          >
            {row?.applicant?.name?.[0]}
          </el-avatar>
          <div class="flex flex-col">
            <span class="font-medium text-[14px]">{row?.applicant?.name}</span>
            <span class="text-[12px] text-[var(--el-text-color-secondary)]">
              {row?.applicant?.username}
            </span>
          </div>
        </div>
      )
    },
    {
      label: "时间信息",
      prop: "time_info",
      width: 180,
      align: "center",
      cellRenderer: ({ row }) => (
        <div class="flex flex-col gap-1">
          <el-tooltip
            content={`创建时间：${dayjs(row.created_at).format("YYYY-MM-DD HH:mm:ss")}`}
            placement="top"
          >
            <div class="flex items-center whitespace-nowrap">
              <el-tag
                size="small"
                type="info"
                class="mr-1"
                style="padding: 0 4px;"
              >
                创 建
              </el-tag>
              <span class="text-gray-600">
                {dayjs(row.created_at).format("YYYY-MM-DD HH:mm")}
              </span>
            </div>
          </el-tooltip>
          {row.plan_time && (
            <el-tooltip
              content={`计划执行：${dayjs(row.plan_time).format("YYYY-MM-DD HH:mm:ss")}`}
              placement="top"
            >
              <div class="flex items-center whitespace-nowrap">
                <el-tag
                  size="small"
                  type="success"
                  class="mr-1"
                  style="padding: 0 4px;"
                >
                  计 划
                </el-tag>
                <span class="text-gray-600">
                  {dayjs(row.plan_time).format("YYYY-MM-DD HH:mm")}
                </span>
              </div>
            </el-tooltip>
          )}
        </div>
      )
    },
    {
      label: "操作",
      prop: "action",
      width: 160,
      align: "center",
      cellRenderer: ({ row }) => (
        <div class="flex justify-center items-center">
          <RouterLink to={{ name: "工单详情", params: { id: row.id } }}>
            <el-button type="primary" size="large" link plain>
              查看
            </el-button>
          </RouterLink>
          <RouterLink
            to={{ name: "工单详情", params: { id: row.id } }}
            target="_blank"
            class="ml-2"
          >
            <el-button type="success" size="large" link plain>
              在新窗口查看
            </el-button>
          </RouterLink>
        </div>
      )
    }
  ];

  function handleSizeChange(val: number) {
    pagination.pageSize = val;
    onSearch();
  }

  function handleCurrentChange(val: number) {
    pagination.currentPage = val;
    onSearch();
  }

  async function onSearch() {
    loading.value = true;
    getOrdersAPI({
      page: pagination.currentPage,
      limit: pagination.pageSize,
      keyword: form.keyword,
      status: form.status,
      ext_type: form.ext_type,
      applicant_id: form.applicant_id
    })
      .then(res => {
        if (res.success) {
          dataList.value = res.data;
          pagination.total = res.count;
          pagination.pageSize = pagination.pageSize;
          pagination.currentPage = pagination.currentPage;
        } else {
          dataList.value = [];
          pagination.total = 0;
          pagination.pageSize = pagination.pageSize;
          pagination.currentPage = pagination.currentPage;
        }
      })
      .catch(() => {
        message("请求失败", { type: "error" });
      })
      .finally(() => {
        loading.value = false;
      });
  }

  const resetForm = formEl => {
    if (!formEl) return;
    formEl.resetFields();
    form.ext_type = undefined;
    onSearch();
  };

  onMounted(() => {
    onSearch();
    getAllUsers();
  });

  return {
    form,
    loading,
    columns,
    dataList,
    pagination,
    onSearch,
    resetForm,
    handleSizeChange,
    handleCurrentChange,
    users,
    getAllUsers
  };
}
