<script setup lang="ts">
import { ref } from "vue";
import { useRole } from "./hook";
import { PureTableBar } from "@/components/RePureTableBar";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import Refresh from "@iconify-icons/ep/refresh";
import { OrderStatuses, OrderExtTypes } from "@/config/order-enum";

defineOptions({
  name: "Orders"
});

const formRef = ref();
const tableRef = ref();

const {
  form,
  loading,
  columns,
  dataList,
  pagination,
  onSearch,
  resetForm,
  handleSizeChange,
  handleCurrentChange,
  users,
  getAllUsers
} = useRole();

// 搜索表单配置
const searchItems = [
  {
    label: "关键字",
    prop: "keyword",
    component: "el-input",
    attrs: {
      placeholder: "请输入关键字",
      clearable: true,
      class: "!w-[250px]"
    }
  },
  {
    label: "状态",
    prop: "status",
    component: "el-select",
    attrs: {
      placeholder: "请选择状态",
      clearable: true,
      filterable: true,
      class: "!w-[120px]"
    },
    options: OrderStatuses
  },
  {
    label: "工单分类",
    prop: "ext_type",
    component: "el-select",
    attrs: {
      placeholder: "请选择工单分类",
      clearable: true,
      filterable: true,
      class: "!w-[220px]"
    },
    options: OrderExtTypes
  }
];

// 添加行样式
const tableRowClassName = ({ row }) => {
  return row.critical ? "critical-row" : "";
};
</script>

<template>
  <div class="main">
    <el-card class="search-card">
      <el-form
        ref="formRef"
        :inline="true"
        :model="form"
        class="search-form"
        @submit.prevent
      >
        <template v-for="item in searchItems" :key="item.prop">
          <el-form-item :label="item.label" :prop="item.prop">
            <component
              :is="item.component"
              v-model="form[item.prop]"
              v-bind="item.attrs"
              @change="onSearch"
              @keyup.enter="onSearch"
            >
              <el-option
                v-for="[value, label] in item.options || []"
                :key="value"
                :label="label"
                :value="value"
                filterable
                clearable
              />
            </component>
          </el-form-item>
        </template>

        <el-form-item label="申请人" prop="applicant_id">
          <el-select
            v-model="form.applicant_id"
            placeholder="请选择申请人"
            clearable
            filterable
            class="!w-[220px]"
            @change="onSearch"
          >
            <el-option
              v-for="user in users"
              :key="user.id"
              :label="`${user.name}（${user.username}）`"
              :value="user.id"
              filterable
              clearable
            />
          </el-select>
          <el-button
            type="primary"
            :icon="useRenderIcon('ep-refresh')"
            :loading="loading"
            link
            @click="getAllUsers"
          >
            刷新
          </el-button>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            :icon="useRenderIcon('ri:search-line')"
            :loading="loading"
            class="search-button"
            style="
              background: linear-gradient(135deg, #409eff 0%, #4facff 100%);
              border: none;
              box-shadow: 0 4px 12px rgb(64 158 255 / 30%);
              transition: all 0.3s ease;
            "
            @click="onSearch"
          >
            搜索
          </el-button>
          <el-button
            :icon="useRenderIcon(Refresh)"
            class="reset-button"
            style="
              color: #606266;
              border-color: #dcdfe6;
              transition: all 0.3s ease;
            "
            @click="resetForm(formRef)"
          >
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <PureTableBar title="所有工单" :columns="columns" @refresh="onSearch">
      <template #default="{ size, dynamicColumns }">
        <pure-table
          ref="tableRef"
          row-key="id"
          align-whole="left"
          table-layout="auto"
          :loading="loading"
          :height="'100%'"
          :data="dataList"
          :columns="dynamicColumns"
          :pagination="{ ...pagination, size }"
          :header-cell-style="{
            fontWeight: '600',
            borderBottom: '2px solid #e4e7ed',
            marginTop: '10px'
          }"
          :row-style="{
            cursor: 'pointer',
            transition: 'background-color 0.2s ease'
          }"
          :cell-style="{
            padding: '12px 10px'
          }"
          :row-class-name="tableRowClassName"
          border
          stripe
          @page-size-change="handleSizeChange"
          @page-current-change="handleCurrentChange"
        >
          <template #empty>
            <el-empty
              description="暂无数据"
              :image-size="120"
              style="padding: 40px 0"
            />
          </template>
        </pure-table>
      </template>
    </PureTableBar>
  </div>
</template>

<style lang="scss" scoped>
.main {
  .search-card {
    margin-bottom: 20px;
    border-radius: 8px;
    box-shadow: 0 1px 2px rgb(0 0 0 / 6%);
  }

  .search-form {
    padding: 20px;
  }

  .search-button {
    margin-left: 10px;
  }

  .reset-button {
    margin-left: 10px;
  }

  :deep(.pure-table) {
    overflow: hidden;
    border: 1px solid #e4e7ed;
    border-radius: 12px;
    box-shadow: 0 4px 16px rgb(0 0 0 / 4%);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-synthesis: none;

    .el-table__header {
      background: linear-gradient(135deg, #f5f7fa 0%, #f8f9fb 100%) !important;
    }

    .el-table__row {
      transition: all 0.2s ease;

      &:hover {
        box-shadow: 0 2px 8px rgb(0 0 0 / 6%);
      }
    }

    .el-table {
      overflow: visible;
      // 表格圆角
      border-radius: 8px;

      // 单元格样式
      td {
        padding: 12px 0;
      }

      // 链接样式
      .el-link {
        font-weight: 500;
      }

      // 标签样式优化
      .el-tag {
        height: 28px;
        padding: 0 8px;
        line-height: 28px;
        border-radius: 4px;

        &--large {
          min-width: 80px;
          text-align: center;
        }
      }

      // 紧急工单样式
      .critical-row {
        background-color: var(--el-color-danger-light-9);

        &:hover td {
          background-color: var(--el-color-danger-light-8) !important;
        }
      }

      // 鼠标悬停效果
      tr:hover td {
        background-color: var(--el-fill-color-light) !important;
        transition: background-color 0.3s ease;
      }

      // 表格分割线
      td,
      th {
        border-color: var(--el-border-color-lighter);
      }
    }
  }

  // 分页器样式
  :deep(.el-pagination) {
    padding-right: 1rem;
    margin-top: 1rem;
    text-align: right;

    .el-pagination__total,
    .el-pagination__sizes {
      margin-right: 16px;
    }

    .btn-prev,
    .btn-next {
      border-radius: 4px;

      &:hover {
        background-color: var(--el-color-primary-light-9);
      }
    }

    .el-pager li {
      border-radius: 4px;

      &:hover {
        background-color: var(--el-color-primary-light-9);
      }

      &.is-active {
        background-color: var(--el-color-primary);
      }
    }
  }
}

// 状态标签的特殊样式
:deep(.el-tag--success) {
  color: var(--el-color-success);
  background-color: var(--el-color-success-light-9);
  border-color: var(--el-color-success-light-5);
}

:deep(.el-tag--warning) {
  color: var(--el-color-warning);
  background-color: var(--el-color-warning-light-9);
  border-color: var(--el-color-warning-light-5);
}

:deep(.el-tag--danger) {
  color: var(--el-color-danger);
  background-color: var(--el-color-danger-light-9);
  border-color: var(--el-color-danger-light-5);
}

:deep(.el-tag--info) {
  color: var(--el-color-info);
  background-color: var(--el-color-info-light-9);
  border-color: var(--el-color-info-light-5);
}
</style>
