<template>
  <div>
    <el-row :gutter="20" class="order-container">
      <el-col :span="16">
        <el-card class="main-card" shadow="hover" style="margin-left: 20px">
          <template #header>
            <div class="card-header">
              <h2 class="title">
                <iconify-icon-online
                  icon="material-symbols:domain"
                  class="icon"
                />
                域名申请工单
              </h2>
            </div>
          </template>

          <el-form
            ref="ruleFormRef"
            :model="form"
            class="order-form"
            status-icon
            label-position="top"
            size="large"
          >
            <el-form-item label="申请清单" class="list-form-item">
              <div class="model-list-container">
                <div class="model-list-header">
                  <span>域名配置清单</span>
                  <el-button
                    type="primary"
                    size="small"
                    class="header-add-button"
                    @click="onAdd"
                  >
                    <iconify-icon-online
                      icon="ep:plus"
                      style="margin-right: 4px"
                    />
                    添加域名
                  </el-button>
                </div>
                <pure-table
                  ref="tableRef"
                  row-key="id"
                  align-whole="center"
                  :header-cell-style="{
                    background: '#f5f7fa',
                    color: '#606266',
                    padding: '8px 0',
                    height: '40px',
                    fontWeight: 'bold',
                    fontSize: '14px'
                  }"
                  :cell-style="{
                    padding: '6px 0',
                    fontSize: '13px'
                  }"
                  :row-style="
                    ({ rowIndex }) => {
                      return {
                        height: '36px',
                        backgroundColor:
                          rowIndex % 2 === 0 ? '#ffffff' : '#fafafa'
                      };
                    }
                  "
                  :data="modelList"
                  :columns="columns"
                  scrollbar-always-on
                  style="margin-bottom: 4px; transition: all 0.3s"
                  border
                >
                  <template #empty>
                    <div class="empty-data">
                      <iconify-icon-online
                        icon="ant-design:file-add-outlined"
                        class="empty-icon"
                      />
                      <p class="empty-text">暂无数据，请添加域名配置</p>
                    </div>
                  </template>
                </pure-table>
              </div>
            </el-form-item>

            <el-form-item
              label="申请理由"
              :rules="[
                { required: true, message: '请输入申请理由', trigger: 'blur' },
                {
                  max: 5000,
                  message: '理由不能超过5000个字符',
                  trigger: 'blur'
                }
              ]"
              prop="content"
            >
              <el-input
                v-model="form.content"
                type="textarea"
                show-word-limit
                maxlength="5000"
                :autosize="{ minRows: 6, maxRows: 16 }"
                placeholder="请详细描述申请理由..."
              />
            </el-form-item>

            <el-form-item>
              <el-button type="primary" @click="onSubmit">
                <iconify-icon-online
                  icon="material-symbols:send"
                  style="margin-right: 4px"
                />提交</el-button
              >
              <RouterLink :to="{ name: '我的工单' }">
                <el-button style="margin-left: 50px">
                  <iconify-icon-online
                    icon="material-symbols:cancel"
                    style="margin-right: 4px"
                  />
                  取消
                </el-button>
              </RouterLink>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
      <el-col :span="8">
        <ApprovalFlow :processes="processes" />
      </el-col>
    </el-row>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, watch, onUnmounted, nextTick } from "vue";
import { applyOrderAPI, type OrderForm } from "@/api/workflow/order-domain";
import { ElMessageBox, type FormInstance } from "element-plus";
import { message } from "@/utils/message";
import {
  getProcesses,
  processes,
  modelList,
  modelColumns as columns,
  onAdd
} from "./hook";
import ApprovalFlow from "../components/ApprovalFlow.vue";

// 表格引用
const tableRef = ref(null);
let resizeObserver = null;

const form = ref<OrderForm>({
  order_type: "",
  content: "",
  domain_list: undefined,
  orders: [],
  env: undefined,
  critical: undefined
});

const ruleFormRef = ref<FormInstance>();

// 添加表格高度计算函数
const calcTableHeight = () => {
  setTimeout(() => {
    if (!tableRef.value) return;

    const tableEl = tableRef.value.$el;
    if (!tableEl) return;

    const tableHeader = tableEl.querySelector(".el-table__header-wrapper");
    const tableBody = tableEl.querySelector(".el-table__body-wrapper");
    if (!tableHeader || !tableBody) return;

    const headerHeight = tableHeader.offsetHeight || 40;
    const rows = tableBody.querySelectorAll(".el-table__row");
    const rowCount = rows.length || 1;
    const rowHeight = rowCount > 0 && rows[0] ? rows[0].offsetHeight : 36;

    let contentHeight = 0;
    if (rowCount > 0) {
      contentHeight = rowCount * rowHeight;
    } else {
      const emptyBlock = tableBody.querySelector(".el-table__empty-block");
      contentHeight = emptyBlock ? emptyBlock.offsetHeight : 80;
    }

    const padding = 10;
    let targetHeight = headerHeight + contentHeight + padding;

    const minHeight =
      rowCount <= 1 ? 100 : Math.min(100, rowCount * rowHeight + headerHeight);
    const maxHeight = Math.max(400, rowCount * rowHeight + headerHeight);

    targetHeight = Math.max(minHeight, Math.min(maxHeight, targetHeight));

    tableBody.style.maxHeight = `${targetHeight - headerHeight}px`;
    tableEl.style.maxHeight = `${targetHeight}px`;

    const tableInstance = tableRef.value.instance;
    if (tableInstance && typeof tableInstance.doLayout === "function") {
      tableInstance.doLayout();
    }
  }, 100);
};

// 刷新表格布局
const refreshTableLayout = () => {
  setTimeout(() => {
    const tables = document.querySelectorAll(".pure-table .el-table");
    tables.forEach(table => {
      const tableInstance = (table as any).__vueParentComponent?.proxy;
      if (tableInstance && typeof tableInstance.doLayout === "function") {
        tableInstance.doLayout();
      }
    });

    calcTableHeight();
  }, 100);
};

// 监听数据变化
watch(
  modelList,
  () => {
    nextTick(() => {
      refreshTableLayout();
      calcTableHeight();
    });
  },
  { deep: true }
);

const onSubmit = async () => {
  if (modelList.value.length === 0) {
    message("请填写申请域名信息", { type: "error" });
    return;
  } else {
    let isEmpty = false;
    modelList.value.forEach((item, index) => {
      if (item.domain === "") {
        message("请填写第" + (index + 1) + "条域名信息", { type: "error" });
        isEmpty = true;
      }
    });
    if (isEmpty) return;
  }
  const valid = await new Promise(resolve => {
    ruleFormRef.value.validate((valid: boolean) => {
      form.value.domain_list = modelList.value;
      resolve(valid);
    });
  });
  if (valid) {
    try {
      await ElMessageBox.confirm("是否确认提交工单？", "提交确认", {
        confirmButtonText: "提交",
        cancelButtonText: "取消",
        type: "warning",
        draggable: true,
        center: true,
        customClass: "custom-message-box",
        icon: "Warning"
      });
      const res = await applyOrderAPI(form.value);
      if (res.success) {
        message("提交成功", { type: "success" });
        setTimeout(() => {
          window.location.href = "/workflow/my/order";
        }, 300);
      } else {
        message(res.msg || "提交失败", { type: "error" });
      }
    } catch (error) {
      if (error !== "cancel") {
        message(error.message || "操作取消", { type: "warning" });
      }
    }
  } else {
    message("请检查表单数据", { type: "warning" });
  }
};

onMounted(() => {
  getProcesses();
  modelList.value = [];

  // 添加窗口调整大小监听
  window.addEventListener("resize", refreshTableLayout);

  // 初始表格布局渲染后进行处理
  nextTick(() => {
    calcTableHeight();

    // 创建ResizeObserver监听表格大小变化
    if (tableRef.value?.$el) {
      resizeObserver = new ResizeObserver(() => {
        calcTableHeight();
      });
      resizeObserver.observe(tableRef.value.$el);
    }
  });
});

// 组件销毁时移除所有监听器
onUnmounted(() => {
  window.removeEventListener("resize", refreshTableLayout);

  if (resizeObserver) {
    resizeObserver.disconnect();
    resizeObserver = null;
  }
});
</script>

<style scoped>
.order-container {
  margin-top: 20px;
}

.main-card {
  margin-left: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 8%);
  transition: all 0.3s ease;
}

.main-card:hover {
  box-shadow: 0 6px 20px rgb(0 0 0 / 12%);
}

.card-header {
  display: flex;
  align-items: center;
  padding: 8px 0;
}

.title {
  display: flex;
  align-items: center;
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.icon {
  margin-right: 12px;
  font-size: 24px;
  color: #409eff;
}

.order-form {
  padding: 10px;
  margin-top: 20px;
}

.model-list-container {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 0;
  margin: 0 0 8px;
  overflow: hidden;
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 8px;
  box-shadow: 0 1px 4px 0 rgb(0 0 0 / 5%);
}

.model-list-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 40px;
  padding: 8px 12px;
  font-size: 14px;
  font-weight: bold;
  color: #606266;
  background-color: #f5f7fa;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.header-add-button {
  display: flex;
  align-items: center;
  height: 28px;
  padding: 4px 12px;
  font-size: 13px;
}

.empty-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 80px;
  padding: 16px 0;
  color: #909399;
}

.empty-icon {
  margin-bottom: 8px;
  font-size: 28px;
  color: #c0c4cc;
}

.empty-text {
  margin: 4px 0;
  font-size: 14px;
  color: #909399;
}

/* 调整表格样式 */
:deep(.pure-table) {
  .el-table {
    width: 100% !important;
    table-layout: fixed !important;
    border: none;
  }

  .el-table__empty-block {
    min-height: 80px !important;
  }

  .el-table__header-wrapper {
    border-bottom: 1px solid #f0f0f0;
  }

  .el-table__body-wrapper {
    overflow-y: auto !important;
  }

  .el-table__row {
    transition: background-color 0.2s;
  }

  .el-table__row:hover {
    background-color: #f5f7fa;
  }

  .el-table__header th {
    font-weight: bold;
  }

  /* 调整输入框样式 */
  .el-input,
  .el-input-number {
    font-size: 13px;
  }

  .el-input__wrapper {
    padding: 0 8px;
  }

  .el-button--link {
    padding: 4px 0;
    font-size: 13px;
  }
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-select .el-input) {
  transition: all 0.3s;
}

:deep(.el-input__inner) {
  transition: all 0.3s;
}

:deep(.el-input__inner:focus) {
  border-color: #409eff;
}

:deep(.el-textarea__inner) {
  transition: all 0.3s;
}

:deep(.el-textarea__inner:focus) {
  border-color: #409eff;
}

:deep(.el-date-editor.el-input) {
  transition: all 0.3s;
}

:deep(.custom-select) {
  width: 100%;
}

:deep(.el-button--primary) {
  padding: 12px 24px;
  font-weight: 500;
  transition: all 0.3s;
}

:deep(.el-button--primary:hover) {
  background-color: #66b1ff;
  box-shadow: 0 2px 8px rgb(64 158 255 / 20%);
}

:deep(.el-button--default) {
  padding: 12px 24px;
  font-weight: 500;
  transition: all 0.3s;
}

:deep(.el-button--default:hover) {
  background-color: #f2f6fc;
}

/* 表单提交按钮区域样式 */
:deep(.el-form-item:last-child) {
  padding-top: 20px;
  margin-top: 32px;
  border-top: 1px dashed #ebeef5;
}

.list-form-item {
  margin-bottom: 16px !important;
}

:deep(.list-form-item .el-form-item__content) {
  display: flex;
  flex-direction: column;
}
</style>
