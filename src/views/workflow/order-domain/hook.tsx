import { getProcessesAPI, type Process } from "@/api/workflow/process";
import { OrderType } from "@/config/order-enum";
import { message } from "@/utils/message";
import { ref } from "vue";

export const processes = ref<Process[]>([]);
export const getProcesses = () => {
  getProcessesAPI(OrderType.DomainOrderType, undefined)
    .then(res => {
      if (res.success) {
        processes.value = res.data;
      } else {
        message(res.msg, { type: "error" });
      }
    })
    .catch(error => {
      message("请求失败" + error, { type: "error" });
    });
};
export const modelList = ref<Model[]>([]);

export const modelColumns: TableColumnList = [
  {
    label: "序号",
    prop: "index",
    width: 60,
    fixed: "left",
    align: "center"
  },
  {
    label: "域名",
    prop: "domain",
    minWidth: 180,
    align: "center",
    cellRenderer: ({ row }) => (
      <el-input
        v-model={row.domain}
        placeholder="请输入域名"
        clearable
        style={{ width: "100%" }}
      />
    )
  },
  {
    label: "环境",
    prop: "env",
    minWidth: 120,
    align: "center",
    cellRenderer: ({ row }) => (
      <el-select
        v-model={row.env}
        placeholder="请选择环境"
        style={{ width: "100%" }}
      >
        <el-option value="dev" label="开发环境" />
        <el-option value="test" label="测试环境" />
        <el-option value="yf" label="预发环境" />
        <el-option value="prod" label="生产环境" />
      </el-select>
    )
  },

  {
    label: "限制公司访问",
    prop: "limit_office",
    width: 120,
    align: "center",
    cellRenderer: ({ row }) => (
      <el-switch
        v-model={row.limit_office}
        style={{ "--el-switch-on-color": "#13ce66" }}
      />
    )
  },
  {
    label: "备注",
    prop: "remark",
    minWidth: 150,
    align: "center",
    cellRenderer: ({ row }) => (
      <el-input
        v-model={row.remark}
        placeholder="请输入备注信息"
        clearable
        style={{ width: "100%" }}
      />
    )
  },
  {
    label: "操作",
    width: 80,
    fixed: "right",
    align: "center",
    cellRenderer: ({ row }) => (
      <el-button link type="danger" onClick={() => onDel(row)} icon="Delete">
        删除
      </el-button>
    )
  }
];

type Model = {
  index: number;
  domain: string;
  env: string;
  limit_office: boolean;
  remark: string;
};

export const onAdd = () => {
  const newIndex = modelList.value.length + 1;
  modelList.value.push({
    index: newIndex,
    domain: "",
    env: "dev",
    limit_office: false,
    remark: ""
  });
};

export const onDel = row => {
  const index = modelList.value.indexOf(row);
  if (index !== -1) {
    modelList.value.splice(index, 1);
    // 重新计算序号
    modelList.value.forEach((item, idx) => {
      item.index = idx + 1;
    });
  }
};
