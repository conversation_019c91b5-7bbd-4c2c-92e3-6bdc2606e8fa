<template>
  <div class="main">
    <el-card class="search-card">
      <el-form
        ref="formRef"
        :inline="true"
        :model="form"
        class="search-form"
        @submit.prevent
      >
        <el-form-item label="关键字" prop="keyword">
          <el-input
            v-model="form.keyword"
            placeholder="请输入关键字"
            clearable
            style="width: 220px"
            @keyup.enter="onSearch"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select
            v-model="form.status"
            placeholder="请选择状态"
            clearable
            style="width: 220px"
            @change="onSearch"
          >
            <el-option :key="100" label="待我评价" :value="100" />
            <el-option
              v-for="status in OrderStatuses"
              :key="status[0]"
              :label="status[1]"
              :value="status[0]"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="工单分类" prop="ext_type">
          <el-select
            v-model="form.ext_type"
            placeholder="请选择工单分类"
            clearable
            style="width: 220px"
            @change="onSearch"
          >
            <el-option
              v-for="orderType in OrderExtTypes"
              :key="orderType[0]"
              :label="orderType[1]"
              :value="orderType[0]"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            :icon="useRenderIcon('ri:search-line')"
            :loading="loading"
            class="search-button"
            style="
              background: linear-gradient(135deg, #409eff 0%, #4facff 100%);
              border: none;
              box-shadow: 0 4px 12px rgb(64 158 255 / 30%);
              transition: all 0.3s ease;
            "
            @click="onSearch"
          >
            搜索
          </el-button>
          <el-button
            :icon="useRenderIcon(Refresh)"
            class="reset-button"
            style="
              color: #606266;
              border-color: #dcdfe6;
              transition: all 0.3s ease;
            "
            @click="resetForm(formRef)"
          >
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card class="search-card" style="margin-top: 6px">
      <el-form class="search-form">
        <el-form-item label="工单申请：">
          <div class="flex flex-wrap gap-4">
            <RouterLink
              v-for="(item, index) in orderApplications"
              :key="index"
              :to="item.route"
            >
              <el-button
                type="primary"
                :icon="useRenderIcon(item.icon)"
                class="order-apply-button"
              >
                {{ item.label }}
              </el-button>
            </RouterLink>
          </div>
        </el-form-item>
      </el-form>
    </el-card>

    <PureTableBar title="我的工单" :columns="columns" @refresh="onSearch">
      <template #buttons>
        <el-button
          type="warning"
          :disabled="!selectedUnevaluatedOrders?.length"
          :loading="isEvaluating"
          @click="showBatchConfirmDialog"
        >
          批量评价
          <span v-if="selectedUnevaluatedOrders?.length"
            >({{ selectedUnevaluatedOrders.length }})</span
          >
        </el-button>
      </template>
      <template #default="{ size, dynamicColumns }">
        <div class="table-container">
          <pure-table
            ref="tableRef"
            row-key="id"
            align-whole="left"
            table-layout="auto"
            :loading="loading"
            :size="size"
            :minHeight="500"
            :data="dataList"
            :columns="dynamicColumns"
            :pagination="{ ...pagination, size }"
            :header-cell-style="{
              fontWeight: '600'
            }"
            :row-style="{
              cursor: 'pointer',
              transition: 'background-color 0.2s ease'
            }"
            :cell-style="{
              padding: '12px 10px'
            }"
            :row-class-name="tableRowClassName"
            border
            stripe
            highlight-current-row
            @page-size-change="handleSizeChange"
            @page-current-change="handleCurrentChange"
            @selection-change="handleSelectionChange"
            @select-all="handleSelectAll"
          >
            <template #empty>
              <el-empty
                description="暂无数据"
                :image-size="120"
                style="padding: 40px 0"
              />
            </template>
          </pure-table>
        </div>
      </template>
    </PureTableBar>

    <!-- 批量评价对话框 -->
    <el-dialog
      v-model="evaluationDialogVisible"
      title="批量评价工单"
      width="600px"
      :close-on-click-modal="false"
      :close-on-press-escape="!isEvaluating"
      :show-close="!isEvaluating"
    >
      <div class="batch-evaluation-dialog">
        <p class="mb-4">
          您正在对
          <span class="font-bold text-warning">{{
            selectedUnevaluatedOrders.length
          }}</span>
          个工单进行批量评价
        </p>

        <!-- 与 ServiceEval.vue 保持一致的界面 -->
        <div class="eval-main-row">
          <!-- 左侧区域：评分选项和按钮 -->
          <div class="rating-column">
            <!-- 问题 -->
            <div class="eval-question">
              <span>本次工单服务如何？</span>
            </div>

            <!-- 评分选项 -->
            <div class="rating-options">
              <div
                v-for="option in ratingOptions"
                :key="option.value"
                class="rating-card"
                :class="{
                  'is-selected': batchEvaluationForm.evaluation === option.value
                }"
                @click="batchEvaluationForm.evaluation = option.value"
              >
                <div class="rating-icon-wrapper">
                  <el-icon class="rating-icon" :class="option.colorClass">
                    <component :is="getIconComponent(option.icon)" />
                  </el-icon>
                </div>
                <div class="rating-label">{{ option.label }}</div>
              </div>
            </div>
          </div>

          <!-- 右侧区域：评价详情 -->
          <transition name="slide-fade">
            <div
              v-if="batchEvaluationForm.evaluation === '不满意'"
              class="comments-section"
            >
              <div class="comments-label">评价详情：</div>
              <el-input
                v-model="batchEvaluationForm.comment"
                type="textarea"
                placeholder="请输入您的评价和建议，帮助我们提升服务质量"
                :rows="3"
                maxlength="2000"
                show-word-limit
                class="comments-input"
              />
            </div>
          </transition>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button
            :disabled="isEvaluating"
            @click="evaluationDialogVisible = false"
            >取消</el-button
          >
          <el-button
            type="primary"
            :loading="isEvaluating"
            @click="submitBatchEvaluation"
          >
            提交评价
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useRole } from "./hook";
import { PureTableBar } from "@/components/RePureTableBar";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";

import Refresh from "@iconify-icons/ep/refresh";
import { OrderStatuses, OrderExtTypes } from "@/config/order-enum";
import { RouterLink } from "vue-router";
import {
  CircleCheckFilled,
  CircleCheck,
  Warning
} from "@element-plus/icons-vue";

defineOptions({
  name: "MyOrders"
});

// 获取图标组件
function getIconComponent(iconName) {
  switch (iconName) {
    case "circle-check-filled":
      return CircleCheckFilled;
    case "circle-check":
      return CircleCheck;
    case "warning":
      return Warning;
    default:
      return CircleCheckFilled;
  }
}

const formRef = ref();
const tableRef = ref();

const {
  form,
  loading,
  columns,
  dataList,
  pagination,
  // multipleSelection 不直接使用但在handleSelectionChange中隐式使用
  selectedUnevaluatedOrders,
  evaluationDialogVisible,
  batchEvaluationForm,
  isEvaluating,
  ratingOptions,
  onSearch,
  resetForm,
  handleSizeChange,
  handleCurrentChange,
  handleSelectionChange,
  handleSelectAll,
  // openEvaluationDialog 不直接使用但在showBatchConfirmDialog中隐式使用
  submitBatchEvaluation,
  showBatchConfirmDialog
} = useRole();

const orderApplications = [
  {
    label: "服务器申请",
    route: { name: "服务器工单申请" },
    icon: "grommet-icons:host"
  },
  {
    label: "权限申请",
    route: { name: "权限工单申请" },
    icon: "icon-park-outline:permissions"
  },
  {
    label: "域名申请",
    route: { name: "域名工单申请" },
    icon: "eos-icons:dns"
  },
  {
    label: "数据库申请",
    route: { name: "数据库工单申请" },
    icon: "cib:mysql"
  },
  {
    label: "业务需求申请",
    route: { name: "业务需求工单申请" },
    icon: "eos-icons:service-plan"
  },
  {
    label: "MongoDB账户开通",
    route: { name: "MongoDB工单申请" },
    icon: "teenyicons:mongodb-outline"
  },
  {
    label: "Gitlab权限申请",
    route: { name: "Gitlab权限工单申请" },
    icon: "mdi:gitlab"
  },
  {
    label: "桌面技术支持",
    route: { name: "桌面技术支持工单申请" },
    icon: "streamline:desktop-help"
  }
];

// 添加行样式
const tableRowClassName = ({ row }) => {
  return row.critical ? "critical-row" : "";
};
</script>

<style scoped lang="scss">
.main {
  gap: 20px;
  border-radius: 12px;
}

.search-card {
  margin-bottom: 6px;
  border-radius: 6px;
  box-shadow: 0 4px 6px rgb(0 0 0 / 8%);
  transition: box-shadow 0.3s ease;

  &:hover {
    box-shadow: 0 6px 20px rgb(0 0 0 / 12%);
  }
}

// 批量评价对话框样式，与ServiceEval.vue保持一致
.batch-evaluation-dialog {
  .eval-main-row {
    display: flex;
    flex-wrap: nowrap;
    gap: 8px;
    align-items: flex-start;
    width: 100%;

    @media screen and (width <= 768px) {
      flex-direction: column;
      gap: 8px;
    }
  }

  .rating-column {
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    gap: 4px;
    width: auto;
    min-width: 180px;
  }

  .eval-question {
    margin-bottom: 8px;
    font-size: 14px;
    font-weight: 500;
    color: #303133;
  }

  .rating-options {
    display: flex;
    flex-wrap: nowrap;
    gap: 4px;
    justify-content: flex-start;
    width: 100%;
    padding: 2px 0;

    @media screen and (width <= 768px) {
      gap: 8px;
    }
  }

  .rating-card {
    position: relative;
    display: inline-flex;
    flex-direction: row;
    gap: 4px;
    align-items: center;
    min-width: 55px;
    padding: 3px 5px;
    overflow: hidden;
    cursor: pointer;
    background-color: #f9f9f9;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgb(0 0 0 / 5%);
    transition: all 0.2s ease;

    &:hover {
      background-color: #f5f7fa;
      border-color: #c6e2ff;
      box-shadow: 0 2px 5px rgb(64 158 255 / 8%);
      transform: translateY(-1px);
    }

    &.is-selected {
      background-color: #ecf5ff;
      border-color: #409eff;
      box-shadow: 0 2px 8px rgb(64 158 255 / 15%);
      transform: translateY(-2px);

      &::after {
        position: absolute;
        top: 0;
        right: 0;
        content: "";
        border-color: transparent #409eff transparent transparent;
        border-style: solid;
        border-width: 0 16px 16px 0;
      }

      .rating-icon-wrapper {
        transform: scale(1.1);
      }
    }
  }

  .rating-icon-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 22px;
    height: 22px;
    background-color: #fff;
    border-radius: 50%;
    box-shadow: 0 1px 2px rgb(0 0 0 / 6%);
    transition: all 0.2s;
  }

  .rating-label {
    font-size: 12px;
    font-weight: 500;
    color: #303133;
  }

  .rating-icon {
    width: 1em;
    height: 1em;
    font-size: 14px;
  }

  .rating-good {
    color: #67c23a;
  }

  .rating-neutral {
    color: #e6a23c;
  }

  .rating-bad {
    color: #f56c6c;
  }

  .comments-section {
    display: flex;
    flex: 1;
    flex-direction: column;
    gap: 3px;
    min-width: 180px;
    max-width: 100%;
    padding: 6px;
    overflow: hidden;
    background-color: #f9f9f9;
    border-left: 2px solid #f56c6c;
    border-radius: 3px;
  }

  .comments-label {
    font-size: 12px;
    font-weight: 500;
    color: #303133;
  }

  .comments-input {
    margin-top: 2px;

    :deep(.el-textarea__inner) {
      padding: 6px;
      font-size: 12px;
      border: 1px solid #dcdfe6;
      border-radius: 3px;
      transition: all 0.2s;

      &:focus {
        border-color: #409eff;
        box-shadow: 0 0 0 2px rgb(64 158 255 / 20%);
      }
    }
  }
}

// 动画效果
.slide-fade-enter-active {
  transition: all 0.3s ease-out;
}

.slide-fade-leave-active {
  transition: all 0.3s cubic-bezier(1, 0.5, 0.8, 1);
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;

  .el-form-item {
    margin-bottom: 0;

    &__label {
      font-weight: 500;
      color: #606266;
    }
  }
}

.search-button,
.reset-button {
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.order-apply-button {
  margin-right: 10px;
  margin-bottom: 10px;
}

.table-container {
  width: 100%;
  overflow-x: auto;
}

:deep(.pure-table) {
  overflow: hidden;
  border: 1px solid #e4e7ed;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 4%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-synthesis: none;

  .el-table__header {
    background: linear-gradient(135deg, #f5f7fa 0%, #f8f9fb 100%) !important;
  }

  .el-table__row {
    transition: all 0.2s ease;

    &:hover {
      box-shadow: 0 2px 8px rgb(0 0 0 / 6%);
    }
  }

  // 紧急工单样式
  .critical-row {
    background-color: var(--el-color-danger-light-9);

    &:hover td {
      background-color: var(--el-color-danger-light-8) !important;
    }
  }
}
</style>
