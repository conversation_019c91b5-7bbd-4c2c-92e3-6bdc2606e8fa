import dayjs from "dayjs";
import type { PaginationProps } from "@pureadmin/table";
import { reactive, ref, onMounted, onBeforeUnmount, computed } from "vue";
import { message } from "@/utils/message";
import { getMyOrdersAPI } from "@/api/workflow/my-order";
import {
  ENVColors,
  ENVs,
  OrderExtTypes,
  OrderStatusColors,
  OrderStatuses
} from "@/config/order-enum";
import type { Order } from "@/api/workflow/order";
import { RouterLink, useRoute } from "vue-router";
import { formatDuration } from "@/utils/date";
import { ElMessageBox } from "element-plus";
import { saveBatchEvaluationsLocalAPI } from "@/api/workflow/order-detail";

export function useRole() {
  const route = useRoute();
  const form = reactive({
    keyword: undefined,
    ext_type: undefined,
    status: undefined
  });
  const dataList = ref<Order[]>([]);
  const loading = ref<boolean>(true);
  
  // 批量评价相关
  const multipleSelection = ref<Order[]>([]);
  const selectedAll = ref<boolean>(false);
  const isEvaluating = ref<boolean>(false);
  const evaluationDialogVisible = ref<boolean>(false);
  const batchEvaluationForm = reactive({
    evaluation: "非常满意", // 默认使用非常满意
    comment: ""
  });
  
  // 评价选项配置，与ServiceEval.vue保持一致
  const ratingOptions = [
    {
      value: "非常满意",
      label: "非常满意",
      icon: "circle-check-filled",
      colorClass: "rating-good"
    },
    {
      value: "满意",
      label: "满意",
      icon: "circle-check",
      colorClass: "rating-neutral"
    },
    {
      value: "不满意",
      label: "不满意",
      icon: "warning",
      colorClass: "rating-bad"
    }
  ];

  // 已选择且未评价的工单
  const selectedUnevaluatedOrders = computed(() => {
    // 过滤状态为已处理且未评价的工单
    // 1. 先满足状态为已处理(status === 2)
    // 2. 再满足未评价(evaluated === false)
    // 3. 如果 evaluated 不存在，默认认为未评价
    return multipleSelection.value.filter(order => 
      order.status === 2 && (order.evaluated === false || order.evaluated === undefined)
    );
  });

  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true,
    pageSizes: [10, 20, 30, 40, 50, 100]
  });

  const columns: TableColumnList = [
    {
      type: "selection",
      width: 55,
      align: "center",
      fixed: "left",
      selectable: (row) => {
        // 只有已处理(status === 2)且未评价(evaluated === false)的工单才可以被选择评价
        return row.status === 2 && (row.evaluated === false || row.evaluated === undefined);
      }
    },
    {
      label: "工单信息",
      prop: "order_info",
      cellRenderer: ({ row }) => {
        const extType = OrderExtTypes.get(row.ext_type);
        const orderType = row.order_type;
        const displayType =
          extType === orderType ? extType : `${extType} - ${orderType}`;
        return (
          <div class="order-info-cell flex flex-col items-start gap-0.5 py-1">
            <div class="order-info-title-row flex items-center gap-2 mb-0.5">
              <el-tooltip content={row.title} placement="top">
                <RouterLink to={{ name: "工单详情", params: { id: row.id } }}>
                  <el-link
                    size="large"
                    type="primary"
                    underline={false}
                    class="order-info-title"
                  >
                    {row.title}
                  </el-link>
                </RouterLink>
              </el-tooltip>
              {row.critical && (
                <el-tag
                  type="danger"
                  size="small"
                  effect="dark"
                  class="ml-1 order-info-critical flex items-center gap-1"
                  style={{
                    fontWeight: 500,
                    padding: "1px 7px",
                    borderRadius: "6px",
                    fontSize: "12px",
                    lineHeight: "18px"
                  }}
                >
                  <i class="iconfont icon-warning text-xs mr-1" />
                  紧急
                </el-tag>
              )}
              {row.env > 0 && (
                <el-tag
                  type={ENVColors.get(row.env)}
                  size="small"
                  effect="light"
                  class="ml-1 order-info-env flex items-center gap-1"
                  style={{
                    fontWeight: 500,
                    padding: "2px 7px",
                    borderRadius: "6px",
                    fontSize: "12px",
                    lineHeight: "18px"
                  }}
                >
                  <i class="iconfont icon-env text-xs mr-1" />
                  {ENVs.get(row.env)}
                </el-tag>
              )}
            </div>
            <div class="order-info-type-row mt-0.5">
              <el-text
                class="order-info-type text-[13px] font-normal tracking-wide"
                style={{ lineHeight: "18px" }}
                type="info"
                size="small"
              >
                <i class="iconfont icon-ticket text-xs mr-1" />
                {displayType}
              </el-text>
            </div>
          </div>
        );
      }
    },
    {
      label: "状态",
      prop: "status",
      width: 150,
      align: "center",
      cellRenderer: ({ row }) => (
        <div style="white-space: nowrap; display: flex; flex-direction: column; align-items: center; justify-content: center;">
          <el-tag
            type={OrderStatusColors.get(row.status)}
            effect="light"
            size="default"
            style={{
              fontWeight: 500,
              padding: "4px 8px",
              borderRadius: "4px"
            }}
          >
            {OrderStatuses.get(row.status)}
          </el-tag>
          {row.status === 2 && row.service_duration_seconds > 0 && (
            <div style="margin-top: 14px;">
              <el-tooltip
                content={`处理耗时：${formatDuration(row.service_duration_seconds)}`}
                placement="top"
              >
                <el-tag type="info" size="small" effect="plain">
                  {formatDuration(row.service_duration_seconds)}
                </el-tag>
              </el-tooltip>
            </div>
          )}
        </div>
      )
    },
    {
      label: "时间信息",
      prop: "time_info",
      width: 180,
      align: "center",
      cellRenderer: ({ row }) => (
        <div class="flex flex-col gap-1">
          <el-tooltip
            content={`创建时间：${dayjs(row.created_at).format("YYYY-MM-DD HH:mm:ss")}`}
            placement="top"
          >
            <div class="flex items-center whitespace-nowrap">
              <el-tag
                size="small"
                type="info"
                class="mr-1"
                style="padding: 0 4px;"
              >
                创 建
              </el-tag>
              <span class="text-gray-600">
                {dayjs(row.created_at).format("YYYY-MM-DD HH:mm")}
              </span>
            </div>
          </el-tooltip>
          {row.plan_time && (
            <el-tooltip
              content={`计划执行：${dayjs(row.plan_time).format("YYYY-MM-DD HH:mm:ss")}`}
              placement="top"
            >
              <div class="flex items-center whitespace-nowrap">
                <el-tag
                  size="small"
                  type="success"
                  class="mr-1"
                  style="padding: 0 4px;"
                >
                  计 划
                </el-tag>
                <span class="text-gray-600">
                  {dayjs(row.plan_time).format("YYYY-MM-DD HH:mm")}
                </span>
              </div>
            </el-tooltip>
          )}
        </div>
      )
    },
    {
      label: "操作",
      prop: "action",
      width: 150,
      align: "center",
      cellRenderer: ({ row }) => (
        <div class="flex flex-col gap-2 justify-center items-center">
          <div class="flex justify-center items-center">
            <RouterLink to={{ name: "工单详情", params: { id: row.id } }}>
              <el-button type="primary" size="large" link plain>
                查看
              </el-button>
            </RouterLink>
            <RouterLink
              to={{ name: "工单详情", params: { id: row.id } }}
              target="_blank"
              class="ml-2"
            >
              <el-button type="success" size="large" link plain>
                在新窗口查看
              </el-button>
            </RouterLink>
          </div>
          
          {/* 单个工单评价按钮，只对已处理且未评价的工单显示 */}
          {row.status === 2 && !row.evaluated && (
            <el-button
              type="warning"
              size="small"
              plain
              onClick={() => openEvaluationDialog([row])}
            >
              评价
            </el-button>
          )}
        </div>
      )
    }
  ];

  function handleSizeChange(val: number) {
    pagination.pageSize = val;
    onSearch();
  }

  function handleCurrentChange(val: number) {
    pagination.currentPage = val;
    onSearch();
  }

  function handleSortChange({ prop, order }) {
    // 在这里实现排序逻辑
    console.log(`Sorting by ${prop} in ${order} order`);
    // 可以根据需要更新 dataList 的排序
  }
  
  // 显示批量评价确认框
  function showBatchConfirmDialog() {
    const unevaluatedCount = selectedUnevaluatedOrders.value.length;
    if (unevaluatedCount === 0) {
      message("请选择需要评价的未评价工单", { type: "warning" });
      return;
    }
    
    ElMessageBox.confirm(
      `确定要批量评价选中的 ${unevaluatedCount} 个未评价工单吗？`,
      "批量评价确认",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }
    )
      .then(() => {
        openEvaluationDialog();
      })
      .catch(() => {
        // 用户取消操作
      });
  }

  async function onSearch() {
    loading.value = true;
    getMyOrdersAPI({
      page: pagination.currentPage,
      limit: pagination.pageSize,
      keyword: form.keyword,
      status: form.status,
      ext_type: form.ext_type
    })
      .then(res => {
        if (res.success) {
          // 处理返回的数据
          if (res.count === 0) {
            // 搜索结果为空，不是错误，不需要提示
            dataList.value = [];
            pagination.total = 0;
          } else {
            // 处理数据，确保 evaluated 字段的一致性
            dataList.value = res.data.map(item => {
              // 如果 evaluated 不是布尔值，进行转换或设置默认值
              if (typeof item.evaluated !== 'boolean') {
                // 如果是数字或字符串，转换为布尔值
                if (item.evaluated === '1' || item.evaluated === 1) {
                  item.evaluated = true;
                } else if (item.evaluated === '0' || item.evaluated === 0) {
                  item.evaluated = false;
                } else if (item.evaluated === undefined || item.evaluated === null) {
                  // 如果未定义，默认为未评价
                  item.evaluated = false;
                }
              }
              return item;
            });
            pagination.total = res.count;
          }
          
          // 更新分页信息
          pagination.pageSize = pagination.pageSize;
          pagination.currentPage = pagination.currentPage;
          
          // 翻页后清空多选状态
          multipleSelection.value = [];
        } else {
          // API返回失败状态，但不显示错误提示，只清空数据
          dataList.value = [];
          pagination.total = 0;
          pagination.pageSize = pagination.pageSize;
          pagination.currentPage = pagination.currentPage;
        }
      })
      .catch((error) => {
        // 只有在网络请求失败时才显示错误
        console.error("API请求失败:", error);
        message("网络请求失败，请稍后重试", { type: "error" });
      })
      .finally(() => {
        loading.value = false;
      });
  }
  const resetForm = formEl => {
    if (!formEl) return;
    formEl.resetFields();
    form.ext_type = undefined;
    onSearch();
  };

  onMounted(() => {
    const statusParam = route.query.status;
    if (statusParam) {
      form.status = Number(statusParam);
    }
    onSearch();
  });
  
  // 组件卸载前清理状态和事件监听器
  onBeforeUnmount(() => {
    // 清空多选状态，防止切换页面时发生冲突
    multipleSelection.value = [];
    selectedAll.value = false;
    evaluationDialogVisible.value = false;
  });

  // 处理表格选择变化事件
  function handleSelectionChange(selection: Order[]) {
    multipleSelection.value = selection;
  }

  // 处理全选/取消全选事件
  function handleSelectAll(selection: Order[]) {
    selectedAll.value = selection.length > 0;
    multipleSelection.value = selection;
  }

  // 打开评价对话框
  function openEvaluationDialog(orders: Order[] = []) {
    if (orders.length > 0) {
      multipleSelection.value = orders;
    }
    
    if (selectedUnevaluatedOrders.value.length === 0) {
      message("请选择需要评价的未评价工单", { type: "warning" });
      return;
    }
    
    // 重置表单
    batchEvaluationForm.evaluation = "非常满意"; // 使用非常满意
    batchEvaluationForm.comment = "";
    evaluationDialogVisible.value = true;
  }

  // 批量提交评价
  async function submitBatchEvaluation() {
    if (selectedUnevaluatedOrders.value.length === 0) {
      message("请选择需要评价的未评价工单", { type: "warning" });
      return;
    }

    try {
      isEvaluating.value = true;
      
      // 准备评价数据
      const evaluations = selectedUnevaluatedOrders.value.map(order => ({
        sn: order.sn,
        evaluation: String(batchEvaluationForm.evaluation), // 将数字转换为字符串以匹配 API 类型
        comment: batchEvaluationForm.comment
      }));
      
      // 调用批量评价接口
      const result = await saveBatchEvaluationsLocalAPI(evaluations);
      
      if (result.success) {
        message(result.msg || "批量评价成功", { type: "success" });
        evaluationDialogVisible.value = false;
        // 刷新列表
        onSearch();
      } else {
        message(result.msg || "批量评价失败", { type: "error" });
      }
    } catch (error) {
      console.error("批量评价出错:", error);
      message("批量评价过程中出现错误", { type: "error" });
    } finally {
      isEvaluating.value = false;
    }
  }

  return {
    form,
    loading,
    columns,
    dataList,
    pagination,
    multipleSelection,
    selectedUnevaluatedOrders,
    evaluationDialogVisible,
    batchEvaluationForm,
    isEvaluating,
    ratingOptions,
    onSearch,
    resetForm,
    handleSizeChange,
    handleCurrentChange,
    handleSortChange,
    handleSelectionChange,
    handleSelectAll,
    openEvaluationDialog,
    submitBatchEvaluation,
    showBatchConfirmDialog
  };
}
