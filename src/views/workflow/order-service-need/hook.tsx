import { getAllMyOrdersAPI } from "@/api/workflow/my-order";
import type { Order } from "@/api/workflow/order";
import { getProcessesAPI, type Process } from "@/api/workflow/process";
import { OrderExtType, OrderType } from "@/config/order-enum";
import { message } from "@/utils/message";
import { ref } from "vue";
import { getAllUsersAPI, type User } from "@/api/auth/user";

export const processes = ref<Process[]>([]);
export const getProcesses = () => {
  getProcessesAPI(OrderType.ServiceNeedOrderType, undefined)
    .then(res => {
      if (res.success) {
        processes.value = res.data;
      } else {
        message(res.msg, { type: "error" });
      }
    })
    .catch(error => {
      message("请求失败" + error, { type: "error" });
    });
};

export const orders = ref<Order[]>([]);

// 用户相关功能
export const users = ref<User[]>([]);
export const loading = ref<boolean>(false);

// 服务时长
export const service_duration = ref({
  hours: 0.5,
  minutes: 0
});

export const getOrders = () => {
  getAllMyOrdersAPI(OrderExtType.ServiceNeedOrderType)
    .then(res => {
      if (res.success) {
        orders.value = res.data;
      } else {
        message(res.msg, { type: "error" });
      }
    })
    .catch(error => {
      message("请求失败" + error, { type: "error" });
    });
};

// 获取用户列表
export const getUsers = () => {
  loading.value = true;
  getAllUsersAPI()
    .then(res => {
      if (res.success) {
        users.value = res.data as User[];
      } else {
        message(res.msg, { type: "error" });
      }
    })
    .catch(error => {
      message("获取用户列表失败" + error, { type: "error" });
    })
    .finally(() => {
      loading.value = false;
    });
};

// 过滤用户
export const filterUsers = (query: string) => {
  if (query) {
    loading.value = true;
    // 由于getAllUsersAPI不支持查询参数，我们在前端进行过滤
    getAllUsersAPI()
      .then(res => {
        if (res.success) {
          const allUsers = res.data as User[];
          // 在前端过滤用户名或真实姓名包含查询字符串的用户
          users.value = allUsers.filter(user => 
            user.username.toLowerCase().includes(query.toLowerCase()) || 
            user.name.toLowerCase().includes(query.toLowerCase())
          );
        } else {
          message(res.msg, { type: "error" });
        }
      })
      .catch(error => {
        message("搜索用户失败" + error, { type: "error" });
      })
      .finally(() => {
        loading.value = false;
      });
  }
};
