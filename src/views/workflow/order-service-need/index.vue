<template>
  <div class="service-need-container">
    <div class="page-header">
      <h1 class="page-title">
        <iconify-icon-online icon="material-symbols:domain" class="page-icon" />
        业务需求工单
      </h1>
    </div>
    <el-row :gutter="24" class="order-container">
      <el-col :span="16">
        <el-card class="main-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <h2 class="title">
                <iconify-icon-online
                  icon="material-symbols:description-outline-rounded"
                  class="icon"
                />
                <span>工单信息</span>
              </h2>
            </div>
          </template>

          <el-form
            ref="ruleFormRef"
            :model="form"
            class="order-form"
            status-icon
            label-position="top"
            size="large"
          >
            <div
              v-if="hasRole('workflow_admin')"
              class="form-section admin-section"
            >
              <div class="section-header">
                <iconify-icon-online
                  icon="material-symbols:admin-panel-settings-outline"
                  class="section-icon"
                />
                <span class="section-title">管理员设置</span>
              </div>

              <div class="admin-controls-row">
                <el-form-item
                  label="补充申请"
                  prop="help_apply"
                  class="form-item admin-control-item"
                >
                  <el-switch v-model="form.help_apply" active-color="#409EFF" />
                </el-form-item>

                <el-form-item
                  v-if="form.help_apply"
                  label="申请人"
                  prop="applicant_id"
                  class="form-item admin-control-item applicant-item"
                  :rules="[
                    {
                      required: true,
                      message: '请选择申请人',
                      trigger: 'change'
                    }
                  ]"
                >
                  <div class="applicant-select-container">
                    <el-select
                      v-model="form.applicant_id"
                      placeholder="请选择申请人"
                      class="applicant-select"
                      filterable
                      remote
                      :remote-method="filterUsers"
                      :loading="loading"
                    >
                      <el-option
                        v-for="item in users"
                        :key="item.id"
                        :label="`${item.name} (${item.username})`"
                        :value="item.id"
                      />
                    </el-select>
                    <el-button
                      type="primary"
                      class="refresh-button"
                      @click="getUsers"
                    >
                      <iconify-icon-online
                        icon="mdi:refresh"
                      />
                    </el-button>
                  </div>
                </el-form-item>

                <el-form-item
                  v-if="form.help_apply && hasRole('workflow_admin')"
                  label="耗时"
                  class="form-item admin-control-item duration-item"
                >
                  <div class="duration-inputs">
                    <el-input-number
                      v-model="service_duration.hours"
                      :min="0"
                      :step="0.1"
                      placeholder="时"
                      class="duration-input"
                      controls-position="right"
                    >
                    </el-input-number>
                    <span>时</span>
                  </div>
                </el-form-item>
              </div>
            </div>

            <div class="form-section">
              <div class="section-header">
                <iconify-icon-online
                  icon="material-symbols:description-outline"
                  class="section-icon"
                />
                <span class="section-title">基本信息</span>
              </div>
              <el-form-item
                label="标题"
                prop="title"
                :rules="[
                  { required: true, message: '请输入标题', trigger: 'blur' },
                  {
                    max: 255,
                    message: '标题长度不能超过255个字符',
                    trigger: 'blur'
                  }
                ]"
              >
                <el-input
                  v-model="form.title"
                  placeholder="请输入标题"
                  maxlength="255"
                  show-word-limit
                  clearable
                />
              </el-form-item>

              <div class="form-section-row">
                <div class="form-section-col">
                  <el-form-item
                    label="工单类型"
                    prop="order_type"
                    :rules="[
                      {
                        required: true,
                        message: '请选择工单类型',
                        trigger: 'change'
                      }
                    ]"
                  >
                    <el-radio-group
                      v-model="form.order_type"
                      class="order-type-radio-group"
                      size="large"
                    >
                      <el-radio-button
                        v-for="(orderType, index) in ServiceneedOrderTypes"
                        :key="index"
                        :label="orderType"
                        class="order-type-radio-button"
                      />
                    </el-radio-group>
                  </el-form-item>
                </div>
                <div class="form-section-col env-col">
                  <el-form-item
                    label="环境"
                    prop="env"
                    :rules="[
                      {
                        required: true,
                        message: '请选择环境',
                        trigger: 'change'
                      }
                    ]"
                  >
                    <el-select
                      v-model="form.env"
                      placeholder="请选择环境"
                      class="env-select"
                    >
                      <el-option
                        v-for="env in Array.from(ENVs.entries())"
                        :key="env[0]"
                        :label="env[1]"
                        :value="Number(env[0])"
                      />
                    </el-select>
                  </el-form-item>
                </div>
              </div>
            </div>

            <div class="form-section">
              <div class="section-header">
                <iconify-icon-online
                  icon="material-symbols:settings-outline"
                  class="section-icon"
                />
                <span class="section-title">高级配置</span>
              </div>

              <div class="form-section-row">
                <div class="form-section-col">
                  <el-form-item label="云平台" prop="cloud_platforms">
                    <el-select
                      v-model="form.cloud_platforms"
                      placeholder="请选择云平台"
                      multiple
                      class="custom-select"
                      tag-type="primary"
                      clearable
                      filterable
                    >
                      <el-option
                        v-for="platform in CloudPlatforms"
                        :key="platform"
                        :label="platform"
                        :value="platform"
                      />
                    </el-select>
                  </el-form-item>
                </div>

                <div class="form-section-col">
                  <el-form-item
                    v-if="
                      form.order_type === '业务部署' ||
                      form.order_type === '业务配置变更'
                    "
                    label="迭代版本"
                    prop="iteration_version"
                    :rules="[
                      {
                        required: true,
                        message: '请输入迭代版本',
                        trigger: 'blur'
                      }
                    ]"
                  >
                    <el-input
                      v-model="form.iteration_version"
                      placeholder="请输入迭代版本，例如：v1.2.3"
                      maxlength="255"
                      show-word-limit
                      clearable
                    >
                      <template #append>
                        <el-tooltip
                          content="请输入当前迭代的版本号，通常采用语义化版本格式如v1.2.3"
                          placement="top"
                        >
                          <el-icon><QuestionFilled /></el-icon>
                        </el-tooltip>
                      </template>
                    </el-input>
                  </el-form-item>
                </div>

                <div class="form-section-col">
                  <el-form-item
                    v-if="form.order_type === '业务部署'"
                    label="是否需要解密statinfo"
                    prop="need_decrypt_stat_info"
                  >
                    <el-switch v-model="form.need_decrypt_stat_info" />
                  </el-form-item>
                </div>
              </div>
            </div>

            <div class="form-section">
              <div class="section-header">
                <iconify-icon-online
                  icon="material-symbols:description"
                  class="section-icon"
                />
                <span class="section-title">需求详情</span>
              </div>

              <el-form-item
                label="需求描述"
                prop="content"
                :rules="[
                  {
                    required: true,
                    message: '请输入需求描述',
                    trigger: 'blur'
                  },
                  {
                    min: 10,
                    message: '需求描述不能少于10个字符',
                    trigger: 'blur'
                  },
                  {
                    max: 5000,
                    message: '需求描述不能超过5000个字符',
                    trigger: 'blur'
                  }
                ]"
              >
                <el-input
                  v-model="form.content"
                  type="textarea"
                  show-word-limit
                  maxlength="5000"
                  :autosize="{ minRows: 6, maxRows: 16 }"
                  placeholder="请详细描述您的需求..."
                />
              </el-form-item>
            </div>

            <div class="form-section">
              <div class="section-header">
                <iconify-icon-online
                  icon="material-symbols:schedule"
                  class="section-icon"
                />
                <span class="section-title">计划与关联</span>
              </div>

              <div class="form-section-row">
                <div class="form-section-col">
                  <el-form-item
                    label="计划时间"
                    prop="plan_time"
                    :rules="[
                      {
                        required: true,
                        message: '请选择计划时间',
                        trigger: 'change'
                      }
                    ]"
                  >
                    <el-date-picker
                      v-model="form.plan_time"
                      type="datetime"
                      class="custom-select"
                      placeholder="选择日期时间"
                      :disabled-date="disabledDateFunction"
                      value-on-clear
                    />
                  </el-form-item>
                </div>

                <div class="form-section-col priority-col">
                  <el-form-item label="是否紧急" prop="critical">
                    <el-switch v-model="form.critical" />
                  </el-form-item>
                </div>

                <div class="form-section-col">
                  <el-form-item label="关联工单" prop="orders">
                    <el-select
                      v-model="form.orders"
                      class="related-orders-select"
                      placeholder="请选择工单"
                      multiple
                      clearable
                      filterable
                      tag-type="primary"
                      tag-size="large"
                    >
                      <el-option
                        v-for="item in orders || []"
                        :key="item.id"
                        :label="item.title"
                        :value="item.sn"
                      />
                    </el-select>
                  </el-form-item>
                </div>
              </div>
            </div>

            <div class="form-section action-section">
              <el-form-item class="action-buttons">
                <el-button
                  type="primary"
                  size="large"
                  class="submit-button"
                  @click="onSubmit"
                >
                  <iconify-icon-online
                    icon="material-symbols:send"
                    style="margin-right: 8px"
                  />
                  提交
                </el-button>
                <RouterLink :to="{ name: '我的工单' }">
                  <el-button size="large" class="cancel-button">
                    <iconify-icon-online
                      icon="material-symbols:cancel"
                      style="margin-right: 8px"
                    />
                    取消
                  </el-button>
                </RouterLink>
              </el-form-item>
            </div>
          </el-form>
        </el-card>
      </el-col>
      <el-col :span="8">
        <ApprovalFlow :processes="processes" class="approval-flow" />
      </el-col>
    </el-row>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, h } from "vue";
import {
  applyOrderAPI,
  type OrderForm
} from "@/api/workflow/order-service-need";
import { ElMessageBox, type FormInstance } from "element-plus";
import { QuestionFilled } from "@element-plus/icons-vue";
import { message } from "@/utils/message";
import {
  getProcesses,
  processes,
  orders,
  getOrders,
  users,
  getUsers,
  filterUsers,
  service_duration,
  loading
} from "./hook";
import { ENVs, ServiceneedOrderTypes } from "@/config/order-enum";
import ApprovalFlow from "../components/ApprovalFlow.vue";
import { CloudPlatforms } from "@/config/enum";
import { hasRole } from "@/router/utils";

// 使用从hook导入的loading变量
const form = ref<OrderForm>({
  title: "",
  order_type: "业务部署",
  content: "",
  plan_time: undefined,
  orders: [],
  env: undefined,
  critical: false,
  iteration_version: "",
  cloud_platforms: undefined,
  need_decrypt_stat_info: false,
  help_apply: false
});

const ruleFormRef = ref<FormInstance>();
const onSubmit = async () => {
  const valid = await new Promise(resolve => {
    ruleFormRef.value.validate((valid: boolean) => {
      resolve(valid);
    });
  });
  if (valid) {
    // 如果是管理员补充申请，需要设置服务时长
    if (form.value.help_apply && hasRole("workflow_admin")) {
      form.value.service_duration_seconds = service_duration.value.hours * 3600;
    }
    try {
      // 使用confirm方法创建确认对话框
      await ElMessageBox.confirm(
        h("div", { class: "custom-confirm-content" }, [
          h("iconify-icon-online", {
            icon: "material-symbols:help-outline-rounded",
            class: "confirm-icon"
          }),
          h("span", null, "确定要提交此工单吗？")
        ]),
        "提交确认",
        {
          confirmButtonClass: "confirm-button-with-icon",
          cancelButtonClass: "cancel-button-with-icon",
          confirmButtonText: "确认提交",
          cancelButtonText: "取消",
          type: "warning",
          draggable: true,
          center: true,
          customClass: "custom-message-box enhanced-dialog",
          showClose: true,
          closeOnClickModal: false,
          closeOnPressEscape: true
        }
      );
      const res = await applyOrderAPI(form.value);
      if (res.success) {
        message("提交成功", { type: "success" });
        setTimeout(() => {
          window.location.href = "/workflow/my/order";
        }, 300);
      } else {
        message(res.msg || "提交失败", { type: "error" });
      }
    } catch (error) {
      if (error !== "cancel") {
        message(error.message || "操作取消", { type: "warning" });
      }
    }
  } else {
    message("请检查表单数据", { type: "warning" });
  }
};

// 禁用日期函数 - 禁用今天之前的日期和周末
const disabledDateFunction = date => {
  const currentDate = new Date();
  const oneWorkingDayLater = getOneWorkingDayLater(currentDate);
  return date < oneWorkingDayLater || isWeekend(date);
};

// 计算一个工作日之后的日期
const getOneWorkingDayLater = date => {
  let nextDate = new Date(date);
  do {
    nextDate.setDate(nextDate.getDate() + 1);
  } while (isWeekend(nextDate));
  return nextDate;
};

// 判断是否为周末
const isWeekend = date => {
  const dayOfWeek = date.getDay();
  return dayOfWeek === 0 || dayOfWeek === 6;
};

onMounted(() => {
  getProcesses();
  getOrders();
  getUsers(); // 初始化用户列表
  const currentDate = new Date();
  form.value.plan_time = getOneWorkingDayLater(currentDate);
});
</script>

<style scoped>


@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.service-need-container {
  min-height: calc(100vh - 60px);
  padding: 0 0 32px;
  background-color: #f5f7fa;
}

.page-header {
  padding: 16px 24px;
  margin-bottom: 24px;
  background-color: #fff;
  border-bottom: 1px solid #ebeef5;
  box-shadow: 0 2px 8px rgb(0 0 0 / 5%);
}

.page-title {
  display: flex;
  align-items: center;
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.page-icon {
  margin-right: 12px;
  font-size: 24px;
  color: #409eff;
}

.order-container {
  margin: 0 24px;
}

.main-card {
  margin-bottom: 24px;
  overflow: hidden;
  border-radius: 12px;
  box-shadow: 0 6px 16px rgb(0 0 0 / 8%);
  transition: all 0.3s ease;
}

.main-card:hover {
  box-shadow: 0 8px 24px rgb(0 0 0 / 12%);
  transform: translateY(-2px);
}

/* 表单分区样式 */
.form-section {
  padding-bottom: 8px;
  margin-bottom: 18px;
}

.form-section:last-child {
  padding-bottom: 0;
  margin-bottom: 0;
  border-bottom: none;
}

.admin-section {
  width: 100%;
  padding: 16px;
  margin-bottom: 20px;
  background-color: #f8faff;
  border: 1px solid #e6efff;
  border-radius: 8px;
}

.admin-controls-row {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  align-items: flex-start;
  width: 100%;
}

.admin-control-item {
  min-width: 120px;
  margin-bottom: 0;
}

.applicant-item {
  flex: 2;
  min-width: 300px;
}

.duration-item {
  flex: 1;
  min-width: 150px;
}

.section-header {
  display: flex;
  align-items: center;
  padding-bottom: 8px;
  margin-bottom: 12px;
  border-bottom: 1px solid #ebeef5;
}

.section-icon {
  margin-right: 8px;
  font-size: 20px;
  color: #409eff;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.form-section-row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -12px;
}

.form-section-col {
  flex: 1;
  min-width: 250px;
  padding: 0 10px;
  margin-bottom: 4px;
}

.priority-col {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 表单项悬停效果 */
.el-form-item {
  padding: 2px 8px;
  margin-bottom: 12px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.el-form-item:hover {
  background-color: rgb(64 158 255 / 5%);
}

/* 表单分区动画效果 */
.form-section-animation {
  transition: all 0.3s ease;
}

.form-section-animation:hover .section-header .section-icon {
  transform: scale(1.1);
}

/* 表单项标签样式 */
:deep(.el-form-item__label) {
  padding-bottom: 4px;
  font-size: 14px;
  font-weight: 500;
  line-height: 1.3;
  color: #303133;
}

/* 表单项内容区域样式 */
:deep(.el-form-item__content) {
  margin-top: 2px;
  line-height: 1.4;
}

/* 动态表单项样式 */
.el-form-item[v-if] {
  animation: fade-in 0.5s ease;
}

.card-header {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  background-color: #f9fafc;
  border-bottom: 1px solid #ebeef5;
}

.title {
  display: flex;
  align-items: center;
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  letter-spacing: 0.5px;
}

.title span {
  margin-left: 12px;
}

.icon {
  font-size: 24px;
  color: #409eff;
}

.order-form {
  padding: 24px;
  margin-top: 0;
}

.approval-flow {
  margin-top: 0;
}

.related-orders-select {
  width: 100%;
}

.custom-select {
  width: 100%;
}

.env-select {
  width: 100%;
  min-width: 180px;
}

.env-col {
  flex: 1;
  min-width: 200px;
}

.custom-radio-group {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
}

:deep(.el-form-item__label-extra) {
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

:deep(.el-form-item) {
  margin-bottom: 24px;
}

:deep(.el-select .el-input) {
  transition: all 0.3s;
}

:deep(.el-input__inner) {
  height: 40px;
  line-height: 40px;
  border-radius: 4px;
  transition: all 0.3s;
}

:deep(.el-input__inner:focus) {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgb(64 158 255 / 20%);
}

:deep(.el-textarea__inner) {
  padding: 12px;
  line-height: 1.6;
  border-radius: 4px;
  transition: all 0.3s;
}

:deep(.el-textarea__inner:focus) {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgb(64 158 255 / 20%);
}

:deep(.el-date-editor.el-input) {
  width: 100%;
  transition: all 0.3s;
}

.action-buttons {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin-top: 0;
}

.action-section {
  padding: 16px 0;
  margin-top: 24px;
}

.submit-button {
  min-width: 120px;
  padding: 10px 20px;
  font-weight: 500;
  letter-spacing: 0.5px;
  border-radius: 6px;
}

.cancel-button {
  min-width: 120px;
  padding: 10px 20px;
  margin-left: 16px;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0.5px;
  border-radius: 6px;
}

:deep(.el-button--default:hover) {
  background-color: #f2f6fc;
  box-shadow: 0 4px 12px rgb(0 0 0 / 10%);
  transform: translateY(-2px);
}

.applicant-select-container {
  display: flex;
  gap: 12px;
  align-items: center;
}

.applicant-select {
  flex: 1;
  width: 100%;
  min-width: 400px;
}

.duration-input {
  width: 120px;
}

/* 自定义单选按钮组样式已在上方定义 */

/* 申请人选择下拉框样式 */
:deep(.applicant-select-dropdown) {
  min-width: 280px !important;
  max-width: 400px !important;
  padding: 8px 0;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 10%);
}

:deep(.applicant-select-dropdown .el-select-dropdown__item) {
  height: auto;
  padding: 10px 16px;
  line-height: 1.5;
  transition: all 0.2s;
}

:deep(.applicant-select-dropdown .el-select-dropdown__item.hover) {
  background-color: #f5f7fa;
}

:deep(.applicant-select-dropdown .el-select-dropdown__item.selected) {
  font-weight: 500;
  color: #409eff;
  background-color: #ecf5ff;
}

.refresh-button {
  flex-shrink: 0;
  height: 40px;
  padding: 0 16px;
  border-radius: 4px;
  transition: all 0.3s;
}

.refresh-button:hover {
  box-shadow: 0 4px 12px rgb(64 158 255 / 30%);
  transform: translateY(-2px);
}

.applicant-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.applicant-name {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.applicant-username {
  margin-left: 8px;
  font-size: 13px;
  color: #909399;
}

.duration-inputs {
  display: flex;
  gap: 8px;
  align-items: center;
  width: 100%;
}

/* 工单类型单选按钮组样式 */
.order-type-radio-group {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
}

:deep(.order-type-radio-group .el-radio-button) {
  margin-right: 12px;
  margin-bottom: 12px;
}

:deep(.order-type-radio-group .el-radio-button:last-child) {
  margin-right: 0;
}

:deep(.order-type-radio-group .el-radio-button__inner) {
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 500;
  border: 1px solid #dcdfe6;
  border-radius: 4px !important;
  box-shadow: 0 2px 4px rgb(0 0 0 / 5%);
  transition: all 0.3s;
}

:deep(
    .order-type-radio-group .el-radio-button:first-child .el-radio-button__inner
  ) {
  border-left: 1px solid #dcdfe6;
}

:deep(
    .order-type-radio-group
      .el-radio-button__orig-radio:checked
      + .el-radio-button__inner
  ) {
  color: white;
  background-color: #409eff;
  border-color: #409eff;
  box-shadow: 0 2px 8px rgb(64 158 255 / 30%);
}

:deep(.order-type-radio-group .el-radio-button__inner:hover) {
  color: #409eff;
  border-color: #409eff;
  box-shadow: 0 4px 8px rgb(0 0 0 / 10%);
  transform: translateY(-2px);
}

:deep(
    .order-type-radio-group
      .el-radio-button__orig-radio:checked
      + .el-radio-button__inner:hover
  ) {
  color: white;
}

:deep(.el-radio) {
  margin-right: 0;
  margin-bottom: 8px;
}

/* 表单提交按钮区域样式已移至.action-buttons */

/* 基本消息框样式 */
:deep(.custom-message-box) {
  padding: 30px 30px 20px;
  border-radius: 8px;
}

:deep(.custom-message-box .el-message-box__status) {
  top: 30px;
  font-size: 24px !important;
}

:deep(.custom-message-box .el-message-box__header) {
  padding: 0 0 10px;
}

:deep(.custom-message-box .el-message-box__title) {
  font-size: 16px;
  font-weight: 500;
}

:deep(.custom-message-box .el-message-box__content) {
  padding: 20px 0;
  font-size: 14px;
}

:deep(.custom-message-box .el-message-box__btns) {
  padding: 10px 0 0;
}

:deep(.custom-message-box .el-message-box__btns button) {
  min-width: 80px;
  padding: 8px 20px;
  margin-left: 12px;
  font-size: 14px;
  border-radius: 4px;
}

:deep(.custom-message-box .el-message-box__btns .el-button--primary) {
  background-color: var(--el-color-warning);
  border-color: var(--el-color-warning);
}

:deep(.custom-message-box .el-message-box__btns .el-button--primary:hover) {
  background-color: var(--el-color-warning-light-3);
  border-color: var(--el-color-warning-light-3);
}

/* 增强型对话框样式 */
:deep(.enhanced-dialog) {
  overflow: hidden;
  border: none;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgb(0 0 0 / 15%);
}

:deep(.enhanced-dialog .el-message-box__header) {
  padding: 16px 20px;
  background-color: #f9f9f9;
  border-bottom: 1px solid #eee;
}

:deep(.enhanced-dialog .confirm-title) {
  display: flex;
  gap: 8px;
  align-items: center;
}

:deep(.enhanced-dialog .title-icon) {
  font-size: 20px;
  color: var(--el-color-warning);
}

:deep(.enhanced-dialog .el-message-box__content) {
  padding: 24px 20px;
  font-size: 16px;
}

:deep(.enhanced-dialog .confirm-content) {
  display: flex;
  gap: 10px;
  align-items: center;
  padding: 10px 0;
}

:deep(.enhanced-dialog .confirm-icon) {
  font-size: 24px;
  color: var(--el-color-warning);
}

:deep(.enhanced-dialog .el-message-box__btns) {
  padding: 16px 20px;
  background-color: #f9f9f9;
  border-top: 1px solid #eee;
}

:deep(.enhanced-dialog .el-button) {
  padding: 10px 20px;
  font-weight: 500;
  border-radius: 6px;
  transition: all 0.3s;
}

:deep(.enhanced-dialog .el-button--primary:hover) {
  background-color: var(--el-color-primary-light-3);
  border-color: var(--el-color-primary-light-3);
  box-shadow: 0 4px 12px rgb(64 158 255 / 20%);
  transform: translateY(-2px);
}

:deep(.enhanced-dialog .el-button--default:hover) {
  border-color: #dcdfe6;
  box-shadow: 0 4px 12px rgb(0 0 0 / 10%);
  transform: translateY(-2px);
}

:deep(.enhanced-dialog .confirm-btn-text),
:deep(.enhanced-dialog .cancel-btn-text) {
  display: flex;
  align-items: center;
}

/* 按钮图标样式 */
:deep(.confirm-button-with-icon) {
  position: relative;
  padding-left: 36px !important;
}

:deep(.confirm-button-with-icon)::before {
  position: absolute;
  top: 50%;
  left: 12px;
  width: 16px;
  height: 16px;
  content: "";
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"><path fill="white" d="M9 16.17L4.83 12l-1.42 1.41L9 19L21 7l-1.41-1.41L9 16.17z"/></svg>')
    no-repeat center center;
  transform: translateY(-50%);
}

:deep(.cancel-button-with-icon) {
  position: relative;
  padding-left: 36px !important;
}

:deep(.cancel-button-with-icon)::before {
  position: absolute;
  top: 50%;
  left: 12px;
  width: 16px;
  height: 16px;
  content: "";
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"><path fill="%23606266" d="M19 6.41L17.59 5L12 10.59L6.41 5L5 6.41L10.59 12L5 17.59L6.41 19L12 13.41L17.59 19L19 17.59L13.41 12L19 6.41z"/></svg>')
    no-repeat center center;
  transform: translateY(-50%);
}

/* 自定义确认内容样式 */
:deep(.custom-confirm-content) {
  display: flex;
  gap: 12px;
  align-items: center;
  padding: 10px 0;
  font-size: 16px;
}

:deep(.custom-confirm-content .confirm-icon) {
  font-size: 24px;
  color: var(--el-color-warning);
}

/* 添加标题图标 */
:deep(.enhanced-dialog .el-message-box__title) {
  position: relative;
  padding-left: 30px;
}

:deep(.enhanced-dialog .el-message-box__title)::before {
  position: absolute;
  top: 50%;
  left: 0;
  width: 24px;
  height: 24px;
  content: "";
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path fill="%23E6A23C" d="M4 20q-.825 0-1.413-.588T2 18V6q0-.825.588-1.413T4 4h16q.825 0 1.413.588T22 6v12q0 .825-.588 1.413T20 20H4Zm8-7l8-5V6l-8 5l-8-5v2l8 5Z"/></svg>')
    no-repeat center center;
  transform: translateY(-50%);
}

/* 确认按钮样式 */
:deep(.enhanced-dialog .el-button--primary) {
  position: relative;
  overflow: hidden;
}

:deep(.enhanced-dialog .el-button--primary)::before {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  content: "";
  background: linear-gradient(
    90deg,
    transparent,
    rgb(255 255 255 / 20%),
    transparent
  );
  transition: 0.5s;
}

:deep(.enhanced-dialog .el-button--primary):hover::before {
  left: 100%;
}
</style>
