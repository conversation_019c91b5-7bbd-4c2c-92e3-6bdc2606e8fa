<script lang="ts" setup>
import { ref, watch, computed, onMounted } from "vue";
import { useUserStore } from "@/store/modules/user";
import dayjs from "dayjs";
import MonacoEditor from "monaco-editor-vue3";
import {
  CircleCheckFilled,
  CircleCloseFilled,
  SuccessFilled,
  InfoFilled
} from "@element-plus/icons-vue";

import {
  ENV,
  ENVs,
  MySQLOrderTypes,
  OrderStatus,
  OrderStatuses,
  OrderExtTypes
} from "@/config/order-enum";

import { execSQLAPI } from "@/api/database/mysql/sql";
import { message } from "@/utils/message";
import { getOrderMySQLTablesAPI } from "@/api/workflow/order-mysql";
import { formatStorage } from "@/utils/format";
import { formatDuration } from "@/utils/date";

defineOptions({
  name: "MySQLOrderDetail"
});

export interface FormProps {
  formInline: {
    orderDetailData: any;
  };
}
const props = withDefaults(defineProps<FormProps>(), {
  formInline: () => ({ orderDetailData: undefined })
});
const newFormInline = ref(props.formInline);
const isMobile = ref(false);

// 检测是否为移动设备
const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768;
};

onMounted(() => {
  checkMobile();
  window.addEventListener("resize", checkMobile);
});

// 监听数据变化
watch(
  () => newFormInline.value.orderDetailData,
  newVal => {
    console.log("SQL内容更新:", newVal.order?.sql_content);
  },
  { deep: true }
);

// 计算编辑器高度
const calculateEditorHeight = computed(() => {
  const content = newFormInline.value.orderDetailData.order?.sql_content || "";
  const lineCount = (content.match(/\n/g) || []).length + 1;
  const lineHeight = 50; // 每行的预估高度增加
  return Math.min(Math.max(lineCount * lineHeight + 40, 150), 600) + "px"; // 最小150px，最大600px
});

// 添加复制到剪贴板的函数
const copyToClipboard = () => {
  const sqlContent = newFormInline.value.orderDetailData.order?.sql_content;
  if (sqlContent) {
    navigator.clipboard
      .writeText(sqlContent)
      .then(() => {
        message("SQL内容已复制到剪贴板", { type: "success" });
      })
      .catch(() => {
        message("复制失败，请手动复制", { type: "warning" });
      });
  }
};

const dataTables = ref<any[]>([]);
function getTables() {
  getOrderMySQLTablesAPI(newFormInline.value.orderDetailData.order?.sn).then(
    res => {
      if (res.success) {
        dataTables.value = res.data.map(table => ({
          ...table,
          data_length: formatStorage(table.data_length),
          index_length: formatStorage(table.index_length)
        }));
      } else {
        message(res.msg, { type: "warning" });
      }
    }
  );
}

// Monaco编辑器配置
const monacoOptions = {
  readOnly: true,
  language: "sql",
  theme: "vs-dark",
  minimap: {
    enabled: false
  },
  automaticLayout: true,
  scrollBeyondLastLine: false,
  wordWrap: "on",
  lineNumbers: "on",
  renderLineHighlight: "all",
  contextmenu: false,
  fontSize: 20
};

const store = useUserStore();

const sqlResult = ref([]);
const checkResult = ref<{ success: boolean; msg: string } | null>(null); // 用于存储检查结果和信息

const executeSQL = async () => {
  const sqlData = {
    sql_content: newFormInline.value.orderDetailData?.order?.sql_content,
    db_name: newFormInline.value.orderDetailData?.order?.db_name,
    project_id: newFormInline.value.orderDetailData?.order?.project?.id,
    backup: false // 根据需要设置备份选项
  };

  try {
    const response = await execSQLAPI(sqlData);
    if (response.success) {
      message("SQL执行成功", { type: "success" });
      sqlResult.value = response.data;
      checkResult.value = { success: true, msg: response.msg };
    } else {
      message(response.msg, { type: "warning" });
      sqlResult.value = response.data;
      checkResult.value = { success: false, msg: response.msg };
    }
  } catch (error) {
    message("执行SQL时发生错误", { type: "warning" });
  }
};
</script>

<template>
  <div class="order-detail-container" :class="{ 'mobile-container': isMobile }">
    <el-descriptions
      title="工单详情"
      class="margin-top details-card"
      direction="vertical"
      border
      :size="isMobile ? 'default' : 'large'"
      :column="isMobile ? 1 : 3"
      :label-class-name="isMobile ? 'mobile-label' : ''"
      :content-class-name="isMobile ? 'mobile-content' : ''"
    >
      <el-descriptions-item label="标题" :span="isMobile ? 1 : 3" class="detail-item">
        <el-text class="detail-text" size="large">
          {{ newFormInline.orderDetailData.order?.title }}
        </el-text>
      </el-descriptions-item>

      <el-descriptions-item label="工单类型" class="detail-item">
        <el-tag size="large" effect="plain" type="warning">
          {{ newFormInline.orderDetailData.order?.order_type }}
        </el-tag>
      </el-descriptions-item>

      <el-descriptions-item label="工单分类" class="detail-item">
        <span class="detail-text">
          {{ OrderExtTypes.get(newFormInline.orderDetailData.order?.ext_type) }}
        </span>
      </el-descriptions-item>

      <el-descriptions-item label="是否是紧急" class="detail-item">
        <el-text
          :type="
            newFormInline.orderDetailData.order?.critical ? 'danger' : 'info'
          "
          class="detail-text"
        >
          {{ newFormInline.orderDetailData.order?.critical ? "是" : "否" }}
        </el-text>
      </el-descriptions-item>

      <el-descriptions-item label="申请人" class="detail-item">
        <el-text class="detail-text">
          <el-avatar
            :size="24"
            :src="newFormInline.orderDetailData.order?.applicant?.avatar"
            :fallback="() => false"
            :data-letter="
              newFormInline.orderDetailData.order?.applicant?.name?.[0]?.toUpperCase()
            "
            style="background: var(--el-color-primary-light-5)"
          >
            {{
              newFormInline.orderDetailData.order?.applicant?.name?.[0]?.toUpperCase()
            }}
          </el-avatar>
          {{ newFormInline.orderDetailData.order?.applicant?.name }}
          <el-tag size="small" effect="plain">
            {{ newFormInline.orderDetailData.order?.applicant?.username }}
          </el-tag>
        </el-text>
      </el-descriptions-item>

      <el-descriptions-item label="环境" class="detail-item env-item">
        <div class="env-container">
          <el-tag
            :class="[
              'env-tag',
              newFormInline.orderDetailData.order?.env === ENV.ProdEnv
                ? 'env-prod'
                : newFormInline.orderDetailData.order?.env === ENV.TestEnv
                  ? 'env-test'
                  : 'env-dev'
            ]"
            :type="
              newFormInline.orderDetailData.order?.env === ENV.ProdEnv
                ? 'danger'
                : newFormInline.orderDetailData.order?.env === ENV.TestEnv
                  ? 'warning'
                  : 'info'
            "
            effect="dark"
            size="large"
          >
            <span class="env-text">{{
              ENVs.get(newFormInline.orderDetailData.order?.env)
            }}</span>
          </el-tag>
        </div>
      </el-descriptions-item>

      <el-descriptions-item label="工单状态" class="detail-item">
        <el-text
          :type="
            newFormInline.orderDetailData.order?.status ===
            OrderStatus.Approving
              ? 'success'
              : newFormInline.orderDetailData.order?.status ===
                  OrderStatus.Rejected
                ? 'danger'
                : newFormInline.orderDetailData.order?.status ===
                    OrderStatus.Completed
                  ? 'primary'
                  : 'info'
          "
          class="status-text"
        >
          <el-icon
            v-if="
              newFormInline.orderDetailData.order?.status ===
              OrderStatus.Approving
            "
          >
            <CircleCheckFilled />
          </el-icon>
          <el-icon
            v-else-if="
              newFormInline.orderDetailData.order?.status ===
              OrderStatus.Rejected
            "
          >
            <CircleCloseFilled />
          </el-icon>
          <el-icon
            v-else-if="
              newFormInline.orderDetailData.order?.status ===
              OrderStatus.Completed
            "
          >
            <SuccessFilled />
          </el-icon>
          <el-icon v-else>
            <InfoFilled />
          </el-icon>
          {{
            OrderStatuses.get(newFormInline.orderDetailData.order?.status)
          }} </el-text
        ><el-text
          v-if="
            newFormInline.orderDetailData.order?.service_duration_seconds > 0
          "
          type="info"
          style="margin-left: 20px"
        >
          耗时：
          {{
            formatDuration(
              newFormInline.orderDetailData.order?.service_duration_seconds
            )
          }}</el-text
        >
      </el-descriptions-item>

      <el-descriptions-item label="计划时间" class="detail-item">
        <span class="time-text">
          {{
            newFormInline.orderDetailData.order?.plan_time &&
            dayjs(newFormInline.orderDetailData.order?.plan_time).format(
              "YYYY-MM-DD HH:mm:ss"
            )
          }}
        </span>
      </el-descriptions-item>

      <el-descriptions-item label="申请时间" class="detail-item">
        <span class="time-text">
          {{
            dayjs(newFormInline.orderDetailData.order?.created_at).format(
              "YYYY-MM-DD HH:mm:ss"
            )
          }}
        </span>
      </el-descriptions-item>

      <el-descriptions-item label="项目" class="detail-item">
        <span class="detail-text">
          {{ newFormInline.orderDetailData.order?.project?.name || "-" }}
        </span>
      </el-descriptions-item>

      <el-descriptions-item label="库名" class="detail-item">
        <span class="detail-text">
          {{ newFormInline.orderDetailData.order?.db_name || "-" }}
        </span>
      </el-descriptions-item>

      <el-descriptions-item label="账户权限" class="detail-item">
        <span class="detail-text">
          {{ newFormInline.orderDetailData.order?.permission || "-" }}
        </span>
      </el-descriptions-item>

      <el-descriptions-item label="附件" class="detail-item">
        <p
          v-for="item in newFormInline.orderDetailData.order?.attachments"
          :key="item"
        >
          <el-link
            :href="`${item.url}?sn=${newFormInline.orderDetailData.order?.sn}`"
            target="_blank"
            :download="item.url"
            class="detail-text"
          >
            {{ item.filename }}
          </el-link>
        </p>
      </el-descriptions-item>

      <el-descriptions-item
        v-if="
          newFormInline.orderDetailData.order?.related_orders &&
          newFormInline.orderDetailData.order?.related_orders.length > 0
        "
        label="关联工单"
        :span="4"
        class="reason-item"
      >
        <div class="reason-text" style="white-space: pre-wrap">
          <template
            v-for="order in newFormInline.orderDetailData.order.related_orders"
            :key="order"
          >
            <li>
              <el-link
                :href="'/workflow/order/detail/' + order.id"
                target="_blank"
                type="primary"
              >
                {{ order.title }}
              </el-link>
            </li>
          </template>
        </div>
      </el-descriptions-item>
      <el-descriptions-item label="申请理由" :span="4" class="reason-item">
        <div class="reason-text" style="white-space: pre-wrap">
          {{ newFormInline.orderDetailData.order?.content }}
        </div>
      </el-descriptions-item>

      <el-descriptions-item
        v-if="
          newFormInline.orderDetailData.order?.order_type === MySQLOrderTypes[0]
        "
        label="SQL内容"
        :span="4"
        class="sql-editor-container"
      >
        <div
          class="editor-wrapper"
          style="display: flex; flex-direction: column; align-items: flex-end"
        >
          <el-button
            type="primary"
            size="small"
            style="margin-bottom: 10px"
            @click="copyToClipboard"
          >
            快速复制
          </el-button>
          <MonacoEditor
            v-if="newFormInline.orderDetailData.order?.sql_content"
            :value="newFormInline.orderDetailData.order?.sql_content"
            :options="monacoOptions"
            :style="{ height: calculateEditorHeight }"
            class="sql-editor"
          />
          <div v-else class="empty-content">暂无SQL内容</div>
        </div>
      </el-descriptions-item>
      <el-descriptions-item
        v-if="
          newFormInline.orderDetailData.order?.order_type ===
            MySQLOrderTypes[0] &&
          (store.IS_ADMIN() || newFormInline.orderDetailData.order?.critical)
        "
        label="变更表"
        :span="4"
        class="sql-table-container"
      >
        <div class="table-container">
          <el-button
            v-if="!newFormInline.orderDetailData.order?.tables"
            type="primary"
            size="small"
            @click="getTables"
          >
            获取变更表
          </el-button>
          <el-table :data="dataTables" style="width: 100%">
            <el-table-column prop="table_schema" label="数据库名" />
            <el-table-column prop="name" label="表名" />
            <el-table-column prop="collation" label="字符集" />
            <el-table-column prop="table_rows" label="行数" />
            <el-table-column prop="data_length" label="数据大小" />
            <el-table-column prop="index_length" label="索引大小" />
          </el-table>
        </div>
      </el-descriptions-item>

      <el-descriptions-item
        v-if="sqlResult.length > 0"
        label="SQL执行结果"
        :span="4"
        class="sql-result-container"
      >
        <el-table :data="sqlResult" style="width: 100%">
          <el-table-column prop="order_id" label="序号" />
          <el-table-column prop="sequence" label="序列" />
          <el-table-column prop="affected_rows" label="影响行数" />
          <el-table-column prop="error_message" label="错误信息" />
          <el-table-column prop="execute_time" label="执行时间" />
          <el-table-column prop="sql" label="SQL语句" />
          <el-table-column prop="stage" label="阶段" />``
          <el-table-column prop="stage_status" label="阶段状态" />
        </el-table>
      </el-descriptions-item>

      <el-descriptions-item
        v-if="
          newFormInline.orderDetailData?.is_last &&
          store.IS_ADMIN() &&
          newFormInline.orderDetailData?.order?.env === ENV.TestEnv &&
          newFormInline.orderDetailData?.order?.sql_content !== ''
        "
        label="操作"
        class="action-item"
      >
        <el-button type="primary" @click="executeSQL">执行SQL</el-button>
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<style lang="scss" scoped>
@import url("./styles/detail.scss");

@media screen and (width <= 768px) {
  .order-detail-container {
    padding: 8px !important;
  }
  
  :deep(.el-descriptions__title) {
    padding-left: 8px !important;
    margin-bottom: 12px !important;
    font-size: 16px !important;
  }
  
  :deep(.el-descriptions__body) {
    background-color: transparent !important;
  }
  
  :deep(.el-descriptions-item__label) {
    width: 30% !important;
    min-width: 80px !important;
    padding: 6px 8px !important;
  }
  
  :deep(.el-descriptions-item__content) {
    width: 70% !important;
    padding: 6px 8px !important;
  }
  
  :deep(.el-tag) {
    height: 24px !important;
    padding: 0 6px !important;
    font-size: 12px !important;
    line-height: 22px !important;
  }
  
  :deep(.el-avatar) {
    width: 20px !important;
    height: 20px !important;
    font-size: 12px !important;
  }
  
  .detail-text {
    display: flex !important;
    flex-wrap: wrap !important;
    gap: 4px !important;
    align-items: center !important;
    font-size: 13px !important;
  }
  
  .status-text {
    padding: 2px 6px !important;
    font-size: 13px !important;
  }
  
  .time-text {
    padding: 2px 6px !important;
    font-size: 12px !important;
  }
  
  .reason-text {
    min-height: 60px !important;
    padding: 8px !important;
    font-size: 13px !important;
    line-height: 1.5 !important;
  }
  
  .env-tag {
    margin: 4px 0 !important;
    
    .env-text {
      font-size: 12px !important;
    }
  }
  
  .sql-content {
    margin-top: 8px !important;
    
    .sql-editor {
      height: 120px !important;
    }
    
    .sql-actions {
      gap: 8px !important;
      margin-top: 8px !important;
      
      .el-button {
        padding: 6px 10px !important;
        font-size: 12px !important;
      }
    }
  }
  
  .sql-result-table {
    margin-top: 8px !important;
    overflow-x: auto !important;
    
    :deep(.el-table) {
      font-size: 12px !important;
      
      th, td {
        padding: 6px !important;
      }
    }
  }
  
  .tables-list {
    flex-wrap: wrap !important;
    gap: 4px !important;
    
    .el-tag {
      margin: 2px !important;
    }
  }
}

/* 移动端适配样式 */
.mobile-container {
  padding: 8px !important;
}

.mobile-label {
  padding: 8px !important;
  font-size: 13px !important;
  background-color: var(--el-fill-color-light) !important;
}

.mobile-content {
  padding: 8px !important;
}

.editor-wrapper {
  margin-top: 12px;
  overflow: hidden;
  border-radius: 4px;
}
</style>
