<script lang="ts" setup>
import { onBeforeMount, ref, onMounted } from "vue";
import { useRoute } from "vue-router";
import {
  getOrderDetailAPI,
  type OrderDetailData
} from "@/api/workflow/order-detail";
import { message } from "@/utils/message";
import { parmaQueryNumber } from "@/utils/parmaQuery";
import page403 from "@/views/error/403.vue";
import OrderTypeWrapper from "./components/OrderTypeWrapper.vue";
import ApprovalProcess from "./components/ApprovalProcess.vue";
import ApprovalForm from "./components/ApprovalForm.vue";
import { useUserStoreHook } from "@/store/modules/user";
import ServiceEval from "./components/ServiceEval.vue";
import OrderComment from "./components/OrderComment.vue";

defineOptions({
  name: "OrderDetail"
});

const orderID = ref<number>(0);
const noAccess = ref<boolean>(true);
const isMobile = ref(false);
const showProcessPanel = ref(true);

const orderDetailData = ref<OrderDetailData>({
  access: false,
  can_approve: false,
  can_edit: false,
  is_last: false,
  order: undefined,
  can_eval: false
});

// 检测是否为移动设备
const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768;
};

// 切换流程面板显示
const toggleProcessPanel = () => {
  showProcessPanel.value = !showProcessPanel.value;
};

const getOrderDetail = async () => {
  try {
    const res = await getOrderDetailAPI(orderID.value);
    if (res.success) {
      orderDetailData.value = res.data;
      noAccess.value = !res.data.access;
    } else {
      message(res.msg, { type: "error" });
    }
  } catch (error) {
    message(`请求失败: ${error}`, { type: "error" });
  }
};

const sensitiveForm = ref(orderDetailData.value.order?.is_sensitive);
const showSensitiveEdit = () => {
  return (
    orderDetailData.value.order?.ext_type === 2 &&
    orderDetailData.value.can_approve &&
    useUserStoreHook().username === "lijiaye" &&
    !sensitiveForm.value &&
    orderDetailData.value.order?.order_type === "DGC(数据仓库)"
  );
};

onMounted(() => {
  checkMobile();
  window.addEventListener("resize", checkMobile);
});

onBeforeMount(() => {
  const route = useRoute();
  const id = route.params.id;
  orderID.value = parmaQueryNumber(id);

  if (orderID.value > 0) {
    noAccess.value = false;
    getOrderDetail();
  }
});
</script>

<template>
  <div>
    <el-card class="button-group">
      <div class="nav-buttons">
        <RouterLink :to="{ name: '审批工单' }">
          <el-button
            link
            type="primary"
            :size="isMobile ? 'small' : 'default'"
            class="nav-button"
          >
            审批工单
          </el-button>
        </RouterLink>
        <RouterLink :to="{ name: '我的工单' }">
          <el-button
            link
            type="primary"
            :size="isMobile ? 'small' : 'default'"
            class="nav-button"
          >
            我的工单
          </el-button>
        </RouterLink>
      </div>
    </el-card>
    <div v-if="noAccess" class="main">
      <page403 />
    </div>
    <div v-else class="main">
      <!-- 移动端流程面板切换按钮 -->
      <div v-if="isMobile" class="mobile-process-toggle">
        <el-button 
          type="primary" 
          size="small" 
          class="toggle-button"
          @click="toggleProcessPanel"
        >
          {{ showProcessPanel ? '隐藏审批流程' : '显示审批流程' }}
        </el-button>
      </div>
      
      <el-row :gutter="isMobile ? 0 : 20">
        <!-- 移动端先显示审批流程 -->
        <el-col v-if="isMobile && showProcessPanel" :span="24">
          <ApprovalProcess
            class="approval-process mobile-process"
            :processes="orderDetailData.order?.processes || []"
            v-bind="$attrs"
          />
        </el-col>
        
        <!-- 工单内容区域 -->
        <el-col :span="isMobile ? 24 : 16">
          <div class="left-content">
            <OrderTypeWrapper :orderDetailData="orderDetailData" />
            <ApprovalForm
              class="approval-form"
              :canApprove="orderDetailData.can_approve"
              :orderId="orderID"
              :canEdit="orderDetailData.can_edit"
              :ext_type="orderDetailData.order?.ext_type || 0"
              :show_sensitive_edit="showSensitiveEdit()"
              :is_last="orderDetailData.is_last"
              v-bind="$attrs"
            />
            <!-- 服务评价放在工单详情和审批表单之后 -->
            <div
              v-if="orderDetailData.order && orderDetailData.order.status === 2"
              class="eval-container"
            >
              <ServiceEval
                :sn="orderDetailData.order.sn"
                :exist="orderDetailData.evaluation || null"
                :canEval="orderDetailData.can_eval"
                @refresh="getOrderDetail"
              />
            </div>
            <!-- 工单评论组件：只要有查看权限就展示 -->
            <OrderComment
              v-if="orderDetailData.order && orderDetailData.access"
              :sn="orderDetailData.order.sn"
              :comments="(orderDetailData.comments as any) || []"
              @refresh="getOrderDetail"
            />
          </div>
        </el-col>
        
        <!-- 桌面端审批流程 -->
        <el-col v-if="!isMobile" :span="8">
          <ApprovalProcess
            class="approval-process"
            :processes="orderDetailData.order?.processes || []"
            v-bind="$attrs"
          />
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<style lang="scss" scoped>


/* 移动端适配 */
@media screen and (width <= 768px) {
  .button-group {
    margin: 8px;
  }
  
  .main {
    padding: 8px;
    
    :deep(.el-col) {
      padding: 0 5px;
    }
  }
  
  .left-content {
    gap: 8px !important;
  }
  
  :deep(.el-card) {
    border-radius: 6px !important;
    
    .el-card__header {
      padding: 12px !important;
      font-size: 14px !important;
    }
    
    .el-card__body {
      padding: 12px !important;
    }
  }
  
  :deep(.el-form-item) {
    margin-bottom: 12px !important;
  }
  
  :deep(.el-form-item__label) {
    padding-bottom: 4px !important;
    font-size: 13px !important;
  }
  
  :deep(.el-button) {
    padding: 8px 12px !important;
  }
  
  :deep(.el-input-number) {
    width: 100px !important;
  }
  
  :deep(.el-select) {
    width: 100% !important;
  }
  
  :deep(.el-textarea__inner) {
    min-height: 80px !important;
  }
}

.button-group {
  margin: 12px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgb(0 0 0 / 10%);
}

.nav-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.nav-button {
  font-weight: bold;
}

.mobile-process-toggle {
  display: flex;
  justify-content: center;
  margin-bottom: 12px;
  
  .toggle-button {
    width: 100%;
    max-width: 300px;
    font-weight: 500;
  }
}

.main {
  padding: 16px;
  background-color: var(--el-bg-color-page);

  @media screen and (width <= 1400px) {
    .el-col {
      &:first-child {
        width: 100%;
      }

      &:last-child {
        width: 100%;
        margin-top: 16px;

        .approval-process {
          position: static;
        }
      }
    }
  }

  .left-content {
    display: flex;
    flex-direction: column;
    gap: 12px;
    height: "auto";

    .detail-card {
      flex-shrink: 0;
      border-radius: 8px;
      box-shadow: var(--el-box-shadow-light);
      transition: all 0.3s ease;

      &:hover {
        box-shadow: var(--el-box-shadow);
      }

      :deep(.el-card__body) {
        padding: 0;
      }
    }

    .approval-form {
      display: flex;
      flex: 1;
      flex-direction: column;
      min-height: min-content;
      margin-bottom: 16px;
    }

    .eval-container {
      margin-top: 8px;
    }
  }

  .approval-process {
    position: sticky;
    top: 16px;
    
    &.mobile-process {
      position: static;
      margin-bottom: 16px;
    }
  }

  :deep(.el-card) {
    overflow: hidden;
    border-radius: 8px;
  }

  :deep(.el-row) {
    height: 100%;
    margin: 0 !important;
  }

  :deep(.el-col) {
    height: 100%;
    padding: 0 10px;

    &:first-child {
      display: flex;
      flex-direction: column;
    }
  }
}
</style>
