export const ServerModelColumns: TableColumnList = [
  {
    label: "序号",
    prop: "index"
  },
  {
    label: "实例规格",
    prop: "spec"
  },
  {
    label: "本地存储",
    prop: "storage"
  },
  {
    label: "CPU(核)",
    prop: "cpu"
  },
  {
    label: "内存(GB)",
    prop: "memory"
  },
  {
    label: "GPU",
    prop: "gpu"
  },
  {
    label: "价格",
    prop: "price"
  },
  {
    label: "数量",
    prop: "amount"
  },
  {
    label: "总价",
    prop: "total_price"
  }
];
export const DomainModelColumns: TableColumnList = [
  {
    label: "序号",
    prop: "index"
  },
  {
    label: "域名",
    prop: "domain"
  },
  {
    label: "环境",
    prop: "env",
    cellRenderer: ({ row }) => (
      <el-select v-model={row.env} disabled>
        <el-option value="dev" label="开发环境">
          开发环境
        </el-option>
        <el-option value="test" label="测试环境">
          测试环境
        </el-option>
        <el-option value="yf" label="预发环境">
          预发环境
        </el-option>
        <el-option value="prod" label="生产环境">
          生产环境
        </el-option>
      </el-select>
    )
  },

  {
    label: "限制公司访问",
    prop: "limit_office",
    cellRenderer: ({ row }) => <el-switch v-model={row.limit_office} disabled />
  },
  {
    label: "备注",
    prop: "remark"
  }
];
