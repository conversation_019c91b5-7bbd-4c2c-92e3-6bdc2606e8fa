<script lang="ts" setup>
import { ref, onMounted } from "vue";
import dayjs from "dayjs";
import {
  CircleCheckFilled,
  CircleCloseFilled,
  SuccessFilled,
  InfoFilled
} from "@element-plus/icons-vue";

import { OrderStatus, OrderStatuses, OrderExtTypes } from "@/config/order-enum";
import { formatDuration } from "@/utils/date";

defineOptions({
  name: "DesktopOPSOrderDetail"
});

export interface FormProps {
  formInline: {
    orderDetailData: any;
  };
}
const props = withDefaults(defineProps<FormProps>(), {
  formInline: () => ({ orderDetailData: undefined })
});
const newFormInline = ref(props.formInline);
const isMobile = ref(false);

// 检测是否为移动设备
const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768;
};

onMounted(() => {
  checkMobile();
  window.addEventListener("resize", checkMobile);
});
</script>

<template>
  <div class="order-detail-container" :class="{ 'mobile-container': isMobile }">
    <el-descriptions
      title="工单详情"
      class="margin-top details-card"
      direction="vertical"
      :border="true"
      :size="isMobile ? 'default' : 'large'"
      :column="isMobile ? 1 : 4"
      :label-class-name="isMobile ? 'mobile-label' : ''"
      :content-class-name="isMobile ? 'mobile-content' : ''"
    >
      <el-descriptions-item label="标题" class="detail-item" :span="isMobile ? 1 : 2">
        <el-text class="detail-text" size="large">
          {{ newFormInline.orderDetailData.order?.title }}
        </el-text>
      </el-descriptions-item>

      <el-descriptions-item label="工单分类" class="detail-item">
        <el-tag size="large" effect="plain">
          {{ OrderExtTypes.get(newFormInline.orderDetailData.order?.ext_type) }}
        </el-tag>
      </el-descriptions-item>

      <el-descriptions-item label="申请人" class="detail-item">
        <el-text class="detail-text">
          <el-avatar
            :size="24"
            :src="newFormInline.orderDetailData.order?.applicant?.avatar"
            :fallback="() => false"
            :data-letter="
              newFormInline.orderDetailData.order?.applicant?.name?.[0]?.toUpperCase()
            "
            style="background: var(--el-color-primary-light-5)"
          >
            {{
              newFormInline.orderDetailData.order?.applicant?.name?.[0]?.toUpperCase()
            }}
          </el-avatar>
          {{ newFormInline.orderDetailData.order?.applicant?.name }}
          <el-tag size="small" effect="plain">
            {{ newFormInline.orderDetailData.order?.applicant?.username }}
          </el-tag>
        </el-text>
      </el-descriptions-item>

      <el-descriptions-item label="工单状态" class="detail-item">
        <el-text
          :type="
            newFormInline.orderDetailData.order?.status ===
            OrderStatus.Approving
              ? 'success'
              : newFormInline.orderDetailData.order?.status ===
                  OrderStatus.Rejected
                ? 'danger'
                : newFormInline.orderDetailData.order?.status ===
                    OrderStatus.Completed
                  ? 'primary'
                  : 'info'
          "
          class="status-text"
        >
          <el-icon
            v-if="
              newFormInline.orderDetailData.order?.status ===
              OrderStatus.Approving
            "
          >
            <CircleCheckFilled />
          </el-icon>
          <el-icon
            v-else-if="
              newFormInline.orderDetailData.order?.status ===
              OrderStatus.Rejected
            "
          >
            <CircleCloseFilled />
          </el-icon>
          <el-icon
            v-else-if="
              newFormInline.orderDetailData.order?.status ===
              OrderStatus.Completed
            "
          >
            <SuccessFilled />
          </el-icon>
          <el-icon v-else>
            <InfoFilled />
          </el-icon>
          {{ OrderStatuses.get(newFormInline.orderDetailData.order?.status) }}
        </el-text>
        <el-text
          v-if="
            newFormInline.orderDetailData.order?.service_duration_seconds > 0
          "
          type="info"
          style="margin-left: 20px"
        >
          耗时：
          {{
            formatDuration(
              newFormInline.orderDetailData.order?.service_duration_seconds
            )
          }}</el-text
        >
      </el-descriptions-item>

      <el-descriptions-item label="工位" class="detail-item">
        <el-text type="warning" class="detail-text">
          <b>{{ newFormInline.orderDetailData.order?.cubicle || "-" }}</b>
        </el-text>
      </el-descriptions-item>

      <el-descriptions-item label="计划时间" class="detail-item">
        <span class="time-text">
          {{
            newFormInline.orderDetailData.order?.plan_time &&
            dayjs(newFormInline.orderDetailData.order?.plan_time).format(
              "YYYY-MM-DD HH:mm:ss"
            )
          }}
        </span>
      </el-descriptions-item>

      <el-descriptions-item label="申请时间" class="detail-item">
        <span class="time-text">
          {{
            dayjs(newFormInline.orderDetailData.order?.created_at).format(
              "YYYY-MM-DD HH:mm:ss"
            )
          }}
        </span>
      </el-descriptions-item>

      <el-descriptions-item label="申请理由" :span="isMobile ? 1 : 4" class="reason-item">
        <div class="reason-text" style="white-space: pre-wrap">
          {{ newFormInline.orderDetailData.order?.content }}
        </div>
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<style lang="scss" scoped>
@import url("./styles/detail.scss");

@media screen and (width <= 768px) {
  .order-detail-container {
    padding: 8px !important;
  }
  
  :deep(.el-descriptions__title) {
    padding-left: 8px !important;
    margin-bottom: 12px !important;
    font-size: 16px !important;
  }
  
  :deep(.el-descriptions__body) {
    background-color: transparent !important;
  }
  
  :deep(.el-descriptions-item__label) {
    width: 30% !important;
    min-width: 80px !important;
    padding: 6px 8px !important;
  }
  
  :deep(.el-descriptions-item__content) {
    width: 70% !important;
    padding: 6px 8px !important;
  }
  
  :deep(.el-tag) {
    height: 24px !important;
    padding: 0 6px !important;
    font-size: 12px !important;
    line-height: 22px !important;
  }
  
  :deep(.el-avatar) {
    width: 20px !important;
    height: 20px !important;
    font-size: 12px !important;
  }
  
  .detail-text {
    display: flex !important;
    flex-wrap: wrap !important;
    gap: 4px !important;
    align-items: center !important;
    font-size: 13px !important;
  }
  
  .status-text {
    padding: 2px 6px !important;
    font-size: 13px !important;
  }
  
  .time-text {
    padding: 2px 6px !important;
    font-size: 12px !important;
  }
  
  .reason-text {
    min-height: 60px !important;
    padding: 8px !important;
    font-size: 13px !important;
    line-height: 1.5 !important;
  }
}

/* 移动端适配样式 */
.mobile-container {
  padding: 8px !important;
}

.mobile-label {
  padding: 8px !important;
  font-size: 13px !important;
  background-color: var(--el-fill-color-light) !important;
}

.mobile-content {
  padding: 8px !important;
}
</style>
