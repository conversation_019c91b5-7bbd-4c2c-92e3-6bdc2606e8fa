<script lang="ts" setup>
import { OrderExtType } from "@/config/order-enum";
import MySQLDetail from "../MySQL.vue";
import DesktopOPS from "../DesktopOPS.vue";
import Server from "../Server.vue";
import Permission from "../Permission.vue";
import Domain from "../Domain.vue";
import ServiceNeed from "../ServiceNeed.vue";
import MongoDB from "../MongoDB.vue";
import Gitlab from "../Gitlab.vue";

interface Props {
  orderDetailData: any;
}

const { orderDetailData } = defineProps<Props>();

const componentMap = {
  [OrderExtType.DatabaseOrderType]: MySQLDetail,
  [OrderExtType.DesktopOPSOrderType]: DesktopOPS,
  [OrderExtType.ServerOrderType]: Server,
  [OrderExtType.PermissionOrderType]: Permission,
  [OrderExtType.DomainOrderType]: Domain,
  [OrderExtType.ServiceNeedOrderType]: ServiceNeed,
  [OrderExtType.MongoDBOrderType]: MongoDB,
  [OrderExtType.GitlabOrderType]: Gitlab
} as const;
</script>

<template>
  <div class="order-type-wrapper">
    <component
      :is="componentMap[orderDetailData.order.ext_type]"
      v-if="orderDetailData.order?.ext_type"
      :formInline="{ orderDetailData }"
    />
  </div>
</template>
