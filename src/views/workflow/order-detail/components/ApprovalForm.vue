<script lang="ts" setup>
import { ref, h, onMounted } from "vue";
import { message } from "@/utils/message";
import {
  type AddOrTransferFrom,
  addOrTransferProcessAPI,
  ApproveAPI
} from "@/api/workflow/approver";
import { OrderStatus } from "@/config/order-enum";
import { ElMessageBox } from "element-plus";
import { addDialog } from "@/components/ReDialog";
import { cancelOrderAPI } from "@/api/workflow/order";
import UserSelectDialog from "./UserSelectDialog.vue";
import { CloudPlatforms } from "@/config/enum";
const props = defineProps<{
  canApprove: boolean;
  is_last: boolean;
  canEdit: boolean;
  orderId: number;
  ext_type: number;
  show_sensitive_edit: boolean;
}>();

const isMobile = ref(false);

// 检测是否为移动设备
const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768;
};

onMounted(() => {
  checkMobile();
  window.addEventListener("resize", checkMobile);
});

const emit = defineEmits(["approve-success"]);

// 表单数据
const comment = ref("");
const is_sensitive = ref(false);
const loading = ref(false);
const cloud_platforms = ref<string[]>([]);
const service_duration = ref({
  hours: 0.5
});

// 添加计算属性，将天时分秒转换为秒
const calculateTotalSeconds = (): number => {
  const { hours } = service_duration.value;
  return (
    hours * 60 * 60 // 小时转秒
  );
};

// 提交审批
const handleApprove = async (status: OrderStatus) => {
  if (!comment.value.trim()) {
    if (status === OrderStatus.Completed) {
      comment.value = "同意";
    } else if (status === OrderStatus.Rejected) {
      comment.value = "驳回";
    } else {
      message("请输入审批意见", { type: "error" });
      // return;
    }
  }

  try {
    loading.value = true;
    const res = await ApproveAPI(props.orderId, {
      status: status,
      comment: comment.value,
      is_sensitive: is_sensitive.value,
      service_duration_seconds: calculateTotalSeconds() // 使用转换后的总秒数
    });

    if (res.success) {
      message("审批提交成功", { type: "success" });
      comment.value = ""; // 清空表单
      emit("approve-success"); // 通知父组件刷新数据
      window.location.href = "/workflow/approver/orders"; // 跳转到审批列表页
    } else {
      message(res.msg || "审批失败", { type: "error" });
    }
  } catch (error) {
    message("审批提交出错", { type: "error" });
    console.error(error);
  } finally {
    loading.value = false;
  }
};

// 处理驳回操作
const handleReject = async () => {
  try {
    await ElMessageBox.confirm("确定要驳回该申请吗？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    });
    await handleApprove(OrderStatus.Rejected);
  } catch {
    // 用户取消操作，不做处理
  }
};

// 添加撤回处理函数
const handleWithdraw = () => {
  addDialog({
    title: "撤回确认",
    width: 500,
    contentRenderer: () => h("p", "确认要撤回该工单吗？"),
    beforeSure: async done => {
      try {
        loading.value = true;
        const res = await cancelOrderAPI(props.orderId);
        if (res.success) {
          message("撤回成功", { type: "success" });
          emit("approve-success"); // 通知父组件刷新数据
          done(); // 关闭对话框
        } else {
          message(res.msg || "撤回失败", { type: "error" });
        }
      } catch (error) {
        message("撤回失败", { type: "error" });
        console.error(error);
      } finally {
        loading.value = false;
      }
    }
  });
};

const addOrTransferFrom = ref<AddOrTransferFrom>({
  approvers_ids: [],
  node_name: "",
  comment: ""
});

const childRef = ref(null);
// 临时实现加签处理函数
const handleAddSign = () => {
  addOrTransferFrom.value.node_name = "加签节点";
  addDialog({
    title: "加签确认",
    contentRenderer: () =>
      h(UserSelectDialog, {
        action: "add",
        form: addOrTransferFrom.value,
        ref: childRef
      }),
    beforeSure: done => {
      console.log(childRef.value, "childRef");
      if (childRef.value.ruleFormRef) {
        childRef.value.ruleFormRef
          .validate()
          .then(() => {
            addOrTransferProcessAPI(
              props.orderId,
              "add",
              addOrTransferFrom.value
            )
              .then(res => {
                if (res.success) {
                  message("加签成功", { type: "success" });
                  window.location.reload();
                  done();
                } else {
                  message(res.msg || "加签失败", { type: "error" });
                }
              })
              .catch(err => {
                message("加签失败:" + err, { type: "error" });
              });
          })
          .catch(err => {
            message("加签失败,请检查表单数据" + err, { type: "error" });
          });
      } else {
        message("请检查表单数据", { type: "error" });
      }
    }
  });
};

// 实现转签处理函数
const handleTransfer = () => {
  addOrTransferFrom.value.node_name = "转签节点";
  addDialog({
    title: "转签确认",
    contentRenderer: () =>
      h(UserSelectDialog, {
        action: "transfer",
        form: addOrTransferFrom.value,
        ref: childRef
      }),
    beforeSure: done => {
      if (childRef.value.ruleFormRef) {
        childRef.value.ruleFormRef
          .validate()
          .then(() => {
            addOrTransferProcessAPI(
              props.orderId,
              "transfer",
              addOrTransferFrom.value
            )
              .then(res => {
                if (res.success) {
                  message("转签成功", { type: "success" });
                  window.location.reload();
                  done();
                } else {
                  message(res.msg || "转签失败", { type: "error" });
                }
              })
              .catch(err => {
                message("转签失败:" + err, { type: "error" });
              });
          })
          .catch(err => {
            message("转签失败,请检查表单数据" + err, { type: "error" });
          });
      }
    }
  });
};
</script>

<template>
  <div class="approval-form-container">
    <el-card v-if="canApprove" class="approval-card">
      <template v-slot:header>
        <div>审批</div>
      </template>
      <el-form label-position="top" :size="isMobile ? 'small' : 'large'">
        <div class="form-row" :class="{ 'mobile-row': isMobile }">
          <el-form-item v-if="props.is_last" label="耗时" class="duration-item">
            <div class="duration-inputs">
              <el-input-number
                v-model="service_duration.hours"
                :min="0"
                :step="0.1"
                placeholder="时"
                class="duration-input"
                controls-position="right"
                :size="isMobile ? 'small' : 'default'"
              >
              </el-input-number>
              <span>时</span>
            </div>
          </el-form-item>
          <el-form-item
            v-if="props.is_last"
            label="云平台"
            class="platform-item"
          >
            <el-select
              v-model="cloud_platforms"
              multiple
              clearable
              filterable
              :size="isMobile ? 'small' : 'large'"
              tag-type="primary"
              class="custom-select"
            >
              <el-option
                v-for="item in CloudPlatforms"
                :key="item"
                :label="item"
                :value="item"
              />
            </el-select>
          </el-form-item>
        </div>
        <el-form-item label="审批意见">
          <el-input
            v-model="comment"
            type="textarea"
            :rows="isMobile ? 4 : 6"
            placeholder="请输入审批意见"
            maxlength="5000"
            show-word-limit
            :disabled="loading"
          />
        </el-form-item>
        <el-form-item v-if="show_sensitive_edit" label="是否敏感信息">
          <el-switch v-model="is_sensitive" />
        </el-form-item>
        <el-form-item>
          <div class="action-buttons" :class="{ 'mobile-buttons': isMobile }">
            <el-button
              type="success"
              :loading="loading"
              :size="isMobile ? 'small' : 'default'"
              @click="handleApprove(OrderStatus.Completed)"
            >
              同意
            </el-button>
            <el-button 
              type="danger" 
              :loading="loading" 
              :size="isMobile ? 'small' : 'default'"
              @click="handleReject"
            >
              驳回
            </el-button>
            <el-button 
              type="primary" 
              :loading="loading" 
              :size="isMobile ? 'small' : 'default'"
              @click="handleAddSign"
            >
              加签
            </el-button>
            <el-button 
              type="warning" 
              :loading="loading" 
              :size="isMobile ? 'small' : 'default'"
              @click="handleTransfer"
            >
              转签
            </el-button>
          </div>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card v-if="canEdit" class="edit-card">
      <template v-slot:header>
        <div>编辑</div>
      </template>
      <div class="button-group">
        <el-button 
          type="danger" 
          :loading="loading" 
          :size="isMobile ? 'small' : 'default'"
          @click="handleWithdraw"
        >
          撤回
        </el-button>
      </div>
    </el-card>
  </div>
</template>

<style lang="scss" scoped>
.approval-form-container {
  width: 100%;
}

.approval-card,
.edit-card {
  margin-top: 10px;
  
  @media screen and (width <= 768px) {
    margin-top: 8px;
    
    :deep(.el-card__header) {
      padding: 12px 15px;
      font-size: 15px;
    }
    
    :deep(.el-card__body) {
      padding: 15px;
    }
  }
}

.form-row {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  
  &.mobile-row {
    flex-direction: column;
    gap: 10px;
  }
  
  .duration-item,
  .platform-item {
    margin-bottom: 0;
  }
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  
  @media screen and (width <= 768px) {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    width: 100%;
    
    .el-button {
      width: 100%;
      padding: 8px 0;
      margin: 0;
      font-size: 13px;
    }
  }
  
  &.mobile-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    width: 100%;
    
    .el-button {
      width: 100%;
      padding: 8px 0;
      margin: 0;
      font-size: 13px;
    }
  }
}

.button-group {
  display: flex;
  gap: 10px;
  
  @media screen and (width <= 768px) {
    display: grid;
    grid-template-columns: 1fr;
    gap: 8px;
    width: 100%;
    
    .el-button {
      width: 100%;
      padding: 8px 0;
      margin: 0;
      font-size: 13px;
    }
  }
}

.custom-select {
  width: 300px;
  
  @media screen and (width <= 768px) {
    width: 100%;
  }
}

.duration-inputs {
  display: flex;
  gap: 10px;
  align-items: center;
}

.duration-input {
  width: 120px;
  
  @media screen and (width <= 768px) {
    width: 100px;
  }
}

:deep(.el-input-number__suffix) {
  margin-left: 2px;
  color: var(--el-text-color-regular);
}

@media screen and (width <= 768px) {
  :deep(.el-form-item__label) {
    padding-bottom: 4px;
    font-size: 13px;
  }
  
  :deep(.el-form-item) {
    margin-bottom: 12px;
  }
  
  :deep(.el-card__header) {
    padding: 12px;
    font-size: 15px;
  }
  
  :deep(.el-card__body) {
    padding: 12px;
  }
}
</style>
