<script lang="ts" setup>
import { OrderStatus } from "@/config/order-enum";
import ProcessStatus from "./ProcessStatus.vue";
import { computed, ref, onMounted } from "vue";
import { ArrowDown } from "@element-plus/icons-vue";

const props = defineProps<{
  processes: any[];
}>();

const isMobile = ref(false);

// 检测是否为移动设备
const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768;
};

onMounted(() => {
  checkMobile();
  window.addEventListener("resize", checkMobile);
});

// 存储每个节点的展开状态
const expandedNodes = ref<{ [key: number]: boolean }>({});

// 初始化展开状态：当前审批中的节点自动展开
const initExpandedNodes = () => {
  props.processes.forEach((process, index) => {
    expandedNodes.value[index] = process.status === OrderStatus.Approving;
  });
};
initExpandedNodes();

// 切换节点展开状态
const toggleNode = (index: number) => {
  expandedNodes.value[index] = !expandedNodes.value[index];
};

const getAvatarText = computed(() => (name: string) => {
  return name ? name.charAt(0).toUpperCase() : "?";
});
</script>

<template>
  <el-card class="process-timeline-card">
    <template #header>
      <div class="card-header">
        <h2 class="title" :class="{ 'mobile-title': isMobile }">审批流程</h2>
      </div>
    </template>

    <el-timeline class="approval-timeline">
      <el-timeline-item
        v-for="(process, index) in processes"
        :key="index"
        :timestamp="process.node_name"
        placement="top"
        type="primary"
        :hollow="process.status === OrderStatus.Approving"
      >
        <el-card
          class="process-card"
          shadow="hover"
          :class="{ 'current-node': process.status === OrderStatus.Approving }"
        >
          <div class="process-header" @click="toggleNode(index)">
            <template
              v-if="
                (process.status > OrderStatus.Approving && index !== 0) ||
                (process.status <= OrderStatus.Approving &&
                  index !== 0 &&
                  process?.approvers?.length)
              "
            >
              <el-tooltip
                content="点击查看/隐藏详细信息"
                placement="top"
                :show-after="500"
              >
                <div class="header-main">
                  <div class="header-content">
                    <ProcessStatus :process="process" />
                    <!-- 已审批节点显示审批人 -->
                    <template v-if="index !== 0">
                      <!-- 已审批节点显示审批人 -->
                      <template v-if="process.status > OrderStatus.Approving">
                        <div class="approver-brief">
                          <div class="user-avatar-wrapper compact">
                            <div class="user-avatar primary">
                              {{ getAvatarText(process.approver?.name) }}
                            </div>
                            <span class="user-name">
                              {{ process.approver?.name }}
                            </span>
                          </div>
                        </div>
                      </template>
                      <!-- 审批中节点显示可审批人 -->
                      <template
                        v-else-if="
                          process.status === OrderStatus.Approving &&
                          process?.approvers?.length
                        "
                      >
                        <div class="approver-brief">
                          <div class="approvers-count warning">
                            {{ process.approvers.length }}人可审批
                          </div>
                        </div>
                      </template>
                    </template>
                  </div>
                  <div class="expand-hint">
                    <span class="hint-text">{{
                      expandedNodes[index] ? "收起" : "展开"
                    }}</span>
                    <el-icon
                      class="expand-icon"
                      :class="{ 'is-expanded': expandedNodes[index] }"
                    >
                      <component :is="ArrowDown" />
                    </el-icon>
                  </div>
                </div>
              </el-tooltip>
            </template>
            <template v-else>
              <div class="header-main">
                <div class="header-content">
                  <ProcessStatus :process="process" />
                  <!-- 已审批节点显示审批人 -->
                  <template v-if="index !== 0">
                    <!-- 已审批节点显示审批人 -->
                    <template v-if="process.status > OrderStatus.Approving">
                      <div class="approver-brief">
                        <div class="user-avatar-wrapper compact">
                          <div class="user-avatar primary">
                            {{ getAvatarText(process.approver?.name) }}
                          </div>
                          <span class="user-name">
                            {{ process.approver?.name }}
                          </span>
                        </div>
                      </div>
                    </template>
                    <!-- 审批中节点显示可审批人 -->
                    <template
                      v-else-if="
                        process.status === OrderStatus.Approving &&
                        process?.approvers?.length
                      "
                    >
                      <div class="approver-brief">
                        <div class="approvers-count warning">
                          {{ process.approvers.length }}人可审批
                        </div>
                      </div>
                    </template>
                  </template>
                </div>
                <el-icon
                  class="expand-icon"
                  :class="{ 'is-expanded': expandedNodes[index] }"
                >
                  <el-icon-arrow-down />
                </el-icon>
              </div>
            </template>
          </div>

          <!-- 审批信息区域 -->
          <div v-show="expandedNodes[index]" class="expandable-content">
            <template
              v-if="process.status > OrderStatus.Approving && index !== 0"
            >
              <el-divider />
              <div class="approval-info">
                <div class="info-row">
                  <span class="time-label">审批时间：</span>
                  <span class="time-value">
                    {{ new Date(process?.approve_time).toLocaleString() }}
                  </span>
                </div>

                <div class="approver-info">
                  <span class="approver-label">审批人详情：</span>
                  <div class="user-avatar-wrapper">
                    <div class="user-avatar primary">
                      {{ getAvatarText(process.approver?.name) }}
                    </div>
                    <span class="user-name">
                      {{ process.approver?.name }}
                      <span class="username">
                        ({{ process.approver?.username }})
                      </span>
                    </span>
                  </div>
                </div>

                <div class="comment-section">
                  <div class="comment-header">审批意见</div>
                  <div class="comment-content" style="white-space: pre-wrap">
                    {{ process?.comment || "无" }}
                  </div>
                </div>

                <!-- 当时的可审批人列表 -->
                <template v-if="process?.approvers?.length">
                  <div class="approvers-info history">
                    <div class="info-label">当时的可审批人：</div>
                    <div class="approvers-list">
                      <div
                        v-for="approver in process.approvers"
                        :key="approver?.id"
                        class="user-avatar-wrapper"
                      >
                        <div class="user-avatar info">
                          {{ getAvatarText(approver?.name) }}
                        </div>
                        <span class="user-name">
                          {{ approver?.name }}
                          <span class="username">
                            ({{ approver?.username }})
                          </span>
                        </span>
                      </div>
                    </div>
                  </div>
                </template>
              </div>
            </template>
          </div>

          <!-- 申请人/审批人信息 -->
          <div class="user-info">
            <!-- 申请节点显示申请人 -->
            <template v-if="index === 0">
              <div class="info-label">申请人：</div>
              <div class="user-avatar-wrapper">
                <div class="user-avatar success">
                  {{ getAvatarText(process?.approver?.name) }}
                </div>
                <span class="user-name">
                  {{ process?.approver?.name }}
                  <span class="username">
                    ({{ process?.approver?.username }})
                  </span>
                </span>
              </div>
              <div class="comment-section">
                <div class="comment-header">发起人意见</div>
                <div class="comment-content" style="white-space: pre-wrap">
                  {{ process?.comment || "无" }}
                </div>
              </div>
            </template>
            <!-- 非申请节点，且在审批中或未审批时显示可审批人列表 -->
            <template v-else-if="process.status <= OrderStatus.Approving">
              <div v-show="expandedNodes[index]" class="approvers-info">
                <div class="info-label">可审批人：</div>
                <div class="approvers-list">
                  <div
                    v-for="approver in process?.approvers"
                    :key="approver?.id"
                    class="user-avatar-wrapper"
                  >
                    <div class="user-avatar warning">
                      {{ getAvatarText(approver?.name) }}
                    </div>
                    <span class="user-name primary">
                      {{ approver?.name }}
                      <span class="username"> ({{ approver?.username }}) </span>
                    </span>
                  </div>
                </div>
              </div>
            </template>
          </div>

          <!-- 抄送人信息 -->
          <div v-if="process?.ccs?.length" class="cc-info">
            <div class="info-label">抄送：</div>
            <div class="cc-list">
              <div
                v-for="cc in process.ccs"
                :key="cc.id"
                class="user-avatar-wrapper"
              >
                <div class="user-avatar warning">
                  {{ getAvatarText(cc.name) }}
                </div>
                <span class="user-name">
                  {{ cc.name }}
                  <span class="username"> ({{ cc.username }}) </span>
                </span>
              </div>
            </div>
          </div>
        </el-card>
      </el-timeline-item>
    </el-timeline>
  </el-card>
</template>

<style lang="scss" scoped>


/* Mobile-specific styles */
@media screen and (width <= 768px) {
  :deep(.el-timeline-item__node) {
    width: 10px !important;
    height: 10px !important;
  }
  
  :deep(.el-timeline-item__wrapper) {
    padding-left: 18px !important;
  }
  
  :deep(.el-timeline-item__timestamp) {
    margin-bottom: 4px !important;
    font-size: 12px !important;
  }
  
  :deep(.el-timeline-item__tail) {
    left: 4px !important;
  }
  
  :deep(.el-tag) {
    padding: 0 4px !important;
    font-size: 11px !important;
  }
  
  .approvers-count {
    font-size: 12px !important;
  }
  
  .user-avatar-wrapper {
    margin-right: 6px !important;
    font-size: 12px !important;
    
    .user-avatar {
      width: 24px !important;
      height: 24px !important;
      font-size: 12px !important;
    }
  }
  
  .comment-content {
    padding: 8px !important;
    font-size: 12px !important;
  }
  
  .header-main {
    padding: 2px 4px !important;
  }
  
  .process-header {
    margin-bottom: 4px !important;
  }
  
  .expand-icon {
    font-size: 14px !important;
  }
}

.process-timeline-card {
  .card-header {
    .title {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
      color: var(--el-text-color-primary);
      
      &.mobile-title {
        font-size: 14px;
      }
    }
  }

  .approval-timeline {
    padding: 16px;
    
    @media screen and (width <= 768px) {
      padding: 8px;
    }
  }

  .process-card {
    border: 1px solid var(--el-border-color-lighter);
    transition: all 0.3s ease;
    
    @media screen and (width <= 768px) {
      margin-bottom: 4px;
    }

    &.current-node {
      border-color: var(--el-color-primary);
      box-shadow: 0 0 8px rgb(var(--el-color-primary-rgb) 0.2);
    }

    .process-header {
      margin-bottom: 6px;
      cursor: pointer;

      &:hover {
        .header-main {
          background-color: var(--el-fill-color-light);
        }
      }

      .header-main {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 4px 8px;
        border-radius: 4px;
        transition: background-color 0.3s ease;

        .header-content {
          display: flex;
          gap: 12px;
          align-items: center;
          
          @media screen and (width <= 768px) {
            gap: 8px;
          }
        }
      }

      .approver-brief {
        .user-avatar-wrapper.compact {
          margin: 0;
          font-size: 12px;

          .user-avatar {
            width: 24px;
            height: 24px;
            font-size: 12px;
          }
        }

        .approvers-count {
          padding: 2px 8px;
          font-size: 12px;
          color: var(--el-color-warning);
          background-color: var(--el-color-warning-light-9);
          border: 1px solid var(--el-color-warning-light-7);
          border-radius: 12px;
        }
      }

      .expand-icon {
        margin-left: 8px;
        font-size: 16px;
        color: var(--el-text-color-secondary);
        transition: transform 0.3s ease;

        &.is-expanded {
          transform: rotate(180deg);
        }
      }
    }

    .expandable-content {
      transition: all 0.3s ease;
      
      @media screen and (width <= 768px) {
        padding: 8px;
        font-size: 12px;
      }
    }
  }

  .approval-info {
    .info-row {
      margin-bottom: 8px;
      font-size: 13px;

      .time-label {
        color: var(--el-text-color-secondary);
      }

      .time-value {
        color: var(--el-text-color-primary);
      }
    }
  }

  .comment-section {
    margin-top: 12px;

    .comment-header {
      margin-bottom: 8px;
      font-size: 13px;
      font-weight: 500;
      color: var(--el-text-color-primary);
    }

    .comment-content {
      padding: 12px;
      font-size: 13px;
      line-height: 1.5;
      color: var(--el-text-color-regular);
      border: 1px solid var(--el-border-color-lighter);
      border-radius: 4px;
    }
  }

  .user-avatar-wrapper {
    display: inline-flex;
    align-items: center;
    margin-right: 12px;
    margin-bottom: 3px;

    .user-avatar {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 28px;
      height: 28px;
      margin-right: 3px;
      font-size: 14px;
      font-weight: 500;
      color: #fff;
      border-radius: 50%;

      &.primary {
        background-color: var(--el-color-primary);
      }

      &.success {
        background-color: var(--el-color-success);
      }

      &.info {
        background-color: var(--el-color-info);
      }

      &.warning {
        background-color: var(--el-color-warning);
      }
    }

    .user-name {
      font-size: 13px;
      color: var(--el-text-color-primary);

      .username {
        color: var(--el-text-color-secondary);
      }
    }
  }

  .info-label {
    margin-bottom: 8px;
    font-size: 13px;
    font-weight: 500;
    color: var(--el-text-color-primary);
  }

  .approvers-info {
    padding: 12px;
    margin-top: 8px;
    background-color: var(--el-fill-color-light);
    border-radius: 4px;

    &.history {
      margin-top: 16px;
      background-color: var(--el-fill-color);
      border: 1px dashed var(--el-border-color);
    }
  }

  .approvers-list,
  .cc-list {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    
    @media screen and (width <= 768px) {
      gap: 2px;
    }
  }

  .cc-info {
    padding-top: 8px;
    margin-top: 8px;
    border-top: 1px solid var(--el-border-color-lighter);
    
    @media screen and (width <= 768px) {
      padding-top: 4px;
      font-size: 12px;
    }
  }
}
</style>
