<script lang="ts" setup>
import { OrderStatus, OrderStatuses } from "@/config/order-enum";

defineProps<{
  process: any;
}>();

const getStatusType = (status: number) => {
  switch (status) {
    case OrderStatus.Completed:
      return "success";
    case OrderStatus.Rejected:
      return "danger";
    case OrderStatus.Approving:
      return "warning";
    default:
      return "info";
  }
};
</script>

<template>
  <el-text :type="getStatusType(process.status)">
    {{ process.status === 0 ? "待审批" : OrderStatuses.get(process.status) }}
  </el-text>
</template>
