<template>
  <div
    class="service-eval"
    :class="{ 'has-evaluation': hasExistingEvaluation }"
  >
    <div class="eval-header">
      <el-icon class="header-icon"><Star /></el-icon>
      <span>服务评价</span>
    </div>
    <div v-if="hasExistingEvaluation || canEval" class="eval-content">
      <div class="eval-main-row">
        <!-- 左侧区域：评分选项和按钮 -->
        <div class="rating-column">
          <!-- 问题和评价时间 -->
          <div class="eval-question">
            <span>本次工单服务如何？</span>
            <span v-if="hasExistingEvaluation" class="existing-evaluation-label">
              (已评价于 {{ evaluationTime }})
            </span>
          </div>
          
          <!-- 评分选项 -->
          <div class="rating-options">
            <div
              v-for="option in ratingOptions"
              :key="option.value"
              class="rating-card"
              :class="{
                'is-selected': evaluation === option.value,
                'is-disabled': hasExistingEvaluation
              }"
              @click="!hasExistingEvaluation && (evaluation = option.value)"
            >
              <div class="rating-icon-wrapper">
                <el-icon class="rating-icon" :class="option.colorClass">
                  <component :is="option.icon" />
                </el-icon>
              </div>
              <div class="rating-label">{{ option.label }}</div>
            </div>
          </div>
          
          <!-- 操作按钮 -->
          <div v-if="!hasExistingEvaluation && canEval" class="action-buttons">
            <el-button plain size="small" @click="resetForm">重置</el-button>
            <el-button
              type="primary"
              size="small"
              :disabled="!isFormValid"
              :loading="submitting"
              @click="submit"
            >
              提交评价
            </el-button>
          </div>
        </div>
        
        <!-- 右侧区域：评价详情 -->
        <transition name="slide-fade">
          <div v-if="evaluation === '不满意'" class="comments-section">
            <div class="comments-label">评价详情：</div>
            <el-input
              v-model="comment"
              type="textarea"
              placeholder="请输入您的评价和建议，帮助我们提升服务质量"
              :rows="3"
              maxlength="2000"
              show-word-limit
              class="comments-input"
              :disabled="hasExistingEvaluation"
            ></el-input>
          </div>
        </transition>
      </div>

      <transition name="fade">
        <div v-if="showSuccessMessage" class="success-message">
          <el-icon class="success-icon"><CircleCheckFilled /></el-icon>
          <span>感谢您的评价！</span>
        </div>
      </transition>
    </div>
    <div v-else-if="!hasExistingEvaluation" class="eval-content">
      <span>申请人未评价</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, markRaw } from "vue";
import {
  Star,
  CircleCheckFilled,
  CircleCheck,
  Warning
} from "@element-plus/icons-vue";
import {
  saveEvaluationAPI,
  type EvaluationData
} from "@/api/workflow/order-detail";
import { message } from "@/utils/message";
// 评价选项配置
const ratingOptions = [
  {
    value: "非常满意",
    label: "非常满意",
    icon: markRaw(CircleCheckFilled),
    colorClass: "rating-good"
  },
  {
    value: "满意",
    label: "满意",
    icon: markRaw(CircleCheck),
    colorClass: "rating-neutral"
  },
  {
    value: "不满意",
    label: "不满意",
    icon: markRaw(Warning),
    colorClass: "rating-bad"
  }
];

const props = defineProps<{
  sn: string;
  exist: EvaluationData | null;
  canEval: boolean;
}>();

const emit = defineEmits<{
  (e: "refresh"): void;
}>();

// 评价：非常满意，满意，不满意
const evaluation = ref(props.exist ? props.exist.evaluation : "非常满意");
// 评价内容
const comment = ref(props.exist ? props.exist.comment : "");
// 提交状态
const submitting = ref(false);
// 成功消息显示状态
const showSuccessMessage = ref(false);

// 表单验证
const isFormValid = computed(() => {
  if (evaluation.value === "不满意" && !comment.value?.trim()) {
    return false;
  }
  return true;
});

// 是否已经提交过评价
const hasExistingEvaluation = computed(
  () => !!props.exist && props.exist.id > 0
);

// 评价时间
const evaluationTime = computed(() => {
  if (props.exist && props.exist.created_at) {
    return new Date(props.exist.created_at).toLocaleString();
  }
  return "";
});

// 提交评价
const submit = async () => {
  if (!isFormValid.value) return;

  submitting.value = true;
  try {
    // 调用API提交评价
    const res = await saveEvaluationAPI({
      sn: props.sn,
      evaluation: evaluation.value,
      comment: comment.value
    });
    if (res.success) {
      // 显示成功消息
      showSuccessMessage.value = true;
      setTimeout(() => {
        showSuccessMessage.value = false;
        // 通知父组件刷新数据
        emit("refresh");
      }, 1000);
    } else {
      message(res.msg, { type: "error" });
    }
  } catch (error) {
    console.error("提交评价失败:", error);
  } finally {
    submitting.value = false;
  }
};

// 重置表单
const resetForm = () => {
  if (props.exist) {
    // 如果存在评价，重置为原始评价
    evaluation.value = props.exist.evaluation;
    comment.value = props.exist.comment;
  } else {
    // 如果不存在评价，重置为默认值
    evaluation.value = "非常满意";
    comment.value = "";
  }
};
</script>
<style lang="scss" scoped>
@keyframes pulse {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.1);
  }

  100% {
    transform: scale(1);
  }
}

.service-eval {
  display: flex;
  flex-direction: column;
  gap: 4px;
  width: 100%;
  padding: 8px;
  margin: 0;
  background-color: #fff;
  border-radius: 6px;
  box-shadow: 0 1px 4px rgb(0 0 0 / 5%);

  .eval-header {
    display: flex;
    gap: 4px;
    align-items: center;
    padding-bottom: 6px;
    margin-bottom: 6px;
    font-size: 14px;
    font-weight: 600;
    color: #303133;
    border-bottom: 1px solid #ebeef5;

    .header-icon {
      font-size: 16px;
      color: #409eff;
    }
  }

  .eval-content {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 0;
  }

  .eval-question {
    display: flex;
    flex-wrap: wrap;
    gap: 3px;
    align-items: center;
    justify-content: flex-start;
    margin-bottom: 3px;
    font-size: 13px;
    font-weight: 500;
    color: #303133;
    text-align: left;

    .existing-evaluation-label {
      font-size: 12px;
      font-weight: normal;
      color: #909399;
    }
  }

  .rating-options-container {
    width: 100%;
    margin: 0 auto;
  }

  .rating-options {
    display: flex;
    flex-wrap: nowrap;
    gap: 4px;
    justify-content: flex-start;
    width: 100%;
    padding: 2px 0;

    @media screen and (width <= 768px) {
      gap: 8px;
    }
  }

  .rating-card {
    position: relative;
    display: inline-flex;
    flex-direction: row;
    gap: 4px;
    align-items: center;
    min-width: 55px;
    padding: 3px 5px;
    overflow: hidden;
    cursor: pointer;
    background-color: #f9f9f9;
    border: 1px solid transparent;
    border-radius: 4px;
    box-shadow: 0 1px 2px rgb(0 0 0 / 5%);
    transition: all 0.2s cubic-bezier(0.25, 0.8, 0.25, 1);

    @media screen and (width <= 768px) {
      min-width: 55px;
      padding: 3px 5px;
    }

    @media screen and (width <= 480px) {
      gap: 3px;
      min-width: 50px;
      padding: 2px 4px;
    }

    &.is-disabled {
      pointer-events: none;
      cursor: not-allowed;
      opacity: 0.8;
    }

    &:hover {
      box-shadow: 0 2px 8px rgb(0 0 0 / 8%);
      transform: translateY(-2px);
    }

    &.is-selected {
      background-color: #ecf5ff;
      border-color: #409eff;
      box-shadow: 0 2px 8px rgb(64 158 255 / 15%);
      transform: translateY(-2px);

      &::after {
        position: absolute;
        top: 0;
        right: 0;
        content: "";
        border-color: transparent #409eff transparent transparent;
        border-style: solid;
        border-width: 0 16px 16px 0;
      }

      .rating-icon-wrapper {
        transform: scale(1.1);
      }
    }
  }

  .rating-icon-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 22px;
    height: 22px;
    background-color: #fff;
    border-radius: 50%;
    box-shadow: 0 1px 2px rgb(0 0 0 / 6%);
    transition: all 0.2s;

    @media screen and (width <= 768px) {
      width: 20px;
      height: 20px;
    }

    @media screen and (width <= 480px) {
      width: 18px;
      height: 18px;
    }
  }

  .rating-label {
    font-size: 12px;
    font-weight: 500;
    color: #303133;
  }

  .rating-icon {
    width: 1em;
    height: 1em;
    font-size: 14px;

    @media screen and (width <= 768px) {
      font-size: 12px;
    }

    @media screen and (width <= 480px) {
      font-size: 10px;
    }
  }

  .rating-good {
    color: #67c23a;
  }

  .rating-neutral {
    color: #e6a23c;
  }

  .rating-bad {
    color: #f56c6c;
  }

  .eval-main-row {
    display: flex;
    flex-wrap: nowrap;
    gap: 8px;
    align-items: flex-start;
    width: 100%;
    
    @media screen and (width <= 768px) {
      flex-direction: column;
      gap: 8px;
    }
  }
  
  .rating-column {
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    gap: 4px;
    width: auto;
    min-width: 180px;
  }

  .comments-section {
    display: flex;
    flex: 1;
    flex-direction: column;
    gap: 3px;
    min-width: 180px;
    max-width: 100%;
    padding: 6px;
    overflow: hidden;
    background-color: #f9f9f9;
    border-left: 2px solid #f56c6c;
    border-radius: 3px;
  }

  .comments-label {
    font-size: 12px;
    font-weight: 500;
    color: #303133;
  }

  .comments-input {
    margin-top: 2px;

    :deep(.el-textarea__inner) {
      padding: 6px;
      font-size: 12px;
      border: 1px solid #dcdfe6;
      border-radius: 3px;
      transition: all 0.2s;

      &:focus {
        border-color: #409eff;
        box-shadow: 0 0 0 2px rgb(64 158 255 / 20%);
      }
    }
  }

  .action-buttons {
    display: flex;
    gap: 4px;
    justify-content: flex-start;

    @media screen and (width <= 480px) {
      flex-direction: column;
      gap: 8px;
      width: 100%;
    }

    .el-button {
      padding: 4px 8px;
      font-size: 12px;
      border-radius: 3px;
      transition: all 0.2s;

      @media screen and (width <= 480px) {
        width: 100%;
        padding: 3px 8px;
      }

      &:hover {
        opacity: 0.9;
        transform: translateY(-1px);
      }
    }
  }

  .success-message {
    display: flex;
    gap: 4px;
    align-items: center;
    justify-content: center;
    padding: 6px;
    margin-top: 6px;
    font-size: 13px;
    color: #67c23a;
    background-color: #f0f9eb;
    border-radius: 4px;
    box-shadow: 0 1px 2px rgb(103 194 58 / 10%);

    .success-icon {
      margin-right: 2px;
      font-size: 16px;
      color: inherit;
    }
  }

  &.has-evaluation {
    position: relative;
    border-left: 4px solid #409eff;

    &::after {
      position: absolute;
      top: 0;
      right: 0;
      width: 0;
      height: 0;
      content: "";
      border-color: transparent #409eff transparent transparent;
      border-style: solid;
      border-width: 0 24px 24px 0;
    }
  }
}

// 动画效果
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-fade-enter-active {
  transition: all 0.3s ease-out;
}

.slide-fade-leave-active {
  transition: all 0.3s cubic-bezier(1, 0.5, 0.8, 1);
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}
</style>
