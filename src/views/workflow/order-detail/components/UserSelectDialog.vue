<template>
  <el-form
    ref="ruleFormRef"
    :model="form"
    label-width="100px"
    class="user-select-form"
    @submit.prevent
  >
    <el-form-item
      v-if="props.action === 'add'"
      label="节点名称"
      prop="node_name"
      :rules="[
        { required: true, message: '节点名称不能为空', trigger: 'blur' },
        { max: 255, message: '节点名称不能超过255个字符', trigger: 'blur' }
      ]"
    >
      <el-input
        v-model="form.node_name"
        placeholder="请输入节点名称"
        maxlength="255"
        show-word-limit
        clearable
        class="input-field"
      />
    </el-form-item>
    <el-form-item
      v-if="props.action === 'transfer'"
      prop="comment"
      label="转签信息"
      :rules="[
        { required: true, message: '转签信息不能为空', trigger: 'blur' },
        { max: 255, message: '转签信息不能超过255个字符', trigger: 'blur' }
      ]"
    >
      <el-input
        v-model="form.comment"
        placeholder="请输入转签信息"
        clearable
        class="input-field"
        show-word-limit
        type="textarea"
        maxlength="255"
      />
    </el-form-item>
    <el-form-item
      label="审批人"
      prop="approvers_ids"
      :rules="[{ required: true, message: '审批人不能为空', trigger: 'blur' }]"
    >
      <el-select
        v-model="form.approvers_ids"
        multiple
        placeholder="请选择审批人"
        clearable
        filterable
        :loading="loading"
        size="large"
        tag-type="warning"
        class="w-full"
      >
        <el-option
          v-for="user in users"
          :key="user.id"
          :label="user.name + ' (' + user.username + ')'"
          :value="user.id"
        >
          <div class="flex items-center user-option">
            <el-avatar :size="28" class="user-avatar">
              {{ user.name.charAt(0) }}
            </el-avatar>
            <div class="user-info">
              <div class="user-name">
                {{ user.name }} （{{ user.username }}）
              </div>
            </div>
          </div>
        </el-option>
      </el-select>
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
import { ref, defineProps, onMounted } from "vue";
import { getAllUsersAPI, type User } from "@/api/auth/user";
import { message } from "@/utils/message";
import type { AddOrTransferFrom } from "@/api/workflow/approver";
import type { FormInstance } from "element-plus";

const props = defineProps<{
  action: "add" | "transfer";
  form: AddOrTransferFrom;
}>();

const ruleFormRef = ref<FormInstance>();
const loading = ref(false);
const users = ref([]);
const form = props.form;

defineExpose({ ruleFormRef });

const getAllUsers = async () => {
  loading.value = true;
  try {
    const res = await getAllUsersAPI();
    if (res.success) {
      users.value = res.data as User[];
    } else {
      message(res.msg, { type: "error" });
    }
  } finally {
    loading.value = false;
  }
};

onMounted(async () => {
  if (props.action === "add") {
    form.comment = "加签";
  } else if (props.action === "transfer") {
    form.comment = "转签";
  }
  getAllUsers();
});
</script>

<style scoped>
.user-select-form {
  padding: 24px 0;
  font-family:
    system-ui,
    -apple-system,
    "Segoe UI",
    sans-serif;
}

.input-field {
  padding: 8px;
  transition: all 0.3s ease;
}

:deep(.el-select) {
  width: 100%;
}

.w-full {
  width: 100%;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.user-option {
  margin-bottom: 10px;
}

.user-avatar {
  font-weight: 500;
  background: var(--el-color-primary);
}

.user-info {
  display: flex;
  flex-direction: column;
}

.user-name {
  color: var(--el-text-color-primary);
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: var(--el-text-color-regular);
}

:deep(.el-input__wrapper),
:deep(.el-textarea__inner) {
  box-shadow: 0 0 0 1px var(--el-border-color) !important;
  transition: all 0.3s ease;
}

:deep(.el-input__wrapper:hover),
:deep(.el-textarea__inner:hover) {
  box-shadow: 0 0 0 1px var(--el-color-primary) !important;
}

:deep(.el-input__wrapper:focus-within),
:deep(.el-textarea__inner:focus-within) {
  box-shadow: 0 0 0 1px var(--el-color-primary) !important;
}

:deep(.el-select__tags) {
  padding-top: 4px;
  padding-bottom: 4px;
}

.el-dialog__header {
  background-color: #f5f7fa;
  border-bottom: 1px solid #ebeef5;
}

.el-dialog__title {
  font-weight: bold;
  color: #303133;
}
</style>
