<script lang="ts" setup>
import { ref } from "vue";
import { ElMessage } from "element-plus";
import { addCommentAPI, type OrderComment } from "@/api/workflow/comment";
import { ChatDotRound } from "@element-plus/icons-vue";

defineOptions({
  name: "OrderComment"
});

// 定义评论项接口

interface Props {
  comments?: OrderComment[];
  sn: string;
}

const props = withDefaults(defineProps<Props>(), {
  comments: () => []
});

const emit = defineEmits(["refresh"]);

const commentContent = ref("");
const submitting = ref(false);

// 格式化时间显示 - 简洁版
const formatTime = (dateStr: Date | string) => {
  if (!dateStr) return "";
  
  const date = new Date(dateStr instanceof Date ? dateStr.getTime() : dateStr);
  if (isNaN(date.getTime())) return typeof dateStr === 'string' ? dateStr : '';
  
  const now = new Date();
  
  // 检查是否是今天
  const isToday = date.getDate() === now.getDate() && 
                 date.getMonth() === now.getMonth() && 
                 date.getFullYear() === now.getFullYear();
  
  if (isToday) {
    return `今天 ${date.getHours().toString().padStart(2, "0")}:${date.getMinutes().toString().padStart(2, "0")}`;
  }
  
  // 检查是否是昨天
  const yesterday = new Date(now);
  yesterday.setDate(now.getDate() - 1);
  const isYesterday = date.getDate() === yesterday.getDate() && 
                     date.getMonth() === yesterday.getMonth() && 
                     date.getFullYear() === yesterday.getFullYear();
  
  if (isYesterday) {
    return `昨天 ${date.getHours().toString().padStart(2, "0")}:${date.getMinutes().toString().padStart(2, "0")}`;
  }
  
  // 检查是否在一周内
  const oneWeekAgo = new Date(now);
  oneWeekAgo.setDate(now.getDate() - 7);
  
  if (date > oneWeekAgo) {
    const days = ["日", "一", "二", "三", "四", "五", "六"];
    return `周${days[date.getDay()]} ${date.getHours().toString().padStart(2, "0")}:${date.getMinutes().toString().padStart(2, "0")}`;
  }
  
  // 超过一周
  return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, "0")}-${date.getDate().toString().padStart(2, "0")} ${date.getHours().toString().padStart(2, "0")}:${date.getMinutes().toString().padStart(2, "0")}`;
};

// 格式化完整时间显示 - 用于悬停提示
const formatFullTime = (dateStr: Date | string): string => {
  if (!dateStr) return "";
  
  const date = new Date(dateStr instanceof Date ? dateStr.getTime() : dateStr);
  if (isNaN(date.getTime())) return typeof dateStr === 'string' ? dateStr : '';
  
  return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, "0")}-${date.getDate().toString().padStart(2, "0")} ${date.getHours().toString().padStart(2, "0")}:${date.getMinutes().toString().padStart(2, "0")}:${date.getSeconds().toString().padStart(2, "0")}`;
};

// 提交评论
const submitComment = async () => {
  if (!commentContent.value.trim()) {
    ElMessage.warning("评论内容不能为空");
    return;
  }

  try {
    submitting.value = true;
    // 调用评论接口
    const res = await addCommentAPI({
      sn: props.sn,
      content: commentContent.value
    });

    if (res.success) {
      ElMessage.success("评论提交成功");
      commentContent.value = "";
      emit("refresh"); // 刷新评论列表
    } else {
      ElMessage.error(res.msg || "评论提交失败");
    }
  } catch (error) {
    ElMessage.error(`评论提交失败: ${error}`);
  } finally {
    submitting.value = false;
  }
};
</script>

<template>
  <el-card class="comment-container" shadow="hover">
    <template #header>
      <div class="comment-header">
        <el-icon class="header-icon"><ChatDotRound /></el-icon>
        <span class="title">工单评论</span>
        <span v-if="props.comments.length > 0" class="count"
          >({{ props.comments.length }})</span
        >
      </div>
    </template>

    <!-- 评论列表 -->
    <div
      v-if="props.comments && props.comments.length > 0"
      class="comment-list"
    >
      <div
        v-for="comment in props.comments"
        :key="comment.id"
        class="comment-item"
      >
        <div class="comment-content">
          <div class="comment-info">
            <span class="username"
              >{{ comment.user.name }}({{ comment.user.username }})</span
            >
            <el-tooltip 
              :content="formatFullTime(comment.created_at)" 
              placement="top" 
              effect="light"
            >
              <span class="time">{{ formatTime(comment.created_at) }}</span>
            </el-tooltip>
          </div>
          <div class="comment-text">{{ comment.content }}</div>
        </div>
      </div>
    </div>

    <!-- 无评论时显示 -->
    <el-empty v-else description="暂无评论" :image-size="80" />

    <!-- 评论输入框 -->
    <div class="comment-form">
      <el-input
        v-model="commentContent"
        type="textarea"
        :rows="3"
        placeholder="请输入您的评论..."
        resize="none"
      />
      <div class="form-actions">
        <el-button type="primary" :loading="submitting" @click="submitComment">
          提交评论
        </el-button>
      </div>
    </div>
  </el-card>
</template>

<style lang="scss" scoped>
.comment-container {
  margin-top: 12px;
  border-radius: 6px;
  box-shadow: 0 1px 4px rgb(0 0 0 / 5%);
  transition: all 0.2s ease;

  &:hover {
    box-shadow: 0 2px 8px rgb(0 0 0 / 8%);
  }

  .comment-header {
    display: flex;
    align-items: center;
    font-size: 15px;
    font-weight: bold;
    
    .header-icon {
      margin-right: 6px;
      font-size: 16px;
      color: #409eff;
    }

    .count {
      margin-left: 8px;
      font-size: 13px;
      font-weight: normal;
      color: var(--el-text-color-secondary);
    }
  }

  .comment-list {
    max-height: 350px;
    padding: 0 2px;
    margin-bottom: 12px;
    overflow-y: auto;

    .comment-item {
      display: flex;
      padding: 10px 0;
      border-bottom: 1px dashed var(--el-border-color-lighter);
      
      &:hover {
        background-color: rgb(64 158 255 / 3%);
      }

      &:last-child {
        border-bottom: none;
      }

      .comment-content {
        flex: 1;

        .comment-info {
          display: flex;
          align-items: center;
          margin-bottom: 6px;

          .username {
            font-size: 13px;
            font-weight: 500;
            color: #409eff;
          }

          .time {
            padding: 1px 6px;
            margin-left: 10px;
            font-size: 12px;
            color: var(--el-text-color-secondary);
            background-color: #f5f7fa;
            border-radius: 10px;
          }
        }

        .comment-text {
          padding-left: 2px;
          font-size: 13px;
          line-height: 1.5;
          color: var(--el-text-color-regular);
          word-break: break-word;
        }
      }
    }
  }

  .comment-form {
    padding-top: 12px;
    margin-top: 12px;
    border-top: 1px solid var(--el-border-color-lighter);
    
    :deep(.el-textarea__inner) {
      font-size: 13px;
      border-radius: 4px;
      transition: all 0.2s;
      
      &:focus {
        box-shadow: 0 0 0 2px rgb(64 158 255 / 10%);
      }
    }

    .form-actions {
      display: flex;
      justify-content: flex-end;
      margin-top: 8px;
      
      .el-button {
        padding: 6px 12px;
        font-size: 13px;
        border-radius: 4px;
        transition: all 0.2s;
      }
    }
  }
}
</style>
