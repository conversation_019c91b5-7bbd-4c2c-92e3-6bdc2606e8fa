.order-detail-container {
  padding: 24px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 5%);

  .details-card {
    .el-descriptions__title {
      position: relative;
      padding-left: 12px;
      margin-bottom: 24px;
      font-size: 20px;
      font-weight: 600;
      color: var(--el-text-color-primary);

      &::before {
        position: absolute;
        top: 50%;
        left: 0;
        width: 4px;
        height: 16px;
        content: "";
        background: var(--el-color-primary);
        border-radius: 2px;
        transform: translateY(-50%);
      }

      &:hover::before {
        height: 20px;
        background: var(--el-color-primary-light-3);
        transition: all 0.3s ease;
      }
    }

    .detail-item {
      .detail-text {
        display: flex;
        gap: 8px;
        align-items: center;
        font-size: 14px;
        line-height: 1.6;
        color: var(--el-text-color-regular);

        :deep(.el-avatar) {
          flex-shrink: 0;
          font-size: 14px;
          font-weight: 500;
          color: #1677ff !important;
          background: #e6f4ff !important;
          border: 1px solid var(--el-border-color-lighter);

          &[data-letter="A"],
          &[data-letter="J"],
          &[data-letter="S"] {
            color: #1677ff !important;
            background: #e6f4ff !important;
          }

          &[data-letter="B"],
          &[data-letter="K"],
          &[data-letter="T"] {
            color: #52c41a !important;
            background: #f6ffed !important;
          }

          &[data-letter="C"],
          &[data-letter="L"],
          &[data-letter="U"] {
            color: #fa8c16 !important;
            background: #fff7e6 !important;
          }

          &[data-letter="D"],
          &[data-letter="M"],
          &[data-letter="V"] {
            color: #eb2f96 !important;
            background: #fff0f6 !important;
          }

          &[data-letter="E"],
          &[data-letter="N"],
          &[data-letter="W"] {
            color: #722ed1 !important;
            background: #f9f0ff !important;
          }

          &[data-letter="F"],
          &[data-letter="O"],
          &[data-letter="X"] {
            color: #a0d911 !important;
            background: #fcffe6 !important;
          }

          &[data-letter="G"],
          &[data-letter="P"],
          &[data-letter="Y"] {
            color: #fa541c !important;
            background: #fff2e8 !important;
          }

          &[data-letter="H"],
          &[data-letter="Q"],
          &[data-letter="Z"] {
            color: #13c2c2 !important;
            background: #e6fffb !important;
          }

          &[data-letter="I"],
          &[data-letter="R"] {
            color: #f5222d !important;
            background: #fff1f0 !important;
          }

          &:hover {
            transition: transform 0.3s ease;
            transform: scale(1.1);
          }
        }

        .el-tag {
          margin-left: 4px;
        }
      }

      .status-text {
        display: inline-flex;
        gap: 6px;
        align-items: center;
        padding: 4px 8px;
        background: var(--el-fill-color-light);
        border-radius: 4px;

        .el-icon {
          font-size: 16px;
        }
      }

      .time-text {
        padding: 4px 8px;
        font-family: Monaco, monospace;
        font-size: 13px;
        color: var(--el-text-color-secondary);
        letter-spacing: 0.5px;
        background: var(--el-fill-color-light);
        border-radius: 4px;
      }

      b {
        font-weight: 600;
        color: var(--el-color-warning);
      }

      .el-link.detail-text {
        display: inline-flex;
        gap: 4px;
        align-items: center;

        .el-icon {
          font-size: 14px;
        }

        &:hover {
          color: var(--el-color-primary-light-3);
          text-decoration: none;
        }
      }

      .el-tag {
        &.el-tag--large {
          height: 32px;
          padding: 0 12px;
          font-size: 14px;
        }

        &.el-tag--small {
          height: 20px;
          padding: 0 8px;
          font-size: 12px;
        }
      }
    }

    .reason-item {
      .reason-text {
        position: relative;
        min-height: 80px;
        padding: 16px;
        font-size: 14px;
        line-height: 1.8;
        color: var(--el-text-color-regular);
        background: var(--el-fill-color-light);
        border-left: 4px solid var(--el-color-primary-light-5);
        border-radius: 4px;
        transition: all 0.3s ease;

        &:hover {
          background: var(--el-fill-color-lighter);
          border-left-color: var(--el-color-primary);
        }
      }
    }

    .check-tags-container {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      padding: 8px 0;

      .detail-tag {
        background: var(--el-fill-color-light);
        border: 1px solid var(--el-border-color-lighter);
        transition: all 0.3s;

        &:hover {
          color: var(--el-color-primary);
          background: var(--el-color-primary-light-9);
          border-color: var(--el-color-primary-light-5);
        }
      }
    }

    .table-container {
      margin-top: 12px;

      .detail-table {
        width: 100%;
        overflow: hidden;
        border: 1px solid var(--el-border-color-lighter);
        border-radius: 4px;

        :deep(.el-table) {
          --el-table-border-color: var(--el-border-color-lighter);

          th.el-table__cell {
            font-weight: 600;
            color: var(--el-text-color-primary);
            background-color: var(--el-fill-color-light);
          }

          .el-table__cell {
            padding: 12px;

            .cell {
              line-height: 1.6;
              color: var(--el-text-color-regular);
            }
          }

          .el-table__row {
            transition: background-color 0.3s;

            &:hover {
              background-color: var(--el-fill-color-lighter);
            }
          }
        }
      }
    }
  }

  .amount-item {
    .amount-container {
      display: flex;
      align-items: center;
    }
    
    .amount-text {
      display: flex;
      align-items: baseline;
      padding: 8px 16px;
      font-size: 16px;
      font-weight: 600;
      color: var(--el-color-danger);
      background: linear-gradient(to right, rgb(245 108 108 / 10%), transparent);
      border-radius: 6px;
      box-shadow: 0 2px 6px rgb(245 108 108 / 10%);
      transition: all 0.3s ease;
      
      &:hover {
        box-shadow: 0 4px 8px rgb(245 108 108 / 20%);
        transform: translateY(-2px);
      }
      
      .currency-symbol {
        margin-right: 2px;
        font-size: 14px;
      }
      
      .amount-value {
        font-size: 20px;
        font-weight: 700;
      }
      
      .currency-unit {
        margin-left: 4px;
        font-size: 14px;
        color: var(--el-text-color-secondary);
      }
    }
  }

  .business-text {
    font-size: 15px;

    b {
      font-weight: 600;
      color: var(--el-color-warning);
    }
  }

  .sql-editor-container {
    .editor-wrapper {
      margin-top: 12px;
      overflow: hidden;
      border: 1px solid var(--el-border-color-lighter);
      border-radius: 4px;

      .sql-editor {
        width: 100%;
      }

      .empty-content {
        padding: 24px;
        font-size: 14px;
        color: var(--el-text-color-secondary);
        text-align: center;
        background: var(--el-fill-color-light);
      }
    }
  }
}

.statinfo-item {
  .statinfo-tag {
    display: flex;
    gap: 8px;
    align-items: center;
    padding: 8px 12px;
    font-size: 14px;
    border-radius: 6px;
    transition: all 0.3s ease;
    
    .tag-icon {
      font-size: 16px;
    }
    
    &.statinfo-yes {
      color: #fff;
      background: linear-gradient(135deg, #f56c6c, #e74c3c);
      border: none;
      box-shadow: 0 2px 6px rgb(229 76 60 / 30%);
      
      &:hover {
        box-shadow: 0 4px 8px rgb(229 76 60 / 40%);
        transform: translateY(-2px);
      }
    }
    
    &.statinfo-no {
      color: #606266;
      background: #f5f7fa;
      border: 1px solid #e4e7ed;
      
      &:hover {
        color: #409eff;
        background-color: #ecf5ff;
        border-color: #c6e2ff;
      }
    }
  }
}

.env-item {
  .env-container {
    display: flex;
    align-items: center;
  }
  
  .env-tag {
    display: flex;
    gap: 8px;
    align-items: center;
    padding: 8px 16px;
    font-weight: 500;
    border-radius: 6px;
    transition: all 0.3s ease;
    
    .env-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 20px;
      height: 20px;
      background-color: rgb(255 255 255 / 20%);
      border-radius: 50%;
    }
    
    .env-text {
      font-size: 14px;
    }
    
    &.env-prod {
      background: linear-gradient(135deg, #f56c6c, #c0392b);
      box-shadow: 0 2px 6px rgb(192 57 43 / 30%);
      
      &:hover {
        box-shadow: 0 4px 8px rgb(192 57 43 / 40%);
        transform: translateY(-2px);
      }
    }
    
    &.env-test {
      background: linear-gradient(135deg, #e6a23c, #d35400);
      box-shadow: 0 2px 6px rgb(211 84 0 / 30%);
      
      &:hover {
        box-shadow: 0 4px 8px rgb(211 84 0 / 40%);
        transform: translateY(-2px);
      }
    }
    
    &.env-dev {
      background: linear-gradient(135deg, #67c23a, #27ae60);
      box-shadow: 0 2px 6px rgb(39 174 96 / 30%);
      
      &:hover {
        box-shadow: 0 4px 8px rgb(39 174 96 / 40%);
        transform: translateY(-2px);
      }
    }
  }
}

.sensitive-item {
  .sensitive-tag {
    display: flex;
    gap: 8px;
    align-items: center;
    padding: 8px 12px;
    font-size: 14px;
    border-radius: 6px;
    transition: all 0.3s ease;
    
    .tag-icon {
      font-size: 16px;
    }
    
    &.sensitive-yes {
      color: #fff;
      background: linear-gradient(135deg, #f56c6c, #c0392b);
      border: none;
      box-shadow: 0 2px 6px rgb(192 57 43 / 30%);
      
      &:hover {
        box-shadow: 0 4px 8px rgb(192 57 43 / 40%);
        transform: translateY(-2px);
      }
    }
    
    &.sensitive-no {
      color: #606266;
      background: #f5f7fa;
      border: 1px solid #e4e7ed;
      
      &:hover {
        color: #409eff;
        background-color: #ecf5ff;
        border-color: #c6e2ff;
      }
    }
  }
}

.order-type-item {
  .order-type-container {
    display: flex;
    align-items: center;
  }
  
  .order-type-tag {
    display: flex;
    align-items: center;
    padding: 8px 16px;
    font-weight: 500;
    background: linear-gradient(135deg, #e6a23c, #d35400);
    border-radius: 6px;
    box-shadow: 0 2px 6px rgb(211 84 0 / 30%);
    transition: all 0.3s ease;
    
    &:hover {
      box-shadow: 0 4px 8px rgb(211 84 0 / 40%);
      transform: translateY(-2px);
    }
    
    .tag-text {
      font-size: 14px;
    }
  }
}

.permission-item {
  .permission-container {
    display: flex;
    align-items: center;
  }
  
  .permission-tag {
    display: flex;
    align-items: center;
    padding: 8px 16px;
    font-weight: 500;
    background: linear-gradient(135deg, #67c23a, #27ae60);
    border-radius: 6px;
    box-shadow: 0 2px 6px rgb(39 174 96 / 30%);
    transition: all 0.3s ease;
    
    &:hover {
      box-shadow: 0 4px 8px rgb(39 174 96 / 40%);
      transform: translateY(-2px);
    }
    
    .tag-text {
      font-size: 14px;
    }
  }
}

.repo-item {
  .repo-tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .repo-tag {
    display: flex;
    align-items: center;
    padding: 6px 12px;
    background-color: #f5f7fa;
    border: 1px solid #e4e7ed;
    border-radius: 20px;
    transition: all 0.3s ease;
    
    &:hover {
      color: #409eff;
      background-color: #ecf5ff;
      border-color: #c6e2ff;
      transform: translateY(-2px);
    }
    
    .repo-name {
      font-size: 13px;
      font-weight: 500;
    }
  }
}

.action-item {
  .action-button {
    padding: 10px 20px;
    font-weight: 500;
    background: linear-gradient(135deg, #409eff, #1976d2);
    border: none;
    border-radius: 6px;
    box-shadow: 0 2px 6px rgb(25 118 210 / 30%);
    transition: all 0.3s ease;
    
    &:hover {
      box-shadow: 0 4px 8px rgb(25 118 210 / 40%);
      transform: translateY(-2px);
    }
    
    .button-text {
      font-size: 14px;
    }
  }
  
  .open-result {
    padding: 8px 12px;
    margin-top: 12px;
    font-size: 14px;
    border-radius: 4px;
    transition: all 0.3s ease;
    
    &.success {
      color: #67c23a;
      background-color: rgb(103 194 58 / 10%);
      border-left: 3px solid #67c23a;
    }
    
    &.error {
      color: #f56c6c;
      background-color: rgb(245 108 108 / 10%);
      border-left: 3px solid #f56c6c;
    }
  }
}

@media screen and (width <= 768px) {
  .order-detail-container {
    padding: 16px;

    .details-card {
      .el-descriptions__title {
        font-size: 18px;
      }

      .detail-item {
        .detail-text {
          flex-wrap: wrap;
        }

        .el-tag {
          &.el-tag--large {
            height: 28px;
            font-size: 13px;
          }
        }
      }
    }
  }
}
