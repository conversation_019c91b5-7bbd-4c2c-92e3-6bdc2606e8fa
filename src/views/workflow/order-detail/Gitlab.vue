<script lang="ts" setup>
import { onMounted, ref } from "vue";
import dayjs from "dayjs";

import {
  OrderStatus,
  OrderStatuses,
  GitlabPermissions
} from "@/config/order-enum";
import {
  CircleCheckFilled,
  CircleCloseFilled,
  SuccessFilled,
  InfoFilled
} from "@element-plus/icons-vue";
import { useUserStore } from "@/store/modules/user";
import { autoOpenGitlabAPI } from "@/api/workflow/order-gitlab";
import { formatDuration } from "@/utils/date";
defineOptions({
  name: "GitlabOrderDetail"
});

export interface FormProps {
  formInline: {
    orderDetailData: any;
  };
}
const props = withDefaults(defineProps<FormProps>(), {
  formInline: () => ({ orderDetailData: undefined })
});
const newFormInline = ref(props.formInline);
const isMobile = ref(false);

// 检测是否为移动设备
const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768;
};

onMounted(() => {
  checkMobile();
  window.addEventListener("resize", checkMobile);
});
const store = useUserStore();
const openResult = ref("");
const openResultType = ref("");

const autoOpenGitlab = () => {
  autoOpenGitlabAPI(newFormInline.value.orderDetailData.order?.id).then(res => {
    if (res.success) {
      openResult.value = `开通成功: ${res.msg}`;
      openResultType.value = "success";
    } else {
      openResult.value = `开通失败: ${res.msg}`;
      openResultType.value = "error";
    }
  });
};
</script>

<template>
  <div class="order-detail-container" :class="{ 'mobile-container': isMobile }">
    <el-descriptions
      title="工单详情"
      class="margin-top details-card"
      direction="vertical"
      border
      :size="isMobile ? 'default' : 'large'"
      :column="isMobile ? 1 : 4"
      :label-class-name="isMobile ? 'mobile-label' : ''"
      :content-class-name="isMobile ? 'mobile-content' : ''"
    >
      <el-descriptions-item label="标题" :span="2" class="detail-item">
        <el-text class="detail-text" size="large">
          {{ newFormInline.orderDetailData.order?.title }}
        </el-text>
      </el-descriptions-item>

      <el-descriptions-item
        label="工单类型"
        class="detail-item order-type-item"
      >
        <div class="order-type-container">
          <el-tag
            class="order-type-tag"
            size="large"
            effect="dark"
            type="warning"
          >
            <span class="tag-text">{{
              newFormInline.orderDetailData.order?.order_type
            }}</span>
          </el-tag>
        </div>
      </el-descriptions-item>

      <el-descriptions-item label="权限" class="detail-item permission-item">
        <div class="permission-container">
          <el-tag
            class="permission-tag"
            size="large"
            effect="dark"
            type="success"
          >
            <span class="tag-text">{{
              GitlabPermissions.get(
                newFormInline.orderDetailData.order?.permission
              )
            }}</span>
          </el-tag>
        </div>
      </el-descriptions-item>

      <el-descriptions-item label="申请人" class="detail-item">
        <el-text class="detail-text">
          <el-avatar
            :size="24"
            :src="newFormInline.orderDetailData.order?.applicant?.avatar"
            :fallback="() => false"
            :data-letter="
              newFormInline.orderDetailData.order?.applicant?.name?.[0]?.toUpperCase()
            "
            style="background: var(--el-color-primary-light-5)"
          >
            {{
              newFormInline.orderDetailData.order?.applicant?.name?.[0]?.toUpperCase()
            }}
          </el-avatar>
          <span class="applicant-name">{{
            newFormInline.orderDetailData.order?.applicant?.name
          }}</span>
          <el-tag size="small" effect="plain">
            {{ newFormInline.orderDetailData.order?.applicant?.username }}
          </el-tag>
        </el-text>
      </el-descriptions-item>

      <el-descriptions-item label="工单状态" class="detail-item">
        <el-text
          :type="
            newFormInline.orderDetailData.order?.status ===
            OrderStatus.Approving
              ? 'success'
              : newFormInline.orderDetailData.order?.status ===
                  OrderStatus.Rejected
                ? 'danger'
                : newFormInline.orderDetailData.order?.status ===
                    OrderStatus.Completed
                  ? 'primary'
                  : 'info'
          "
          class="status-text"
        >
          <el-icon>
            <component
              :is="
                newFormInline.orderDetailData.order?.status ===
                OrderStatus.Approving
                  ? CircleCheckFilled
                  : newFormInline.orderDetailData.order?.status ===
                      OrderStatus.Rejected
                    ? CircleCloseFilled
                    : newFormInline.orderDetailData.order?.status ===
                        OrderStatus.Completed
                      ? SuccessFilled
                      : InfoFilled
              "
            />
          </el-icon>
          {{
            OrderStatuses.get(newFormInline.orderDetailData.order?.status)
          }} </el-text
        ><el-text
          v-if="
            newFormInline.orderDetailData.order?.service_duration_seconds > 0
          "
          type="info"
          style="margin-left: 20px"
        >
          耗时：
          {{
            formatDuration(
              newFormInline.orderDetailData.order?.service_duration_seconds
            )
          }}</el-text
        >
      </el-descriptions-item>

      <el-descriptions-item label="计划时间" class="detail-item">
        <span class="time-text">
          {{
            newFormInline.orderDetailData.order?.plan_time &&
            dayjs(newFormInline.orderDetailData.order?.plan_time).format(
              "YYYY-MM-DD HH:mm:ss"
            )
          }}
        </span>
      </el-descriptions-item>

      <el-descriptions-item label="申请时间" class="detail-item">
        <span class="time-text">
          {{
            dayjs(newFormInline.orderDetailData.order?.created_at).format(
              "YYYY-MM-DD HH:mm:ss"
            )
          }}
        </span>
      </el-descriptions-item>

      <el-descriptions-item
        label="Gitlab仓库"
        :span="isMobile ? 1 : 4"
        class="detail-item repo-item"
      >
        <div class="selected-projects-block">
          <div
            v-for="(proj, idx) in newFormInline.orderDetailData.order
              ?.projects || []"
            :key="proj"
            class="project-tag-item full-info"
          >
            <span class="repo-index">{{ idx + 1 }}.</span>
            <div class="repo-info-block">
              <div class="repo-main-full">
                <iconify-icon-online
                  icon="simple-icons:gitlab"
                  width="18"
                  height="18"
                  style="
                    margin-right: 4px;
                    color: #fc6d26;
                    vertical-align: middle;
                  "
                />
                <a
                  :href="proj"
                  target="_blank"
                  rel="noopener"
                  class="repo-link"
                  >{{ proj }}</a
                >
              </div>
            </div>
          </div>
        </div>
      </el-descriptions-item>

      <el-descriptions-item
        label="申请理由"
        :span="isMobile ? 1 : 4"
        class="reason-item"
      >
        <div class="reason-text" style="white-space: pre-wrap">
          {{ newFormInline.orderDetailData.order?.content }}
        </div>
      </el-descriptions-item>

      <el-descriptions-item
        v-if="newFormInline.orderDetailData.is_last && store.IS_ADMIN()"
        label="操作"
        class="detail-item action-item"
      >
        <el-button type="primary" class="action-button" @click="autoOpenGitlab">
          <span class="button-text">自动开通</span>
        </el-button>
        <div v-if="openResult" :class="['open-result', openResultType]">
          {{ openResult }}
        </div>
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<style lang="scss" scoped>
@import url("./styles/detail.scss");

@media screen and (width <= 768px) {
  .order-detail-container {
    padding: 8px !important;
  }

  :deep(.el-descriptions__title) {
    padding-left: 8px !important;
    margin-bottom: 12px !important;
    font-size: 16px !important;
  }

  :deep(.el-descriptions__body) {
    background-color: transparent !important;
  }

  :deep(.el-descriptions-item__label) {
    width: 30% !important;
    min-width: 80px !important;
    padding: 6px 8px !important;
  }

  :deep(.el-descriptions-item__content) {
    width: 70% !important;
    padding: 6px 8px !important;
  }

  :deep(.el-tag) {
    height: 24px !important;
    padding: 0 6px !important;
    font-size: 12px !important;
    line-height: 22px !important;
  }

  :deep(.el-avatar) {
    width: 20px !important;
    height: 20px !important;
    font-size: 12px !important;
  }

  .detail-text {
    display: flex !important;
    flex-wrap: wrap !important;
    gap: 4px !important;
    align-items: center !important;
    font-size: 13px !important;
  }

  .status-text {
    padding: 2px 6px !important;
    font-size: 13px !important;
  }

  .time-text {
    padding: 2px 6px !important;
    font-size: 12px !important;
  }

  .reason-text {
    min-height: 60px !important;
    padding: 8px !important;
    font-size: 13px !important;
    line-height: 1.5 !important;
  }

  .repo-tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
  }

  .repo-tag {
    margin: 2px !important;

    .repo-name {
      font-size: 12px !important;
    }
  }
}

.order-detail-container {
  .detail-item {
    margin-bottom: 16px; // 增加项之间的间距
  }

  .applicant-name {
    margin-left: 8px; // 申请人名称与头像之间的间距
  }

  .open-result {
    margin-top: 8px; // 开通结果与按钮之间的间距
  }

  .success {
    color: var(--el-color-success); // 成功消息颜色
  }

  .error {
    color: var(--el-color-danger); // 失败消息颜色
  }
}

/* 移动端适配样式 */
.mobile-container {
  padding: 8px !important;
}

.mobile-label {
  padding: 8px !important;
  font-size: 13px !important;
  background-color: var(--el-fill-color-light) !important;
}

.mobile-content {
  padding: 8px !important;
}

.selected-projects-block {
  display: flex;
  flex-direction: column;
  gap: 6px 0;
  padding: 10px 0 6px;
  margin-top: 8px;
  border-radius: 10px;
}

.project-tag-item.full-info {
  position: relative;
  display: flex;
  align-items: flex-start;
  min-height: 44px;
  padding: 8px 0;
  margin: 0 0 2px;
  font-size: 13px;
  background: none;
  border-radius: 8px;
  box-shadow: none;
  transition: none;
}

.project-tag-item.full-info:hover {
  background: none;
  box-shadow: none;
}

.repo-index {
  min-width: 24px;
  margin-right: 12px;
  font-size: 15px;
  font-weight: bold;
  color: #e24329;
  text-align: right;
}

.repo-info-block {
  display: flex;
  flex: 1;
  flex-direction: column;
  min-width: 0;
}

.repo-main-full {
  display: flex;
  align-items: center;
  margin-bottom: 2px;
  font-size: 14px;
  font-weight: 500;
  color: #e24329;
  word-break: break-all;
}

.repo-desc-full {
  display: none;
}

.repo-link {
  color: #e24329;
  text-decoration: underline dotted;
  word-break: break-all;
  transition: color 0.2s;
}

.repo-link:hover {
  color: #fc6d26;
  text-decoration: underline solid;
}
</style>
