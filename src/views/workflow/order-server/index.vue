<template>
  <div>
    <el-row :gutter="24" class="order-container">
      <el-col :span="16">
        <el-card class="main-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <h2 class="title">
                <iconify-icon-online
                  icon="material-symbols:dns-outline"
                  class="icon"
                />
                服务器申请工单
              </h2>
              <div class="header-description">
                <iconify-icon-online
                  icon="mdi:information-outline"
                  class="info-icon"
                />
                <span>请填写以下信息完成服务器申请</span>
              </div>
            </div>
          </template>

          <el-form
            ref="ruleFormRef"
            :model="form"
            class="order-form"
            status-icon
            label-position="top"
            size="large"
          >
            <!-- 基础信息区域 -->
            <div class="form-section">
              <div class="section-header">
                <iconify-icon-online
                  icon="tabler:file-description"
                  class="section-icon"
                />
                <span class="section-title">基本信息</span>
              </div>

              <!-- 基本信息在同一行显示 -->
              <div class="basic-info-row">
                <!-- 工单类型 -->
                <el-form-item
                  label="工单类型"
                  prop="order_type"
                  class="basic-info-item"
                >
                  <el-radio-group
                    v-model="form.order_type"
                    class="custom-radio-group"
                  >
                    <el-radio-button value="新服务器申请">
                      <div class="radio-button-content">
                        <iconify-icon-online
                          icon="material-symbols:add-circle-outline"
                        />
                        <span>新服务器申请</span>
                      </div>
                    </el-radio-button>
                    <el-radio-button value="旧服务器升级">
                      <div class="radio-button-content">
                        <iconify-icon-online icon="material-symbols:upgrade" />
                        <span>旧服务器升级</span>
                      </div>
                    </el-radio-button>
                  </el-radio-group>
                </el-form-item>

                <!-- 所属业务 -->
                <el-form-item
                  label="所属业务"
                  :rules="[
                    {
                      required: true,
                      message: '请选择所属业务',
                      trigger: 'change'
                    }
                  ]"
                  prop="business"
                  class="basic-info-item"
                >
                  <el-select
                    v-model="form.business"
                    placeholder="请选择所属业务"
                    class="custom-select"
                    filterable
                  >
                    <el-option
                      v-for="business in businesses"
                      :key="business.id"
                      :label="business.name"
                      :value="business.name"
                    />
                  </el-select>
                </el-form-item>

                <!-- 云平台 -->
                <el-form-item
                  label="云平台"
                  prop="cloud_platforms"
                  class="basic-info-item"
                >
                  <el-select
                    v-model="form.cloud_platforms"
                    placeholder="请选择云平台"
                    class="custom-select"
                    multiple
                    tag-type="primary"
                    clearable
                    filterable
                    collapse-tags
                    collapse-tags-tooltip
                  >
                    <el-option
                      v-for="platform in CloudPlatforms"
                      :key="platform"
                      :label="platform"
                      :value="platform"
                    />
                  </el-select>
                </el-form-item>
              </div>
            </div>
            <!-- 结束基础信息区域 -->

            <!-- 需求详情区域 -->
            <div class="form-section">
              <div class="section-header">
                <iconify-icon-online
                  icon="material-symbols:description-outline"
                  class="section-icon"
                />
                <span class="section-title">需求详情</span>
              </div>

              <!-- 申请理由 -->
              <el-form-item
                label="申请理由"
                :rules="[
                  {
                    required: true,
                    message: '请输入申请理由',
                    trigger: 'blur'
                  },
                  {
                    max: 5000,
                    message: '理由不能超过5000个字符',
                    trigger: 'blur'
                  }
                ]"
                prop="content"
              >
                <el-input
                  v-model="form.content"
                  type="textarea"
                  show-word-limit
                  maxlength="5000"
                  :autosize="{ minRows: 6, maxRows: 16 }"
                  :placeholder="getContentPlaceholder()"
                  class="content-textarea"
                />
              </el-form-item>
            </div>

            <!-- 服务器配置区域 -->
            <div class="form-section">
              <div class="section-header">
                <iconify-icon-online
                  icon="fluent:server-20-regular"
                  class="section-icon"
                />
                <span class="section-title">服务器配置</span>
              </div>

              <!-- 申请清单 -->
              <el-form-item label="申请清单" class="list-form-item">
                <div class="model-list-container">
                  <div class="model-list-header">
                    <span>服务器配置清单</span>
                    <el-button
                      type="primary"
                      size="small"
                      class="header-add-button primary-btn"
                      @click="openDialog"
                    >
                      <iconify-icon-online
                        icon="ep:plus"
                        style="margin-right: 4px"
                      />
                      添加实例
                    </el-button>
                  </div>
                  <pure-table
                    ref="tableRef"
                    row-key="id"
                    align-whole="center"
                    :header-cell-style="{
                      background: '#f5f7fa',
                      color: '#606266',
                      fontWeight: 500,
                      fontSize: '13px',
                      height: '40px',
                      padding: '6px 8px',
                      borderBottom: '1px solid #ebeef5'
                    }"
                    :cell-style="{
                      fontSize: '13px',
                      padding: '8px',
                      borderBottom: '1px solid #ebeef5'
                    }"
                    :data="modelList"
                    :columns="columns"
                    scrollbar-always-on
                    style="margin-bottom: 4px; transition: all 0.3s"
                    border
                  >
                    <template #empty>
                      <div class="empty-data">
                        <iconify-icon-online
                          icon="ant-design:file-add-outlined"
                          class="empty-icon"
                        />
                        <p class="empty-text">暂无数据，请添加服务器实例</p>
                      </div>
                    </template>
                  </pure-table>
                </div>
              </el-form-item>

              <!-- 申请金额 -->
              <el-form-item
                label="申请金额"
                :rules="[
                  { required: true, message: '请输入申请金额', trigger: 'blur' }
                ]"
                prop="amount"
                class="amount-form-item"
              >
                <div class="amount-container">
                  <el-input-number
                    v-model="form.amount"
                    :min="1"
                    :precision="2"
                    :step="1000"
                    controls-position="right"
                    style="width: 240px"
                    class="amount-input"
                    disabled
                  />
                  <span class="amount-tip">元</span>
                  <el-tooltip
                    effect="dark"
                    content="请根据实际需求设定申请金额"
                    placement="top"
                  >
                    <iconify-icon-online
                      icon="ep:question-filled"
                      class="help-icon"
                    />
                  </el-tooltip>
                </div>
              </el-form-item>
            </div>
            <!-- 结束服务器配置区域 -->

            <!-- 表单操作按钮 -->
            <el-form-item class="action-form-item">
              <el-button
                type="primary"
                :loading="loading"
                class="submit-button"
                @click="onSubmit"
              >
                <iconify-icon-online
                  icon="material-symbols:check-circle-outline"
                  style="margin-right: 8px"
                />
                提交申请
              </el-button>
              <RouterLink to="/workflow/my/order">
                <el-button class="cancel-button">
                  <iconify-icon-online
                    icon="material-symbols:cancel"
                    style="margin-right: 8px"
                  />
                  取消
                </el-button>
              </RouterLink>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
      <el-col :span="8">
        <ApprovalFlow :processes="processes" />
      </el-col>
    </el-row>
    <el-dialog
      v-model="dialogVisible"
      title="选择实例规格"
      width="65%"
      :close-on-click-modal="false"
      :destroy-on-close="true"
    >
      <ServerModel
        v-model:select-model="defaultModelForm"
        :on-close="handleDialogClose"
      />
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, watch, onUnmounted, nextTick } from "vue";
import { applyOrderAPI, type OrderForm } from "@/api/workflow/order-server";
import { ElMessageBox, type FormInstance } from "element-plus";
import { message } from "@/utils/message";
import type { TableColumns } from "@pureadmin/table";
import {
  getProcesses,
  processes,
  modelColumns,
  modelList,
  getAllBusinesses,
  businesses
} from "./hook";
import ServerModel from "./components/ServerModel.vue";
import ApprovalFlow from "../components/ApprovalFlow.vue";
import { CloudPlatforms } from "@/config/enum";

const form = ref<OrderForm>({
  order_type: "新服务器申请",
  content: "",
  model_list: undefined,
  business: undefined,
  amount: undefined,
  orders: [],
  env: undefined,
  critical: undefined,
  cloud_platforms: undefined
});
const ruleFormRef = ref<FormInstance>();
const dialogVisible = ref(false);
const loading = ref(false);

// 定义服务器模型的默认值
const defaultModelForm = ref({
  spec: "",
  cpu: "",
  memory: "",
  price: 0,
  count: 1
});

const tableRef = ref(null);

// 定义在外部，方便访问和清理
let resizeObserver = null;

const onSubmit = async () => {
  // 1. 检查申请清单是否为空
  if (!modelList.value || modelList.value.length === 0) {
    message("请填写申请清单信息", { type: "error" });
    return;
  }

  // 2. 检查清单中是否有空项
  let isEmpty = false;
  modelList.value.forEach((item, index) => {
    if (!item.spec) {
      message("请填写第" + (index + 1) + "条清单信息", { type: "error" });
      isEmpty = true;
    }
  });
  if (isEmpty) return;

  // 3. 确保将模型列表赋值给表单，不管验证是否通过
  form.value.model_list = modelList.value;

  // 4. 安全地进行表单验证
  const valid = await new Promise(resolve => {
    if (!ruleFormRef.value) {
      resolve(false);
      return;
    }

    ruleFormRef.value.validate((valid: boolean) => {
      resolve(valid);
    });
  });

  if (valid) {
    try {
      await ElMessageBox.confirm("是否确认提交工单？", "提交确认", {
        confirmButtonText: "提交",
        cancelButtonText: "取消",
        type: "warning",
        draggable: true,
        center: true,
        customClass: "custom-message-box",
        icon: "Warning"
      });
      const res = await applyOrderAPI(form.value);
      if (res.success) {
        message("提交成功", { type: "success" });
        setTimeout(() => {
          window.location.href = "/workflow/my/order";
        }, 300);
      } else {
        message(res.msg || "提交失败", { type: "error" });
      }
    } catch (error) {
      if (error !== "cancel") {
        message(error.message || "操作取消", { type: "warning" });
      }
    }
  } else {
    message("请检查表单数据", { type: "warning" });
  }
};

// 生命周期钩子
onMounted(() => {
  getProcesses();
  getAllBusinesses();
  modelList.value = [
    {
      index: 0,
      storage: "系统盘：40G",
      spec: "c7a.xlarge4",
      gpu: "无",
      cpu: "1",
      memory: "2G",
      price: 629,
      amount: 1,
      total_price: 629
    }
  ];

  // 添加窗口调整大小监听，用于刷新表格布局
  window.addEventListener("resize", refreshTableLayout);

  // 初始表格布局渲染后进行处理
  nextTick(() => {
    // 初始计算表格高度
    calcTableHeight();

    // 创建ResizeObserver监听表格大小变化
    if (tableRef.value?.$el) {
      resizeObserver = new ResizeObserver(() => {
        calcTableHeight();
      });
      resizeObserver.observe(tableRef.value.$el);
    }
  });
});

// 组件销毁时移除所有监听器
onUnmounted(() => {
  window.removeEventListener("resize", refreshTableLayout);

  if (resizeObserver) {
    resizeObserver.disconnect();
    resizeObserver = null;
  }
});

// 刷新表格布局
const refreshTableLayout = () => {
  setTimeout(() => {
    const tables = document.querySelectorAll(".pure-table .el-table");
    tables.forEach(table => {
      const tableInstance = (table as any).__vueParentComponent?.proxy;
      if (tableInstance && typeof tableInstance.doLayout === "function") {
        tableInstance.doLayout();
      }
    });

    // 刷新布局后计算高度
    calcTableHeight();
  }, 100);
};

// 添加一个更智能的自动计算表格高度的函数
const calcTableHeight = () => {
  setTimeout(() => {
    if (!tableRef.value) return;

    const tableEl = tableRef.value.$el;
    if (!tableEl) return;

    // 获取表格容器
    const container = tableEl.closest(".model-list-container");
    if (!container) return;

    // 获取表头和表体
    const tableHeader = tableEl.querySelector(".el-table__header-wrapper");
    const tableBody = tableEl.querySelector(".el-table__body-wrapper");
    if (!tableHeader || !tableBody) return;

    // 计算表头高度
    const headerHeight = tableHeader.offsetHeight || 40;

    // 获取行数和每行高度
    const rows = tableBody.querySelectorAll(".el-table__row");
    const rowCount = rows.length || 1;
    const rowHeight = rowCount > 0 && rows[0] ? rows[0].offsetHeight : 36;

    // 计算内容高度 - 使用实际行数计算
    let contentHeight = 0;
    if (rowCount > 0) {
      // 如果有行，使用行数 * 行高
      contentHeight = rowCount * rowHeight;
    } else {
      // 如果没有行，使用空状态的高度
      const emptyBlock = tableBody.querySelector(".el-table__empty-block");
      contentHeight = emptyBlock ? emptyBlock.offsetHeight : 80;
    }

    // 计算表格合适的高度 (表头 + 内容 + 边距)
    const padding = 10;
    let targetHeight = headerHeight + contentHeight + padding;

    // 根据行数动态设置最小和最大高度
    const minHeight =
      rowCount <= 1 ? 100 : Math.min(100, rowCount * rowHeight + headerHeight);
    const maxHeight = Math.max(400, rowCount * rowHeight + headerHeight);

    // 应用最小/最大高度限制
    targetHeight = Math.max(minHeight, Math.min(maxHeight, targetHeight));

    // 应用高度
    tableBody.style.maxHeight = `${targetHeight - headerHeight}px`;
    tableEl.style.maxHeight = `${targetHeight}px`;

    // 刷新表格布局以确保正确渲染
    const tableInstance = tableRef.value.instance;
    if (tableInstance && typeof tableInstance.doLayout === "function") {
      tableInstance.doLayout();
    }
  }, 100);
};

// 在modelList变化时调用高度计算
watch(
  modelList,
  newList => {
    // 计算总金额：每行的 total_price 的总和
    const totalAmount = newList.reduce((sum, item) => {
      return sum + item.total_price;
    }, 0);

    // 计算4折价格并更新到表单
    form.value.amount = Number((totalAmount * 0.4).toFixed(2));

    // 数据变化后刷新表格布局
    refreshTableLayout();

    // 计算并应用表格高度
    calcTableHeight();
  },
  { deep: true, immediate: true }
);

// 修改选择模型后的处理逻辑
watch(
  () => dialogVisible.value,
  (newVal, oldVal) => {
    if (
      !newVal &&
      oldVal &&
      defaultModelForm.value &&
      Object.keys(defaultModelForm.value).length > 0
    ) {
      // 仅当对话框从打开状态变为关闭状态且有选择的模型时执行
      // 计算金额和总价
      const modelData = defaultModelForm.value;
      const amount = modelData.count || 1;
      const price = modelData.price || 0;
      const total_price = amount * price;

      modelList.value.push({
        index: modelList.value.length + 1,
        spec: modelData.spec || "",
        storage: "系统盘：40G",
        gpu: "无",
        cpu: modelData.cpu || "",
        memory: modelData.memory || "",
        price: price,
        amount: amount,
        total_price: total_price
      });

      // 重置form对象
      form.value = {
        order_type: "新服务器申请",
        content: "",
        model_list: undefined,
        business: undefined,
        amount: undefined,
        orders: [],
        env: undefined,
        critical: undefined,
        cloud_platforms: undefined
      };
    }
  }
);

// 定义添加和编辑函数
const openDialog = () => {
  dialogVisible.value = true;
  // 重置默认模型表单
  defaultModelForm.value = {
    spec: "",
    cpu: "",
    memory: "",
    price: 0,
    count: 1
  };
  nextTick(calcTableHeight);
};

// 处理对话框关闭的函数
const handleDialogClose = () => {
  dialogVisible.value = false;
};

// 根据工单类型返回不同的申请理由提示文本
const getContentPlaceholder = () => {
  if (form.value.order_type === "新服务器申请") {
    return "请详细描述新服务器申请的理由，包括：\n1. 项目背景和业务需求\n2. 为什么需要新服务器\n3. 预期的服务器用途\n4. 其他相关说明";
  } else {
    return "请详细说明旧服务器升级的原因，包括：\n1. 现有服务器计算资源情况\n2. 遇到的具体问题或瓶颈\n3. 升级后预期效果\n4. 其他相关说明";
  }
};

// 定义编辑函数依然保留，但使用下划线前缀避免 lint 错误
// 该功能可能在未来需要使用
const _handleEdit = row => {
  dialogVisible.value = true;
  form.value = { ...row };
  nextTick(calcTableHeight);
};

// 修改类型断言
const columns = modelColumns as TableColumns[];

// 确保当表格可见性变化时也重新计算高度
watch(
  () => dialogVisible.value,
  (newVal, oldVal) => {
    if (!newVal && oldVal) {
      // 当对话框关闭且之前是打开状态，重新计算高度
      nextTick(calcTableHeight);
    }
  }
);
</script>

<style scoped>


/* 在小屏幕上垂直显示 */
@media (width <= 1200px) {
  .basic-info-row {
    flex-direction: column;
    gap: 10px;
  }

  .basic-info-item {
    width: 100%;
  }
}

.order-container {
  margin-top: 20px;
}

.main-card {
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 8%);
  transition: all 0.3s ease;
}

.main-card:hover {
  box-shadow: 0 6px 20px rgb(0 0 0 / 12%);
}

.card-header {
  display: flex;
  flex-direction: column;
  padding: 16px 0 8px;
}

.title {
  display: flex;
  align-items: center;
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.header-description {
  display: flex;
  align-items: center;
  margin-top: 8px;
  font-size: 14px;
  color: #909399;
}

.info-icon {
  margin-right: 6px;
  font-size: 16px;
  color: #909399;
}

.icon {
  margin-right: 12px;
  font-size: 24px;
  color: #409eff;
}

.order-form {
  padding: 10px;
  margin-top: 20px;
}

/* 表单分区样式 */
.form-section {
  padding: 16px;
  margin-bottom: 24px;
  background-color: #f9fafc;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 5%);
  transition: all 0.3s ease;
}

.form-section:hover {
  box-shadow: 0 4px 12px rgb(0 0 0 / 8%);
}

.section-header {
  display: flex;
  align-items: center;
  padding-bottom: 8px;
  margin-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.section-icon {
  margin-right: 8px;
  font-size: 18px;
  color: #409eff;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.form-section-row {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 10px;
}

.form-section-col {
  flex: 1;
  min-width: 250px;
}

/* 基本信息一行显示样式 */
.basic-info-row {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 10px;
}

.basic-info-item {
  flex: 1;
  min-width: 180px;
}

/* 工单类型区域的宽度调整 */
.basic-info-item:first-child {
  flex: 0 0 auto;
  margin-right: 10px;
}

/* 优化单选按钮组的宽度 */
.custom-radio-group .el-radio-button {
  min-width: initial;
}

.custom-radio-group {
  display: flex;
  align-items: center;
}

/* 按钮内容单行显示 */
.radio-button-content {
  display: flex;
  align-items: center;
  white-space: nowrap;
}

.radio-button-content iconify-icon-online {
  margin-right: 4px;
}

.radio-button-content span {
  display: inline-block;
}

.el-radio-button {
  display: flex;
  align-items: center;
}

.el-radio-button .icon {
  margin-right: 4px;
}

/* 内容文本框样式 */
.content-textarea {
  font-size: 14px;
  line-height: 1.6;
  transition: all 0.3s ease;
}

/* 金额容器样式 */
.amount-form-item {
  margin-top: 16px;
}

.amount-container {
  display: flex;
  align-items: center;
}

.amount-input {
  transition: all 0.3s ease;
}

.amount-tip {
  margin-left: 10px;
  font-size: 14px;
  color: #606266;
}

.help-icon {
  margin-left: 10px;
  font-size: 16px;
  color: #909399;
  cursor: pointer;
  transition: all 0.3s ease;
}

.help-icon:hover {
  color: #409eff;
}

/* 清单容器样式 */
.model-list-container {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 0;
  margin: 0 0 8px;
  overflow: hidden;
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 8px;
  box-shadow: 0 1px 4px 0 rgb(0 0 0 / 5%);
  transition: all 0.3s ease;
}

.model-list-container:hover {
  box-shadow: 0 2px 12px rgb(0 0 0 / 10%);
}

.model-list-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 40px;
  padding: 8px 12px;
  font-size: 14px;
  font-weight: bold;
  color: #606266;
  background-color: #f5f7fa;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.header-add-button {
  display: flex;
  align-items: center;
  height: 28px;
  padding: 4px 12px;
  font-size: 13px;
  transition: all 0.3s ease;
}

.primary-btn {
  background-color: #409eff;
  border-color: #409eff;
}

.primary-btn:hover {
  background-color: #66b1ff;
  border-color: #66b1ff;
}

.empty-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 80px;
  padding: 16px 0;
  color: #909399;
}

.empty-icon {
  margin-bottom: 8px;
  font-size: 28px;
  color: #c0c4cc;
}

.empty-text {
  margin: 4px 0;
  font-size: 14px;
  color: #909399;
}

/* 调整表格样式 */
:deep(.pure-table) {
  .el-table {
    width: 100% !important;
    table-layout: fixed !important;
    border: none;
  }

  .el-table__empty-block {
    min-height: 80px !important;
  }

  .el-table__header-wrapper {
    border-bottom: 1px solid #f0f0f0;
  }

  .el-table__body-wrapper {
    overflow-y: auto !important;
  }

  .el-table__row {
    transition: background-color 0.2s;
  }

  .el-table__row:hover {
    background-color: #f5f7fa;
  }

  .el-table__header th {
    font-weight: bold;
  }

  /* 调整输入框样式 */
  .el-input,
  .el-input-number {
    font-size: 13px;
  }

  .el-input__wrapper {
    padding: 0 8px;
  }

  .el-input-number__decrease,
  .el-input-number__increase {
    width: 22px;
  }

  .el-button--link {
    padding: 4px 0;
    font-size: 13px;
  }
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-select .el-input) {
  transition: all 0.3s;
}

:deep(.el-input__inner) {
  transition: all 0.3s;
}

:deep(.el-input__inner:focus) {
  border-color: #409eff;
}

:deep(.el-textarea__inner) {
  transition: all 0.3s;
}

:deep(.el-textarea__inner:focus) {
  border-color: #409eff;
}

:deep(.el-date-editor.el-input) {
  transition: all 0.3s;
}

:deep(.custom-select) {
  width: 100%;
}

:deep(.el-button--primary) {
  padding: 12px 24px;
  font-weight: 500;
  transition: all 0.3s;
}

:deep(.el-button--primary:hover) {
  background-color: #66b1ff;
  box-shadow: 0 2px 8px rgb(64 158 255 / 20%);
}

:deep(.el-button--default) {
  padding: 12px 24px;
  font-weight: 500;
  transition: all 0.3s;
}

:deep(.el-button--default:hover) {
  background-color: #f2f6fc;
}

/* 表单提交按钮区域样式 */
.action-form-item {
  padding-top: 20px;
  margin-top: 32px;
  border-top: 1px dashed #ebeef5;
}

.list-form-item {
  margin-bottom: 16px !important;
}

:deep(.list-form-item .el-form-item__content) {
  display: flex;
  flex-direction: column;
}

.submit-button {
  min-width: 120px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.submit-button:hover {
  background-color: #66b1ff;
  box-shadow: 0 4px 12px rgb(64 158 255 / 20%);
  transform: translateY(-1px);
}

.cancel-button {
  min-width: 120px;
  margin-left: 12px;
  transition: all 0.3s ease;
}

.cancel-button:hover {
  background-color: #f2f6fc;
  transform: translateY(-1px);
}
</style>
