import { ref } from "vue";
import { message } from "@/utils/message";
import { getAllBusinessesAPI, type Business } from "@/api/asset/bussiness";
import { getProcessesAPI, type Process } from "@/api/workflow/process";
import { OrderType } from "@/config/order-enum";
import type { TableColumns } from "@pureadmin/table";

// 类型定义
export interface ServerModelType {
  spec: string;
  spec_family: string;
  cpu: string;
  memory: string;
  price: number;
}

export type PartialServerModel = Partial<ServerModelType>;

export interface ModelItem {
  index: number;
  storage: string;
  spec: string;
  gpu: string;
  cpu: string;
  memory: string;
  price: number;
  amount: number;
  total_price: number;
}

// 状态管理
export const processes = ref<Process[]>([]);
export const modelList = ref<ModelItem[]>([]);
export const businesses = ref<Business[]>([]);

// 表格列定义
export const modelColumns: TableColumns[] = [
  {
    label: "序号",
    prop: "index",
    width: 60,
    fixed: "left",
    align: "center"
  },
  {
    label: "实例规格",
    prop: "spec",
    minWidth: 120,
    align: "center",
    cellRenderer: ({ row }) => (
      <el-input
        v-model={row.spec}
        placeholder="请输入规格"
        clearable
        style={{ width: "100%", minWidth: "100px" }}
        size="small"
      />
    )
  },
  {
    label: "本地存储",
    prop: "storage",
    minWidth: 120,
    align: "center",
    cellRenderer: ({ row }) => (
      <el-input
        v-model={row.storage}
        placeholder="请输入存储"
        clearable
        style={{ width: "100%", minWidth: "100px" }}
        size="small"
      />
    )
  },
  {
    label: "CPU",
    prop: "cpu",
    minWidth: 80,
    align: "center",
    cellRenderer: ({ row }) => (
      <el-input
        v-model={row.cpu}
        placeholder="CPU"
        clearable
        style={{ width: "100%", minWidth: "80px" }}
        size="small"
      />
    )
  },
  {
    label: "内存",
    prop: "memory",
    minWidth: 80,
    align: "center",
    cellRenderer: ({ row }) => (
      <el-input
        v-model={row.memory}
        placeholder="内存"
        clearable
        style={{ width: "100%", minWidth: "80px" }}
        size="small"
      />
    )
  },
  {
    label: "GPU",
    prop: "gpu",
    minWidth: 80,
    align: "center",
    cellRenderer: ({ row }) => (
      <el-input
        v-model={row.gpu}
        placeholder="GPU"
        clearable
        style={{ width: "100%", minWidth: "80px" }}
        size="small"
      />
    )
  },
  {
    label: "价格",
    prop: "price",
    minWidth: 100,
    align: "center",
    cellRenderer: ({ row }) => (
      <el-input-number
        v-model={row.price}
        step={0.01}
        min={0}
        precision={2}
        controls-position="right"
        style={{ width: "100px" }}
        size="small"
        onChange={(value: number) => {
          row.total_price = Number((value * row.amount).toFixed(2));
        }}
      />
    )
  },
  {
    label: "数量",
    prop: "amount",
    minWidth: 100,
    align: "center",
    cellRenderer: ({ row }) => (
      <el-input-number
        v-model={row.amount}
        step={1}
        min={1}
        controls-position="right"
        style={{ width: "100px" }}
        size="small"
        onChange={(value: number) => {
          row.total_price = Number((row.price * value).toFixed(2));
        }}
      />
    )
  },
  {
    label: "总价",
    prop: "total_price",
    minWidth: 100,
    align: "center",
    cellRenderer: ({ row }) => (
      <el-input-number
        v-model={row.total_price}
        min={0}
        precision={2}
        controls-position="right"
        style={{ width: "100px" }}
        size="small"
        disabled
      />
    )
  },
  {
    label: "操作",
    width: 80,
    fixed: "right",
    align: "center",
    cellRenderer: ({ row }) => (
      <el-button
        link
        type="danger"
        onClick={() => onDel(row)}
        size="small"
        icon="Delete"
      >
        删除
      </el-button>
    )
  }
];

export const getProcesses = () => {
  getProcessesAPI(OrderType.ServerOrderType, undefined)
    .then(res => {
      if (res.success) {
        processes.value = res.data;
      } else {
        message(res.msg, { type: "error" });
      }
    })
    .catch(error => {
      message("请求失败" + error, { type: "error" });
    });
};

export const onDel = (row: ModelItem) => {
  const index = modelList.value.indexOf(row);
  if (index !== -1) modelList.value.splice(index, 1);
};

export const getAllBusinesses = () => {
  getAllBusinessesAPI()
    .then(res => {
      if (res.success) {
        businesses.value = res.data;
      } else {
        message(res.msg, { type: "error" });
      }
    })
    .catch(error => {
      message("请求失败" + error, { type: "error" });
    });
};
