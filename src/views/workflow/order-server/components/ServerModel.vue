<template>
  <div class="main">
    <el-form
      :model="form"
      label-width="60px"
      class="search-form"
      @submit.prevent
    >
      <div class="search-fields">
        <el-form-item label="规格">
          <el-input
            v-model="form.spec"
            placeholder="请输入实例规格"
            clearable
            size="default"
          />
        </el-form-item>
        <el-form-item label="CPU">
          <el-input
            v-model="form.cpu"
            placeholder="请输入CPU"
            clearable
            size="default"
          />
        </el-form-item>
        <el-form-item label="内存">
          <el-input
            v-model="form.memory"
            placeholder="请输入内存"
            clearable
            size="default"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" size="default" @click="onSearch">
            <el-icon><Search /></el-icon>搜索
          </el-button>
        </el-form-item>
      </div>
    </el-form>
    <PureTableBar
      title="实例规格列表"
      :columns="columns"
      style="margin-top: 8px"
      @refresh="onSearch"
    >
      <template v-slot="{ size, dynamicColumns }">
        <pure-table
          ref="tableRef"
          row-key="id"
          align-whole="center"
          table-layout="fixed"
          :loading="loading"
          :size="size || 'default'"
          adaptive
          :adaptiveConfig="{
            offsetBottom: 32,
            minHeight: 50,
            maxHeight: 400
          }"
          :data="dataList"
          :columns="dynamicColumns"
          :pagination="{ ...pagination, size }"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)',
            height: '32px',
            fontSize: '13px',
            padding: '2px 4px'
          }"
          :cell-style="{
            padding: '2px 4px',
            fontSize: '13px'
          }"
          :row-style="{
            height: '32px'
          }"
          :row-class-name="tableRowClassName"
          border
          @page-size-change="handleSizeChange"
          @page-current-change="handleCurrentChange"
        />
      </template>
    </PureTableBar>
  </div>
</template>

<script setup lang="ts">
import { PureTableBar } from "@/components/RePureTableBar";
import { useServerModel } from "./hook";
import type { PropType } from "vue";
import type { ServerModelType, PartialServerModel } from "../hook";
import { Search } from "@element-plus/icons-vue";

defineOptions({
  name: "ServerModel"
});

const emit = defineEmits<{
  "update:selectModel": [value: PartialServerModel];
}>();

const props = defineProps({
  selectModel: {
    type: Object as PropType<PartialServerModel>,
    default: () => ({})
  },
  onClose: {
    type: Function as PropType<() => void>,
    default: () => {}
  }
});

const tableRowClassName = ({ rowIndex }: { rowIndex: number }) => {
  return rowIndex % 2 === 0 ? "even-row" : "odd-row";
};

const {
  dataList,
  columns,
  pagination,
  onSearch,
  handleCurrentChange,
  handleSizeChange,
  loading,
  form
} = useServerModel((row: ServerModelType) => {
  emit("update:selectModel", row as PartialServerModel);
  props.onClose();
});
</script>

<style scoped lang="scss">
.main {
  padding: 0 8px;
}

.search-form {
  padding: 6px 10px;
  margin-bottom: 6px;
  background-color: var(--el-fill-color-light);
  border-radius: 4px;
}

.search-fields {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;

  :deep(.el-form-item) {
    margin-bottom: 0;

    .el-input {
      width: 140px;
      height: 30px;
    }

    .el-form-item__label {
      padding-right: 4px;
      font-size: 13px;
    }
  }
}

:deep(.el-pagination) {
  justify-content: flex-end;
  padding: 0;
  margin-top: 10px;
}

:deep(.pure-table) {
  .el-input,
  .el-input-number {
    font-size: 13px;

    .el-input__inner {
      font-size: 13px;
    }
  }

  .el-button--text {
    padding: 2px 4px;
  }

  .el-button--link {
    padding: 2px 0;
    font-size: 13px;
  }
}

.model-dialog-container {
  .el-dialog {
    margin-top: 5vh !important;
    border-radius: 4px !important;

    .el-dialog__header {
      padding: 12px 16px;
      margin: 0;
      border-bottom: 1px solid var(--el-border-color-lighter);

      .el-dialog__title {
        font-size: 15px;
        font-weight: 500;
      }
    }

    .el-dialog__body {
      padding: 12px 16px;
    }

    .el-dialog__footer {
      padding: 8px 16px;
      border-top: 1px solid var(--el-border-color-lighter);
    }
  }
}
</style>
