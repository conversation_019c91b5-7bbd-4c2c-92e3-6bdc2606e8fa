import { ref, onMounted, reactive } from "vue";
import { getServerModelsAPI } from "@/api/workflow/server-model"; // 导入 API 函数
import { message } from "@/utils/message";
import type { PaginationProps, TableColumns } from "@pureadmin/table";
import type { ServerModelType } from "../hook";

export function useServerModel(onSelect: (row: ServerModelType) => void) {
  const dataList = ref<ServerModelType[]>([]);
  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true,
    pageSizes: [5, 10, 20, 30, 40, 50, 100]
  });

  function onSearch() {
    pagination.currentPage = 1;
    fetchData();
  }

  function handleCurrentChange(page: number) {
    pagination.currentPage = page;
    fetchData();
  }

  function handleSizeChange(size: number) {
    pagination.pageSize = size;
    pagination.currentPage = 1;
    fetchData();
  }

  const loading = ref(false);
  const form = reactive({
    spec: undefined,
    cpu: undefined,
    memory: undefined
  });

  function fetchData() {
    loading.value = true;
    getServerModelsAPI({
      page: pagination.currentPage,
      limit: pagination.pageSize,
      spec: form.spec,
      cpu: form.cpu,
      memory: form.memory
    })
      .then(res => {
        if (res.success) {
          dataList.value = res.data.map(item => ({
            spec: String(item.spec),
            spec_family: String(item.spec_family),
            cpu: String(item.cpu),
            memory: String(item.memory),
            price: Number(item.price)
          }));
          pagination.total = res.count;
        } else {
          message(res.msg, { type: "error" });
        }
      })
      .catch(err => {
        message(err.message, { type: "error" });
      })
      .finally(() => {
        loading.value = false;
      });
  }

  onMounted(() => {
    fetchData();
  });
  const columns: TableColumns[] = [
    {
      label: "实例规格",
      prop: "spec",
      minWidth: 150
    },
    {
      label: "实例系列",
      prop: "spec_family",
      minWidth: 150
    },
    {
      label: "CPU",
      prop: "cpu",
      minWidth: 150
    },
    {
      label: "内存",
      prop: "memory",
      minWidth: 150
    },
    {
      label: "价格",
      prop: "price",
      width: 150
    },
    {
      label: "操作",
      width: 100,
      cellRenderer: ({ row }) => {
        return (
          <el-button
            link
            type="primary"
            onClick={() => {
              onSelect(row);
            }}
          >
            选择
          </el-button>
        );
      }
    }
  ];

  return {
    dataList,
    columns,
    pagination,
    onSearch,
    handleCurrentChange,
    handleSizeChange,
    fetchData,
    loading,
    form
  };
}
