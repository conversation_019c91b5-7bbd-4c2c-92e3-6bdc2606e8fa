<template>
  <div class="flow-form">
    <el-card shadow="never" class="form-card">
      <el-form
        ref="ruleFormRef"
        :model="newFormInline.form"
        label-width="auto"
        style="max-width: 600px"
        label-position="top"
        status-icon
        size="large"
      >
        <!-- 节点名称 -->
        <el-form-item
          label="节点名称"
          :rules="[
            { required: true, message: '请输入节点名称', trigger: 'blur' },
            {
              max: 255,
              message: '节点名称长度不能超过255个字符',
              trigger: 'blur'
            }
          ]"
        >
          <el-input
            v-model="newFormInline.form.name"
            placeholder="请输入节点名称"
            clearable
          />
        </el-form-item>

        <!-- 审批者类型 -->
        <el-form-item
          label="审批者类型"
          :rules="[
            { required: true, message: '请选择审批者类型', trigger: 'change' }
          ]"
        >
          <el-select
            v-model="newFormInline.form.approver_type"
            placeholder="请选择审批者类型"
            class="custom-select"
            style="min-width: 200px"
          >
            <el-option :value="0" label="普通用户">
              <div class="select-option">
                <span>普通用户</span>
              </div>
            </el-option>
            <el-option :value="1" label="上级领导">
              <div class="select-option">
                <span>上级领导</span>
              </div>
            </el-option>
            <el-option :value="2" label="用户分组">
              <div class="select-option">
                <span>用户分组</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>

        <!-- 审批者 -->
        <el-form-item
          v-if="newFormInline.form.approver_type !== 1"
          label="审批者"
        >
          <div class="select-with-refresh">
            <el-select
              v-model="newFormInline.form.approver_ids"
              multiple
              clearable
              filterable
              placeholder="请选择审批者"
              class="custom-select"
              style="min-width: 200px"
            >
              <el-option
                v-for="item in selectApproverTypeDatas"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
                <div class="select-option">
                  <span>{{ item.name }}</span>
                </div>
              </el-option>
            </el-select>
            <el-button
              class="refresh-button"
              :icon="useRenderIcon('ep:refresh')"
              @click="refreshData"
            />
          </div>
        </el-form-item>

        <!-- 是否抄送 -->
        <el-form-item label="是否抄送" class="switch-item">
          <el-switch
            v-model="isCC"
            active-text="开启抄送"
            inactive-text="关闭抄送"
          />
        </el-form-item>

        <!-- 抄送类型 -->
        <el-form-item v-if="isCC" label="抄送类型">
          <el-select
            v-model="newFormInline.form.cc_type"
            placeholder="请选择抄送类型"
            class="custom-select"
            style="min-width: 200px"
          >
            <el-option :value="0" label="普通用户">
              <div class="select-option">
                <span>普通用户</span>
              </div>
            </el-option>
            <el-option :value="1" label="上级领导">
              <div class="select-option">
                <span>上级领导</span>
              </div>
            </el-option>
            <el-option :value="2" label="用户分组">
              <div class="select-option">
                <span>用户分组</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>

        <!-- 抄送者 -->
        <el-form-item
          v-if="newFormInline.form.cc_type !== 1 && isCC"
          label="抄送者"
        >
          <div class="select-with-refresh">
            <el-select
              v-model="newFormInline.form.cc_ids"
              multiple
              clearable
              filterable
              placeholder="请选择抄送者"
              class="custom-select"
              style="min-width: 200px"
            >
              <el-option
                v-for="item in selectCCTypeDatas"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
                <div class="select-option">
                  <span>{{ item.name }}</span>
                </div>
              </el-option>
            </el-select>
            <el-button
              class="refresh-button"
              :icon="useRenderIcon('ep:refresh')"
              @click="refreshData"
            />
          </div>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { defineProps, onBeforeMount, ref, watch } from "vue";
import type { FormInstance } from "element-plus";
import type { FlowNode } from "@/api/workflow/flow";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { getAllGroups, getAllUsers, users, groups } from "./hook";
import type { User } from "@/api/auth/user";
import type { Group } from "@/api/auth/group";

const isCC = ref<boolean>(false);
export interface FormProps {
  formInline: {
    form: FlowNode;
  };
}

const props = withDefaults(defineProps<FormProps>(), {
  formInline: () => ({
    form: undefined
  })
});

const selectApproverTypeDatas = ref<User[] | Group[]>([]);
const selectCCTypeDatas = ref<User[] | Group[]>([]);
const newFormInline = ref(props.formInline);
const ruleFormRef = ref<FormInstance>();

// 修改初始化逻辑
onBeforeMount(async () => {
  await refreshData();
  // 确保默认选择普通用户时，初始化审批者数据
  if (newFormInline.value.form.approver_type === 0) {
    selectApproverTypeDatas.value = users.value;
  }
});

// 优化刷新数据逻辑
const refreshData = async () => {
  try {
    await Promise.all([getAllGroups(), getAllUsers()]);
    // 根据当前选择的类型更新数据
    updateSelectData();
  } catch (error) {
    console.error("Failed to refresh data:", error);
  }
};

// 优化更新选择数据的逻辑
const updateSelectData = () => {
  const approverType = newFormInline.value.form.approver_type;
  const ccType = newFormInline.value.form.cc_type;

  // 设置审批者数据
  if (approverType === 1) {
    selectApproverTypeDatas.value = [];
  } else {
    selectApproverTypeDatas.value =
      approverType === 2 ? groups.value : users.value;
  }

  // 设置抄送者数据
  if (ccType === 1) {
    selectCCTypeDatas.value = [];
  } else {
    selectCCTypeDatas.value = ccType === 2 ? groups.value : users.value;
  }
};

// 优化 watch 逻辑
watch(
  () => newFormInline.value.form.approver_type,
  (newValue, oldValue) => {
    if (oldValue === newValue) return;

    // 重置审批者选择
    newFormInline.value.form.approver_ids = [];

    if (newValue === 1) {
      selectApproverTypeDatas.value = [];
      newFormInline.value.form.name = "上级领导";
    } else {
      if (oldValue === 1) {
        newFormInline.value.form.name = "";
      }
      selectApproverTypeDatas.value =
        newValue === 2 ? groups.value : users.value;
    }
  }
);

watch(
  () => newFormInline.value.form.cc_type,
  (newValue, oldValue) => {
    if (oldValue === newValue) return;

    // ���置抄送者选择
    newFormInline.value.form.cc_ids = [];
    selectCCTypeDatas.value = newValue === 2 ? groups.value : users.value;
  }
);

watch(
  () => isCC.value,
  (newValue, oldValue) => {
    if (!newValue && oldValue !== newValue) {
      // 关闭抄送时重置相关数据
      newFormInline.value.form.cc_ids = [];
      newFormInline.value.form.cc_type = 0;
    }
  }
);

defineExpose({ ruleFormRef });
</script>

<style scoped lang="scss">
.flow-form {
  padding: 12px;

  .form-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgb(0 0 0 / 5%);
  }

  :deep(.el-form-item) {
    margin-bottom: 16px;

    .el-form-item__label {
      padding-bottom: 4px;
      font-size: 14px;
      font-weight: 500;
      line-height: 1.4;
      color: #1e293b;
    }
  }

  // 选择框容器
  .select-with-refresh {
    display: flex;
    gap: 8px;
    align-items: center;

    .custom-select {
      flex: 1;
    }

    .refresh-button {
      width: 32px;
      height: 32px;
      padding: 0;
      font-size: 14px;
    }
  }

  :deep(.el-select) {
    .el-input__wrapper {
      min-height: 32px !important;
      padding: 2px 8px !important;
    }

    // 优化多选标签样式
    .el-select__tags {
      gap: 2px;
      padding: 2px 0;
      margin-right: 24px;

      .el-tag {
        height: 24px;
        padding: 0 6px;
        margin: 1px;

        .el-tag__content {
          font-size: 12px;
          line-height: 22px;
        }
      }
    }
  }

  // 优化下拉面板
  :deep(.el-select-dropdown) {
    padding: 4px;

    .el-select-dropdown__item {
      height: 32px;
      padding: 0 8px;
      margin: 1px 0;
      line-height: 32px;

      .select-option {
        gap: 6px;

        span {
          font-size: 13px;
        }
      }
    }
  }

  // 优化开关组件
  .switch-item {
    :deep(.el-switch) {
      margin-left: -8px;
      transform: scale(0.9);
    }
  }

  // 优化输入框
  :deep(.el-input) {
    .el-input__wrapper {
      min-height: 32px;
    }
  }
}
</style>
