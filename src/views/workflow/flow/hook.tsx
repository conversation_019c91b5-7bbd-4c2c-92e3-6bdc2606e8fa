import {
  type Flow,
  type FlowNode,
  getAllFlowsAPI,
  updateFlowAPI,
  type UpdateNodesForm
} from "@/api/workflow/flow";
import { message } from "@/utils/message";
import { h, ref } from "vue";
import Uform from "./Uform.vue";
import { addDrawer } from "@/components/ReDrawer";
import { getAllUsersAPI, type User } from "@/api/auth/user";
import { getAllGroupsAPI, type Group } from "@/api/auth/group";
import { addDialog } from "@/components/ReDialog";

export const flows = ref<Flow[]>([]);

export const getFlows = () => {
  getAllFlowsAPI().then(res => {
    if (res.success) {
      flows.value = res.data;
    } else {
      message(res.msg, { type: "error" });
    }
  });
};

const childrenRef = ref(null);
const updateForm = ref<FlowNode>();
const updateNodesForm = ref<UpdateNodesForm>();
export const updateFlow = (flow: any, flowIndex: number, prenode: string) => {
  updateForm.value = {
    id: 0,
    name: "",
    flow_id: flow.id,
    flow_index: 0,
    approver_type: 0,
    approver_ids: [],
    approvers_names: "",
    cc_type: 0,
    cc_ids: [],
    cc_names: ""
  };
  addDrawer({
    headerRenderer: ({ titleId, titleClass }) => (
      <div class="flex flex-row justify-between">
        <h4 id={titleId} class={titleClass}>
          <b>
            <el-text type="warning">{flow.name}</el-text>
          </b>
          在
          <b>
            <el-text type="warning"> {prenode}</el-text>
          </b>
          之后添加流程
        </h4>
      </div>
    ),
    contentRenderer: () =>
      h(Uform, {
        formInline: {
          form: updateForm.value
        },
        ref: childrenRef
      }),
    beforeSure: done => {
      if (!childrenRef.value.ruleFormRef) return;
      childrenRef.value.ruleFormRef.validate((valid: boolean) => {
        if (valid) {
          if (
            updateForm.value.approver_ids.length == 0 &&
            updateForm.value.approver_type !== 1
          ) {
            message("请选择审批人", { type: "error" });
          } else {
            let newNode = [];
            if (flowIndex === 0) {
              newNode.push(updateForm.value);
              flow.nodes.forEach(item => {
                newNode.push(item);
              });
            } else {
              flow.nodes.forEach((item, index) => {
                if (flowIndex > index + 1) {
                  newNode.push(item);
                } else if (flowIndex === index + 1) {
                  newNode.push(item);
                  newNode.push(updateForm.value);
                } else {
                  newNode.push(item);
                }
              });
            }
            newNode.forEach((item, index) => {
              item.flow_index = index + 1;
            });
            updateNodesForm.value = {
              flow_id: flow.id,
              nodes: newNode
            };
            updateFlowAPI(updateNodesForm.value)
              .then(res => {
                if (res.success) {
                  message(res.msg, { type: "success" });
                  getFlows();
                  done();
                } else {
                  message(res.msg, { type: "error" });
                }
              })
              .catch(error => {
                message(error, { type: "error" });
              });
          }
        } else {
          message("请检查表单数据", { type: "warning" });
        }
      });
    }
  });
};

export const deleteNode = (flow: any, node: FlowNode) => {
  addDialog({
    headerRenderer: ({ titleId, titleClass }) => (
      <div class="flex flex-row justify-between">
        <h4 id={titleId} class={titleClass}>
          删除{" "}
          <b>
            <el-text type="danger">{flow.name}</el-text>
          </b>
          在
          <b>
            <el-text type="danger"> {node.name}</el-text>
          </b>
          的流程
        </h4>
      </div>
    ),
    contentRenderer: () => <div class="flex flex-col">确认删除？</div>,
    beforeSure: done => {
      let newNode = [];
      flow.nodes.forEach(item => {
        if (item.flow_index !== node.flow_index) {
          newNode.push(item);
        }
      });
      flow.nodes = newNode;
      updateNodesForm.value = {
        flow_id: flow.id,
        nodes: newNode
      };
      updateFlowAPI(updateNodesForm.value)
        .then(res => {
          if (res.success) {
            message(res.msg, { type: "success" });
            getFlows();
            done();
          } else {
            message(res.msg, { type: "error" });
          }
        })
        .catch(error => {
          message(error, { type: "error" });
        });
    }
  });
};

export const users = ref<User[]>([]);
export const groups = ref<Group[]>([]);
export const getAllUsers = () => {
  getAllUsersAPI().then(res => {
    if (res.success) {
      users.value = res.data as User[];
      users.value.forEach(item => {
        item.name = item.name + "(" + item.username + ")";
      });
    } else {
      message(res.msg, { type: "error" });
    }
  });
};

export const getAllGroups = () => {
  getAllGroupsAPI().then(res => {
    if (res.success) {
      groups.value = res.data;
    } else {
      message(res.msg, { type: "error" });
    }
  });
};
