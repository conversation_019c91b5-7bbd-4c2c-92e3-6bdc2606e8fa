<script lang="ts" setup>
import { onMounted } from "vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import {
  flows,
  getFlows,
  updateFlow,
  deleteNode,
  getAllGroups,
  getAllUsers
} from "./hook";

onMounted(() => {
  getFlows();
  getAllGroups();
  getAllUsers();
});
</script>

<template>
  <el-card shadow="never">
    <div class="card-header">
      <div class="header-title">工单流程</div>
      <div class="header-actions">
        <el-button
          type="primary"
          link
          :icon="useRenderIcon('ep:refresh')"
          @click="getFlows"
        >
          刷新
        </el-button>
      </div>
    </div>
    <el-row>
      <el-col :span="24">
        <el-collapse class="flow-collapse">
          <el-collapse-item
            v-for="(flow, index) in flows"
            :key="index"
            :title="flow.name"
            :name="flow.name"
          >
            <div class="flow-container">
              <!-- 申请人节点 -->
              <div class="flow-node">
                <el-card class="node-card applicant-node">
                  <template #header>
                    <div class="node-header">
                      <b>申请人</b>
                    </div>
                  </template>
                  <div class="node-body">
                    <iconify-icon-online icon="ep:user" class="node-icon" />
                    <el-text type="info">发起申请</el-text>
                  </div>
                  <template #footer>
                    <div class="node-footer">
                      <el-button
                        link
                        type="warning"
                        :icon="useRenderIcon('ep:edit')"
                        @click="() => updateFlow(flow, 0, '申请人')"
                      >
                        添加
                      </el-button>
                    </div>
                  </template>
                </el-card>
              </div>

              <!-- 流程节点 -->
              <div v-for="(node, ii) in flow.nodes" :key="ii" class="flow-node">
                <el-card class="node-card process-node">
                  <template #header>
                    <div class="node-header">
                      <el-text type="primary"
                        ><b>{{ node.name }}</b></el-text
                      >
                    </div>
                  </template>
                  <div class="node-body">
                    <div class="approval-info">
                      <div class="info-item">
                        <div class="info-label">
                          <div class="label-content">
                            <iconify-icon-online icon="ep:user" />
                            <span>审批人：</span>
                          </div>
                          <div class="info-content">
                            {{ node.approvers_names }}
                          </div>
                        </div>
                      </div>
                      <div v-if="node.cc_names" class="info-item">
                        <div class="info-label">
                          <div class="label-content">
                            <iconify-icon-online icon="ep:user" />
                            <span>抄送给：</span>
                          </div>
                          <div class="info-content">{{ node.cc_names }}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <template #footer>
                    <div class="node-footer">
                      <el-button
                        link
                        type="warning"
                        :icon="useRenderIcon('ep:edit')"
                        @click="() => updateFlow(flow, ii + 1, node.name)"
                      >
                        添加
                      </el-button>
                      <el-button
                        link
                        type="danger"
                        :icon="useRenderIcon('ep:edit')"
                        @click="() => deleteNode(flow, node)"
                      >
                        删除
                      </el-button>
                    </div>
                  </template>
                </el-card>
              </div>

              <!-- 结束节点 -->
              <div class="flow-node">
                <el-card class="node-card end-node">
                  <template #header>
                    <div class="node-header">
                      <b>结束</b>
                    </div>
                  </template>
                  <div class="node-body">
                    <el-text type="info" :icon="useRenderIcon('ep:bell')">
                      通知申请人
                    </el-text>
                  </div>
                </el-card>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </el-col>
    </el-row>
  </el-card>
</template>

<style scoped lang="scss">
.flow-collapse {
  margin: 12px;

  :deep(.el-collapse-item) {
    margin-bottom: 12px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgb(0 0 0 / 4%);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 16px rgb(0 0 0 / 8%);
      transform: translateY(-2px);
    }

    .el-collapse-item__header {
      padding: 12px 16px;
      font-size: 16px;
      font-weight: 600;
      color: #2c3e50;
      border-radius: 8px 8px 0 0;
      transition: all 0.3s ease;

      &:hover {
        color: var(--el-color-primary);
      }
    }
  }
}

.flow-container {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
  align-items: flex-start;
  min-height: 200px;
  padding: 24px 16px;

  &::before {
    position: absolute;
    top: 0;
    left: 50%;
    width: calc(100% - 48px);
    height: 100%;
    content: "";
    border-radius: 12px;
    transform: translateX(-50%);
  }
}

.node-card {
  position: relative;
  z-index: 1;
  min-width: 240px;
  border-radius: 12px;
  box-shadow:
    0 4px 6px -1px rgb(0 0 0 / 10%),
    0 2px 4px -1px rgb(0 0 0 / 6%);
  transition: all 0.3s ease;

  &:hover {
    box-shadow:
      0 10px 15px -3px rgb(0 0 0 / 10%),
      0 4px 6px -2px rgb(0 0 0 / 5%);
    transform: translateY(-2px);
  }

  .node-header {
    padding: 12px;
    font-size: 14px;
    text-align: center;
    border-radius: 12px 12px 0 0;
  }

  .node-body {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 16px;
  }

  &.applicant-node {
    border-left: 4px solid #10b981;

    .node-header {
      color: #047857;
    }
  }

  &.process-node {
    border-left: 4px solid #3b82f6;

    .node-header {
      color: #1d4ed8;
    }
  }

  &.end-node {
    border-left: 4px solid #f59e0b;

    .node-header {
      color: #b45309;
    }
  }
}

.approval-info {
  .info-item {
    padding: 12px;
    margin-bottom: 8px;
    border-radius: 8px;
    transition: all 0.3s ease;

    &:hover {
      border-color: #3b82f6;
      box-shadow: 0 2px 4px rgb(59 130 246 / 10%);
    }

    .label-content {
      display: flex;
      gap: 6px;
      align-items: center;
      margin-bottom: 6px;

      .iconify-icon {
        padding: 4px;
        font-size: 16px;
        color: #3b82f6;
        border-radius: 8px;
        transition: all 0.3s ease;
      }

      span {
        font-size: 13px;
        font-weight: 500;
        color: #64748b;
      }
    }

    .info-content {
      padding: 6px 10px;
      margin-left: 24px;
      font-size: 13px;
      line-height: 1.6;
      color: #1e293b;
    }
  }
}

// 连接线样式优化
.flow-node {
  position: relative;
  z-index: 1;

  &:not(:last-child)::after {
    position: absolute;
    top: 50%;
    right: -24px;
    width: 24px;
    height: 2px;
    content: "";
    background: linear-gradient(90deg, #3b82f6, #60a5fa);
  }

  &:not(:last-child)::before {
    position: absolute;
    top: calc(50% - 5px);
    right: -24px;
    z-index: 2;
    width: 10px;
    height: 10px;
    content: "";
    border-top: 2px solid #3b82f6;
    border-right: 2px solid #3b82f6;
    transform: rotate(45deg);
  }
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px;
  margin-bottom: 12px;

  .header-title {
    font-size: 16px;
    font-weight: 600;
    color: #1e293b;
  }

  .header-actions {
    .el-button {
      padding: 6px 12px;
      font-size: 13px;
      color: #3b82f6;
      transition: all 0.3s ease;

      &:hover {
        color: #2563eb;
        border-radius: 6px;
        transform: translateY(-2px);
      }

      .iconify-icon {
        margin-right: 3px;
        font-size: 14px;
      }
    }
  }
}

// 节点底部按钮样式
.node-footer {
  display: flex;
  gap: 8px;
  justify-content: center;
  padding: 8px;

  .el-button {
    padding: 6px 12px;
    border-radius: 6px;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
    }

    &[type="warning"] {
      color: #f59e0b;

      &:hover {
        color: #d97706;
        background: #fef3c7;
      }
    }

    &[type="danger"] {
      color: #ef4444;

      &:hover {
        color: #dc2626;
        background: #fee2e2;
      }
    }
  }
}
</style>
