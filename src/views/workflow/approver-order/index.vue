<script setup lang="ts">
import { onBeforeMount, ref, onMounted } from "vue";
import { useRole } from "./hook";
import { PureTableBar } from "@/components/RePureTableBar";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import Refresh from "@iconify-icons/ep/refresh";
import { OrderStatuses, OrderExtTypes } from "@/config/order-enum";
import { useRoute } from "vue-router";
import { parmaQueryNumber } from "@/utils/parmaQuery";

defineOptions({
  name: "ApproverOrders"
});

const formRef = ref();
const tableRef = ref();
const isMobile = ref(false);

const {
  form,
  loading,
  columns,
  dataList,
  pagination,
  onSearch,
  resetForm,
  handleSizeChange,
  handleCurrentChange,
  users,
  getAllUsers
} = useRole();

// 添加行样式
const tableRowClassName = ({ row }) => {
  return row.critical ? "critical-row" : "";
};

// 检测是否为移动设备
const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768;
};

onMounted(() => {
  checkMobile();
  window.addEventListener("resize", checkMobile);
});

onBeforeMount(() => {
  const { query } = useRoute();
  const { ext_type, extType } = query;
  if (query) {
    if (ext_type) {
      form.ext_type = parmaQueryNumber(ext_type);
    } else if (extType) {
      form.ext_type = parmaQueryNumber(extType);
    }
  }
});
</script>

<template>
  <div class="main">
    <el-card class="search-card">
      <el-form
        ref="formRef"
        :inline="!isMobile"
        :model="form"
        class="search-form"
        @submit.prevent
      >
        <el-form-item label="关键字" prop="keyword">
          <el-input
            v-model="form.keyword"
            placeholder="请输入关键字"
            clearable
            :class="isMobile ? 'w-full' : '!w-[250px]'"
            :size="isMobile ? 'small' : 'default'"
            @keyup.enter="onSearch"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select
            v-model="form.status"
            placeholder="请选择状态"
            clearable
            :class="isMobile ? 'w-full' : '!w-[120px]'"
            :size="isMobile ? 'small' : 'default'"
            @change="onSearch"
          >
            <el-option :key="100" label="待我审批" :value="100" />
            <el-option
              v-for="status in OrderStatuses"
              :key="status[0]"
              :label="status[1]"
              :value="status[0]"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="工单分类" prop="ext_type">
          <el-select
            v-model="form.ext_type"
            placeholder="请选择工单分类"
            clearable
            filterable
            :class="isMobile ? 'w-full' : '!w-[220px]'"
            :size="isMobile ? 'small' : 'default'"
            @change="onSearch"
          >
            <el-option
              v-for="orderType in OrderExtTypes"
              :key="orderType[0]"
              :label="orderType[1]"
              :value="orderType[0]"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="申请人" prop="applicant_id">
          <div class="applicant-wrapper">
            <el-select
              v-model="form.applicant_id"
              placeholder="请选择申请人"
              clearable
              filterable
              :class="isMobile ? 'w-full' : '!w-[220px]'"
              :size="isMobile ? 'small' : 'default'"
              @change="onSearch"
            >
              <el-option
                v-for="user in users"
                :key="user.id"
                :label="`${user.name}（${user.username}）`"
                :value="user.id"
              />
            </el-select>
            <el-button
              type="primary"
              :icon="useRenderIcon('ep-refresh')"
              :loading="loading"
              link
              :size="isMobile ? 'small' : 'default'"
              @click="getAllUsers"
            >
              刷新
            </el-button>
          </div>
        </el-form-item>
        <el-form-item class="action-buttons">
          <el-button
            type="primary"
            :icon="useRenderIcon('ri:search-line')"
            :loading="loading"
            class="search-button"
            :size="isMobile ? 'small' : 'default'"
            style="
              background: linear-gradient(135deg, #409eff 0%, #4facff 100%);
              border: none;
              box-shadow: 0 4px 12px rgb(64 158 255 / 30%);
              transition: all 0.3s ease;
            "
            @click="onSearch"
          >
            搜索
          </el-button>
          <el-button
            :icon="useRenderIcon(Refresh)"
            class="reset-button"
            :size="isMobile ? 'small' : 'default'"
            style="
              color: #606266;
              border-color: #dcdfe6;
              transition: all 0.3s ease;
            "
            @click="resetForm(formRef)"
          >
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <PureTableBar title="审批工单" :columns="columns" @refresh="onSearch">
      <template #default="{ size, dynamicColumns }">
        <div class="table-container">
          <pure-table
            ref="tableRef"
            row-key="id"
            align-whole="left"
            table-layout="auto"
            :loading="loading"
            :size="isMobile ? 'small' : size"
            :minHeight="isMobile ? 300 : 500"
            :data="dataList"
            :columns="dynamicColumns"
            :pagination="{ ...pagination, size: isMobile ? 'small' : size }"
            :header-cell-style="{
              fontWeight: '600',
              padding: isMobile ? '8px 4px' : '12px 10px'
            }"
            :row-style="{
              cursor: 'pointer',
              transition: 'background-color 0.2s ease'
            }"
            :cell-style="{
              padding: isMobile ? '8px 4px' : '12px 10px'
            }"
            :row-class-name="tableRowClassName"
            border
            stripe
            @page-size-change="handleSizeChange"
            @page-current-change="handleCurrentChange"
          />
        </div>
      </template>
    </PureTableBar>
  </div>
</template>

<style lang="scss" scoped>


/* 移动端适配 */
@media screen and (width <= 768px) {
  .search-card {
    margin-bottom: 12px;
    border-radius: 8px;
  }

  .search-form {
    flex-direction: column;
    gap: 8px;
    width: 100%;

    .el-form-item {
      width: 100%;
      margin-right: 0;
      margin-bottom: 8px;
    }
  }

  .action-buttons {
    display: flex;
    justify-content: space-between;
    width: 100%;
    margin-top: 8px;

    .search-button,
    .reset-button {
      flex: 1;
      margin: 0 4px;
    }
  }

  :deep(.el-pagination) {
    flex-wrap: wrap;
    justify-content: center;
    padding: 8px 4px;
  }

  :deep(.el-table .cell) {
    padding-right: 4px;
    padding-left: 4px;
  }

  :deep(.el-table__header) th {
    padding: 6px 0;
  }

  :deep(.el-table__body) td {
    padding: 6px 0;
  }
}

.main {
  width: 100%;
}

.search-card {
  margin-bottom: 16px;
  overflow: hidden;
  border-radius: 12px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;

  .el-form-item {
    margin-bottom: 0;

    &__label {
      font-weight: 500;
      color: #606266;
    }
  }
}

.applicant-wrapper {
  display: flex;
  align-items: center;
  width: 100%;
}

.action-buttons {
  display: flex;
  justify-content: flex-start;
  margin-top: 8px;
}

.search-button,
.reset-button {
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.table-container {
  width: 100%;
  overflow-x: auto;
}

:deep(.pure-table) {
  overflow: hidden;
  border: 1px solid #e4e7ed;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 4%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-synthesis: none;

  .el-table__header {
    background: linear-gradient(135deg, #f5f7fa 0%, #f8f9fb 100%) !important;
  }

  .el-table__row {
    transition: all 0.2s ease;

    &:hover {
      box-shadow: 0 2px 8px rgb(0 0 0 / 6%);
    }
  }
}
</style>
