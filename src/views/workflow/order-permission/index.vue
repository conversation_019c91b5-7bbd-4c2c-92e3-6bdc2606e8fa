<template>
  <div class="permission-container">
    <el-row :gutter="20">
      <el-col :span="18">
        <el-card class="main-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <h2 class="title">
                <iconify-icon-online
                  icon="material-symbols:security"
                  class="icon"
                />
                权限工单
              </h2>
            </div>
          </template>
          <el-form
            ref="ruleFormRef"
            :model="form"
            class="permission-form"
            status-icon
            label-position="top"
            size="large"
          >
            <el-form-item
              label="工单类型"
              :rules="[
                { required: true, message: '请选择工单类型', trigger: 'change' }
              ]"
              prop="order_type"
            >
              <div class="order-type-container">
                <el-radio-group
                  v-model="form.order_type"
                  class="order-type-radio-group"
                >
                  <el-radio-button
                    v-for="(orderType, index) in PermissionOrderTypes"
                    :key="index"
                    :label="orderType"
                    :value="orderType"
                    class="order-type-radio-button"
                  >
                    <div class="radio-button-content">
                      <iconify-icon-online
                        :icon="getTypeIcon(orderType)"
                        class="type-icon"
                      />
                      <span>{{ orderType }}</span>
                    </div>
                  </el-radio-button>
                </el-radio-group>
              </div>
            </el-form-item>

            <el-form-item
              v-if="form.order_type === '系统权限申请'"
              label="主机IP"
              prop="hosts"
              :rules="[
                { required: true, message: '请输入主机IP', trigger: 'blur' }
              ]"
            >
              <div class="compact-list-container">
                <div class="compact-list-header">
                  <span>IP列表</span>
                </div>
                <div class="compact-list-content">
                  <el-select
                    v-model="form.hosts"
                    multiple
                    placeholder="输入IP后回车"
                    allow-create
                    filterable
                    default-first-option
                    clearable
                    size="default"
                    :reserve-keyword="false"
                    class="compact-select"
                    collapse-tags
                    collapse-tags-tooltip
                  />
                  <div class="compact-list-help">最多可添加50个IP地址</div>
                </div>
              </div>
            </el-form-item>

            <el-form-item
              v-if="form.order_type === 'OSS权限'"
              label="Bucket"
              prop="bucket"
              :rules="[
                { required: true, message: '请输入bucket', trigger: 'blur' }
              ]"
            >
              <div class="compact-list-container">
                <div class="compact-list-header">
                  <span>OSS Bucket信息</span>
                </div>
                <div class="compact-list-content">
                  <el-input
                    v-model="form.bucket"
                    placeholder="请输入bucket"
                    size="default"
                    class="compact-input"
                  />
                </div>
              </div>
            </el-form-item>

            <el-form-item
              v-if="form.order_type === 'DGC(数据仓库)'"
              label="是否敏感"
              prop="is_sensitive"
              class="sensitive-switch"
            >
              <div class="switch-container">
                <el-switch v-model="form.is_sensitive" />
                <span class="switch-label">{{
                  form.is_sensitive ? "敏感" : "非敏感"
                }}</span>
              </div>
            </el-form-item>

            <el-form-item
              label="申请理由"
              :rules="[
                { required: true, message: '请输入申请理由', trigger: 'blur' },
                {
                  max: 5000,
                  message: '理由不能超过5000个字符',
                  trigger: 'blur'
                }
              ]"
              prop="content"
            >
              <el-input
                v-model="form.content"
                type="textarea"
                show-word-limit
                maxlength="5000"
                :autosize="{ minRows: 6, maxRows: 16 }"
                class="content-textarea"
                :placeholder="
                  form.order_type === '应用发布权限'
                    ? '请填写以下信息：\n应用名称：\n应用gitlab地址：'
                    : form.order_type === 'Wiki权限'
                    ? '请填写以下信息：\nwiki地址：'
                    : form.order_type === 'k8s权限申请'
                    ? '请填写以下信息：\n集群名称：\n命名空间：'
                    : form.order_type === '阿里云子账号'
                    ? '请填写以下信息：\n业务用途：\n所需服务：'
                    : form.order_type === '华为云子账号'
                    ? '请填写以下信息：\n业务用途：\n所需服务：'
                    : form.order_type === 'OSS权限'
                    ? '请填写以下信息：\n业务用途：\nBucket名称：'
                    : form.order_type === 'CDN权限'
                    ? '请填写以下信息：\n域名：\n业务用途：'
                    : '请输入申请理由'
                "
              />
            </el-form-item>

            <el-form-item class="action-buttons">
              <el-button type="primary" class="submit-button" @click="onSubmit">
                <iconify-icon-online
                  icon="material-symbols:send"
                  style="margin-right: 4px"
                />
                提交
              </el-button>
              <RouterLink :to="{ name: '我的工单' }">
                <el-button class="cancel-button">
                  <iconify-icon-online
                    icon="material-symbols:cancel"
                    style="margin-right: 4px"
                  />
                  取消
                </el-button>
              </RouterLink>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
      <el-col :span="6">
        <ApprovalFlow :processes="processes" />
      </el-col>
    </el-row>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, watch } from "vue";
import { applyOrderAPI, type OrderForm } from "@/api/workflow/order-permission";
import { ElMessageBox, type FormInstance } from "element-plus";
import { message } from "@/utils/message";
import { getProcesses, processes } from "./hook";
import { PermissionOrderTypes } from "@/config/order-enum";
import ApprovalFlow from "../components/ApprovalFlow.vue";

const form = ref<OrderForm>({
  order_type: "DGC(数据仓库)",
  content: "",
  bucket: "",
  hosts: [],
  os: [],
  is_sensitive: false
});
const ruleFormRef = ref<FormInstance>();

// 为不同类型提供对应图标
const getTypeIcon = (type: string): string => {
  const iconMap: Record<string, string> = {
    "DGC(数据仓库)": "material-symbols:database",
    系统权限申请: "material-symbols:computer",
    k8s权限申请: "mdi:kubernetes",
    阿里云子账号: "mdi:cloud",
    华为云子账号: "mdi:cloud-check",
    应用发布权限: "material-symbols:deployed-code",
    Wiki权限: "material-symbols:description",
    OSS权限: "typcn:cloud-storage",
    CDN权限: "material-symbols:cloud-sync",
    其他权限: "material-symbols:more-horiz"
  };

  return iconMap[type] || "material-symbols:question-mark";
};

const onSubmit = async () => {
  const valid = await new Promise(resolve => {
    ruleFormRef.value.validate((valid: boolean) => {
      resolve(valid);
    });
  });
  if (valid) {
    try {
      await ElMessageBox.confirm("是否确认提交工单？", "提交确认", {
        confirmButtonText: "提交",
        cancelButtonText: "取消",
        type: "warning",
        draggable: true,
        center: true,
        customClass: "custom-message-box",
        icon: "Warning"
      });
      const res = await applyOrderAPI(form.value);
      if (res.success) {
        message("提交成功", { type: "success" });
        setTimeout(() => {
          window.location.href = "/workflow/my/order";
        }, 300);
      } else {
        message(res.msg || "提交失败", { type: "error" });
      }
    } catch (error) {
      if (error !== "cancel") {
        message(error.message || "操作取消", { type: "warning" });
      }
    }
  } else {
    message("请检查表单数据", { type: "warning" });
  }
};

onMounted(() => {
  getProcesses(form.value.order_type);
});

watch(
  () => form.value.order_type,
  () => {
    getProcesses(form.value.order_type);
  }
);
</script>

<style scoped>
.permission-container {
  padding: 20px;
}

.main-card {
  border-radius: 8px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 8%);
  transition: all 0.3s ease;
}

.main-card:hover {
  box-shadow: 0 6px 20px rgb(0 0 0 / 12%);
}

.card-header {
  display: flex;
  align-items: center;
  padding: 8px 0;
}

.title {
  display: flex;
  align-items: center;
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.icon {
  margin-right: 12px;
  font-size: 24px;
  color: #409eff;
}

.permission-form {
  padding: 10px;
  margin-top: 20px;
}

/* 工单类型样式优化 */
.order-type-container {
  width: 100%;
  margin-bottom: 8px;
  overflow-x: auto;
}

.order-type-radio-group {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.order-type-radio-button {
  margin-bottom: 8px;
}

.order-type-radio-button :deep(.el-radio-button__inner) {
  display: flex;
  align-items: center;
  justify-content: center;
  height: auto;
  min-height: 38px;
  padding: 8px 16px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgb(0 0 0 / 5%);
  transition: all 0.3s;
}

.order-type-radio-button
  :deep(.el-radio-button__original-radio:checked + .el-radio-button__inner) {
  font-weight: 500;
  color: #409eff;
  background-color: #ecf5ff;
  border-color: #409eff;
  box-shadow: 0 0 0 1px #409eff;
}

.order-type-radio-button:not(.is-active):hover :deep(.el-radio-button__inner) {
  background-color: #f5f7fa;
  border-color: #c0c4cc;
}

.radio-button-content {
  display: flex;
  gap: 6px;
  align-items: center;
}

.type-icon {
  font-size: 18px;
}

.switch-container {
  display: flex;
  align-items: center;
}

.switch-label {
  margin-left: 10px;
  color: #606266;
}

.content-textarea {
  border-radius: 4px;
  transition: all 0.3s;
}

.content-textarea:focus {
  box-shadow: 0 0 0 2px rgb(64 158 255 / 20%);
}

.action-buttons {
  display: flex;
  gap: 16px;
  padding-top: 20px;
  margin-top: 32px;
  border-top: 1px dashed #ebeef5;
}

.submit-button {
  display: flex;
  align-items: center;
  padding: 12px 24px;
  margin-right: 10px;
  font-weight: 500;
}

.submit-button:hover {
  background-color: #66b1ff;
  box-shadow: 0 2px 8px rgb(64 158 255 / 20%);
}

.cancel-button {
  display: flex;
  align-items: center;
  padding: 12px 24px;
  font-weight: 500;
}

.cancel-button:hover {
  background-color: #f2f6fc;
}

.custom-select {
  width: 100%;
}

.sensitive-switch {
  margin-bottom: 24px;
}

/* Custom message box styling */
:deep(.custom-message-box) {
  padding: 30px 30px 20px;
  border-radius: 8px;
}

:deep(.custom-message-box .el-message-box__status) {
  top: 30px;
  font-size: 24px !important;
}

:deep(.custom-message-box .el-message-box__header) {
  padding: 0 0 10px;
}

:deep(.custom-message-box .el-message-box__title) {
  font-size: 16px;
  font-weight: 500;
}

:deep(.custom-message-box .el-message-box__content) {
  padding: 20px 0;
  font-size: 14px;
}

:deep(.custom-message-box .el-message-box__btns) {
  padding: 10px 0 0;
}

:deep(.custom-message-box .el-message-box__btns button) {
  min-width: 80px;
  padding: 8px 20px;
  margin-left: 12px;
  font-size: 14px;
  border-radius: 4px;
}

:deep(.custom-message-box .el-message-box__btns .el-button--primary) {
  background-color: var(--el-color-warning);
  border-color: var(--el-color-warning);
}

:deep(.custom-message-box .el-message-box__btns .el-button--primary:hover) {
  background-color: var(--el-color-warning-light-3);
  border-color: var(--el-color-warning-light-3);
}

/* 添加表单元素优化样式 */
:deep(.el-input__inner) {
  transition: all 0.3s;
}

:deep(.el-input__inner:focus) {
  border-color: #409eff;
}

:deep(.el-textarea__inner) {
  transition: all 0.3s;
}

:deep(.el-textarea__inner:focus) {
  border-color: #409eff;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-select .el-input) {
  transition: all 0.3s;
}

:deep(.el-date-editor.el-input) {
  transition: all 0.3s;
}

/* 紧凑型列表容器 */
.compact-list-container {
  position: relative;
  width: 100%;
  padding: 0;
  margin: 0 0 12px;
  overflow: hidden;
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 8px;
  box-shadow: 0 1px 4px 0 rgb(0 0 0 / 2%);
}

.compact-list-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 28px;
  padding: 4px 8px;
  font-size: 12px;
  font-weight: 500;
  color: #606266;
  background-color: #f5f7fa;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.compact-list-content {
  padding: 8px;
  background-color: #fff;
}

.compact-list-help {
  margin-top: 4px;
  font-size: 12px;
  color: #909399;
}

.compact-select {
  --el-select-input-focus-border-color: #409eff;

  width: 100%;
}

.compact-select :deep(.el-input__wrapper) {
  padding: 0 8px;
}

.compact-select :deep(.el-input__inner) {
  height: 32px;
  font-size: 13px;
}

.compact-select :deep(.el-select__tags) {
  padding: 2px 0;
}

.compact-input {
  --el-input-height: 32px;

  font-size: 13px;
}

.compact-input :deep(.el-input__wrapper) {
  padding: 0 8px;
}
</style>
