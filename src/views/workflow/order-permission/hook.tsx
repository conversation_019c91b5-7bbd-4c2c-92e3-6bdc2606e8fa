import { getProcessesAPI, type Process } from "@/api/workflow/process";
import { OrderType } from "@/config/order-enum";
import { message } from "@/utils/message";
import { ref } from "vue";

export const processes = ref<Process[]>([]);
export const getProcesses = (orderTypeStr: string) => {
  let orderType = OrderType.PermissionOrderType;
  if (orderTypeStr === "DGC(数据仓库)") {
    orderType = OrderType.PermissionOrderDGCType;
  } else if (
    orderTypeStr === "OSS权限" ||
    orderTypeStr === "CDN权限" ||
    orderTypeStr === "阿里云子账号" ||
    orderTypeStr === "华为云子账号"
  ) {
    orderType = OrderType.CloudAccountOrderType;
  }
  getProcessesAPI(orderType, undefined)
    .then(res => {
      if (res.success) {
        processes.value = res.data;
      } else {
        message(res.msg, { type: "error" });
      }
    })
    .catch(error => {
      message("请求失败" + error, { type: "error" });
    });
};
