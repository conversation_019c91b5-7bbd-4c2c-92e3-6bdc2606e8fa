import { getAllProjectsAPI, type Project } from "@/api/database/mysql/project";
import { getAllMyOrdersAPI } from "@/api/workflow/my-order";
import type { Order } from "@/api/workflow/order";
import { getProcessesAPI, type Process } from "@/api/workflow/process";
import { OrderExtType, OrderType } from "@/config/order-enum";
import { message } from "@/utils/message";
import { ref } from "vue";

export const processes = ref<Process[]>([]);
export const getProcesses = () => {
  getProcessesAPI(OrderType.MySQLOrderType, undefined)
    .then(res => {
      if (res.success) {
        processes.value = res.data;
      } else {
        message(res.msg, { type: "error" });
      }
    })
    .catch(error => {
      message("请求失败" + error, { type: "error" });
    });
};

export const projects = ref<Project[]>([]);

export const getProjects = () => {
  getAllProjectsAPI()
    .then(res => {
      if (res.success) {
        projects.value = res.data;
      } else {
        message(res.msg, { type: "error" });
      }
    })
    .catch(error => {
      message("请求失败" + error, { type: "error" });
    });
};

export const orders = ref<Order[]>([]);

export const getOrders = () => {
  getAllMyOrdersAPI(OrderExtType.DatabaseOrderType)
    .then(res => {
      if (res.success) {
        orders.value = res.data;
      } else {
        message(res.msg, { type: "error" });
      }
    })
    .catch(error => {
      message("请求失败" + error, { type: "error" });
    });
};
