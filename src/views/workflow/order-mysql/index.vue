<template>
  <div>
    <el-row :gutter="20" class="order-container">
      <el-col :span="16">
        <el-card class="main-card" shadow="hover" style="margin-left: 20px">
          <template #header>
            <div class="card-header">
              <h2 class="title">
                <iconify-icon-online
                  icon="material-symbols:database"
                  class="icon"
                />
                MySQL数据库工单
              </h2>
            </div>
          </template>

          <el-form
            ref="ruleFormRef"
            :model="form"
            class="order-form"
            status-icon
            label-position="top"
            size="large"
          >
            <el-form-item
              label="标题"
              prop="title"
              :rules="[
                { required: true, message: '请输入标题', trigger: 'blur' },
                {
                  max: 255,
                  message: '标题长度不能超过255个字符',
                  trigger: 'blur'
                }
              ]"
            >
              <el-input
                v-model="form.title"
                placeholder="请输入标题"
                maxlength="255"
                show-word-limit
                clearable
              />
            </el-form-item>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item
                  label="工单类型"
                  prop="order_type"
                  :rules="[
                    {
                      required: true,
                      message: '请选择工单类型',
                      trigger: 'change'
                    }
                  ]"
                >
                  <el-select
                    v-model="form.order_type"
                    placeholder="请选择工单类型"
                    class="custom-select"
                  >
                    <el-option
                      v-for="(orderType, index) in MySQLOrderTypes"
                      :key="index"
                      :label="orderType"
                      :value="orderType"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  label="环境"
                  prop="env"
                  :rules="[
                    { required: true, message: '请选择环境', trigger: 'change' }
                  ]"
                >
                  <el-select
                    v-model="form.env"
                    placeholder="请选择环境"
                    class="custom-select"
                  >
                    <el-option
                      v-for="env in ENVs"
                      :key="env[0]"
                      :label="env[1]"
                      :value="env[0]"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item
                  v-if="form.order_type !== '新建数据库'"
                  label="库名"
                  prop="db_name"
                  :rules="[
                    { required: true, message: '请选择库名', trigger: 'change' }
                  ]"
                >
                  <el-select
                    v-model="form.db_name"
                    placeholder="请选择库名"
                    filterable
                    clearable
                    class="custom-select"
                    @change="onDbNameChange"
                  >
                    <el-option
                      v-for="(project, index) in projectsDBs"
                      :key="index"
                      :label="`${project.db_name} （所属项目：${project.project_name}）`"
                      :value="`${project.project_name}:${project.db_name}`"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item
                  v-if="form.order_type === '新建数据库'"
                  label="库名前缀"
                  prop="db_prefix"
                >
                  <el-input
                    v-model="dbPrefix"
                    placeholder="请输入库名前缀（可选）"
                    maxlength="32"
                    clearable
                  >
                    <template #append>
                      <el-tooltip
                        content="数据库名称会自动添加此前缀，例如：prefix_dbname"
                        placement="top"
                      >
                        <el-icon><QuestionFilled /></el-icon>
                      </el-tooltip>
                    </template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item
                  v-if="
                    form.order_type === '个人账号开通' ||
                    form.order_type === '数据库应用账号开通'
                  "
                  prop="permission"
                  :rules="[
                    { required: true, message: '请选择权限', trigger: 'change' }
                  ]"
                  label="账户权限"
                >
                  <el-select
                    v-model="form.permission"
                    placeholder="请选择权限"
                    filterable
                    clearable
                    class="custom-select"
                  >
                    <el-option label="只读" value="只读" />
                    <el-option label="读写" value="读写" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item
                  v-if="form.order_type === '新建数据库'"
                  label="数据库名称"
                  prop="db_name"
                  :rules="[
                    {
                      required: true,
                      message: '请输入数据库名称',
                      trigger: 'blur'
                    },
                    {
                      pattern: /^[a-z][a-z0-9_]*$/,
                      message:
                        '数据库名必须以小写字母开头，只能包含小写字母、数字和下划线',
                      trigger: 'blur'
                    }
                  ]"
                >
                  <el-input
                    v-model="form.db_name"
                    placeholder="请输入数据库名称"
                    maxlength="64"
                    show-word-limit
                    clearable
                  >
                    <template v-if="dbPrefix" #prepend>
                      {{ dbPrefix + "_" }}
                    </template>
                    <template #append>
                      <el-tooltip
                        content="请输入数据库名称，必须以小写字母开头，只能包含小写字母、数字和下划线"
                        placement="top"
                      >
                        <el-icon><QuestionFilled /></el-icon>
                      </el-tooltip>
                    </template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item
                  v-if="
                    form.order_type === '数据库应用账号开通' ||
                    form.order_type === '数据库SQL执行' ||
                    form.order_type === '新建数据库'
                  "
                  label="迭代版本"
                  prop="iteration_version"
                  :rules="[
                    {
                      required: true,
                      message: '请输入迭代版本',
                      trigger: 'blur'
                    }
                  ]"
                >
                  <el-input
                    v-model="form.iteration_version"
                    placeholder="请输入迭代版本，例如：v1.2.3"
                    maxlength="255"
                    show-word-limit
                    clearable
                  >
                    <template #append>
                      <el-tooltip
                        content="请输入当前迭代的版本号，通常采用语义化版本格式如v1.2.3"
                        placement="top"
                      >
                        <el-icon><QuestionFilled /></el-icon>
                      </el-tooltip>
                    </template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item
                  label="计划时间"
                  prop="plan_time"
                  :rules="[
                    {
                      required: true,
                      message: '请选择计划时间',
                      trigger: 'change'
                    }
                  ]"
                >
                  <el-date-picker
                    v-model="form.plan_time"
                    type="datetime"
                    class="custom-select"
                    placeholder="选择日期时间"
                    :disabled-date="disabledDateFunction"
                    value-on-clear
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="是否紧急" prop="critical">
                  <el-switch v-model="form.critical" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="关联工单" prop="orders">
                  <el-select
                    v-model="form.orders"
                    class="related-orders-select"
                    placeholder="请选择工单"
                    multiple
                    clearable
                    filterable
                    tag-type="primary"
                    tag-size="large"
                  >
                    <el-option
                      v-for="item in orders"
                      :key="item.id"
                      :label="item.title"
                      :value="item.sn"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item
              label="需求描述"
              :rules="[
                { required: true, message: '请输入需求描述', trigger: 'blur' },
                {
                  max: 4294967295,
                  message: '需求描述不能超过4294967295个字符',
                  trigger: 'blur'
                }
              ]"
              prop="content"
            >
              <el-input
                v-model="form.content"
                type="textarea"
                show-word-limit
                maxlength="4294967295"
                :autosize="{ minRows: 6, maxRows: 16 }"
                placeholder="请详细描述您的需求..."
              />
            </el-form-item>

            <el-form-item
              v-if="form.order_type === '数据库SQL执行'"
              label="SQL内容"
              :rules="[
                { required: true, message: '请输入SQL内容', trigger: 'blur' },
                {
                  max: 4294967295,
                  message: 'SQL内容不能超过4294967295个字符',
                  trigger: 'blur'
                }
              ]"
              prop="sql_content"
            >
              <div class="editor-wrapper">
                <div
                  v-if="!form.sql_content && !isEditorReady"
                  class="editor-placeholder"
                >
                  <iconify-icon-online
                    icon="material-symbols:sql"
                    class="placeholder-icon"
                  />
                  <p class="placeholder-text">
                    请在此处输入SQL语句，支持多条SQL语句执行
                  </p>
                  <p class="placeholder-hint">
                    提示：编辑器支持SQL格式化和语法高亮
                  </p>
                </div>
                <div class="editor-container">
                  <MonacoEditor
                    ref="editorRef"
                    v-model:value="form.sql_content"
                    :options="monacoOptions"
                    theme="vs-dark"
                    language="sql"
                    @change="handleEditorChange"
                    @mount="onEditorMounted"
                  />
                </div>
                <el-button
                  type="primary"
                  class="check-button"
                  @click="checkSQL"
                >
                  <iconify-icon-online
                    icon="material-symbols:check-circle"
                    style="margin-right: 4px"
                  />
                  SQL检查
                </el-button>
              </div>
            </el-form-item>

            <!-- 检查结果和信息展示 -->
            <div v-if="checkResult" class="check-result">
              <p>
                <span class="result-label">检查结果:</span>
                <span
                  :class="{
                    success: checkResult.success,
                    failure: !checkResult.success
                  }"
                >
                  {{ checkResult.success ? "成功" : "失败" }}
                </span>
              </p>
              <p>
                <span class="result-label">检查信息:</span>
                {{ checkResult.msg }}
              </p>
            </div>

            <!-- SQL检查结果折叠按钮 -->
            <el-button
              v-if="form.order_type === '数据库SQL执行'"
              link
              class="toggle-button"
              @click="isTableVisible = !isTableVisible"
            >
              <iconify-icon-online
                :icon="isTableVisible ? 'ep:arrow-up' : 'ep:arrow-down'"
                style="margin-right: 4px"
              />
              {{ isTableVisible ? "收起 SQL 检查结果" : "显示 SQL 检查结果" }}
            </el-button>

            <!-- SQL检查结果表格 -->
            <el-table
              v-if="isTableVisible && auditResults.length"
              :data="auditResults"
              class="results-table"
              :header-cell-style="{
                background: '#f5f7fa',
                color: '#606266',
                fontWeight: 'bold',
                padding: '8px 0',
                height: '40px',
                fontSize: '14px'
              }"
              :cell-style="{
                padding: '6px',
                fontSize: '13px'
              }"
              :row-style="
                ({ rowIndex }) => {
                  return {
                    backgroundColor: rowIndex % 2 === 0 ? '#ffffff' : '#fafafa'
                  };
                }
              "
              border
            >
              <el-table-column prop="order_id" label="工单 ID" width="100" />
              <el-table-column prop="sql" label="SQL 语句" min-width="180" />
              <el-table-column prop="stage" label="阶段" width="120" />
              <el-table-column prop="stage_status" label="状态" width="100" />
              <el-table-column
                prop="error_message"
                label="错误信息"
                min-width="150"
              />
              <el-table-column
                prop="affected_rows"
                label="影响行数"
                width="100"
              />
            </el-table>

            <el-row :gutter="20">
              <el-col :span="16">
                <el-form-item label="文件上传" prop="attachments">
                  <div class="upload-container">
                    <el-upload
                      v-model:file-list="fileList"
                      action="/api/v1/workflow/order/upload"
                      :headers="token"
                      multiple
                      class="upload-demo"
                      :on-preview="handlePreview"
                      :on-remove="handleRemove"
                      :limit="30"
                      :on-exceed="handleExceed"
                      :on-success="handleSuccess"
                      :before-upload="beforeUpload"
                      drag
                    >
                      <div class="upload-content">
                        <iconify-icon-online
                          icon="material-symbols:cloud-upload"
                          class="upload-icon"
                        />
                        <div class="upload-text">
                          <span class="upload-main-text"
                            >点击或拖拽文件到此区域上传</span
                          >
                          <span class="upload-sub-text"
                            >支持单个或批量上传</span
                          >
                        </div>
                      </div>
                      <template #tip>
                        <div class="el-upload__tip">
                          <div class="tip-header">
                            <iconify-icon-online
                              icon="material-symbols:info-outline"
                              class="tip-icon"
                            />
                            <span class="tip-title">上传说明</span>
                          </div>
                          <div class="tip-content">
                            <div class="tip-item">
                              <span class="tip-label">支持格式：</span>
                              <span class="tip-value"
                                >.rar、.zip、.txt、.sql、tar.gz、.tar、.gz</span
                              >
                            </div>
                            <div class="tip-item">
                              <span class="tip-label">文件限制：</span>
                              <span class="tip-value">最多30个文件</span>
                            </div>
                            <div class="tip-item tip-warning">
                              <iconify-icon-online
                                icon="material-symbols:warning-outline"
                                class="warning-icon"
                              />
                              <span
                                >如需替换已上传的文件，请先删除后再上传新文件</span
                              >
                            </div>
                          </div>
                        </div>
                      </template>
                    </el-upload>

                    <!-- 文件统计信息 -->
                    <div v-if="fileList.length > 0" class="file-stats">
                      <div class="stats-item">
                        <iconify-icon-online
                          icon="material-symbols:folder-outline"
                          class="stats-icon"
                        />
                        <span>已上传 {{ fileList.length }} 个文件</span>
                      </div>
                    </div>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item>
              <el-button type="primary" @click="onSubmit">
                <iconify-icon-online
                  icon="material-symbols:send"
                  style="margin-right: 4px"
                />提交
              </el-button>
              <RouterLink :to="{ name: '我的工单' }">
                <el-button style="margin-left: 50px">
                  <iconify-icon-online
                    icon="material-symbols:cancel"
                    style="margin-right: 4px"
                  />
                  取消
                </el-button>
              </RouterLink>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
      <el-col :span="8">
        <ApprovalFlow :processes="processes" />
      </el-col>
    </el-row>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, watch, onUnmounted } from "vue";
import { applyOrderAPI, type OrderForm } from "@/api/workflow/order-mysql";
import {
  ElMessage,
  ElMessageBox,
  type FormInstance,
  type UploadProps
} from "element-plus";
import { QuestionFilled } from "@element-plus/icons-vue";
import { message } from "@/utils/message";
import {
  getProcesses,
  getProjects,
  processes,
  projects,
  orders,
  getOrders
} from "./hook";
import { ENV, ENVs, MySQLOrderTypes } from "@/config/order-enum";
import { formatToken, getToken } from "@/utils/auth";
import MonacoEditor from "monaco-editor-vue3";
import ApprovalFlow from "../components/ApprovalFlow.vue";
import { checkSQLAPI } from "@/api/database/mysql/sql";

const form = ref<OrderForm>({
  title: "",
  order_type: "",
  content: "",
  sql_content: "",
  db_name: "",
  project_id: undefined,
  project_dbname: undefined,
  attachments: [],
  plan_time: new Date(),
  orders: [],
  env: undefined,
  permission: "",
  critical: false,
  iteration_version: ""
});

// 数据库名称前缀
const dbPrefix = ref("");

const ruleFormRef = ref<FormInstance>();
const token = ref<any>({});
const onSubmit = async () => {
  // 清空之前的附件列表，避免重复添加
  form.value.attachments = [];

  // 验证表单并处理附件
  const valid = await new Promise(resolve => {
    ruleFormRef.value.validate((valid: boolean) => {
      // 使用Set来确保不添加重复的文件
      const uniqueFiles = new Set();

      fileList.value.forEach(item => {
        if (item.response && item.response.success) {
          const data = item.response?.data;
          // 使用文件URL作为唯一标识
          const fileKey = data?.url;

          if (fileKey && !uniqueFiles.has(fileKey)) {
            uniqueFiles.add(fileKey);
            form.value.attachments.push({
              filename: data?.filename,
              url: data?.url
            });
          }
        }
      });

      resolve(valid);
    });
  });
  if (valid) {
    try {
      await ElMessageBox.confirm("是否确认提交工单？", "提交确认", {
        confirmButtonText: "提交",
        cancelButtonText: "取消",
        type: "warning",
        draggable: true,
        center: true,
        customClass: "custom-message-box",
        icon: "Warning"
      });

      // 检查是否为测试环境且工单类型为 SQL 执行
      if (
        form.value.env === ENV.TestEnv &&
        form.value.order_type === "数据库SQL执行"
      ) {
        // 先进行 SQL 检查
        await checkSQL(); // 调用 SQL 检查函数
        if (!checkResult.value?.success) {
          message("SQL 检查未通过，无法提交工单", { type: "warning" });
          return; // 如果检查未通过，阻止提交
        }
      }

      const res = await applyOrderAPI(form.value);
      if (res.success) {
        message("提交成功", { type: "success" });
        setTimeout(() => {
          window.location.href = "/workflow/my/order";
        }, 300);
      } else {
        message(res.msg || "提交失败", { type: "error" });
      }
    } catch (error) {
      if (error !== "cancel") {
        message(error.message || "操作取消", { type: "warning" });
      }
    }
  } else {
    message("请检查表单数据", { type: "warning" });
  }
};

type ProjectDB = {
  project_id: number;
  project_name: string;
  db_name: string;
};
const projectsDBs = ref<ProjectDB[]>([]);

watch(
  () => form.value.env,
  (newVal, oldVal) => {
    if (newVal !== oldVal) {
      const ps = projects.value.filter(item => item.env === newVal);
      projectsDBs.value = [];
      ps.forEach(item => {
        let dbnames = [];
        item.dbs?.forEach(db => {
          dbnames = [...dbnames, db];
        });
        item.custom_dbs?.forEach(db => {
          dbnames = [...dbnames, db];
        });
        dbnames = [...new Set(dbnames)];
        dbnames?.forEach(db => {
          projectsDBs.value.push({
            project_id: item.id,
            project_name: item.name,
            db_name: db
          });
        });
      });
      form.value.db_name = "";
    }
  }
);

const fileList = ref<any[]>([]);

const handleSuccess: UploadProps["onSuccess"] = (
  response,
  uploadFile,
  uploadFiles
) => {
  // 检查上传是否成功
  if (response && response.success) {
    // 更新文件列表，确保没有重复文件
    const uniqueFiles = [];
    const fileNames = new Set();

    // 处理新上传的文件列表，保留最新的同名文件
    uploadFiles.forEach(file => {
      if (!fileNames.has(file.name)) {
        fileNames.add(file.name);
        uniqueFiles.push(file);
      } else {
        // 如果有同名文件，保留最新上传的（通常是列表中最后一个）
        const index = uniqueFiles.findIndex(f => f.name === file.name);
        if (index !== -1) {
          uniqueFiles[index] = file;
        }
      }
    });

    fileList.value = uniqueFiles;

    // 显示上传成功的提示
    ElMessage({
      message: `文件 "${uploadFile.name}" 上传成功`,
      type: "success",
      duration: 3000
    });

    // 更新文件统计信息
    updateFileStats();
  } else {
    // 上传失败，显示错误信息
    ElMessage({
      message: response?.msg || `文件 "${uploadFile.name}" 上传失败`,
      type: "error",
      duration: 5000,
      showClose: true
    });
  }
};

const handleRemove: UploadProps["onRemove"] = (file, uploadFiles) => {
  fileList.value = uploadFiles;

  // 显示删除成功的提示
  ElMessage({
    message: `文件 "${file.name}" 已删除`,
    type: "info",
    duration: 2000
  });

  // 更新文件统计信息
  updateFileStats();
};

// 更新文件统计信息
const updateFileStats = () => {
  // 这里可以添加更多的文件统计逻辑，如文件总大小等
  // 目前只是简单地统计文件数量，已经在模板中通过 fileList.length 实现
};

const handlePreview: UploadProps["onPreview"] = uploadFile => {
  // 检查文件类型，对于文本文件和SQL文件，可以在新窗口中打开
  const fileExtension = uploadFile.name.split(".").pop().toLowerCase();
  const textFileTypes = ["txt", "sql"];

  if (textFileTypes.includes(fileExtension)) {
    // 对于文本文件，可以提供更好的预览体验
    ElMessageBox.confirm("如何查看此文件？", "文件预览", {
      confirmButtonText: "在新窗口打开",
      cancelButtonText: "直接下载",
      type: "info",
      distinguishCancelAndClose: true,
      customClass: "preview-message-box"
    })
      .then(() => {
        // 在新窗口打开
        window.open(uploadFile.url);
      })
      .catch(action => {
        if (action === "cancel") {
          // 直接下载
          const link = document.createElement("a");
          link.href = uploadFile.url;
          link.download = uploadFile.name;
          link.click();
        }
      });
  } else {
    // 对于其他类型的文件，直接下载
    const link = document.createElement("a");
    link.href = uploadFile.url;
    link.download = uploadFile.name;
    link.click();
  }
};

const handleExceed: UploadProps["onExceed"] = (files, uploadFiles) => {
  ElMessage({
    message: `超出文件数量限制！最多允许上传30个文件，当前已有${uploadFiles.length}个文件，无法继续上传${files.length}个新文件`,
    type: "warning",
    duration: 5000,
    showClose: true
  });
};

const beforeUpload: UploadProps["beforeUpload"] = rawFile => {
  // 检查文件类型
  const allowedExtensions = [
    ".rar",
    ".zip",
    ".txt",
    ".sql",
    "tar.gz",
    ".tar",
    ".gz"
  ];

  // 获取文件扩展名
  const fileExtension = "." + rawFile.name.split(".").pop();

  // 检查文件大小（限制为50MB）
  const maxSize = 50 * 1024 * 1024; // 50MB
  if (rawFile.size > maxSize) {
    ElMessage({
      message: `文件 "${rawFile.name}" 超过最大限制（50MB），请压缩后上传`,
      type: "error",
      duration: 5000,
      showClose: true
    });
    return false;
  }

  // 检查文件类型
  if (!allowedExtensions.includes(fileExtension)) {
    ElMessage({
      message: `文件 "${rawFile.name}" 格式不支持，仅支持 .rar、.zip、.txt、.sql、tar.gz、.tar、.gz 格式`,
      type: "error",
      duration: 5000,
      showClose: true
    });
    return false;
  }

  // 检查是否有重名文件，如果有，允许上传但给出提示
  const isDuplicate = fileList.value.some(file => file.name === rawFile.name);
  if (isDuplicate) {
    ElMessage({
      message: `文件 "${rawFile.name}" 已存在，但仍将继续上传。如需替换，请先删除已有文件。`,
      type: "warning",
      duration: 5000,
      showClose: true
    });
  }

  // 显示上传开始的提示
  ElMessage({
    message: `开始上传文件 "${rawFile.name}"`,
    type: "info",
    duration: 2000
  });

  return true;
};

// 计算两个工日之后的日期
const getTwoWorkingDaysLater = (date: Date): Date => {
  let nextDate = new Date(date);
  let workingDays = 0;

  while (workingDays < 2) {
    nextDate.setDate(nextDate.getDate() + 1);
    if (!isWeekend(nextDate)) {
      workingDays++;
    }
  }

  // 设置时 10:00
  nextDate.setHours(10, 0, 0, 0);

  return nextDate;
};

const disabledDateFunction = (date: Date) => {
  const currentDate = new Date();
  const oneWorkingDayLater = getOneWorkingDayLater(currentDate);
  return date < oneWorkingDayLater || isWeekend(date);
};

// 计算一个工作日之后的日期
const getOneWorkingDayLater = date => {
  let nextDate = new Date(date);
  do {
    nextDate.setDate(nextDate.getDate() + 1);
  } while (isWeekend(nextDate));
  return nextDate;
};

// 判断是否为周末
const isWeekend = date => {
  const dayOfWeek = date.getDay();
  return dayOfWeek === 0 || dayOfWeek === 6;
};

// 1. 优化 Monaco Editor 相关配置和初始化
const editorRef = ref<any>(null);
const editorInstance = ref<any>(null);
const isEditorReady = ref(false);

// 2. 改进编辑器挂载处理
const onEditorMounted = (editor: any) => {
  editorInstance.value = editor;

  // 确保编辑器和模型已加载
  if (editor && editor.getModel()) {
    isEditorReady.value = true;

    // 设置编辑器强制布局
    editor.layout({
      width: editor.getContainerDomNode().clientWidth,
      height: 300
    });

    // 监听窗口大小变化，重新计算编辑器大小
    window.addEventListener("resize", () => {
      if (editor) {
        editor.layout({
          width: editor.getContainerDomNode().clientWidth,
          height: 300
        });
      }
    });

    // 初始化时自动格式化
    setTimeout(() => {
      formatSQL();
    }, 500);
  }
};

// 添加状态管理
const auditResults = ref<any[]>([]);
const checkResult = ref<{ success: boolean; msg: string } | null>(null); // 用于存储检查结果和信息
const isTableVisible = ref(false);

const checkSQL = () => {
  // 添加空值检查
  if (!form.value.sql_content || !form.value.db_name) {
    ElMessage.warning("请确保 SQL 内容和库名都已填写。");
    return;
  }

  checkSQLAPI({
    sql_content: form.value.sql_content,
    db_name: form.value.db_name,
    project_id: form.value.project_id
  })
    .then(res => {
      checkResult.value = { success: res.success, msg: res.msg }; // 存储检查结果和信息
      if (res.success) {
        auditResults.value = res.data;
        message("检查成功", { type: "success" });
      } else {
        auditResults.value = res.data;
        message(res.msg, { type: "warning" });
      }
    })
    .catch(err => {
      message(err.message, { type: "error" });
    });
};

// 3. 优化 SQL 格式化函数
const formatSQL = async () => {
  if (!editorInstance.value || !isEditorReady.value) {
    return;
  }
  try {
    const editor = editorInstance.value;
    const model = editor.getModel();

    if (model) {
      await editor.getAction("editor.action.formatDocument").run();
      form.value.sql_content = model.getValue().trim();
    }
  } catch (error) {
    console.error("Format SQL error:", error);
  }
};

// 4. 优化编辑器内容变化处理
const handleEditorChange = (value: string) => {
  if (!value) return;

  if (value.length > 50000) {
    message("SQL内容不能超过50000个字符", { type: "warning" });
    form.value.sql_content = value.slice(0, 50000);
    return;
  }

  form.value.sql_content = value;
  formatSQL();
};

// 5. 改进 Monaco 加载检测
onMounted(() => {
  getProjects();
  getProcesses();
  getOrders();
  token.value = { Authorization: formatToken(getToken().accessToken) };

  // 设置默认计划时间为两个工作日后
  form.value.plan_time = getTwoWorkingDaysLater(new Date());
});

// 组件销毁时清理资源
onUnmounted(() => {
  // 移除窗口大小变化监听器
  window.removeEventListener("resize", () => {
    if (editorInstance.value) {
      editorInstance.value.layout();
    }
  });
});

// 6. 优化编辑器配置
const monacoOptions = {
  minimap: { enabled: false },
  automaticLayout: true,
  scrollBeyondLastLine: false,
  fontSize: 14,
  tabSize: 2,
  wordWrap: "on",
  lineNumbers: "on",
  folding: true,
  lineHeight: 22,
  formatOnPaste: true,
  formatOnType: true,
  autoIndent: "full",
  renderWhitespace: "none",
  scrollbar: {
    vertical: "visible",
    horizontal: "visible",
    useShadows: false
  },
  suggest: {
    preview: true,
    showWords: true
  }
};

const onDbNameChange = (value: string) => {
  try {
    const [projectName, dbName] = value.split(":");
    const selectedProject = projectsDBs.value.find(
      project => project.project_name === projectName
    );

    if (selectedProject) {
      form.value.project_id = selectedProject.project_id; // 设置 project_id
    } else {
      form.value.project_id = undefined; // 如未找到项目，清空 project_id
    }

    form.value.db_name = dbName; // 设置 db_name
  } catch (error) {
    console.error("处理库名变化时出错:", error);
    ElMessage.error("处理库名变化时出错，请重试。"); // 显示错误消息
  }
};
</script>

<style scoped>
.order-container {
  margin-top: 20px;
}

.main-card {
  margin-left: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 8%);
  transition: all 0.3s ease;
}

.main-card:hover {
  box-shadow: 0 6px 20px rgb(0 0 0 / 12%);
}

.card-header {
  display: flex;
  align-items: center;
  padding: 8px 0;
}

.title {
  display: flex;
  align-items: center;
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.icon {
  margin-right: 12px;
  font-size: 24px;
  color: #409eff;
}

.order-form {
  padding: 10px;
  margin-top: 20px;
}

.editor-wrapper {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  overflow: hidden;
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
}

.editor-container {
  width: 100%;
  height: 300px;
  min-height: 300px;
}

.editor-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 300px;
  color: #8e8e8e;
  pointer-events: none;
  background-color: #1e1e1e;
}

.placeholder-icon {
  margin-bottom: 16px;
  font-size: 48px;
  opacity: 0.7;
}

.placeholder-text {
  margin-bottom: 8px;
  font-size: 16px;
}

.placeholder-hint {
  font-size: 14px;
  opacity: 0.7;
}

.check-button {
  align-self: flex-start;
  margin-top: 8px;
  margin-bottom: 8px;
  margin-left: 8px;
}

.check-result {
  padding: 16px;
  margin-top: 16px;
  background-color: #f9f9f9;
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 5%);
}

.result-label {
  margin-right: 8px;
  font-weight: 500;
}

.success {
  font-weight: 500;
  color: #67c23a;
}

.failure {
  font-weight: 500;
  color: #f56c6c;
}

.toggle-button {
  display: flex;
  align-items: center;
  margin-top: 12px;
}

.results-table {
  width: 100%;
  margin-top: 12px;
  margin-bottom: 16px;
  overflow: hidden;
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 5%);
}

.related-orders-select {
  width: 100%;
}

.custom-select {
  width: 100%;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-select .el-input) {
  transition: all 0.3s;
}

:deep(.el-input__inner) {
  transition: all 0.3s;
}

:deep(.el-input__inner:focus) {
  border-color: #409eff;
}

:deep(.el-textarea__inner) {
  transition: all 0.3s;
}

:deep(.el-textarea__inner:focus) {
  border-color: #409eff;
}

:deep(.el-date-editor.el-input) {
  transition: all 0.3s;
}

:deep(.el-button--primary) {
  padding: 12px 24px;
  font-weight: 500;
  transition: all 0.3s;
}

:deep(.el-button--primary:hover) {
  background-color: #66b1ff;
  box-shadow: 0 2px 8px rgb(64 158 255 / 20%);
}

:deep(.el-button--default) {
  padding: 12px 24px;
  font-weight: 500;
  transition: all 0.3s;
}

:deep(.el-button--default:hover) {
  background-color: #f2f6fc;
}

/* 表单提交按钮区域样式 */
:deep(.el-form-item:last-child) {
  padding-top: 20px;
  margin-top: 32px;
  border-top: 1px dashed #ebeef5;
}

.upload-demo {
  margin-top: 8px;
}

:deep(.el-upload-list) {
  margin-top: 12px;
}

:deep(.el-upload-list__item) {
  transition: all 0.3s;
}

:deep(.el-upload__tip-extra) {
  margin-top: 4px;
  font-size: 12px;
  line-height: 1.4;
}

/* 上传组件样式优化 */
.upload-container {
  width: 100%;
  overflow: hidden;
  border-radius: 8px;
}

:deep(.el-upload-dragger) {
  width: 100%;
  height: auto;
  padding: 30px 20px;
  background-color: #f8f9fa;
  border: 2px dashed #dcdfe6;
  border-radius: 8px;
  transition: all 0.3s;
}

:deep(.el-upload-dragger:hover) {
  background-color: rgb(64 158 255 / 5%);
  border-color: #409eff;
  box-shadow: 0 0 10px rgb(64 158 255 / 10%);
  transform: translateY(-2px);
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.upload-icon {
  margin-bottom: 16px;
  font-size: 48px;
  color: #409eff;
  transition: all 0.3s;
}

:deep(.el-upload-dragger:hover) .upload-icon {
  color: #66b1ff;
  transform: scale(1.1);
}

.upload-text {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
}

.upload-main-text {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.upload-sub-text {
  font-size: 14px;
  color: #909399;
}

/* 上传提示样式 */
:deep(.el-upload__tip) {
  width: 100%;
  padding: 12px 16px;
  margin-top: 16px;
  background-color: #f8f9fa;
  border: 1px solid #ebeef5;
  border-radius: 6px;
  box-shadow: 0 2px 6px rgb(0 0 0 / 3%);
}

.tip-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.tip-icon {
  margin-right: 8px;
  font-size: 18px;
  color: #409eff;
}

.tip-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.tip-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.tip-item {
  display: flex;
  align-items: flex-start;
  font-size: 13px;
  line-height: 1.5;
}

.tip-label {
  margin-right: 4px;
  font-weight: 500;
  color: #606266;
  white-space: nowrap;
}

.tip-value {
  color: #606266;
}

.tip-warning {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  margin-top: 4px;
  color: #e6a23c;
  background-color: rgb(230 162 60 / 10%);
  border-radius: 4px;
}

.warning-icon {
  margin-right: 8px;
  font-size: 16px;
  color: #e6a23c;
}

/* 文件统计信息 */
.file-stats {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  margin-top: 16px;
  background-color: #f0f9ff;
  border-left: 3px solid #409eff;
  border-radius: 6px;
}

.stats-item {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #409eff;
}

.stats-icon {
  margin-right: 8px;
  font-size: 18px;
}

/* 上传列表样式优化 - 扩展样式 */
:deep(.el-upload-list.enhanced) {
  margin-top: 16px;
  overflow: hidden;
  border: 1px solid #ebeef5;
  border-radius: 8px;
}

:deep(.el-upload-list.enhanced .el-upload-list__item) {
  padding: 12px 16px;
  margin: 0;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-upload-list__item:last-child) {
  border-bottom: none;
}

:deep(.el-upload-list__item:hover) {
  background-color: #f5f7fa;
}

:deep(.el-upload-list__item-name) {
  font-weight: 500;
  color: #303133;
}

:deep(.el-upload-list__item-status-label) {
  color: #67c23a;
}

:deep(.el-upload-list__item .el-icon--close) {
  top: 12px;
  right: 16px;
  color: #909399;
  transition: all 0.3s;
}

:deep(.el-upload-list__item .el-icon--close:hover) {
  color: #f56c6c;
  background-color: transparent;
  transform: scale(1.2);
}
</style>

<style>
:deep(.custom-message-box) {
  padding: 30px 30px 20px;
  border-radius: 8px;
}

:deep(.custom-message-box .el-message-box__status) {
  top: 30px;
  font-size: 24px !important;
}

:deep(.custom-message-box .el-message-box__header) {
  padding: 0 0 10px;
}

:deep(.custom-message-box .el-message-box__title) {
  font-size: 16px;
  font-weight: 500;
}

:deep(.custom-message-box .el-message-box__content) {
  padding: 20px 0;
  font-size: 14px;
}

:deep(.custom-message-box .el-message-box__btns) {
  padding: 10px 0 0;
}

:deep(.custom-message-box .el-message-box__btns button) {
  min-width: 80px;
  padding: 8px 20px;
  margin-left: 12px;
  font-size: 14px;
  border-radius: 4px;
}

:deep(.custom-message-box .el-message-box__btns .el-button--primary) {
  background-color: var(--el-color-warning);
  border-color: var(--el-color-warning);
}

:deep(.custom-message-box .el-message-box__btns .el-button--primary:hover) {
  background-color: var(--el-color-warning-light-3);
  border-color: var(--el-color-warning-light-3);
}

/* 文件预览对话框样式 */
:deep(.preview-message-box) {
  width: 400px;
  overflow: hidden;
  border-radius: 8px;
}

:deep(.preview-message-box .el-message-box__header) {
  padding: 15px 20px;
  background-color: #f0f9ff;
}

:deep(.preview-message-box .el-message-box__title) {
  font-size: 16px;
  font-weight: 600;
  color: #409eff;
}

:deep(.preview-message-box .el-message-box__content) {
  padding: 20px;
  color: #606266;
}

:deep(.preview-message-box .el-message-box__btns) {
  padding: 10px 20px 20px;
}

:deep(.preview-message-box .el-button) {
  padding: 10px 20px;
  font-size: 14px;
  border-radius: 4px;
  transition: all 0.3s;
}

:deep(.preview-message-box .el-button--primary) {
  background-color: #409eff;
  border-color: #409eff;
}

:deep(.preview-message-box .el-button--primary:hover) {
  background-color: #66b1ff;
  border-color: #66b1ff;
  box-shadow: 0 2px 8px rgb(64 158 255 / 20%);
  transform: translateY(-1px);
}

:deep(.preview-message-box .el-button--default) {
  border-color: #dcdfe6;
}

:deep(.preview-message-box .el-button--default:hover) {
  color: #409eff;
  background-color: #ecf5ff;
  border-color: #c6e2ff;
}

/* Monaco编辑器样式 */
:deep(.monaco-editor) {
  width: 100% !important;
}

:deep(.monaco-editor .monaco-editor-background) {
  background-color: #1e1e1e !important;
}

:deep(.monaco-editor .margin) {
  background-color: #1e1e1e !important;
}

:deep(.monaco-editor .inputarea) {
  padding: 0 !important;
  border: none !important;
}

:deep(.monaco-editor-container) {
  width: 100% !important;
}

:deep(.monaco-editor .overflow-guard) {
  width: 100% !important;
}
</style>
