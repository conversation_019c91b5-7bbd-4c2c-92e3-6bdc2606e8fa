import { ref, onMounted } from "vue";
import type { TableColumns } from "@pureadmin/table";
import type { Bookmark, BookmarkForm } from "@/api/note/bookmark";
import { message } from "@/utils/message";
import {
  getBookmarksAPI,
  deleteBookmarkAPI,
  updateBookmarkAPI,
  addBookmarkAPI
} from "@/api/note/bookmark";
import { addDialog } from "@/components/ReDialog";
import { h } from "vue";
import Uform from "./Uform.vue";

export function useBookmark() {
  const formRef = ref();
  const tableRef = ref();
  const childrenRef = ref(null);
  const editForm = ref<BookmarkForm>();

  const pagination = ref({ current: 1, pageSize: 10, total: 0 });
  const dataList = ref<Bookmark[]>([]);
  const dataLoading = ref(false);
  const form = ref({ keyword: undefined });

  const isValidUrl = (url: string) => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      message("链接已复制到剪贴板", { type: "success" });
    } catch (err) {
      message("复制失败，请手动复制", { type: "error" });
    }
  };

  const columns: TableColumns[] = [
    {
      label: "标题",
      prop: "title",
      minWidth: 200,
      showOverflowTooltip: true,
      cellRenderer: ({ row }) => (
        <div
          style={{
            "white-space": "normal",
            "text-align": "left",
            padding: "8px 0"
          }}
        >
          <el-link
            type="primary"
            onClick={() => window.open(row.url, "_blank")}
            style={{
              "font-weight": "bold",
              "font-size": "14px",
              "line-height": "1.5"
            }}
          >
            {row.title}
          </el-link>
        </div>
      )
    },
    {
      label: "链接",
      prop: "url",
      minWidth: 300,
      showOverflowTooltip: true,
      cellRenderer: ({ row }) => (
        <div
          style={{
            "white-space": "normal",
            "text-align": "left",
            color: "#666",
            "font-size": "13px",
            padding: "8px 0",
            "line-height": "1.5",
            display: "flex",
            "align-items": "center",
            gap: "8px"
          }}
        >
          <div style={{ flex: "1" }}>
            {isValidUrl(row.url) ? (
              <el-link
                type="info"
                href={row.url}
                target="_blank"
                style={{
                  "font-size": "13px",
                  "font-weight": "normal"
                }}
              >
                {row.url}
              </el-link>
            ) : (
              <span>{row.url}</span>
            )}
          </div>
          <el-button
            type="primary"
            link
            style={{
              padding: "4px 8px",
              "font-size": "13px",
              "flex-shrink": "0"
            }}
            onClick={() => copyToClipboard(row.url)}
          >
            <el-icon style={{ "margin-right": "4px" }}>
              <i class="ri-file-copy-line"></i>
            </el-icon>
            复制
          </el-button>
        </div>
      )
    },
    {
      label: "备注",
      prop: "remark",
      minWidth: 300,
      cellRenderer: ({ row }) => (
        <div
          style={{
            "white-space": "pre-wrap",
            "text-align": "left",
            color: "#666",
            "font-size": "13px",
            padding: "8px 0",
            "line-height": "1.6",
            "max-height": "120px",
            "overflow-y": "auto"
          }}
        >
          {row.remark || "-"}
        </div>
      )
    },
    {
      label: "操作",
      prop: "action",
      width: 160,
      cellRenderer: ({ row }) => (
        <div
          class="flex items-center gap-2"
          style={{
            padding: "8px 0"
          }}
        >
          <el-button
            type="primary"
            link
            class="action-button"
            style={{
              "font-size": "13px",
              padding: "4px 8px"
            }}
            onClick={() => handleEditItem(row)}
          >
            编辑
          </el-button>
          <el-button
            type="danger"
            link
            class="action-button"
            style={{
              "font-size": "13px",
              padding: "4px 8px"
            }}
            onClick={() => handleDeleteItem(row)}
          >
            删除
          </el-button>
        </div>
      )
    }
  ];

  const getCardListData = async () => {
    dataLoading.value = true;
    try {
      const res = await getBookmarksAPI({
        limit: pagination.value.pageSize,
        page: pagination.value.current,
        keyword: form.value.keyword
      });
      if (res.success) {
        dataList.value = res.data;
        pagination.value.total = res.count;
      } else {
        message("获取数据失败", { type: "error" });
      }
    } catch (error) {
      message("网络错误，请重试", { type: "error" });
    } finally {
      dataLoading.value = false;
    }
  };

  const onPageSizeChange = (size: number) => {
    pagination.value.pageSize = size;
    pagination.value.current = 1;
    getCardListData();
  };

  const onCurrentChange = (current: number) => {
    pagination.value.current = current;
    getCardListData();
  };

  const searchBookmarks = () => {
    pagination.value.current = 1;
    getCardListData();
  };

  const resetSearch = () => {
    form.value.keyword = undefined;
    pagination.value.current = 1;
    pagination.value.pageSize = 10;
    getCardListData();
  };

  const handleDeleteItem = async (bookmark: Bookmark) => {
    try {
      await deleteBookmarkAPI(bookmark.id);
      dataList.value = dataList.value.filter(item => item.id !== bookmark.id);
      message("书签已删除", { type: "success" });
      getCardListData();
    } catch (error) {
      message("删除书签失败，请重试", { type: "error" });
    }
  };

  const handleEditItem = (bookmark: Bookmark) => {
    editForm.value = {
      title: bookmark.title,
      url: bookmark.url,
      remark: bookmark.remark
    };
    addDialog({
      title: "编辑书签 " + bookmark.title,
      contentRenderer: () => {
        return h(Uform, {
          formInline: {
            form: editForm.value
          },
          ref: childrenRef
        });
      },
      beforeSure: done => {
        if (childrenRef.value.ruleFormRef) {
          childrenRef.value.ruleFormRef
            .validate((valid: boolean) => {
              if (valid) {
                updateBookmarkAPI(bookmark.id, editForm.value)
                  .then(res => {
                    if (res.success) {
                      message(res.msg, { type: "success" });
                      done();
                      getCardListData();
                      editForm.value = undefined;
                    } else {
                      message(res.msg, { type: "error" });
                    }
                  })
                  .catch(error => {
                    message(error, { type: "error" });
                  });
              } else {
                message("请检查表单数据", { type: "error" });
              }
            })
            .catch(error => {
              message("请检查表单数据:" + error, { type: "error" });
            });
        }
      }
    });
  };

  const handleAddBookmark = () => {
    editForm.value = {
      title: "",
      url: "",
      remark: ""
    };
    addDialog({
      title: "新增书签",
      contentRenderer: () => {
        return h(Uform, {
          formInline: {
            form: editForm.value
          },
          ref: childrenRef
        });
      },
      beforeSure: done => {
        if (childrenRef.value.ruleFormRef) {
          childrenRef.value.ruleFormRef
            .validate((valid: boolean) => {
              if (valid) {
                addBookmarkAPI(editForm.value)
                  .then(res => {
                    if (res.success) {
                      message(res.msg, { type: "success" });
                      done();
                      getCardListData();
                      editForm.value = undefined;
                    } else {
                      message(res.msg, { type: "error" });
                    }
                  })
                  .catch(error => {
                    message(error, { type: "error" });
                  });
              } else {
                message("请检查表单数据", { type: "error" });
              }
            })
            .catch(error => {
              message("请检查表单数据:" + error, { type: "error" });
            });
        }
      }
    });
  };

  onMounted(() => {
    getCardListData();
  });

  return {
    formRef,
    tableRef,
    form,
    loading: dataLoading,
    columns,
    dataList,
    pagination,
    onSearch: searchBookmarks,
    resetForm: resetSearch,
    handleSizeChange: onPageSizeChange,
    handleCurrentChange: onCurrentChange,
    addFunc: handleAddBookmark
  };
}
