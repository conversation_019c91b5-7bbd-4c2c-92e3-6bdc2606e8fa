<template>
  <div class="bookmark-form">
    <el-card
      shadow="hover"
      class="form-card"
      style="
        overflow: hidden;
        border-radius: 12px;
        box-shadow: 0 4px 10px rgb(0 0 0 / 10%);
      "
    >
      <el-form
        ref="ruleFormRef"
        :model="newFormInline.form"
        size="large"
        status-icon
        label-position="top"
        class="form-container"
      >
        <el-form-item
          label="标题"
          prop="title"
          :rules="[
            { required: true, message: '请输入标题', trigger: 'blur' },
            { max: 255, message: '标题长度不能超过255个字符', trigger: 'blur' }
          ]"
        >
          <el-input
            v-model="newFormInline.form.title"
            placeholder="请输入书签标题"
            maxlength="255"
            show-word-limit
            clearable
            :prefix-icon="useRenderIcon('ri:bookmark-line')"
          />
        </el-form-item>

        <el-form-item
          label="链接"
          prop="url"
          :rules="[
            { type: 'url', message: '请输入有效的链接地址', trigger: 'blur' },
            { required: true, message: '请输入链接地址', trigger: 'blur' }
          ]"
        >
          <el-input
            v-model="newFormInline.form.url"
            placeholder="请输入链接地址 (以 http:// 或 https:// 开头)"
            maxlength="255"
            show-word-limit
            clearable
            :prefix-icon="useRenderIcon('ri:link')"
          />
        </el-form-item>

        <el-form-item
          label="备注"
          prop="remark"
          :rules="[
            { max: 500, message: '备注长度不能超过500个字符', trigger: 'blur' }
          ]"
        >
          <el-input
            v-model="newFormInline.form.remark"
            type="textarea"
            placeholder="请输入备注信息 (选填)"
            :rows="4"
            maxlength="500"
            show-word-limit
            resize="vertical"
            :prefix-icon="useRenderIcon('ri:file-text-line')"
          />
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { defineProps, ref } from "vue";
import type { FormInstance } from "element-plus";
import type { BookmarkForm } from "@/api/note/bookmark";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";

export interface FormProps {
  formInline: {
    form: BookmarkForm;
  };
}

const props = withDefaults(defineProps<FormProps>(), {
  formInline: () => ({ row: undefined, form: undefined })
});

const newFormInline = ref(props.formInline);
const ruleFormRef = ref<FormInstance>();

defineExpose({ ruleFormRef });
</script>

<style scoped lang="scss">
.bookmark-form {
  .form-container {
    max-width: 600px;
    padding: 20px 0;
    margin: 0 auto;

    :deep(.el-form-item) {
      margin-bottom: 24px;

      .el-form-item__label {
        padding-bottom: 8px;
        font-size: 14px;
        font-weight: 500;
        color: #333;
      }

      .el-input__wrapper,
      .el-textarea__wrapper {
        border-radius: 8px;
        box-shadow: 0 0 0 1px #dcdfe6;
        transition: all 0.3s ease;

        &:hover {
          box-shadow: 0 0 0 1px #409eff;
        }

        &.is-focus {
          box-shadow:
            0 0 0 1px #409eff,
            0 0 0 3px rgb(64 158 255 / 10%);
        }
      }

      .el-input__inner,
      .el-textarea__inner {
        font-size: 14px;
        color: #333;

        &::placeholder {
          font-size: 13px;
          color: #999;
        }
      }

      .el-input__count {
        font-size: 12px;
        color: #999;
        background: transparent;
      }
    }

    :deep(.el-form-item.is-error) {
      .el-input__wrapper,
      .el-textarea__wrapper {
        box-shadow: 0 0 0 1px #f56c6c;

        &:hover,
        &.is-focus {
          box-shadow:
            0 0 0 1px #f56c6c,
            0 0 0 3px rgb(245 108 108 / 10%);
        }
      }
    }
  }
}
</style>
