import dayjs from "dayjs";
import duration from "dayjs/plugin/duration";
import type { PaginationProps } from "@pureadmin/table";
import { reactive, ref, onMounted, h } from "vue";
import { message } from "@/utils/message";
import { addDrawer } from "@/components/ReDrawer";
import { addDialog } from "@/components/ReDialog";
import Uform from "./Uform.vue";
import {
  createJournalAPI,
  deleteJournalAPI,
  getJournalListAPI,
  updateJournalAPI,
  type Journal,
  type JournalForm
} from "@/api/note/journal";
import type { FormInstance } from "element-plus";
import { useRouter } from "vue-router";

// 添加 ref 类型定义
interface UformRef {
  ruleFormRef: FormInstance | undefined;
  newFormInline: {
    form: JournalForm;
    row?: {
      id: number;
      [key: string]: any;
    };
  };
}

// 添加 duration 插件
dayjs.extend(duration);

export function useRole() {
  const form = reactive({
    keyword: undefined,
    project: undefined,
    event_type: undefined,
    tag: undefined
  });
  const dataList = ref<Journal[]>([]);
  const loading = ref<boolean>(true);

  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true,
    pageSizes: [10, 20, 30, 40, 50, 100]
  });

  // 计算时长函数
  const calculateDuration = (startTime?: string, endTime?: string) => {
    if (!startTime || !endTime) return "-";

    const start = dayjs(startTime);
    const end = dayjs(endTime);
    const diff = end.diff(start);

    const dur = dayjs.duration(diff);

    const days = dur.days();
    const hours = dur.hours();
    const minutes = dur.minutes();

    let result = [];
    if (days > 0) result.push(`${days}天`);
    if (hours > 0) result.push(`${hours}小时`);
    if (minutes > 0) result.push(`${minutes}分钟`);

    return result.length > 0 ? result.join(" ") : "小于1分钟";
  };

  const columns: TableColumnList = [
    {
      label: "标题",
      prop: "title",
      minWidth: 120,
      showOverflowTooltip: true,
      cellRenderer: ({ row }) => (
        <div style={{ "white-space": "normal", "text-align": "left" }}>
          <el-link
            type="primary"
            onClick={() => viewDetail(row)}
            style="font-weight: bold;"
          >
            {row.title}
          </el-link>
        </div>
      )
    },
    {
      label: "区域类型",
      prop: "region_type",
      width: 120,
      cellRenderer: ({ row }) => (
        <div>
          <el-text type="primary">{row.region_type}</el-text>
        </div>
      )
    },
    {
      label: "事件类型",
      prop: "event_type",
      minWidth: 120,
      showOverflowTooltip: true
    },
    {
      label: "项目",
      prop: "projects",
      minWidth: 150,
      showOverflowTooltip: true,
      cellRenderer: ({ row }) => (
        <div style={{ "white-space": "normal", "text-align": "left" }}>
          {Array.isArray(row.projects)
            ? row.projects.map(item => (
                <el-tag key={item} size="large" style="margin: 3px;">
                  {item}
                </el-tag>
              ))
            : row.projects}
        </div>
      )
    },
    {
      label: "标签",
      prop: "tags",
      minWidth: 150,
      showOverflowTooltip: true,
      cellRenderer: ({ row }) => (
        <div style={{ "white-space": "normal", "text-align": "left" }}>
          {Array.isArray(row.tags)
            ? row.tags.map(item => (
                <el-tag key={item} size="large" style="margin: 3px;">
                  {item}
                </el-tag>
              ))
            : row.tags}
        </div>
      )
    },
    {
      label: "时间",
      prop: "start_time",
      minWidth: 160,
      sortable: true,
      cellRenderer: ({ row }) => (
        <div>
          <p>开始：{dayjs(row.start_time).format("YYYY-MM-DD HH:mm")}</p>
          <p>结束：{dayjs(row.end_time).format("YYYY-MM-DD HH:mm")}</p>
          <p>持续时长：{calculateDuration(row.start_time, row.end_time)}</p>
        </div>
      )
    },
    {
      label: "创建",
      prop: "created_at",
      minWidth: 160,
      sortable: true,
      cellRenderer: ({ row }) => (
        <div
          style={{
            "white-space": "nowrap",
            alignItems: "center"
          }}
        >
          <p style={{ marginLeft: "10px" }}> {row.created_by}</p>
          <p>{dayjs(row.created_at).format("YYYY-MM-DD HH:mm")}</p>
        </div>
      )
    },
    {
      label: "操作",
      prop: "action",
      width: 180,
      cellRenderer: ({ row }) => (
        <div class="flex items-center gap-2">
          <el-button
            type="primary"
            link
            class="action-button"
            onClick={() => copyTemplate(row)}
          >
            复制
          </el-button>
          <el-button
            type="primary"
            link
            class="action-button"
            onClick={() => viewDetail(row)}
          >
            查看
          </el-button>
          <el-button
            type="primary"
            link
            class="action-button"
            onClick={() => editFunc(row)}
          >
            编辑
          </el-button>
          <el-button
            type="danger"
            link
            class="action-button"
            onClick={() => deleteFunc(row)}
          >
            删除
          </el-button>
        </div>
      )
    }
  ];
  const editForm = ref<JournalForm>();
  const childrenRef = ref<UformRef | null>(null);
  function editFunc(row: Journal) {
    editForm.value = {} as JournalForm;
    addDrawer({
      headerRenderer: ({ titleId, titleClass }) => (
        <div class="flex flex-row justify-between">
          <h4 id={titleId} class={titleClass}>
            编辑事件
            <b>{row.title}</b>
          </h4>
        </div>
      ),
      size: "100%",
      contentRenderer: () =>
        h(Uform, {
          formInline: {
            row,
            form: editForm.value
          },
          ref: childrenRef
        }),
      beforeSure: done => {
        if (childrenRef.value?.ruleFormRef) {
          childrenRef.value.ruleFormRef
            .validate((valid: boolean) => {
              if (valid) {
                // 确保 newFormInline 存在
                if (!childrenRef.value?.newFormInline?.form) {
                  message("表单数据异常", { type: "error" });
                  return;
                }
                const formData = childrenRef.value.newFormInline.form;
                updateJournalAPI(row.id, formData)
                  .then(res => {
                    if (res.success) {
                      message(res.msg, { type: "success" });
                      done();
                      editForm.value = undefined;
                      onSearch();
                    } else {
                      message(res.msg, { type: "error" });
                    }
                  })
                  .catch(error => {
                    message(error, { type: "error" });
                  });
              } else {
                message("请检查表单数据", { type: "error" });
              }
            })
            .catch(error => {
              message("表单验证失败: " + error, { type: "error" });
            });
        }
      }
    });
  }
  function copyTemplate(row: Journal) {
    editForm.value = {
      title: row.title,
      event_type: row.event_type,
      tags: row.tags,
      start_time: row.start_time,
      end_time: row.end_time,
      content: "",
      conclusion: "",
      projects: row.projects,
      created_by: "",
      region_type: row.region_type
    };
    addDrawer({
      headerRenderer: ({ titleId, titleClass }) => (
        <div class="flex flex-row justify-between">
          <h4 id={titleId} class={titleClass}>
            添加事件
          </h4>
        </div>
      ),
      size: "100%",
      contentRenderer: () =>
        h(Uform, {
          formInline: {
            form: editForm.value
          },
          ref: childrenRef
        }),
      beforeSure: done => {
        if (childrenRef.value?.ruleFormRef) {
          childrenRef.value.ruleFormRef.validate((valid: boolean) => {
            if (valid) {
              createJournalAPI(editForm.value)
                .then(res => {
                  if (res.success) {
                    message(res.msg, { type: "success" });
                    editForm.value = undefined;
                    onSearch();
                    done();
                  } else {
                    message(res.msg, { type: "error" });
                  }
                })
                .catch(error => {
                  message(error, { type: "error" });
                });
            } else {
              message("请检查表单数据", { type: "error" });
            }
          });
        } else {
          message("请检查表单", { type: "error" });
        }
      }
    });
  }

  function addFunc() {
    editForm.value = {
      title: "",
      event_type: "",
      tags: [],
      start_time: "",
      end_time: "",
      content: "",
      conclusion: "",
      projects: [],
      created_by: "",
      region_type: "国内"
    };
    addDrawer({
      headerRenderer: ({ titleId, titleClass }) => (
        <div class="flex flex-row justify-between">
          <h4 id={titleId} class={titleClass}>
            添加事件
          </h4>
        </div>
      ),
      size: "100%",
      contentRenderer: () =>
        h(Uform, {
          formInline: {
            form: editForm.value
          },
          ref: childrenRef
        }),
      beforeSure: done => {
        if (childrenRef.value?.ruleFormRef) {
          childrenRef.value.ruleFormRef.validate((valid: boolean) => {
            if (valid) {
              createJournalAPI(editForm.value)
                .then(res => {
                  if (res.success) {
                    message(res.msg, { type: "success" });
                    editForm.value = undefined;
                    onSearch();
                    done();
                  } else {
                    message(res.msg, { type: "error" });
                  }
                })
                .catch(error => {
                  message(error, { type: "error" });
                });
            } else {
              message("请检查表单数据", { type: "error" });
            }
          });
        } else {
          message("请检查表单", { type: "error" });
        }
      }
    });
  }

  function deleteFunc(row: Journal) {
    addDialog({
      title: "",
      width: 500,
      sureBtnLoading: true,
      contentRenderer: () => (
        <p>
          确认删除：
          <b style="color:red">{row.title}</b>
        </p>
      ),
      beforeSure: done => {
        deleteJournalAPI(row.id)
          .then(res => {
            if (res.success) {
              done();
              message(res.msg, { type: "success" });
              onSearch();
            } else {
              message(res.msg, { type: "error" });
            }
          })
          .catch(error => {
            message(error, { type: "error" });
          });
      }
    });
  }

  function handleSizeChange(val: number) {
    pagination.pageSize = val;
    onSearch();
  }

  function handleCurrentChange(val: number) {
    pagination.currentPage = val;
    onSearch();
  }

  async function onSearch() {
    loading.value = true;
    getJournalListAPI({
      page: pagination.currentPage,
      limit: pagination.pageSize,
      keyword: form.keyword
    })
      .then(res => {
        if (res.success) {
          dataList.value = res.data as Journal[];
          pagination.total = res.count;
          pagination.pageSize = pagination.pageSize;
          pagination.currentPage = pagination.currentPage;
        } else {
          dataList.value = [];
          pagination.total = 0;
          pagination.pageSize = pagination.pageSize;
          pagination.currentPage = pagination.currentPage;
        }
      })
      .catch(() => {
        message("请求失败", { type: "error" });
      })
      .finally(() => {
        loading.value = false;
      });
  }

  const resetForm = formEl => {
    if (!formEl) return;
    formEl.resetFields();
    onSearch();
  };

  const router = useRouter();
  function viewDetail(row: Journal) {
    router.push(`/note/journal/detail/${row.id}`);
  }

  onMounted(() => {
    onSearch();
  });

  return {
    form,
    loading,
    columns,
    dataList,
    pagination,
    onSearch,
    resetForm,
    handleSizeChange,
    handleCurrentChange,
    addFunc
  };
}
