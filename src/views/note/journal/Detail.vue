<template>
  <div class="journal-detail">
    <el-card shadow="never" class="detail-card">
      <template #header>
        <div class="flex justify-between items-center">
          <h2 class="text-xl font-bold">{{ detail?.title }}</h2>
          <div class="flex gap-2">
            <el-button type="primary" @click="handleBack">返回</el-button>
          </div>
        </div>
      </template>

      <div class="detail-content">
        <!-- 基本信息 -->
        <div class="info-section mb-6">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="地域类型">
              {{ detail?.region_type }}
            </el-descriptions-item>
            <el-descriptions-item label="事件类型">
              {{ detail?.event_type }}
            </el-descriptions-item>
            <el-descriptions-item label="项目">
              <el-tag
                v-for="project in detail?.projects"
                :key="project"
                size="large"
                type="primary"
                style="margin-right: 5px"
                >{{ project }}</el-tag
              >
            </el-descriptions-item>
            <el-descriptions-item label="标签">
              <el-tag
                v-for="tag in detail?.tags"
                :key="tag"
                size="large"
                type="primary"
                style="margin-right: 5px"
                >{{ tag }}</el-tag
              >
            </el-descriptions-item>
            <el-descriptions-item label="开始～结束时间">
              {{ formatTime(detail?.start_time) }} ~
              {{ formatTime(detail?.end_time) }}
            </el-descriptions-item>
            <el-descriptions-item label="持续时长">
              {{ calculateDuration(detail?.start_time, detail?.end_time) }}
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 内容 -->
        <div class="content-section mb-6">
          <h3 class="text-lg font-bold mb-2">内容</h3>
          <div class="content-box" v-html="detail?.content" />
        </div>

        <!-- 结论 -->
        <div class="conclusion-section">
          <h3 class="text-lg font-bold mb-2">结论</h3>
          <div class="conclusion-box" v-html="detail?.conclusion" />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { getJournalDetailAPI, type Journal } from "@/api/note/journal";
import { message } from "@/utils/message";
import dayjs from "dayjs";
import duration from "dayjs/plugin/duration";

// 添加 duration 插件
dayjs.extend(duration);

const route = useRoute();
const router = useRouter();
const detail = ref<Journal>();

const formatTime = (time: string) => {
  return time ? dayjs(time).format("YYYY-MM-DD HH:mm:ss") : "-";
};

const getDetail = async () => {
  try {
    const id = Number(route.params.id);
    if (!id) return;

    const res = await getJournalDetailAPI(id);
    if (res.success && res.data) {
      detail.value = res.data as Journal;
    } else {
      message("获取详情失败", { type: "error" });
    }
  } catch (error) {
    message("获取详情失败", { type: "error" });
  }
};

// 返回按钮处理函数
const handleBack = () => {
  router.push("/note/journal");
};

// 计算时长
const calculateDuration = (startTime?: string, endTime?: string) => {
  if (!startTime || !endTime) return "-";

  const start = dayjs(startTime);
  const end = dayjs(endTime);
  const diff = end.diff(start);

  const dur = dayjs.duration(diff);

  // 格式化时长显示
  const days = dur.days();
  const hours = dur.hours();
  const minutes = dur.minutes();

  let result = [];
  if (days > 0) result.push(`${days}天`);
  if (hours > 0) result.push(`${hours}小时`);
  if (minutes > 0) result.push(`${minutes}分钟`);

  return result.length > 0 ? result.join(" ") : "小于1分钟";
};

onMounted(() => {
  getDetail();
});
</script>

<style lang="scss" scoped>
.journal-detail {
  padding: 20px;

  .detail-card {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
  }

  .content-box,
  .conclusion-box {
    min-height: 100px;
    padding: 16px;
    background: #fafafa;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
  }

  :deep(.el-descriptions) {
    padding: 16px;
    background: #fafafa;
    border-radius: 4px;
  }
}
</style>
