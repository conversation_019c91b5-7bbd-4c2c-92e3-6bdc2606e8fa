<template>
  <div class="journal-form">
    <el-card shadow="never" class="form-card">
      <el-form
        ref="ruleFormRef"
        label-width="100px"
        style="max-width: 2000px; margin: auto"
        :model="newFormInline.form"
        size="large"
        status-icon
        label-position="left"
      >
        <el-form-item
          label="标题"
          prop="title"
          :rules="[
            { required: true, message: '请输入名称', trigger: 'blur' },
            { max: 255, message: '请输入最大255', trigger: 'blur' }
          ]"
        >
          <el-input
            v-model="newFormInline.form.title"
            placeholder="请输入名称"
            maxlength="255"
            show-word-limit
            class="input-field"
          />
        </el-form-item>
        <el-form-item
          label="地域类型"
          prop="region_type"
          :rules="[
            { required: true, message: '请输入地域类型', trigger: 'blur' }
          ]"
        >
          <el-radio-group
            v-model="newFormInline.form.region_type"
            placeholder="请选择地域类型"
            class="input-field"
          >
            <el-radio label="国内"></el-radio>
            <el-radio label="国际"></el-radio>
          </el-radio-group>
        </el-form-item>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item
              label="项目"
              prop="projects"
              :rules="[
                { required: true, message: '请输入项目', trigger: 'blur' }
              ]"
            >
              <el-select
                v-model="newFormInline.form.projects"
                multiple
                filterable
                allow-create
                default-first-option
                placeholder="请选择项目"
                class="input-field"
              >
                <el-option
                  v-for="project in projectTypes"
                  :key="project"
                  :label="project"
                  :value="project"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="事件类型"
              prop="event_type"
              :rules="[
                { required: true, message: '请输入事件类型', trigger: 'blur' }
              ]"
            >
              <el-select
                v-model="newFormInline.form.event_type"
                filterable
                allow-create
                default-first-option
                placeholder="请选择事件类型"
                class="input-field"
              >
                <el-option
                  v-for="type in eventTypes"
                  :key="type"
                  :label="type"
                  :value="type"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="时间范围" prop="timeRange">
              <el-date-picker
                v-model="timeRange"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                class="input-field"
                @change="handleTimeRangeChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="标签" prop="tags">
              <el-select
                v-model="newFormInline.form.tags"
                multiple
                filterable
                allow-create
                default-first-option
                placeholder="请选择标签"
                class="input-field"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item
          label="内容"
          prop="content"
          :rules="[{ required: true, message: '请输入内容', trigger: 'blur' }]"
        >
          <div class="editor-container">
            <Toolbar
              style="border-bottom: 1px solid #ccc"
              :editor="editorRef"
              :defaultConfig="toolbarConfig"
              :mode="mode"
            />
            <Editor
              v-model="newFormInline.form.content"
              class="editor-content"
              :defaultConfig="editorConfig"
              :mode="mode"
              @onCreated="handleCreated"
            />
          </div>
        </el-form-item>
        <el-form-item label="结论" prop="conclusion">
          <div class="editor-container">
            <Toolbar
              style="border-bottom: 1px solid #ccc"
              :editor="conclusionEditorRef"
              :defaultConfig="toolbarConfig"
              :mode="mode"
            />
            <Editor
              v-model="newFormInline.form.conclusion"
              class="editor-content conclusion-editor"
              :defaultConfig="conclusionEditorConfig"
              :mode="mode"
              @onCreated="handleConclusionCreated"
            />
          </div>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { defineProps, ref, shallowRef, onBeforeUnmount, watch } from "vue";
import type { FormInstance } from "element-plus";
import type { JournalForm } from "@/api/note/journal";
import { getJournalDetailAPI } from "@/api/note/journal";
import { message } from "@/utils/message";
import "@wangeditor/core/dist/css/style.css"; // 引入 css
import { Editor, Toolbar } from "@wangeditor/editor-for-vue";

export interface FormProps {
  formInline: {
    row?: {
      id: number;
      [key: string]: any;
    };
    form: JournalForm;
  };
}
const props = withDefaults(defineProps<FormProps>(), {
  formInline: () => ({ row: undefined, form: undefined })
});

const newFormInline = ref(props.formInline);
const ruleFormRef = ref<FormInstance>();

// 编辑器实例，必须用 shallowRef
const editorRef = shallowRef();
const mode = ref("default");
const toolbarConfig = {
  excludeKeys: [] // 可以在这里配置要排除的工具栏按钮
};
const editorConfig = {
  placeholder: "请输入内容...",
  minHeight: 500,
  height: 500,
  autoFocus: false,
  MENU_CONF: {
    uploadImage: {
      base64LimitSize: 5 * 1024 * 1024, // 5MB 以下使用 base64
      customInsert(res: any, insertFn: Function) {
        // 自定义插入图片
        // 从 res 中获取图片地址
        const url = res.data?.url || res.url || res;
        // 插入图片到编辑器
        insertFn(url);
      },
      // 上传之前触发
      onBeforeUpload(file: File) {
        // 将图片转换为 base64
        return new Promise(resolve => {
          const reader = new FileReader();
          reader.readAsDataURL(file);
          reader.onload = () => {
            resolve(reader.result);
          };
        });
      }
    }
  }
};

// 结论编辑器实例
const conclusionEditorRef = shallowRef();

// 结论编辑器配置
const conclusionEditorConfig = {
  placeholder: "请输入结论...",
  minHeight: 300,
  height: 300,
  autoFocus: false,
  MENU_CONF: {
    uploadImage: {
      base64LimitSize: 5 * 1024 * 1024,
      customInsert(res: any, insertFn: Function) {
        const url = res.data?.url || res.url || res;
        insertFn(url);
      },
      onBeforeUpload(file: File) {
        return new Promise(resolve => {
          const reader = new FileReader();
          reader.readAsDataURL(file);
          reader.onload = () => {
            resolve(reader.result);
          };
        });
      }
    }
  }
};

// 结论编辑器创建回调
const handleConclusionCreated = (editor: any) => {
  conclusionEditorRef.value = editor;
};

// 组件销毁时，同时销毁两个编辑器
onBeforeUnmount(() => {
  const editor = editorRef.value;
  const conclusionEditor = conclusionEditorRef.value;
  if (editor != null) editor.destroy();
  if (conclusionEditor != null) conclusionEditor.destroy();
});

const handleCreated = (editor: any) => {
  editorRef.value = editor; // 记录 editor 实例
};

// 获取默认时间范围
const getDefaultTimeRange = () => {
  const startTime = new Date();
  startTime.setHours(startTime.getHours() - 1); // 设置为一小时前

  const endTime = new Date(startTime);
  endTime.setMinutes(endTime.getMinutes() + 5); // 开始时间后5分钟

  return [startTime, endTime] as [Date, Date];
};

// 时间范围
const timeRange = ref<[Date, Date] | null>(getDefaultTimeRange());

// 监听表单数据变化，同步时间范围
watch(
  () => newFormInline.value.form,
  newVal => {
    if (newVal?.start_time && newVal?.end_time) {
      timeRange.value = [
        new Date(newVal.start_time),
        new Date(newVal.end_time)
      ];
    } else if (!timeRange.value) {
      // 只在 timeRange 为空时设置默认值
      const [startTime, endTime] = getDefaultTimeRange();
      timeRange.value = [startTime, endTime];
      newFormInline.value.form.start_time = startTime.toISOString();
      newFormInline.value.form.end_time = endTime.toISOString();
    }
  },
  { immediate: true }
);

// 处理时间范围变化
const handleTimeRangeChange = (val: [Date, Date] | null) => {
  if (val) {
    newFormInline.value.form.start_time = val[0].toISOString();
    newFormInline.value.form.end_time = val[1].toISOString();
  } else {
    newFormInline.value.form.start_time = "";
    newFormInline.value.form.end_time = "";
  }
};

// 预定义的事件类型选项
const eventTypes = [
  "阿里云故障",
  "应用服务异常导致故障",
  "运维作导致故障",
  "代码发布导致故障",
  "华为云故障",
  "流量异常",
  "网络攻击",
  "其他"
];

// 预定义的项目选项
const projectTypes = ["用户中心", "社区", "经期", "孕期", "搜索", "AI"];

// 获取详情数据
const getDetail = async (id: number) => {
  try {
    const res = await getJournalDetailAPI(id);
    if (res.success && res.data) {
      const detail = res.data as any;
      newFormInline.value.form = {
        title: detail.title,
        event_type: detail.event_type,
        tags: detail.tags,
        start_time: detail.start_time,
        end_time: detail.end_time,
        content: detail.content,
        conclusion: detail.conclusion,
        projects: detail.projects,
        created_by: detail.created_by,
        region_type: detail.region_type
      };
    } else {
      message("获取详情失败", { type: "error" });
    }
  } catch (error) {
    message("获取详情失败", { type: "error" });
  }
};

// 监听 form 变化，如果有 id 则获取详情
watch(
  () => props.formInline,
  newVal => {
    if (newVal?.row?.id) {
      getDetail(newVal.row.id);
    }
  },
  { immediate: true }
);

// 修改 defineExpose，暴露 newFormInline
defineExpose({
  ruleFormRef,
  newFormInline
});
</script>

<style lang="scss">
.journal-form {
  .editor-container {
    z-index: 100;
    border: 1px solid #ccc;

    .editor-content {
      height: auto;
      min-height: 500px;

      &.conclusion-editor {
        height: auto;
        min-height: 500px;
      }
    }
  }

  .w-e-full-screen-container {
    z-index: 9999 !important;
  }

  :deep(.el-form-item) {
    margin-bottom: 20px;
  }

  :deep(.el-row) {
    margin-bottom: -20px; // 抵消 el-form-item 的下边距
  }

  :deep(.el-date-editor.el-input__wrapper) {
    width: 100% !important; // 确保日期选择器宽度填满容器
  }

  :deep(.el-select) {
    width: 100%; // 确保选择器宽度填满容器
  }
}

.input-field {
  border-radius: 4px;
  transition: border-color 0.3s;

  &.el-date-editor.el-input__wrapper {
    width: 100%; // 确保时间范围选择器宽度合适
  }
}

.input-field:focus {
  border-color: #409eff;
}

.form-card {
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
}
</style>
