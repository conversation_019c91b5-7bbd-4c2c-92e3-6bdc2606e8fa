<template>
  <div class="duty-container">
    <el-card
      shadow="never"
      class="action-card"
      style="
        margin-bottom: 20px;
        border-radius: 12px;
        box-shadow: 0 4px 16px rgb(0 0 0 / 8%);
        transition: box-shadow 0.3s ease;
      "
    >
      <div class="action-buttons">
        <el-button
          type="primary"
          link
          class="refresh-button"
          style="margin-right: 12px; color: #409eff; transition: all 0.3s ease"
          @click="fetchDuties"
        >
          刷新数据
        </el-button>
        <el-button
          type="warning"
          link
          class="notify-button"
          style="color: #e6a23c; transition: all 0.3s ease"
          @click="notifyDuty"
        >
          值班通知
        </el-button>
      </div>
    </el-card>

    <el-card
      shadow="never"
      class="tabs-card"
      style="border-radius: 12px; box-shadow: 0 4px 16px rgb(0 0 0 / 4%)"
    >
      <el-tabs
        v-model="activeTab"
        type="card"
        style="

          --el-tabs-header-hover-text-color: #409eff;
          --el-tabs-header-active-text-color: #409eff;
        "
      >
        <el-tab-pane label="值班日历" name="duty">
          <div
            class="calendar-container"
            style="padding: 20px; border-radius: 12px"
          >
            <el-calendar
              v-model="currentDate"
              class="calendar"
              style="

                --el-calendar-cell-width: 100px;
                --el-calendar-cell-height: 100px;
              "
              @cell-click="handleCellClick"
            >
              <template #date-cell="{ data }">
                <div
                  class="date-cell"
                  :class="{
                    weekend: isWeekend(data.day),
                    today: isToday(data.day)
                  }"
                  :style="{
                    backgroundColor: getDayColor(data.day)
                  }"
                  style="
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    height: 100%;
                    padding: 8px;
                    border-radius: 8px;
                    transition: background-color 0.3s ease;
                  "
                >
                  <p
                    class="day"
                    style="
                      margin-bottom: 8px;
                      font-weight: bold;
                      color: #303133;
                    "
                  >
                    {{ data.day.split("-").slice(-1).join("-") }}
                  </p>
                  <p
                    v-if="duties[data.day]"
                    class="person"
                    style="
                      display: flex;
                      align-items: center;
                      margin-bottom: 4px;
                      color: #606266;
                    "
                  >
                    <el-icon
                      name="user"
                      class="icon"
                      style="margin-right: 4px"
                    />
                    {{ duties[data.day].name }}（{{ duties[data.day].sn }}）
                  </p>
                  <p
                    v-if="duties[data.day]"
                    class="person"
                    style="display: flex; align-items: center"
                  >
                    <el-icon
                      name="phone"
                      class="icon"
                      style="margin-right: 4px"
                    />
                    {{ duties[data.day].phone }}
                  </p>
                </div>
              </template>
            </el-calendar>
          </div>
        </el-tab-pane>
        <el-tab-pane label="排班功能" name="schedule">
          <div class="schedule-form" style="padding: 20px; border-radius: 12px">
            <el-form :model="scheduleForm" label-width="120px">
              <el-form-item label="设置值班人员">
                <el-transfer
                  v-model="selectedPersonnel"
                  :data="personnelList"
                  filterable
                  :titles="['可选人员', '已选人员']"
                  :props="transferProps"
                  target-order="push"
                />
              </el-form-item>
              <el-form-item>
                <el-button
                  type="primary"
                  class="small-button"
                  style="
                    background: linear-gradient(
                      135deg,
                      #409eff 0%,
                      #4facff 100%
                    );
                    border: none;
                    border-radius: 8px;
                    box-shadow: 0 4px 12px rgb(64 158 255 / 30%);
                    transition: all 0.3s ease;
                  "
                  @click="handleSetPersonnel"
                >
                  保存
                </el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>
        <el-tab-pane label="重新排班" name="reschedule">
          <div class="schedule-form" style="padding: 20px; border-radius: 12px">
            <el-form :model="scheduleForm" label-width="120px">
              <el-form-item label="开始时间">
                <el-date-picker
                  v-model="scheduleForm.start_time"
                  type="date"
                  placeholder="选择开始时间"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  :disabled-date="disabledDate"
                  :default-value="new Date()"
                  style="

                    --el-input-border-radius: 8px;
                    --el-input-hover-border-color: #4fc3f7;
                    --el-input-focus-border-color: #29b6f6;

                    width: 220px;
                  "
                />
              </el-form-item>
              <el-form-item>
                <el-button
                  type="primary"
                  class="small-button"
                  style="
                    background: linear-gradient(
                      135deg,
                      #409eff 0%,
                      #4facff 100%
                    );
                    border: none;
                    border-radius: 8px;
                    box-shadow: 0 4px 12px rgb(64 158 255 / 30%);
                    transition: all 0.3s ease;
                  "
                  @click="handleReAutoSchedule"
                >
                  自动排班
                </el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from "vue";
import dayjs from "dayjs"; // 引入 dayjs
import { getDutyAPI, notifyDutyAPI } from "@/api/note/duty"; // 确保路径正确
import { ReAutoScheduleAPI } from "@/api/note/duty"; // 引入 ReAutoScheduleAPI
import { getAllUsersAPI, type User } from "@/api/auth/user"; // 引入获取用户的 API
import {
  getAllDutyMembersAPI,
  updateDutyMembersAPI,
  type DutyMemberForm
} from "@/api/note/duty"; // 引入获取和更新值班人员的 API
import { message } from "@/utils/message";
import type { DutyMember, Duty } from "@/api/note/duty";

const currentDate = ref(new Date());
const duties = ref<{ [key: string]: DutyMember }>({}); // 使用对象以便按日期存储值班人员
const scheduleForm = ref({
  start_time: dayjs().add(1, "day").format("YYYY-MM-DD") // 默认填写明天的日期，格式化为 YYYY-MM-DD
});
const activeTab = ref("duty"); // 当前激活的 Tab 页
const selectedPersonnel = ref<number[]>([]); // 存储已选的值班人员
const personnelList = ref([]); // 可选的值班人员列表

// Transfer 组件的属性
const transferProps = {
  key: "id", // 选项的唯一标识
  label: "name" // 显示的文本
};

// 获取当前月份的函数，返回格式为 YYYY-MM
const getCurrentMonth = () => {
  return dayjs(currentDate.value).format("YYYY-MM"); // 使用 dayjs 获取当前月份
};

const fetchDuties = async () => {
  const month = getCurrentMonth(); // 获取当前月份
  try {
    const res = await getDutyAPI(month);
    if (res.success) {
      // 将返回的数据格式化为按日期存储的对象
      duties.value = res.data.reduce((acc, duty: Duty) => {
        acc[duty.due_date_string] = duty; // 将值班人员按日期存储
        return acc;
      }, {});
    } else {
      message(res.msg, { type: "error" });
    }
  } catch (error) {
    message("获取值班数据失败，请重试", { type: "error" });
  }
};

const notifyDuty = async () => {
  try {
    const response = await notifyDutyAPI();
    if (response.success) {
      message(response.msg, { type: "success" });
      // 重新获取值数据
      await fetchDuties();
    } else {
      message(response.msg, { type: "error" });
    }
  } catch (error) {
    message("调用自动排班 API 失败，请重试", { type: "error" });
  }
};

// 获取可选的值班人员列表
const fetchPersonnelList = async () => {
  try {
    const res = await getAllDutyMembersAPI(); // 调用获取值班人员的 API
    if (res.success) {
      const selectedUids = res.data.map(member => member.uid); // 获取已选人员的 uid
      const allUsers = await getAllUsersAPI(); // 获取所有用户
      if (allUsers.success) {
        if (Array.isArray(allUsers.data)) {
          allUsers.data.forEach((user: User) => {
            if (user.is_disabled === false) {
              personnelList.value.push(user);
              if (selectedUids.includes(user.id)) {
                selectedPersonnel.value.push(user.id);
              }
            }
          });
        } else if (allUsers.data.is_disabled === false) {
          personnelList.value = [allUsers.data as User];
          if (selectedUids.includes(allUsers.data.id)) {
            selectedPersonnel.value = [allUsers.data.id];
          }
        }
      } else {
        message(allUsers.msg, { type: "error" });
      }
    } else {
      message(res.msg, { type: "error" });
    }
  } catch (error) {
    message("获取人员列表失败，请重试", { type: "error" });
  }
};

// 监视 currentDate 的变化
watch(currentDate, async () => {
  await fetchDuties(); // 重新获取值班数据
});

// 组件挂载时获取值班数据和人员列表
onMounted(async () => {
  await fetchDuties();
  await fetchPersonnelList();
});

const handleCellClick = date => {
  // 处理单元格点击事件
  console.log("Clicked date:", date);
};

const handleReAutoSchedule = async () => {
  const start_time = scheduleForm.value.start_time; // 只获取开始时间
  if (!start_time) {
    message("请先选择开始时间", { type: "warning" });
    return;
  }
  try {
    const response = await ReAutoScheduleAPI({
      start_time // 只传递开始时间
    });
    if (response.success) {
      message(response.msg, { type: "success" });
      // 重新获取值数据
      await fetchDuties();
    } else {
      message(response.msg, { type: "error" });
    }
  } catch (error) {
    message("调用自动排班 API 失败，请重试", { type: "error" });
  }
};

// 设置值班人员的处理函数
const handleSetPersonnel = async () => {
  try {
    const forms: DutyMemberForm[] = selectedPersonnel.value.map(
      (uid, index) => ({
        uid: uid, // 确保包含 id
        order_index: index + 1 // 如果需要，确保包含 name
      })
    );
    const response = await updateDutyMembersAPI({ members: forms });
    if (response.success) {
      message("已设置值班人员", { type: "success" });
      // 重新获取值班数据
      await fetchDuties();
    } else {
      message(response.msg, { type: "error" });
    }
  } catch (error) {
    message("设置值班人员失败，请重试", { type: "error" });
  }
};

// 判断是否为周末
const isWeekend = dateString => {
  const date = new Date(dateString);
  const day = date.getDay(); // 0: 周日, 6: 周六
  return day === 0 || day === 6;
};

// 判断是否为今天
const isToday = dateString => {
  const today = new Date();
  const date = new Date(dateString);
  return (
    today.getFullYear() === date.getFullYear() &&
    today.getMonth() === date.getMonth() &&
    today.getDate() === date.getDate()
  );
};

// 限制选择的日期
const disabledDate = date => {
  return date.getTime() < Date.now(); // 禁用今天及之前的日期
};

const getDayColor = dateString => {
  const date = new Date(dateString);
  const day = date.getDay(); // 获取星期几

  if (day === 0 || day === 6) {
    // 周末
    return "#f0e68c"; // 柔和的浅黄色
  } else {
    // 工作日
    return "#e6f7ff"; // 柔和的浅蓝色
  }
};
</script>

<style scoped lang="scss">
.duty-container {
  min-height: calc(100vh - 84px);
  padding: 24px;
  background-color: #f8fafc;

  .action-card {
    margin-bottom: 24px;
    background: linear-gradient(135deg, #fff 0%, #f8fafc 100%);
    border: 1px solid rgb(226 232 240 / 80%);
    border-radius: 12px;
    box-shadow: 0 4px 16px rgb(0 0 0 / 5%);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 8px 24px rgb(0 0 0 / 8%);
      transform: translateY(-2px);
    }

    .action-buttons {
      display: flex;
      gap: 16px;
      padding: 16px 24px;

      .refresh-button,
      .notify-button {
        position: relative;
        padding: 8px 16px;
        overflow: hidden;
        font-weight: 500;
        border-radius: 8px;
        transition: all 0.3s ease;

        &::before {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          content: "";
          background: linear-gradient(
            135deg,
            rgb(255 255 255 / 10%) 0%,
            rgb(255 255 255 / 0%) 100%
          );
          opacity: 0;
          transition: opacity 0.3s ease;
        }

        &:hover::before {
          opacity: 1;
        }

        &:hover {
          transform: translateY(-1px);
        }
      }
    }
  }

  .tabs-card {
    background: linear-gradient(135deg, #fff 0%, #f8fafc 100%);
    border: 1px solid rgb(226 232 240 / 80%);
    border-radius: 12px;
    box-shadow: 0 4px 16px rgb(0 0 0 / 5%);

    :deep(.el-tabs__header) {
      padding: 0 24px;
      margin: 0;
      background: rgb(255 255 255 / 80%);
      border-bottom: 1px solid rgb(226 232 240 / 80%);
      border-radius: 12px 12px 0 0;
    }

    :deep(.el-tabs__item) {
      padding: 16px 24px;
      font-size: 15px;
      font-weight: 500;
      color: #64748b;
      transition: all 0.3s ease;

      &.is-active {
        font-weight: 600;
        color: #3b82f6;
      }

      &:hover {
        color: #3b82f6;
      }
    }

    :deep(.el-tabs__nav-wrap::after) {
      display: none;
    }

    :deep(.el-tabs__active-bar) {
      height: 3px;
      background: linear-gradient(90deg, #3b82f6 0%, #60a5fa 100%);
      border-radius: 3px;
    }
  }

  .calendar-container {
    padding: 24px;
    background: rgb(255 255 255 / 80%);
    border-radius: 12px;

    :deep(.el-calendar) {
      background: transparent;
      border: none;

      .el-calendar__header {
        padding: 0 0 16px;
        margin-bottom: 16px;
        border-bottom: 1px solid rgb(226 232 240 / 80%);

        .el-calendar__title {
          font-size: 18px;
          font-weight: 600;
          color: #1e293b;
        }

        .el-calendar__button-group {
          .el-button {
            padding: 8px 16px;
            border-radius: 8px;
            transition: all 0.3s ease;

            &:hover {
              background-color: #f1f5f9;
            }
          }
        }
      }

      .el-calendar__body {
        padding: 0;

        .el-calendar-table {
          th {
            padding: 12px 0;
            font-weight: 600;
            color: #64748b;
            background: transparent;
          }

          td {
            padding: 4px;
          }
        }
      }
    }
  }

  .date-cell {
    position: relative;
    height: 100%;
    padding: 12px;
    background: linear-gradient(135deg, #fff 0%, #f8fafc 100%);
    border: 1px solid rgb(226 232 240 / 80%);
    border-radius: 12px;
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 12px rgb(0 0 0 / 5%);
      transform: translateY(-2px);
    }

    &.weekend {
      background: linear-gradient(135deg, #fff7ed 0%, #ffedd5 100%);
      border-color: rgb(251 191 36 / 20%);
    }

    &.today {
      background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
      border-color: rgb(59 130 246 / 20%);
    }

    .day {
      margin-bottom: 8px;
      font-size: 16px;
      font-weight: 600;
      color: #1e293b;
    }

    .person {
      display: flex;
      align-items: center;
      margin-bottom: 4px;
      font-size: 13px;
      color: #64748b;

      .icon {
        margin-right: 6px;
        font-size: 14px;
        color: #3b82f6;
      }
    }
  }

  .schedule-form {
    padding: 24px;
    background: rgb(255 255 255 / 80%);
    border-radius: 12px;

    :deep(.el-form-item__label) {
      font-weight: 500;
      color: #1e293b;
    }

    :deep(.el-transfer) {
      .el-transfer-panel {
        background: #fff;
        border: 1px solid rgb(226 232 240 / 80%);
        border-radius: 8px;

        .el-transfer-panel__header {
          padding: 12px 16px;
          font-weight: 500;
          color: #1e293b;
          background: #f8fafc;
          border-bottom: 1px solid rgb(226 232 240 / 80%);
        }

        .el-transfer-panel__body {
          padding: 12px;
        }

        .el-transfer-panel__filter {
          margin-bottom: 12px;

          .el-input__wrapper {
            border-radius: 6px;
            box-shadow: 0 0 0 1px rgb(226 232 240 / 80%);
          }
        }
      }

      .el-transfer__buttons {
        padding: 0 12px;

        .el-button {
          padding: 8px 16px;
          border-radius: 6px;
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-1px);
          }
        }
      }
    }

    :deep(.el-date-picker) {
      .el-input__wrapper {
        border-radius: 8px;
        box-shadow: 0 0 0 1px rgb(226 232 240 / 80%);
        transition: all 0.3s ease;

        &:hover {
          box-shadow: 0 0 0 1px #3b82f6;
        }

        &.is-focus {
          box-shadow: 0 0 0 1px #3b82f6;
        }
      }
    }

    .small-button {
      padding: 10px 24px;
      font-weight: 500;
      border-radius: 8px;
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 6px 16px rgb(59 130 246 / 40%);
        transform: translateY(-1px);
      }
    }
  }
}
</style>
