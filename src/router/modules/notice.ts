export default {
  path: "/notice",
  redirect: "/notice/contact",
  meta: {
    icon: "fe:notice-active",
    title: "通知管理",
    rank: 11
  },
  children: [
    {
      path: "/notice/contact",
      name: "联系人",
      component: () => import("@/views/notice/contact/index.vue"),
      meta: {
        title: "联系人",
        roles: ["admin"],
        icon: "lucide:contact"
      }
    },
    {
      path: "/notice/contact-groups",
      name: "联系人分组",
      component: () => import("@/views/notice/contact-group/index.vue"),
      meta: {
        title: "联系人分组",
        roles: ["admin"],
        icon: "fluent:contact-card-group-16-regular"
      }
    },
    {
      path: "/notice/message",
      name: "通知消息",
      component: () => import("@/views/notice/message/index.vue"),
      meta: {
        title: "通知消息",
        roles: ["admin"],
        icon: "ep:message"
      }
    },
    {
      path: "/notice/tools",
      name: "工具",
      component: () => import("@/views/notice/tools/index.vue"),
      meta: {
        title: "工具",
        roles: ["admin"],
        icon: "ep:tools"
      }
    }
  ]
} satisfies RouteConfigsTable;
