export default {
  path: "/db",
  redirect: "/database/mysql",
  meta: {
    icon: "entypo:database",
    title: "数据库",
    rank: 5
  },
  children: [
    {
      path: "/database/mysql",
      name: "MySQL管理",
      meta: {
        title: "MySQL管理",
        roles: ["admin"],
        icon: "devicon:mysql",
        showParent: true
      },
      children: [
        {
          path: "/database/mysql/project",
          name: "MySQL项目",
          component: () => import("@/views/database/mysql/project/index.vue"),
          meta: {
            title: "MySQL项目",
            roles: ["admin"],
            icon: "simple-icons:mysql",
            showParent: true
          }
        },
        {
          path: "/database/mysql/instance",
          name: "MySQL实例",
          component: () => import("@/views/database/mysql/instance/index.vue"),
          meta: {
            title: "MySQL实例",
            roles: ["admin"],
            icon: "cib:mysql",
            showParent: true
          }
        },
        {
          path: "/database/mysql/backup",
          name: "MySQL备份",
          component: () => import("@/views/database/mysql/backup/index.vue"),
          meta: {
            title: "MySQL备份",
            roles: ["admin"],
            icon: "material-symbols:backup",
            showParent: true
          }
        }
      ]
    },
    {
      path: "/database/redis",
      name: "Redis管理",
      redirect: "/database/redis/instance",
      meta: {
        title: "Redis管理",
        roles: ["admin"],
        icon: "simple-icons:redis",
        showParent: true
      },
      children: [
        {
          path: "/database/redis/instance",
          name: "Redis实例",
          component: () => import("@/views/database/redis/instance/index.vue"),
          meta: {
            title: "Redis实例",
            roles: ["admin"],
            icon: "simple-icons:redis",
            showParent: true
          }
        }
      ]
    },
    {
      path: "/database/mongodb",
      name: "MongoDB管理",
      meta: {
        title: "MongoDB管理",
        roles: ["admin"],
        icon: "simple-icons:mongodb",
        showParent: true
      },
      children: [
        {
          path: "/database/mongodb/instance",
          name: "MongoDB实例",
          component: () =>
            import("@/views/database/mongodb/instance/index.vue"),
          meta: {
            title: "MongoDB实例",
            roles: ["admin"],
            icon: "simple-icons:mongodb",
            showParent: true
          }
        }
      ]
    },
    {
      path: "/database/slowlog",
      name: "慢日志",
      redirect: "/database/slowlog/store",
      meta: {
        title: "慢日志",
        roles: ["admin"],
        icon: "ix:log",
        showParent: true
      },
      children: [
        {
          path: "/database/slowlog/store",
          name: "存储管理",
          component: () => import("@/views/database/slowlog/store/index.vue"),
          meta: {
            title: "存储管理",
            roles: ["admin"],
            icon: "grommet-icons:document-store",
            showParent: true
          }
        },
        {
          path: "/database/slowlog/mysql",
          name: "MySQL慢日志",
          component: () => import("@/views/database/slowlog/mysql/index.vue"),
          meta: {
            title: "MySQL慢日志",
            roles: ["admin"],
            icon: "simple-icons:mysql",
            showParent: true
          }
        },
        {
          path: "/database/slowlog/mongodb",
          name: "MongoDB慢日志",
          component: () => import("@/views/database/slowlog/mongodb/index.vue"),
          meta: {
            title: "MongoDB慢日志",
            roles: ["admin"],
            icon: "simple-icons:mongodb",
            showParent: true
          }
        },
        {
          path: "/database/slowlog/redis",
          name: "Redis大Key",
          component: () => import("@/views/database/slowlog/redis/index.vue"),
          meta: {
            title: "Redis大Key",
            roles: ["admin"],
            icon: "simple-icons:redis",
            showParent: true
          }
        }
      ]
    }
  ]
} satisfies RouteConfigsTable;
