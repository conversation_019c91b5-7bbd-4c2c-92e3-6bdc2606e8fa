export default {
  path: "/assets",
  redirect: "/assets/system",
  meta: {
    icon: "material-symbols:inventory-2",
    title: "资产管理",
    rank: 2
  },
  children: [
    {
      path: "/assets/system",
      name: "系统资产",
      component: () => import("@/views/assets/system/index.vue"),
      meta: {
        title: "系统资产",
        roles: ["admin"],
        icon: "material-symbols:settings-outline"
      }
    },
    {
      path: "/assets/system/detail/:id",
      name: "系统详情",
      component: () => import("@/views/assets/system/detail.vue"),
      meta: {
        title: "系统详情",
        roles: ["admin"],
        icon: "material-symbols:settings-outline",
        showLink: false
      }
    },
  ]
}