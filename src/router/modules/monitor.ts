export default {
  path: "/monitor",
  redirect: "/monitor/https",
  meta: {
    icon: "mdi:monitor",
    title: "监控管理",
    rank: 8
  },
  children: [
    {
      path: "/monitor/https",
      name: "域名监控",
      component: () => import("@/views/monitor/https/index.vue"),
      meta: {
        title: "域名监控",
        roles: ["admin"],
        icon: "mdi:dns",
        showParent: true
      }
    },
    {
      path: "/monitor/prometheus/instance",
      name: "Prometheus实例",
      component: () => import("@/views/monitor/prometheus/instance/index.vue"),
      meta: {
        title: "Prometheus实例",
        roles: ["admin"],
        icon: "mdi:chart-areaspline",
        showParent: true
      }
    },
    {
      path: "/monitor/prometheus/host",
      name: "低使用率主机",
      component: () => import("@/views/monitor/prometheus/host/index.vue"),
      meta: {
        title: "低使用率主机",
        roles: ["admin"],
        icon: "mdi:chart-areaspline",
        showParent: true
      }
    }
  ]
} satisfies RouteConfigsTable;
