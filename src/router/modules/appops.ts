export default {
  path: "/appops",
  name: "应用运维",
  redirect: "/appops/cloud-account",
  meta: {
    icon: "tdesign:app",
    title: "应用运维",
    rank: 4
  },
  children: [
    {
      path: "/appops/domain",
      name: "业务域名",
      component: () => import("@/views/appops/domain/index.vue"),
      meta: {
        title: "业务域名",
        roles: ["admin"],
        icon: "gridicons:domains"
      }
    },
    {
      path: "/appops/nginx",
      redirect: "/appops/nginx/backend",
      meta: {
        icon: "simple-icons:nginxproxymanager",
        title: "Nginx配置"
      },
      children: [
        {
          path: "/appops/nginx/backend",
          name: "nginx后端",
          component: () => import("@/views/appops/nginx/backend/index.vue"),
          meta: {
            title: "nginx后端",
            roles: ["admin"],
            icon: "simple-icons:nginx"
          }
        }
      ]
    },
    {
      path: "/appops/harbor",
      name: "镜像仓库",
      component: () => import("@/views/appops/harbor/index.vue"),
      meta: {
        title: "镜像仓库",
        roles: ["admin"],
        icon: "simple-icons:harbor"
      }
    },
    {
      path: "/appops/gitcode",
      redirect: "/appops/gitcode/gitlabs",
      meta: {
        icon: "icomoon-free:git",
        title: "Git项目管理"
      },
      children: [
        {
          path: "/appops/gitcode/gitlabs",
          name: "Gitlab",
          component: () => import("@/views/appops/gitcode/gitlab/index.vue"),
          meta: {
            title: "Gitlab",
            roles: ["admin"],
            icon: "simple-icons:gitlab"
          }
        },
        {
          path: "/appops/gitcode/groups",
          name: "项目分组",
          component: () => import("@/views/appops/gitcode/group/index.vue"),
          meta: {
            title: "项目分组",
            roles: ["admin"],
            icon: "simple-icons:git"
          }
        },
        {
          path: "/appops/gitcode/projects",
          name: "项目列表",
          component: () => import("@/views/appops/gitcode/project/index.vue"),
          meta: {
            title: "项目列表",
            roles: ["admin"],
            icon: "devicon-plain:git-wordmark"
          }
        }
      ]
    }
  ]
} satisfies RouteConfigsTable;
