export default {
  path: "/workflow",
  redirect: "/workflow/flows",
  meta: {
    icon: "material-symbols:work-outline",
    title: "工单管理",
    rank: 6
  },
  children: [
    {
      path: "/workflow/my/order",
      name: "我的工单",
      component: () => import("@/views/workflow/my-order/index.vue"),
      meta: {
        title: "我的工单",
        roles: ["admin", "common"],
        icon: "material-symbols:assignment-ind-outline",
        showParent: true
      }
    },
    {
      path: "/workflow/approver/orders",
      name: "审批工单",
      component: () => import("@/views/workflow/approver-order/index.vue"),
      meta: {
        title: "审批工单",
        roles: ["admin", "common"],
        icon: "material-symbols:approval-outline",
        showParent: true
      }
    },
    {
      path: "/workflow/orders",
      name: "所有工单",
      component: () => import("@/views/workflow/all-order/index.vue"),
      meta: {
        title: "所有工单",
        roles: ["admin"],
        icon: "material-symbols:list-alt-outline",
        showParent: true
      }
    },
    {
      path: "/workflow/flows",
      name: "流程配置",
      component: () => import("@/views/workflow/flow/index.vue"),
      meta: {
        title: "流程配置",
        roles: ["admin"],
        icon: "material-symbols:settings-outline",
        showParent: true
      }
    },
    {
      path: "/workflow/apply",
      name: "工单申请",
      meta: {
        title: "工单申请",
        roles: ["admin", "common"],
        icon: "material-symbols:dns-outline",
        showParent: true
      },
      children: [
        {
          path: "/workflow/apply/server",
          name: "服务器工单申请",
          component: () => import("@/views/workflow/order-server/index.vue"),
          meta: {
            title: "服务器工单申请",
            roles: ["admin", "common"],
            icon: "material-symbols:dns-outline",
            showParent: true,
            showLink: false,
            activePath: "/workflow/my/order"
          }
        },
        {
          path: "/workflow/apply/permission",
          name: "权限工单申请",
          component: () =>
            import("@/views/workflow/order-permission/index.vue"),
          meta: {
            title: "权限工单申请",
            roles: ["admin", "common"],
            icon: "material-symbols:security",
            showParent: true,
            showLink: false,
            activePath: "/workflow/my/order"
          }
        },
        {
          path: "/workflow/apply/domain",
          name: "域名工单申请",
          component: () => import("@/views/workflow/order-domain/index.vue"),
          meta: {
            title: "域名工单申请",
            roles: ["admin", "common"],
            icon: "material-symbols:domain",
            showParent: true,
            showLink: false,
            activePath: "/workflow/my/order"
          }
        },
        {
          path: "/workflow/apply/mysql",
          name: "数据库工单申请",
          component: () => import("@/views/workflow/order-mysql/index.vue"),
          meta: {
            title: "数据库工单申请",
            roles: ["admin", "common"],
            icon: "material-symbols:database",
            showParent: true,
            showLink: false,
            activePath: "/workflow/my/order"
          }
        },
        {
          path: "/workflow/apply/serviceneed",
          name: "业务需求工单申请",
          component: () =>
            import("@/views/workflow/order-service-need/index.vue"),
          meta: {
            title: "业务需求工单申请",
            roles: ["admin", "common"],
            icon: "material-symbols:request-page",
            showParent: true,
            showLink: false,
            activePath: "/workflow/my/order"
          }
        },
        {
          path: "/workflow/apply/mongodb",
          name: "MongoDB工单申请",
          component: () => import("@/views/workflow/order-mongodb/index.vue"),
          meta: {
            title: "MongoDB工单申请",
            roles: ["admin", "common"],
            icon: "simple-icons:mongodb",
            showParent: true,
            showLink: false,
            activePath: "/workflow/my/order"
          }
        },
        {
          path: "/workflow/apply/gitlab",
          name: "Gitlab权限工单申请",
          component: () => import("@/views/workflow/order-gitlab/index.vue"),
          meta: {
            title: "Gitlab权限工单申请",
            roles: ["admin", "common"],
            icon: "simple-icons:gitlab",
            showParent: true,
            showLink: false,
            activePath: "/workflow/my/order"
          }
        },
        {
          path: "/workflow/apply/desktop-ops",
          name: "桌面技术支持工单申请",
          component: () =>
            import("@/views/workflow/order-desktop-ops/index.vue"),
          meta: {
            title: "桌面技术支持工单申请",
            roles: ["admin", "common"],
            icon: "material-symbols:desktop-windows",
            showParent: true,
            showLink: false,
            activePath: "/workflow/my/order"
          }
        }
      ]
    },
    {
      path: "/workflow/order/detail/:id",
      name: "工单详情",
      component: () => import("@/views/workflow/order-detail/index.vue"),
      meta: {
        title: "工单详情",
        roles: ["admin", "common"],
        icon: "material-symbols:description-outline",
        showLink: false,
        showParent: true,
        activePath: "/workflow/approver/orders"
      }
    }
  ]
};
