export default {
  path: "/asset",
  redirect: "/asset/cloud-account",
  meta: {
    icon: "material-symbols:inventory-2",
    title: "资产管理",
    rank: 2
  },
  children: [
    {
      path: "/asset/host",
      name: "主机列表",
      component: () => import("@/views/asset/host/index.vue"),
      meta: {
        title: "主机列表",
        roles: ["admin"],
        icon: "material-symbols:computer-outline"
      }
    },
    {
      path: "/asset/cloud-account",
      name: "云账户",
      component: () => import("@/views/asset/cloud-account/index.vue"),
      meta: {
        title: "云账户",
        roles: ["admin"],
        icon: "material-symbols:cloud-outline"
      }
    },
    {
      path: "/asset/datacenter",
      name: "数据中心",
      component: () => import("@/views/asset/datacenter/index.vue"),
      meta: {
        title: "数据中心",
        roles: ["admin"],
        icon: "material-symbols:database"
      }
    },
    {
      path: "/asset/business",
      name: "业务列表",
      component: () => import("@/views/asset/business/index.vue"),
      meta: {
        title: "业务列表",
        roles: ["admin"],
        icon: "material-symbols:business-center-outline"
      }
    },
    {
      path: "/asset/resource-groups",
      name: "资源组",
      component: () => import("@/views/asset/resource-groups/index.vue"),
      meta: {
        title: "资源组",
        roles: ["admin"],
        icon: "material-symbols:group-work-outline"
      }
    },
    {
      path: "/asset/tag",
      name: "标签",
      component: () => import("@/views/asset/tag/index.vue"),
      meta: {
        title: "标签",
        roles: ["admin"],
        icon: "material-symbols:label-outline"
      }
    },
    {
      path: "/asset/bills",
      name: "月份账单",
      component: () => import("@/views/asset/bill/index.vue"),
      meta: {
        title: "月份账单",
        roles: ["admin"],
        icon: "material-symbols:receipt"
      }
    },
    {
      path: "/asset/private-domain",
      name: "内网域名",
      component: () => import("@/views/asset/private-domain/index.vue"),
      meta: {
        title: "内网域名",
        roles: ["admin"],
        icon: "material-symbols:dns-outline"
      }
    },
    {
      path: "/asset/public-domain",
      name: "外网域名",
      component: () => import("@/views/asset/public-domain/index.vue"),
      meta: {
        title: "外网域名",
        roles: ["admin"],
        icon: "material-symbols:domain"
      }
    },
    {
      path: "/asset/loadbalancer",
      name: "负载均衡",
      component: () => import("@/views/asset/loadbalancer/index.vue"),
      meta: {
        title: "负载均衡",
        roles: ["admin"],
        icon: "material-symbols:balance"
      }
    },
    {
      path: "/asset/subnet",
      name: "子网",
      component: () => import("@/views/asset/subnet/index.vue"),
      meta: {
        title: "子网",
        roles: ["admin"],
        icon: "material-symbols:language"
      }
    },
    {
      path: "/asset/public-ip",
      name: "公网IP",
      component: () => import("@/views/asset/public-ip/index.vue"),
      meta: {
        title: "公网IP",
        roles: ["admin"],
        icon: "material-symbols:language"
      }
    },
    {
      path: "/asset/ddos/instance",
      name: "DDos 防护实例",
      component: () => import("@/views/asset/ddos/instance/index.vue"),
      meta: {
        title: "DDos 防护实例",
        roles: ["admin"],
        icon: "material-symbols:language"
      }
    },
    {
      path: "/asset/ddos/domain",
      name: "DDos 防护域名",
      component: () => import("@/views/asset/ddos/domain/index.vue"),
      meta: {
        title: "DDos 防护域名",
        roles: ["admin"],
        icon: "material-symbols:domain"
      }
    }
  ]
} satisfies RouteConfigsTable;
