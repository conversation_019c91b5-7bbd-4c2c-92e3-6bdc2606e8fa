export default {
  path: "/auth",
  redirect: "/auth/user",
  meta: {
    icon: "material-symbols:admin-panel-settings-outline",
    title: "认证管理",
    rank: 9
  },
  children: [
    {
      path: "/auth/user",
      name: "用户列表",
      component: () => import("@/views/auth/user/index.vue"),
      meta: {
        title: "用户列表",
        roles: ["admin"],
        icon: "material-symbols:person-outline"
      }
    },
    {
      path: "/auth/group",
      name: "用户分组",
      component: () => import("@/views/auth/group/index.vue"),
      meta: {
        title: "用户分组",
        roles: ["admin"],
        icon: "material-symbols:group-outline"
      }
    },
    {
      path: "/auth/department",
      name: "用户部门",
      component: () => import("@/views/auth/department/index.vue"),
      meta: {
        title: "用户部门",
        roles: ["admin"],
        icon: "material-symbols:corporate-fare"
      }
    },
    {
      path: "/auth/key",
      name: "密钥",
      component: () => import("@/views/auth/key/index.vue"),
      meta: {
        title: "密钥",
        roles: ["admin"],
        icon: "material-symbols:key-outline"
      }
    }
  ]
} satisfies RouteConfigsTable;
