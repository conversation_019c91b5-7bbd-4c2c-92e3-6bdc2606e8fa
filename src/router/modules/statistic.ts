export default {
  path: "/statistic",
  redirect: "/statistic/asset/daily-report",
  meta: {
    icon: "material-symbols:analytics-outline",
    title: "统计分析",
    rank: 3
  },
  children: [
    {
      path: "/statistic/asset/daily-report",
      name: "资产日报",
      component: () => import("@/views/statistic/asset/daily-report.vue"),
      meta: {
        title: "资产日报",
        roles: ["admin"],
        icon: "carbon:report"
      }
    },
    {
      path: "/statistic/asset/monthly-report",
      name: "资产月报",
      component: () => import("@/views/statistic/asset/monthly-report.vue"),
      meta: {
        title: "资产月报",
        roles: ["admin"],
        icon: "carbon:report-data"
      }
    },
    {
      path: "/statistic/bill",
      name: "账单统计",
      component: () => import("@/views/statistic/bill/index.vue"),
      meta: {
        title: "账单统计",
        roles: ["admin"],
        icon: "ri:bill-line"
      }
    },
    {
      path: "/statistic/monitor/n9e",
      name: "夜莺告警统计",
      component: () => import("@/views/statistic/monitor/n9e/index.vue"),
      meta: {
        title: "夜莺告警统计",
        roles: ["admin"],
        icon: "mdi:alarm-bell"
      }
    }
  ]
} satisfies RouteConfigsTable;
