export default {
  path: "/audit",
  redirect: "/audit/login",
  meta: {
    icon: "material-symbols:fact-check-outline",
    title: "审计管理",
    rank: 10
  },
  children: [
    {
      path: "/audit/login",
      name: "登陆日志",
      component: () => import("@/views/audit/login/index.vue"),
      meta: {
        title: "登陆日志",
        roles: ["admin"],
        icon: "material-symbols:login"
      }
    },
    {
      path: "/audit/opertaion",
      name: "操作日志",
      component: () => import("@/views/audit/operate/index.vue"),
      meta: {
        title: "操作日志",
        roles: ["admin"],
        icon: "material-symbols:history"
      }
    }
  ]
} satisfies RouteConfigsTable;
