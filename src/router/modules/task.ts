export default {
  path: "/task",
  redirect: "/task/job",
  meta: {
    icon: "material-symbols:task-outline",
    title: "任务管理",
    rank: 7
  },
  children: [
    {
      path: "/task/batch-task",
      name: "任务列表",
      component: () => import("@/views/task/batch-task/index.vue"),
      meta: {
        title: "任务列表",
        roles: ["admin"],
        icon: "material-symbols:list-alt-outline"
      }
    },
    {
      path: "/task/batch-template",
      name: "任务模板",
      component: () => import("@/views/task/batch-template/index.vue"),
      meta: {
        title: "任务模板",
        roles: ["admin"],
        icon: "material-symbols:dashboard-customize-outline"
      }
    },
    {
      path: "/task/job",
      name: "作业列表",
      component: () => import("@/views/task/job/index.vue"),
      meta: {
        title: "作业列表",
        roles: ["admin"],
        icon: "material-symbols:work-outline"
      }
    },
    {
      path: "/task/job-log",
      name: "作业日志",
      component: () => import("@/views/task/job-log/index.vue"),
      meta: {
        title: "作业日志",
        roles: ["admin"],
        icon: "material-symbols:description-outline"
      }
    }
  ]
};
