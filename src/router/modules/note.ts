export default {
  path: "/note",
  redirect: "/note/journal",
  meta: {
    icon: "hugeicons:note-03",
    title: "事件管理",
    rank: 9
  },
  children: [
    {
      path: "/note/journal",
      name: "事件笔记",
      component: () => import("@/views/note/journal/index.vue"),
      meta: {
        title: "事件笔记",
        roles: ["admin", "qa"],
        icon: "ic:sharp-calendar-month",
        showParent: true
      }
    },
    {
      path: "/note/bookmark",
      name: "书签管理",
      component: () => import("@/views/note/bookmark/index.vue"),
      meta: {
        title: "书签管理",
        roles: ["admin"],
        icon: "fe:bookmark"
      }
    },
    {
      path: "/note/duty",
      name: "值班管理",
      component: () => import("@/views/note/duty/index.vue"),
      meta: {
        title: "值班管理",
        roles: ["admin"],
        icon: "ic:sharp-calendar-month"
      }
    },
    {
      path: "/note/journal/detail/:id",
      name: "事件详情",
      component: () => import("@/views/note/journal/Detail.vue"),
      meta: {
        title: "事件详情",
        roles: ["admin", "qa"],
        icon: "ic:sharp-info",
        showLink: false,
        activePath: "/note/journal"
      }
    }
  ]
} satisfies RouteConfigsTable;
