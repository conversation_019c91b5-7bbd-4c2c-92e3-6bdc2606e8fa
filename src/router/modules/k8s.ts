export default {
  path: "/k8s",
  redirect: "/k8s/cluster",
  meta: {
    icon: "mdi:kubernetes",
    title: "K8S集群",
    rank: 4
  },
  children: [
    {
      path: "/k8s/cluster",
      name: "集群列表",
      component: () => import("@/views/asset/k8s/cluster/index.vue"),
      meta: {
        title: "集群列表",
        roles: ["admin"],
        icon: "material-symbols:hub",
        showParent: true
      }
    },
    {
      path: "/k8s/node",
      name: "集群节点",
      component: () => import("@/views/asset/k8s/node/index.vue"),
      meta: {
        title: "集群节点",
        roles: ["admin"],
        icon: "material-symbols:device-hub",
        showParent: true
      }
    },
    {
      path: "/k8s/workload",
      name: "工作负载",
      component: () => import("@/views/asset/k8s/workload/index.vue"),
      meta: {
        title: "工作负载",
        roles: ["admin"],
        icon: "material-symbols:deployed-code",
        showParent: true
      }
    },
    {
      path: "/k8s/pod",
      name: "Pod",
      component: () => import("@/views/asset/k8s/pod/index.vue"),
      meta: {
        title: "Pod",
        roles: ["admin"],
        icon: "pajamas:cloud-pod",
        showParent: true
      }
    },
    {
      path: "/k8s/service",
      name: "服务",
      component: () => import("@/views/asset/k8s/service/index.vue"),
      meta: {
        title: "服务",
        roles: ["admin"],
        icon: "carbon:cloud-satellite-services",
        showParent: true
      }
    }
  ]
} satisfies RouteConfigsTable;
