import { http } from "@/utils/http";

export type GetGroupParmas = {
  page: number;
  limit: number;
  keyword?: string;
};
export type Group = {
  id: number;
  name: string;
  remark: string;
};

export type APIResult = {
  data?: Group[];
  count?: number;
  msg?: string;
  success: boolean;
};

export const getGroupsAPI = (params: GetGroupParmas) => {
  return http.request<APIResult>("get", "/api/v1/auth/groups", {
    params
  });
};

export type GroupForm = {
  name: string;
  remark: string;
};

export const createGroupAPI = (data: GroupForm) => {
  return http.request<APIResult>("post", "/api/v1/auth/groups", {
    data
  });
};

export const deleteGroupAPI = (id: number) => {
  return http.request<APIResult>("delete", `/api/v1/auth/groups/${id}`);
};

export const updateGroupAPI = (id: number, data: GroupForm) => {
  return http.request<APIResult>("put", `/api/v1/auth/groups/${id}`, {
    data
  });
};

export const getAllGroupsAPI = () => {
  return http.request<APIResult>("get", "/api/v1/auth/all/groups");
};
