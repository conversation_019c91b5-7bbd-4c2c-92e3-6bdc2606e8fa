import { http } from "@/utils/http";

export type GetDepartmentParmas = {
  page: number;
  limit: number;
  keyword?: string;
  family?: string;
};
export type Department = {
  id: number;
  name: string;
  family: string;
  remark: string;
};

export type APIResult = {
  data?: Department[];
  count?: number;
  msg?: string;
  success: boolean;
};

export const getDepartmentsAPI = (params: GetDepartmentParmas) => {
  return http.request<APIResult>("get", "/api/v1/auth/departments", {
    params
  });
};
