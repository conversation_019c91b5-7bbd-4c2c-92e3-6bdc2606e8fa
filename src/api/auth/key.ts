import { http } from "@/utils/http";

export type API = {
  id: number;
  name: string;
  uri: string;
  created_at: string;
  updated_at: string;
};

export type APIListResult = {
  data?: API[];
  msg?: string;
  count?: number;
  success: boolean;
};

export const getAPIListAPI = async () => {
  return http.request<APIListResult>("get", "/api/v1/auth/all/apis");
};

export type Key = {
  id: number;
  name: string;
  secret: string;
  remark: string;
  apis: API[];
  created_at: string;
  updated_at: string;
};

export type APIResult = {
  data?: Key[] | Key;
  msg?: string;
  count?: number;
  success: boolean;
};

export type GetKeysParams = {
  page?: number;
  limit?: number;
  keyword?: string;
};

export const getKeysAPI = async (params: GetKeysParams) => {
  return http.request<APIResult>("get", "/api/v1/auth/keys", { params });
};

export type KeyForm = {
  name: string;
  secret: string;
  remark: string;
};

export const createKeyAPI = async (data: KeyForm) => {
  return http.request<APIResult>("post", "/api/v1/auth/keys", { data });
};

export const updateKeyAPI = async (id: number, data: KeyForm) => {
  return http.request<APIResult>("put", `/api/v1/auth/keys/${id}`, { data });
};

export const deleteKeyAPI = async (id: number) => {
  return http.request<APIResult>("delete", `/api/v1/auth/keys/${id}`);
};
export const getKeyAPI = async (id: number) => {
  return http.request<APIResult>("get", `/api/v1/auth/keys/${id}`);
};

type GrantKeyAPIsForm = {
  ids: number[];
};

export const grantKeyAPIsAPI = async (id: number, data: GrantKeyAPIsForm) => {
  return http.request<APIResult>("post", `/api/v1/auth/keys/${id}/grant`, {
    data
  });
};
