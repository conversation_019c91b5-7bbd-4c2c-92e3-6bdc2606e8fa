import { http } from "@/utils/http";
import type { Group } from "echarts/types/src/util/graphic.js";
import type { Department } from "./department";

export type GetUserListParmas = {
  page: number;
  limit: number;
  keyword?: string;
  is_admin?: boolean;
  is_disabled?: boolean;
  group_id?: number;
};

export interface User {
  id: number;
  username: string;
  name: string;
  email: string;
  phone: string;
  sn: number;
  is_admin: boolean;
  roles: string[];
  is_disabled: boolean;
  remark: string;
  created_at: string;
  last_login_time: string;
  leader_id: number;
  leader?: User;
  groups?: Group[];
  departments: Department[];
  manual_leader_id: number;
  synced_at?:string ;
}

type APIResult = {
  data?: User[] | User;
  success: boolean;
  msg?: string;
  count?: number;
};

/** 用户列表记录*/
export const getUsersList = (params: GetUserListParmas) => {
  return http.request<APIResult>("get", "/api/v1/auth/users", {
    params
  });
};

export interface UpdateUserForm {
  phone: string;
  roles: string[];
  is_admin: boolean;
  is_disabled: boolean;
  manual_leader_id: number;
}

export const updateUser = (id: number, form: UpdateUserForm) => {
  return http.request<APIResult>("put", `/api/v1/auth/users/${id}`, {
    data: form
  });
};

export const getAllUsersAPI = () => {
  return http.request<APIResult>("get", "/api/v1/auth/all/users");
};

export type BatchGroupForm = {
  users_ids: number[];
  groups_ids: number[];
  op: string;
};

export const batGroupUsersAPI = (data: BatchGroupForm) => {
  return http.request<APIResult>("post", "/api/v1/auth/batch/group", {
    data
  });
};

type UpdatePersonalPhoneForm = {
  phone: string;
};

export const UpdatePersonalPhoneAPI = (form: UpdatePersonalPhoneForm) => {
  return http.request<APIResult>("put", "/api/v1/auth/personal/phone", {
    data: form
  });
};

type UpdatePersonalPasswordForm = {
  old_password: string;
  password: string;
};
export const UpdatePersonalPasswordAPI = (form: UpdatePersonalPasswordForm) => {
  return http.request<APIResult>("put", "/api/v1/auth/personal/password", {
    data: form
  });
};

export const getPersonalInfo = () => {
  return http.request<APIResult>("get", "/api/v1/auth/personal/info");
};
