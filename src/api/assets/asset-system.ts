import { http } from "@/utils/http";

// 系统
export type AssetSystem = {
  id: number;
  name: string;
  remark: string;
  business: string;
  business_id: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
}
export type AssetSystemList = {
  success: boolean;
  data?: AssetSystem[];
  count?: number;
  msg?: string;
}


export type AssetSystemParams = {
  page: number;
  limit: number;
  keyword?: string;
  business_id?: number;
}



export const getAssetSystemListAPI = async (params: AssetSystemParams) => {
  return http.request<AssetSystemList>("get", "/api/v1/asset/systems", { params });
}


// 系统表单
export type AssetSystemForm = {
  name: string;
  remark: string;
  business_id: number;
}

type OPResponse = {
  success: boolean;
  data?: AssetSystemForm;
  msg?: string;
}

export const createAssetSystemAPI = async (data: AssetSystemForm) => {
  return http.request<OPResponse>("post", "/api/v1/asset/systems", { data });
}

export const updateAssetSystemAPI = async (id: number, data: AssetSystemForm) => {
  return http.request<OPResponse>("put", `/api/v1/asset/systems/${id}`, { data });
}

export const deleteAssetSystemAPI = async (id: number) => {
  return http.request<OPResponse>("delete", `/api/v1/asset/systems/${id}`);
}

export const getAssetSystemDetailAPI = async (id: number) => {
  return http.request<{
    success: boolean;
    data?: AssetSystem;
    msg?: string;
  }>("get", `/api/v1/asset/systems/${id}`);
}
