import type { AssetType } from "@/config/asset-type";
import type { Tag } from "@/api/asset/tag";
import { http } from "@/utils/http";
// 资产节点
export type AssetNode = {
  id: number;
  name: string;
  system_id: number;
  asset_type: AssetType;
  next_nodes: JSON;
  tags: Tag[];
  created_at?: Date;
  updated_at?: Date;
}

export type AssetNodeForm = {
  name: string;
  system_id: number;
  asset_type: AssetType;
  next_nodes: number[];
  tag_ids: number[];
}

export type AssetNodeList = {
  success: boolean;
  data?: AssetNode[];
  msg?: string;
}

export const getAssetNodesAPI = async (system_id: number) => {
  return http.request<AssetNodeList>("get", `/api/v1/asset/systems/${system_id}/nodes`);
}


export const createAssetNodeAPI = async (data: AssetNodeForm) => {
  return http.request<AssetNodeList>("post", `/api/v1/asset/system/nodes`, { data });
}

export const updateAssetNodeAPI = async (id: number, data: AssetNodeForm) => {
  return http.request<AssetNodeList>("put", `/api/v1/asset/system/nodes/${id}`, { data });
}

export const deleteAssetNodeAPI = async (id: number) => {
  return http.request<AssetNodeList>("delete", `/api/v1/asset/system/nodes/${id}`);
}


export type AssetNodeAsset = {
  name: string;
  host: string;
}

export type AssetNodeAssetList = {
  success: boolean;
  data?: AssetNodeAsset[];
  msg?: string;
}

export const getAssetNodeAssetsAPI = async (node_id: number) => {
  return http.request<AssetNodeAssetList>("get", `/api/v1/asset/system/nodes/${node_id}/assets`);
}
