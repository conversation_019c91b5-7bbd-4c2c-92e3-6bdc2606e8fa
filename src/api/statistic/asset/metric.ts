import { http } from '@/utils/http';

/**
 * 指标数据项接口
 */
export interface MetricItem {
  date: string;  // 日期，格式为 YYYY-MM-DD
  value: number; // 指标值
}

/**
 * API 响应基础接口
 */
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  msg?: string;
}

/**
 * 指标数据列表响应
 */
export interface MetricsResponse {
  metrics: MetricItem[];
}

/**
 * 指标值响应
 */
export interface MetricValueResponse {
  value: number;
}

/**
 * 云类型列表响应
 */
export interface CloudTypesResponse {
  cloud_types: string[];
}

/**
 * 区域名称列表响应
 */
export interface RegionNamesResponse {
  region_names: string[];
}

/**
 * 域名列表响应
 */
export interface DomainNamesResponse {
  domain_names: string[];
}

/**
 * 数据资源类型列表响应
 */
export interface DataResourceTypesResponse {
  data_resource_types: string[];
}

/**
 * 可优化资源类型列表响应
 */
export interface OptimizableResourceTypesResponse {
  optimizable_resource_types: string[];
}

/**
 * 资源类型枚举
 */
export enum ResourceType {
  Computer = 'computer',      // 计算资源总计
  Cloud = 'cloud',           // 云计算资源
  Region = 'region',         // 区域计算资源
  Data = 'data',             // 数据资源
  Network = 'network',       // 网络资源
  Optimizable = 'optimizable' // 可优化资源
}

/**
 * 指标类型枚举
 */
export enum MetricType {
  // 计算资源指标类型
  HostTotal = 'host_total',      // 主机总数
  CPUTotal = 'cpu_total',        // CPU总数
  MemoryTotal = 'memory_mb_total', // 内存总数(MB)
  GPUTotal = 'gpu_total',        // GPU总数
  DLICUTotal = 'dli_cu_total',   // DLI CU总数

  // 数据资源指标类型
  MySQLStorage = 'mysql_storage_total',       // MySQL存储总量
  OSSStorage = 'oss_storage_total',           // OSS存储总量
  OBSStorage = 'obs_storage_total',           // OBS存储总量
  SFSStorage = 'sfs_storage_total',           // SFS存储总量
  NASStorage = 'nas_storage_total',           // NAS存储总量
  MongoDBStorage = 'mongodb_storage_total',   // MongoDB存储总量
  StarrocksStorage = 'starrocks_storage_total', // Starrocks存储总量
  ClickhouseStorage = 'clickhouse_storage_total', // Clickhouse存储总量
  TiDBStorage = 'tidb_storage_total',         // TiDB存储总量
  DLIStorage = 'dli_storage_total',           // DLI存储总量

  // 网络资源指标类型
  InBps = 'in_bps',   // 入带宽
  OutBps = 'out_bps', // 出带宽

  // 可优化资源指标类型
  AMDPercent = 'amd_percent',   // AMD百分比
  CPUUsage = 'cpu_usage',       // CPU使用率
  MemoryUsage = 'memory_usage'  // 内存使用率
}

/**
 * 获取资源指标历史趋势
 * @param resourceType 资源类型
 * @param metricType 指标类型
 * @param name 资源名称（当resourceType为cloud时表示云类型名称，当resourceType为region时表示区域名称，当resourceType为network时表示域名）
 * @param startDate 开始日期，格式为 YYYY-MM-DD
 * @param endDate 结束日期，格式为 YYYY-MM-DD
 * @returns 指标数据列表
 */
export function getResourceMetricsAPI(resourceType: ResourceType, metricType: MetricType, name: string, startDate: string, endDate: string) {
  return http.request<ApiResponse<MetricsResponse>>(
    'get',
    '/api/v1/statistic/asset/metrics',
    {
      params: {
        resource_type: resourceType,
        metric_type: metricType,
        name,
        start_date: startDate,
        end_date: endDate
      }
    }
  );
}

/**
 * 获取最新的资源指标值
 * @param resourceType 资源类型
 * @param metricType 指标类型
 * @param name 资源名称（当resourceType为cloud时表示云类型名称，当resourceType为region时表示区域名称，当resourceType为network时表示域名）
 * @returns 指标值
 */
export function getLatestResourceMetricAPI(resourceType: ResourceType, metricType: MetricType, name: string = '') {
  return http.request<ApiResponse<MetricValueResponse>>(
    'get',
    '/api/v1/statistic/asset/metrics/latest',
    {
      params: {
        resource_type: resourceType,
        metric_type: metricType,
        name
      }
    }
  );
}

/**
 * 获取指定日期范围内的资源指标历史趋势
 * @param resourceType 资源类型
 * @param metricType 指标类型
 * @param name 资源名称（当resourceType为cloud时表示云类型名称，当resourceType为region时表示区域名称，当resourceType为network时表示域名）
 * @param days 天数，表示获取最近多少天的数据
 * @returns 指标数据列表
 */
export function getResourceMetricsByDateRangeAPI(resourceType: ResourceType, metricType: MetricType, name: string = '', days: number) {
  return http.request<ApiResponse<MetricsResponse>>(
    'get',
    '/api/v1/statistic/asset/metrics/range',
    {
      params: {
        resource_type: resourceType,
        metric_type: metricType,
        name,
        days
      }
    }
  );
}

/**
 * 获取所有云类型
 * @returns 云类型列表
 */
export function getAllCloudTypesAPI() {
  return http.request<ApiResponse<CloudTypesResponse>>(
    'get',
    '/api/v1/statistic/asset/metrics/cloud-types'
  );
}

/**
 * 获取所有区域名称
 * @returns 区域名称列表
 */
export function getAllRegionNamesAPI() {
  return http.request<ApiResponse<RegionNamesResponse>>(
    'get',
    '/api/v1/statistic/asset/metrics/region-names'
  );
}

/**
 * 获取所有域名
 * @returns 域名列表
 */
export function getAllDomainNamesAPI() {
  return http.request<ApiResponse<DomainNamesResponse>>(
    'get',
    '/api/v1/statistic/asset/metrics/domain-names'
  );
}

/**
 * 获取所有数据资源类型
 * @returns 数据资源类型列表
 */
export function getAllDataResourceTypesAPI() {
  return http.request<ApiResponse<DataResourceTypesResponse>>(
    'get',
    '/api/v1/statistic/asset/metrics/data-resource-types'
  );
}

/**
 * 获取所有可优化资源类型
 * @returns 可优化资源类型列表
 */
export function getAllOptimizableResourceTypesAPI() {
  return http.request<ApiResponse<OptimizableResourceTypesResponse>>(
    'get',
    '/api/v1/statistic/asset/metrics/optimizable-resource-types'
  );
}
