import { http } from "@/utils/http";

export type GetMonthlyReportParams = {
  monthly: string;
};

// 云资源统计
export type CloudResource = {
  name: string;
  cloud_type: string;
  monthly_bills: Array<{
    month: string;
    cost: number;
  }>;
};

// 区域资源统计
export type RegionResource = {
  name: string;
  host_total: number;
  host_total_change: number;
  cpu_total: number;
  cpu_total_change: number;
  memory_mb_total: number;
  memory_mb_total_change: number;
  gpu_total: number;
  gpu_total_change: number;
};

// 计算资源统计数据
export type ComputerResource = {
  host_total: number;
  host_total_change: number;
  cpu_total: number;
  cpu_total_change: number;
  memory_mb_total: number;
  memory_mb_total_change: number;
  gpu_total: number;
  gpu_total_change: number;
  dli_cu_total: number;
  dli_cu_total_change: number;
  cloud_resource: CloudResource[];
  region_resource: RegionResource[];
};

// 数据资源统计数据
export type DataResource = {
  mysql_storage_total: number;
  mysql_storage_total_change: number;
  oss_storage_total: number;
  oss_storage_total_change: number;
  obs_storage_total: number;
  obs_storage_total_change: number;
  sfs_storage_total: number;
  sfs_storage_total_change: number;
  nas_storage_total: number;
  nas_storage_total_change: number;
  mongodb_storage_total: number;
  mongodb_storage_total_change: number;
  starrocks_storage_total: number;
  starrocks_storage_total_change: number;
  clickhouse_storage_total: number;
  clickhouse_storage_total_change: number;
  tidb_storage_total: number;
  tidb_storage_total_change: number;
  dli_storage_total: number;
  dli_storage_total_change: number;
};

// 可优化资源统计数据
export type OptimizableAsset = {
  amd_percent: number;
  amd_percent_change: number;
  cpu_usage: number;
  cpu_usage_change: number;
  memory_usage: number;
  memory_usage_change: number;
};

// 域名带宽统计
export type DomainBpsStat = {
  domain: string;
  in_bps: number;
  out_bps: number;
};

/**
 * 获取每月资产统计报告 API 响应类型
 */
export interface GetMonthlyReportResult {
  success: boolean;
  data: {
    id: number;
    stat_date: string;
    computer_resource: ComputerResource;
    data_resource: DataResource;
    optimizable_asset: OptimizableAsset;
    domain_bps_stats: DomainBpsStat[];
  };
  msg?: string;
}

/**
 * 获取每月资产统计报告
 * @param params 请求参数，包含统计日期
 * @returns 每月资产统计报告数据
 */
export const getMonthlyReportAPI = (params: GetMonthlyReportParams) => {
  return http.request<GetMonthlyReportResult>(
    "get",
    "/api/v1/statistic/report/monthly",
    {
      params
    }
  );
};
