import { http } from "@/utils/http";

// Cloud bill statistics for a specific billing cycle
export type CloudBillStatistic = {
  name: string;
  amount: number;
  month: string;
};

export type APIResultCloudBillStatistic = {
  success: boolean;
  msg?: string;
  data?: CloudBillStatistic[];
  count?: number;
};

export type GetCloudBillStatisticParams = {
  month: string;
};

export const getCloudBillStatisticAPI = (
  params: GetCloudBillStatisticParams
) => {
  return http.request<APIResultCloudBillStatistic>(
    "get",
    `/api/v1/statistic/bill/clouds`,
    {
      params
    }
  );
};

// Cloud bill statistics for a time range
export type CloudBillRangeStatistic = {
  name: string;
  amount: number;
  month: string;
  start_time: string;
  end_time: string;
};

export type CloudBillRange = {
  bills: CloudBillStatistic[];
};

export type APIResultCloudBillRangeStatistic = {
  success: boolean;
  msg?: string;
  data?: CloudBillRange;
  count?: number;
};

export type GetCloudBillRangeStatisticParams = {
  start_time: string;
  end_time: string;
};

export const getCloudBillRangeStatisticAPI = (
  params: GetCloudBillRangeStatisticParams
) => {
  return http.request<APIResultCloudBillRangeStatistic>(
    "get",
    `/api/v1/statistic/bill/clouds/range`,
    {
      params
    }
  );
};

// Cloud bill summary statistics
export type CloudBillSummary = {
  name: string;
  amount: number;
  percentage: number;
  month: string;
};

export type APIResultCloudBillSummary = {
  success: boolean;
  msg?: string;
  data?: CloudBillSummary[];
  count?: number;
};

export type GetCloudBillSummaryParams = {
  start_time: string;
  end_time: string;
};

export const getCloudBillSummaryAPI = (params: GetCloudBillSummaryParams) => {
  return http.request<APIResultCloudBillSummary>(
    "get",
    `/api/v1/statistic/bill/clouds/summary`,
    {
      params
    }
  );
};
