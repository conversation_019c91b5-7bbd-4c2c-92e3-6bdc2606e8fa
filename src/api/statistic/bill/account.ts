import { http } from "@/utils/http";

// Account bill statistics for a specific billing cycle
export type AccountBillStatistic = {
  account_id: number;
  account_name: string;
  cloud_type: string;
  amount: number;
  month: string;
};

export type APIResultAccountBillStatistic = {
  success: boolean;
  msg?: string;
  data?: AccountBillStatistic[];
  count?: number;
};

export type GetAccountBillStatisticParams = {
  month: string;
};

export const getAccountBillStatisticAPI = (params: GetAccountBillStatisticParams) => {
  return http.request<APIResultAccountBillStatistic>(
    "get",
    `/api/v1/statistic/bill/accounts`,
    {
      params
    }
  );
};

// Account bill statistics for a time range
export type AccountBillRangeStatistic = {
  account_id: number;
  account_name: string;
  cloud_type: string;
  amount: number;
  start_time: string;
  end_time: string;
  month: string;
};

export type APIResultAccountBillRangeStatistic = {
  success: boolean;
  msg?: string;
  data?: AccountBillRangeStatistic[];
  count?: number;
};

export type GetAccountBillRangeStatisticParams = {
  start_time: string;
  end_time: string;
};

export const getAccountBillRangeStatisticAPI = (params: GetAccountBillRangeStatisticParams) => {
  return http.request<APIResultAccountBillRangeStatistic>(
    "get",
    `/api/v1/statistic/bill/accounts/range`,
    {
      params
    }
  );
};

// Account bill statistics grouped by cloud type
export type CloudTypeAccountBillStatistic = {
  cloud_type: string;
  cloud_name: string;
  amount: number;
  bill_cycle: string;
};

export type APIResultCloudTypeAccountBillStatistic = {
  success: boolean;
  msg?: string;
  data?: CloudTypeAccountBillStatistic[];
  count?: number;
};

export type GetCloudTypeAccountBillStatisticParams = {
  bill_cycle: string;
};

export const getCloudTypeAccountBillStatisticAPI = (params: GetCloudTypeAccountBillStatisticParams) => {
  return http.request<APIResultCloudTypeAccountBillStatistic>(
    "get",
    `/api/v1/statistic/bill/accounts/cloud-type`,
    {
      params
    }
  );
};
