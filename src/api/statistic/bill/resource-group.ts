import { http } from "@/utils/http";

// 资源组账单统计接口返回类型
export type ResourceGroupBill = {
  month: string;              // 账单周期，格式为 2021-01
  resource_group_name: string; // 资源组名称
  amount: number;             // 账单金额
};

export type APIResultResourceGroupBill = {
  success: boolean;
  msg?: string;
  data?: ResourceGroupBill[];
  count?: number;
};

export type GetResourceGroupBillStatisticParams = {
  month: string;
};

// 获取指定账单周期内所有资源组的账单统计
export function getResourceGroupBillStatisticAPI(params: GetResourceGroupBillStatisticParams) {
  return http.request<APIResultResourceGroupBill>(
    "get",
    "/api/v1/statistic/bill/resource-groups",
    { params }
  );
}

// 按日期范围获取资源组账单统计
export type ResourceGroupBillRangeParams = {
  start_time: string;
  end_time: string;
};

// 按日期范围获取资源组账单统计
export function getResourceGroupBillRangeStatisticAPI(params: ResourceGroupBillRangeParams) {
  return http.request<APIResultResourceGroupBill>(
    "get",
    "/api/v1/statistic/bill/resource-groups/range",
    { params }
  );
}

// 资源组账单汇总接口返回类型
export type ResourceGroupBillSummary = {
  resource_group_name: string; // 资源组名称
  amount: number;             // 总金额
  count: number;              // 资源数量
};

export type APIResultResourceGroupBillSummary = {
  success: boolean;
  msg?: string;
  data?: ResourceGroupBillSummary[];
  count?: number;
};

// 获取资源组账单汇总数据
export function getResourceGroupBillSummaryAPI(params: ResourceGroupBillRangeParams) {
  return http.request<APIResultResourceGroupBillSummary>(
    "get",
    "/api/v1/statistic/bill/resource-groups/summary",
    { params }
  );
}
