import { http } from "@/utils/http";

// 告警严重程度类型
export enum SeverityType {
  Emergency = 0, // 紧急
  Warning = 1, // 警告
  Notice = 2 // 通知
}

// 通用告警统计结果类型
export interface AlertStatistics {
  total_count: number; // 告警总数
  emergency_count: number; // 紧急告警数量
  warning_count: number; // 警告告警数量
  notice_count: number; // 通知告警数量
  recovered_count: number; // 已恢复告警数量
  recovery_rate: number; // 恢复率
  avg_duration: number; // 平均持续时间(秒)
}

// 实例维度告警统计
export interface InstanceAlertStatistics {
  target_ident: string; // 实例标识
  target_note: string; // 实例备注
  total_count: number; // 告警总数
  emergency_count: number; // 紧急告警数量
  warning_count: number; // 警告告警数量
  notice_count: number; // 通知告警数量
  recovered_count: number; // 已恢复告警数量
  recovery_rate: number; // 恢复率
  avg_duration: number; // 平均持续时间(秒)
}

// 规则维度告警统计
export interface RuleAlertStatistics {
  rule_id: number; // 规则ID
  rule_name: string; // 规则名称
  rule_note: string; // 规则备注
  trigger_count: number; // 触发次数
  affected_instances: number; // 影响的实例数
  emergency_count: number; // 紧急告警数量
  warning_count: number; // 警告告警数量
  notice_count: number; // 通知告警数量
  recovered_count: number; // 已恢复告警数量
  recovery_rate: number; // 恢复率
  avg_duration: number; // 平均持续时间(秒)
}

// 集群维度告警统计
export interface ClusterAlertStatistics {
  cluster: string; // 集群名称
  total_count: number; // 告警总数
  emergency_count: number; // 紧急告警数量
  warning_count: number; // 警告告警数量
  notice_count: number; // 通知告警数量
  recovered_count: number; // 已恢复告警数量
  recovery_rate: number; // 恢复率
  avg_duration: number; // 平均持续时间(秒)
  affected_instances: number; // 影响的实例数
}

// 请求参数接口
export interface AlertStatisticsParams {
  start_time: string;
  end_time: string;
  page?: number;
  limit?: number;
}

// API结果类型
export interface APIResultAlertStatistics {
  success: boolean;
  msg?: string;
  data?: AlertStatistics;
}

export interface APIResultInstanceList {
  success: boolean;
  msg?: string;
  data?: InstanceAlertStatistics[];
  count?: number;
}

export interface APIResultRuleList {
  success: boolean;
  msg?: string;
  data?: RuleAlertStatistics[];
  count?: number;
}

export interface APIResultClusterList {
  success: boolean;
  msg?: string;
  data?: ClusterAlertStatistics[];
  count?: number;
}

/**
 * 获取告警总体统计数据
 * @param params 
 * @returns 
 */
export const getAlertStatisticsAPI = (params: AlertStatisticsParams) => {
  return http.request<APIResultAlertStatistics>(
    "get", 
    "/api/v1/statistic/monitor/n9e/alerts", 
    {
      params
    }
  );
};

/**
 * 按实例维度获取告警统计
 * @param params 
 * @returns 
 */
export const getAlertStatisticsByInstanceAPI = (params: AlertStatisticsParams) => {
  return http.request<APIResultInstanceList>(
    "get", 
    "/api/v1/statistic/monitor/n9e/alerts/by-instance", 
    {
      params
    }
  );
};

/**
 * 按告警规则维度获取告警统计
 * @param params 
 * @returns 
 */
export const getAlertStatisticsByRuleAPI = (params: AlertStatisticsParams) => {
  return http.request<APIResultRuleList>(
    "get", 
    "/api/v1/statistic/monitor/n9e/alerts/by-rule", 
    {
      params
    }
  );
};

/**
 * 按集群维度获取告警统计
 * @param params 
 * @returns 
 */
export const getAlertStatisticsByClusterAPI = (params: AlertStatisticsParams) => {
  return http.request<APIResultClusterList>(
    "get", 
    "/api/v1/statistic/monitor/n9e/alerts/by-cluster", 
    {
      params
    }
  );
};
