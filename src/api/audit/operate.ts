import { http } from "@/utils/http";

export type GetOperateParmas = {
  page: number;
  limit: number;
  user?: string;
  ip?: string;
  module?: string;
  start_time?: Date;
  end_time?: Date;
  op_time?: Date[];
};

type OperateLog = {
  id: number;
  user: string;
  username: string;
  ip: string;
  module: string;
  content: string;
  created_at: string;
};

export type OperateLogResult = {
  success: boolean;
  count?: number;
  data?: OperateLog[];
  msg?: string;
};

/** 登陆记录*/
export const getOperateLogsList = (param: GetOperateParmas) => {
  const params: GetOperateParmas = {
    page: param.page,
    limit: param.limit
  };
  if (param.op_time) {
    params.start_time = param.op_time[0];
    params.end_time = param.op_time[1];
  }
  if (param.ip !== "") {
    params.ip = param.ip;
  }
  if (param.module !== "") {
    params.module = param.module;
  }
  if (param.user !== "") {
    params.user = param.user;
  }
  return http.request<OperateLogResult>("get", "/api/v1/audit/op/logs", {
    params
  });
};
