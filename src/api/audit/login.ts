import { http } from "@/utils/http";

export type getLoginLogParams = {
  page: number;
  limit: number;
  user?: string;
  ip?: string;
  start_time?: Date;
  end_time?: Date;
  login_time?: Date[];
};

type LoginLog = {
  id: number;
  user: string;
  username: string;
  ip: string;
  mode: string;
  result: string;
  agent: string;
  created_at: string;
};

export type LoginLogListResult = {
  success: boolean;
  msg?: string;
  count?: number;
  data?: LoginLog[];
};

/** 登陆记录*/
export const getLoginLogsList = (param: getLoginLogParams) => {
  const params: getLoginLogParams = {
    page: param.page,
    limit: param.limit
  };
  if (param.login_time) {
    params.start_time = param.login_time[0];
    params.end_time = param.login_time[1];
    param.login_time = null;
  }
  if (param.ip && param.ip !== "") {
    params.ip = param.ip;
  }
  if (param.user && param.user !== "") {
    params.user = param.user;
  }
  return http.request<LoginLogListResult>("get", "/api/v1/audit/login/logs", {
    params
  });
};
