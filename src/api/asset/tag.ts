import { http } from "@/utils/http";

// 定义Tag类型
export type Tag = {
  id: number;
  key: string;
  value: string;
  remark: string;
  createdAt: string;
  updatedAt: string;
};

// 定义APIResult类型
type APIResult = {
  data?: Tag[];
  msg?: string;
  success?: boolean;
  count?: number;
};

// 定义GetTagListParmas类型
type GetTagListParmas = {
  page: number;
  limit: number;
  keyword: string;
};

// 定义getTagListAPI函数，用于获取标签列表
export const getTagListAPI = (params: GetTagListParmas) => {
  // 发送get请求，获取标签列表
  return http.request<APIResult>("get", "/api/v1/asset/tags", { params });
};

export interface TagForm {
  key: string;
  value: string;
  remark: string;
}
export const addTagAPI = (data: TagForm) => {
  return http.request<APIResult>("post", "/api/v1/asset/tags", { data });
};

export const updateTagAPI = (id: number, data: TagForm) => {
  return http.request<APIResult>("put", `/api/v1/asset/tags/${id}`, { data });
};

export const deleteTagAPI = (id: number) => {
  return http.request<APIResult>("delete", `/api/v1/asset/tags/${id}`);
};

export const getAllTagsAPI = () => {
  return http.request<APIResult>("get", "/api/v1/asset/all/tags");
};
