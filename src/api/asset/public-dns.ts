import { http } from "@/utils/http";

export type PublicDomain = {
  id: number;
  name: string;
  account_id: number;
  account: string;
  cloud_type: number;
  record_count: number;
  remark: string;
  resource_group_id: string;
  domain_id: string;
  created_at: string;
  updated_at: string;
  sync_time: string;
};

type APIResult = {
  data?: PublicDomain[] | PublicDomainRecord[];
  msg?: string;
  count?: number;
  success: boolean;
};

export type GetPublicDomainsParmas = {
  page: number;
  limit: number;
  keyword?: string;
  account_id?: number;
  ip?: string;
};

export const getPublicDomainsAPI = (params: GetPublicDomainsParmas) => {
  return http.request<APIResult>("get", "/api/v1/asset/public-domains", {
    params
  });
};

export type GetDomainRecordsParmas = {
  page: number;
  limit: number;
  keyword?: string;
};

export type PublicDomainRecord = {
  id: number;
  domain_id: number;
  name: string;
  type: string;
  value: string;
  ttl: number;
  line: number;
  remark: string;
  created_at: string;
  updated_at: string;
};

export const getDomainRecordsAPI = (
  domainID: string,
  params: GetDomainRecordsParmas
) => {
  return http.request<APIResult>(
    "get",
    `/api/v1/asset/public-domains/${domainID}/records`,
    {
      params
    }
  );
};
