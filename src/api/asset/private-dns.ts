import { http } from "@/utils/http";

export type PrivateDomain = {
  id: number;
  name: string;
  account_id: number;
  account: string;
  cloud_type: number;
  record_count: number;
  remark: string;
  resource_group_id: string;
  domain_id: string;
  created_at: string;
  updated_at: string;
  sync_time: string;
};

type APIResult = {
  data?: PrivateDomain[] | PrivateDomainRecord[];
  msg?: string;
  count?: number;
  success: boolean;
};

export type GetPrivateDomainsParmas = {
  page: number;
  limit: number;
  keyword?: string;
  account_id?: number;
  ip?: string;
};

export const getPrivateDomainsAPI = (params: GetPrivateDomainsParmas) => {
  return http.request<APIResult>("get", "/api/v1/asset/private-domains", {
    params
  });
};

export type GetDomainRecordsParmas = {
  page: number;
  limit: number;
  keyword?: string;
};

export type PrivateDomainRecord = {
  id: number;
  domain_id: number;
  name: string;
  type: string;
  value: string;
  ttl: number;
  line: number;
  remark: string;
  created_at: string;
  updated_at: string;
};

export const getDomainRecordsAPI = (
  domainID: string,
  params: GetDomainRecordsParmas
) => {
  return http.request<APIResult>(
    "get",
    `/api/v1/asset/private-domains/${domainID}/records`,
    {
      params
    }
  );
};
