import { http } from "@/utils/http";
import type { Tag } from "./tag";
import type { CloudAccount } from "./cloud-account";
import type { Loadbalancer } from "./loadbalancer";
import type { AssetInfo } from "../search";
import type { DDosDomain } from "./ddos";

type GetHostParamas = {
  page: number;
  limit: number;
  ip?: string;
  keyword?: string;
  tag_ids?: string;
  account_id?: number;
  datacenter_id?: number;
  status?: number;
  n9e_monitor?:boolean;
  host_type?: number;
  resource_group_id?: string;
  is_amd?: boolean;
  ping_monitor?: boolean;
};

export type Host = {
  id: number;
  sn: string;
  name: string;
  ip: string;
  public_ip: string;
  host_type: number;
  charge_type: number;
  tags: Tag[];
  datacenter_id: number;
  datacenter: string;
  account_id: number;
  account?: CloudAccount;
  region_id: string;
  cabinet: string;
  position: string;
  remark: string;
  status: number;
  remote_port: number;
  ping_monitor: boolean;
  n9e_monitor?: boolean;
  n9e_monitor_time?: string;
  cpu_thread: number;
  is_amd: boolean;
  gpu_amount: number;
  gpu_spec: string;
  memory: number;
  model: string;
  os: string;
  os_type: string;
  resource_group_id: string;
  resource_group_name: string;
  expired_time: string;
  sync_time: string;
  created_at: string;
  updated_at: string;
};

type APIResult = {
  data?: Host[];
  count?: number;
  msg?: string;
  success: boolean;
};

export const getHostsAPI = async (params: GetHostParamas) => {
  return http.request<APIResult>("get", "/api/v1/asset/hosts", { params });
};

export type TagHostsFrom = {
  tag_ids: number[];
  host_ids: number[];
  op: "delete" | "append" | "replace" | "clear";
};

export const tagHostsAPI = (data: TagHostsFrom) => {
  return http.request<APIResult>("post", "/api/v1/asset/tag/hosts", { data });
};

export type HostForm = {
  name: string;
  ip: string;
  public_ip: string;
  host_type: number;
  datacenter_id: number;
  cabinet: string;
  position: string;
  remark: string;
  status: number;
  remote_port: number;
  ping_monitor: boolean;
  cpu_thread: number;
  gpu_amount: number;
  gpu_spec: string;
  memory: number;
  sn: string;
  model: string;
  os: string;
  os_type: string;
};

export const addHostAPI = (data: HostForm) => {
  return http.request<APIResult>("post", "/api/v1/asset/hosts", { data });
};

export const updateHostAPI = (id: number, data: HostForm) => {
  return http.request<APIResult>("put", `/api/v1/asset/hosts/${id}`, { data });
};

export const deleteHostAPI = (id: number) => {
  return http.request<APIResult>("delete", `/api/v1/asset/hosts/${id}`);
};

export const getAllHostsAPI = () => {
  return http.request<APIResult>("get", "/api/v1/asset/all/hosts");
};

export type BatSetMonitorForm = {
  hosts_ids: number[];
  monitor: boolean;
};

export const batSetMonitorAPI = (data: BatSetMonitorForm) => {
  return http.request<APIResult>("put", "/api/v1/asset/bat-set-monitor/hosts", {
    data
  });
};

export type HostLoadBalancersResponse = {
  data?: Loadbalancer[];
  msg?: string;
  success: boolean;
};

export const getHostLoadBalancersAPI = async (host_id: number) => {
  return http.request<HostLoadBalancersResponse>(
    "get",
    `/api/v1/asset/host/${host_id}/lbs`
  );
};

export type HostAssets = {
  msg?: string;
  success: boolean;
  data?: {
    asset_infos?: AssetInfo[];
    ddos_domains?: DDosDomain[];
    loadbalancers?: Loadbalancer[];
  }
}

export const getHostAssetsAPI = async (host_id: number) => {
  return http.request<HostAssets>(
    "get",
    `/api/v1/asset/host/${host_id}/assets`
  );
};
