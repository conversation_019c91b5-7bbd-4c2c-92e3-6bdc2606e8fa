import { http } from "@/utils/http";

export type HostChangeLog = {
  id: number;
  host_id: number;
  operator: string;
  content: string;
  created_at: string;
};

type APIResult = {
  data?: HostChangeLog[];
  count?: number;
  msg?: string;
  success: boolean;
};

export type GetHostChangeLogParamas = {
  page: number;
  limit: number;
  start_time?: string;
  end_time?: string;
};

const getChangeLogEndpoint = (host_id: number) =>
  `/api/v1/asset/hosts/${host_id}/changelogs`;

export const getHostChangeLogAPI = async (
  host_id: number,
  params: GetHostChangeLogParamas
) => {
  return http.request<APIResult>("get", getChangeLogEndpoint(host_id), {
    params
  });
};

export type ChangeLogForm = {
  content: string;
};

export const createHostChangeLogAPI = (
  host_id: number,
  data: ChangeLogForm
) => {
  return http.request<APIResult>("post", getChangeLogEndpoint(host_id), {
    data
  });
};
