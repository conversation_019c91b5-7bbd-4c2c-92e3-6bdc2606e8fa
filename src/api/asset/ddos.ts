import { http } from "@/utils/http";
import type { Host } from "./host";
export interface DDosProtection {
  id: number;
  account_id: number;
  account: string;
  instance_id: string;
  status: number;
  enabled: boolean;
  ip: string;
  ip_version: string;
  ip_mode: string;
  expire_time: string;
  remark: string;
  create_time: string;
  sync_time: string;
}

export type DDosProtectionListParmas = {
  page: number;
  limit: number;
  keyword?: string;
  account_id?: number;
};

export type DDosProtectionListReponse = {
  success: boolean;
  data?: DDosProtection[];
  count?: number;
  msg?: string;
};

export const getDDosProtectionListAPI = (params: DDosProtectionListParmas) => {
  return http.request<DDosProtectionListReponse>(
    "get",
    "/api/v1/asset/ddos/instances",
    {
      params
    }
  );
};

export interface DDosDomain {
  id: number;
  account_id: number;
  account: string;
  domain: string;
  http2_enable: boolean;
  proxy_types: string;
  real_servers: string[];
  hosts: Host[];
  cname: string;
  created_at: string;
  sync_time: string;
}

export type DDosDomainListParmas = {
  page: number;
  limit: number;
  keyword?: string;
  account_id?: number;
};

export type DDosDomainListReponse = {
  success: boolean;
  data?: DDosDomain[];
  count?: number;
  msg?: string;
};

export const getDDosDomainListAPI = (params: DDosDomainListParmas) => {
  return http.request<DDosDomainListReponse>(
    "get",
    "/api/v1/asset/ddos/domains",
    {
      params
    }
  );
};

export interface DDosDomainBps {
  domain: string;
  stat_date: string;
  in_bps: number;
  out_bps: number;
}

export type DDosDomainBpsListParmas = {
  start_time: string;
  end_time: string;
};

export type DDosDomainBpsListReponse = {
  success: boolean;
  data?: DDosDomainBps[];
  msg?: string;
};

export const getDDosDomainBpsListAPI = (
  id: number,
  params: DDosDomainBpsListParmas
) => {
  return http.request<DDosDomainBpsListReponse>(
    "get",
    `/api/v1/asset/ddos/domains/${id}/bps`,
    {
      params
    }
  );
};

export const getAllDDosDomainBpsListAPI = (params: DDosDomainBpsListParmas) => {
  return http.request<DDosDomainBpsListReponse>(
    "get",
    `/api/v1/asset/ddos/all/domains/bps`,
    {
      params
    }
  );
};

type syncDDosDomainBpsResponse = {
  success: boolean;
  msg?: string;
};

export const syncDDosDomainBpsAPI = (id: number, stat_date: string) => {
  return http.request<syncDDosDomainBpsResponse>(
    "post",
    `/api/v1/asset/ddos/domains/${id}/sync/bps`,
    {
      params: {
        stat_date
      }
    }
  );
};
