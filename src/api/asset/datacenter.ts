import { http } from "@/utils/http";

type GetDatacenterListParmas = {
  page: number;
  limit: number;
  keyword?: string;
  cloud_type: number;
};
export type Datacenter = {
  id: number;
  name: string;
  code: string;
  cloud_type: number;
  remark: string;
  created_at: string;
  updated_at: string;
};
type APIResult = {
  data?: Datacenter[];
  count?: number;
  success?: number;
  msg?: string;
};

export const getDatacenterList = (params: GetDatacenterListParmas) => {
  return http.request<APIResult>("get", "/api/v1/asset/datacenters", {
    params
  });
};

export type DatacenterForm = {
  name: string;
  code: string;
  remark: string;
};

export const addDataceterAPI = (data: DatacenterForm) => {
  return http.request<APIResult>("post", "/api/v1/asset/datacenters", {
    data
  });
};

export const deleteDatacenterAPI = (id: number) => {
  return http.request<APIResult>("delete", `/api/v1/asset/datacenters/${id}`);
};

export const updateDatacenterAPI = (id: number, data: DatacenterForm) => {
  return http.request<APIResult>("put", `/api/v1/asset/datacenters/${id}`, {
    data
  });
};

export const getAllDatacentersAPI = () => {
  return http.request<APIResult>("get", "/api/v1/asset/all/datacenters");
};
