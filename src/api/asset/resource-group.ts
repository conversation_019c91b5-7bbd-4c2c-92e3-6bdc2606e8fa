import { http } from "@/utils/http";

type GetResourceGroupParams = {
  page: number;
  limit: number;
  keyword?: string;
  account_id?: number;
};

export type ResourceGroup = {
  id: number;
  account_id: number;
  group_id: string;
  group_name: string;
  group_display_name: string;
  created_at: string;
  updated_at: string;
  account_name: string;
};

type APIResult = {
  data?: ResourceGroup[];
  count?: number;
  msg?: string;
  success: boolean;
};

export const getResourceGroupesAPI = (params: GetResourceGroupParams) => {
  return http.request<APIResult>("get", "/api/v1/asset/resource-groups", {
    params
  });
};

export const getAllResourceGroupesAPI = () => {
  return http.request<APIResult>("get", "/api/v1/asset/all/resource-groups");
};

export type getMonthlyBillAmountParams = {
  start_time: string;
  end_time: string;
};

export type getMonthlyBillAmountResponse = {
  data?: {
    bill_cycle: string;
    amount: number;
  }[];
  msg?: string;
  success: boolean;
};

export const getMonthlyBillAmountAPI = (
  account_id: number,
  params: getMonthlyBillAmountParams
) => {
  return http.request<getMonthlyBillAmountResponse>(
    "get",
    `/api/v1/asset/resource-groups/${account_id}/month/bills`,
    {
      params
    }
  );
};
