import { http } from "@/utils/http";
type GetSubnetsParams = {
  page: number;
  limit: number;
  keyword?: string;
  ip?: string;
  cidr?: string;
  datacenter_id?: number;
  account_id?: number;
};

export type Subnet = {
  id: number;
  name: string;
  account_id: number;
  account: string;
  datacenter: string;
  resource_group_id: string;
  resource_group_name: string;
  datacenter_id: number;
  zone_id: string;
  ipv4_cidr: string;
  ipv6_cidr: string;
  v_switch_id: string;
  vpc_id: string;
  description: string;
  created_at: Date;
  sync_time: Date | null;
};

type APIResult = {
  data?: Subnet[];
  count?: number;
  msg?: string;
  success: boolean;
};

export const getSubnetsAPI = (params: GetSubnetsParams) => {
  return http.request<APIResult>("get", "/api/v1/asset/subnets", { params });
};
