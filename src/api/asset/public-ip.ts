import { http } from "@/utils/http";
type GetPublicIPsParams = {
  page: number;
  limit: number;
  keyword?: string;
  sn?: string;
  ip?: string;
  cloud_type?: number;
  datacenter_id?: number;
  account_id?: number;
};

export type PublicIP = {};

type APIResult = {
  data?: PublicIP[];
  count?: number;
  msg?: string;
  success: boolean;
};

export const getPublicIPsAPI = (params: GetPublicIPsParams) => {
  return http.request<APIResult>("get", "/api/v1/asset/public-ips", { params });
};
