import { http } from "@/utils/http";

type GetLoadbalancerParmas = {
  page: number;
  limit: number;
  account_id?: number;
  datacenter_id?: number;
  keyword?: string;
  host?: string;
  loadbalancer_type?: string;
  loadbalancer_id?: string;
};

export type Loadbalancer = {
  id: number;
  name: string;
  host: string;
  region_id: string;
  loadbalancer_id: string;
  loadbalancer_type: string;
  status: number;
  spec: string;
  resource_group_id: string;
  resource_group_name: string;
  account_id: number;
  account: string;
  datacenter_id: number;
  datacenter: string;
  created_at: string;
  updated_at: string;
  sync_time: string;
  cabinet?: string;
  sn?: string;
};

type APIResult = {
  data?: Loadbalancer[];
  count?: number;
  msg?: string;
  success: boolean;
};

export const getLoadbalancersAPI = (params: GetLoadbalancerParmas) => {
  return http.request<APIResult>("get", "/api/v1/asset/loadbalancers", {
    params
  });
};
