import { http } from "@/utils/http";
import type { ResourceGroup } from "./resource-group";
import type { CloudAccount } from "./cloud-account";
import type { getMonthlyBillAmountParams } from "./bill";
import type { APIResultAmount } from "./bill";

type GetBusinessParams = {
  page: number;
  limit: number;
  keyword?: string;
};

export type Business = {
  id: number;
  name: string;
  remark: string;
  resource_groups?: ResourceGroup[];
  cloud_accounts?: CloudAccount[];
  host_count?: number;
  created_at: string;
  updated_at: string;
};

type APIResult = {
  data?: Business[];
  count?: number;
  msg?: string;
  success: boolean;
};

export const getBusinessesAPI = (params: GetBusinessParams) => {
  return http.request<APIResult>("get", "/api/v1/asset/businesses", { params });
};

export const getAllBusinessesAPI = () => {
  return http.request<APIResult>("get", "/api/v1/asset/all/businesses");
};

export type BusinessForm = {
  name: string;
  remark: string;
};

export const addBusinessAPI = (data: BusinessForm) => {
  return http.request<APIResult>("post", "/api/v1/asset/businesses", { data });
};
export const deleteBusinessAPI = (id: number) => {
  return http.request<APIResult>("delete", `/api/v1/asset/businesses/${id}`);
};
export const updateBusinessAPI = (id: number, data: BusinessForm) => {
  return http.request<APIResult>("put", `/api/v1/asset/businesses/${id}`, {
    data
  });
};

export type SetResourceGroupForm = {
  resource_group_ids: number[];
  cloud_account_ids: number[];
};

export const setResourceGroupAPI = (id: number, data: SetResourceGroupForm) => {
  return http.request<APIResult>(
    "post",
    `/api/v1/asset/businesses/${id}/set-resource-groups`,
    {
      data
    }
  );
};

export const getBusinessDetailAPI = (id: number) => {
  return http.request<APIResult>("get", `/api/v1/asset/businesses/${id}`);
};

export const getMonthlyBillAmountAPI = (
  business_id: number,
  params: getMonthlyBillAmountParams
) => {
  return http.request<APIResultAmount>(
    "get",
    `/api/v1/asset/businesses/${business_id}/monthly-bills`,
    {
      params
    }
  );
};
