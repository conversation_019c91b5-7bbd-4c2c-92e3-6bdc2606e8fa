import { http } from "@/utils/http";
import type { Label } from "./label";

export type Container = {
  id: number;
  cluster_id: number;
  name: string;
  pod: string;
  namespace: string;
  image: string;
  status: string;
  command: string;
  args: string;
  working_dir: string;
  restart_policy: string;
  envs: string;
  request_cpu?: number;
  limit_cpu?: number;
  request_memory?: number;
  limit_memory?: number;
  sync_time: string;
  labels: Label[];
};

type APIResult = {
  data?: Container[];
  count?: number;
  msg?: string;
  success: boolean;
};

export const getContainersAPI = (pod_id: number) => {
  return http.request<APIResult>(
    "get",
    `/api/v1/asset/k8s/pods/${pod_id}/containers`
  );
};
