import { http } from "@/utils/http";
import type { Label } from "./label";

type GetNodeListParmas = {
  page: number;
  limit: number;
  keyword?: string;
  cluster_id?: number;
};

export type Node = {
  id: number;
  name: string;
  status: string;
  role: string;
  version: string;
  internal_ip: string;
  hostname: string;
  external_ip: string;
  os_image: string;
  kernel_version: string;
  container_runtime_version: string;
  request_cpu: number;
  request_memory: number;
  limit_cpu: number;
  limit_memory: number;
  cpu_total: number;
  memory_total: number;
  cluster_id: number;
  created_at: string;
  sync_time: string;
  labels: Label[];
};

type APIResult = {
  data?: Node[];
  count?: number;
  msg?: string;
  success?: boolean;
};

export const getNodeListAPI = (params: GetNodeListParmas) => {
  return http.request<APIResult>("get", "/api/v1/asset/k8s/nodes", { params });
};
