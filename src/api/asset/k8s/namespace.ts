import { http } from "@/utils/http";
import type { Label } from "./label";

export type Namespace = {
  name: string;
  labels: Label[];
  selector_labels: Label[];
  sync_time: string;
};

type APIResult = {
  count?: number;
  msg?: string;
  data?: Namespace[];
  success?: boolean;
};

export const getClusterNamespacesAPI = async (cluster_id: string) => {
  return http.request<APIResult>(
    "get",
    `/api/v1/asset/k8s/clusters/${cluster_id}/namespaces`
  );
};
