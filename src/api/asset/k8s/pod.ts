import { http } from "@/utils/http";
import type { Container } from "./container";

type GetPodListParmas = {
  page: number;
  limit: number;
  cluster_id?: number;
  keyword?: string;
  ip?: string;
};

export type Pod = {
  id: number;
  cluster_id: number;
  name: string;
  namespace: string;
  status: string;
  containers: Container[];
  pod_ip: string;
  host_ip: string;
  request_cpu?: number;
  limit_cpu?: number;
  request_memory?: number;
  limit_memory?: number;
  created_at: string;
  updated_at: string;
  sync_time: string;
};

type APIResult = {
  data?: Pod[];
  count?: number;
  msg?: string;
  success?: boolean;
};

export const getPodListAPI = (params: GetPodListParmas) => {
  return http.request<APIResult>("get", "/api/v1/asset/k8s/pods", {
    params
  });
};
