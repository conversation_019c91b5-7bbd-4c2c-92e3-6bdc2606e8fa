import { http } from "@/utils/http";
import type { Label } from "./label";

export type Service = {
  id: number;
  cluster_id: number;
  namespace: string;
  name: string;
  port_type: string;
  cluster_ip: string;
  external_ip: string;
  external_ips: string;
  load_balance_ingress: string;
  load_balance_ip: string;
  port: string;
  selector_labels: Label[];
  labels: Label[];
  sync_time: string;
  deleted_at: string;
};

type APIResult = {
  data?: Service[];
  count?: number;
  msg?: string;
  success: boolean;
};

export type GetServicesParams = {
  page: number;
  limit: number;
  cluster_id?: number;
  namespace?: string;
  keyword?: string;
};

export const getServicesAPI = (params: GetServicesParams) => {
  return http.request<APIResult>("get", `/api/v1/asset/k8s/services`, {
    params
  });
};
