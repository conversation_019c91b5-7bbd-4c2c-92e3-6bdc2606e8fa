import { http } from "@/utils/http";
import type { Label } from "./label";
import type { Pod } from "./pod";
import type { Service } from "./service";

type GetWorkloadListParmas = {
  page: number;
  limit: number;
  keyword?: string;
  cluster_id?: number;
  namespace?: string;
  workload_type?: string;
};

export type Workload = {
  id: number;
  name: string;
  workload_type: string;
  namespace: string;
  labels: Label[];
  selector_labels: Label[];
  pods: Pod[];
  services: Service[];
  cluster_id: number;
  cluster_name?: string;
  sync_time: string;
  creation_time: string;
};

type APIResult = {
  data?: Workload[];
  count?: number;
  msg?: string;
  success?: boolean;
};

export const getWorkloadListAPI = (params: GetWorkloadListParmas) => {
  return http.request<APIResult>("get", "/api/v1/asset/k8s/workloads", {
    params
  });
};

type PerAPIResult = {
  data?: Workload;
  msg?: string;
  success?: boolean;
};

export const getWorkloadDetailAPI = (id: number) => {
  return http.request<PerAPIResult>("get", `/api/v1/asset/k8s/workloads/${id}`);
};
