import { http } from "@/utils/http";

export type GetClusterParmas = {
  page: number;
  limit: number;
  keyword?: string;
};

export type Cluster = {
  id: number;
  name: string;
  cluster_type: string;
  cluster_version: string;
  token: string;
  remark: string;
  create_at: string;
  update_at: string;
  node_total: number;
  workload_total: number;
  pod_total: number;
  container_total: number;
  namespace_total: number;
  service_total: number;
};

type APIResult = {
  data?: Cluster[];
  count?: number;
  msg?: string;
  success: boolean;
};

export const getClusters = (params: GetClusterParmas) => {
  return http.request<APIResult>("get", "/api/v1/asset/k8s/clusters", {
    params
  });
};

export const syncCluster = (id: number) => {
  return http.request<APIResult>(
    "post",
    `/api/v1/asset/k8s/clusters/${id}/sync`
  );
};

export interface ClusterForm {
  name: string;
  cluster_type: string;
  cluster_version: string;
  token: string;
  remark: string;
}

export const addClusterAPI = (data: ClusterForm) => {
  return http.request<APIResult>("post", "/api/v1/asset/k8s/clusters", {
    data
  });
};

export const updateClusterAPI = (id: number, data: ClusterForm) => {
  return http.request<APIResult>("put", `/api/v1/asset/k8s/clusters/${id}`, {
    data
  });
};

export const deleteClusterAPI = (id: number) => {
  return http.request<APIResult>("delete", `/api/v1/asset/k8s/clusters/${id}`);
};

export const getAllClustersAPI = () => {
  return http.request<APIResult>("get", "/api/v1/asset/k8s/all/clusters");
};
