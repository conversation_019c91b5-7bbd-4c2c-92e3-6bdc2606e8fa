import { http } from "@/utils/http";

export type GetCloudAccountListParams = {
  page: number;
  limit: number;
  keyword?: string;
  cloud_type?: number;
};

export type CloudAccount = {
  id: number;
  name: string;
  cloud_type: number;
  access_key: string;
  access_secret: string;
  remark: string;
  created_at: string;
  updated_at: string;
  host_total: number;
};

type APIResult = {
  data?: CloudAccount[];
  count?: number;
  msg?: string;
  success: boolean;
};

/** 云账户列表记录*/
export const getCloudAccountList = (params: GetCloudAccountListParams) => {
  return http.request<APIResult>("get", "/api/v1/asset/cloud-accounts", {
    params
  });
};

export const syncCloudAccountAsset = (id: number, assetType: string) => {
  return http.request<APIResult>(
    "post",
    `/api/v1/asset/cloud-accounts/${id}/sync/${assetType}`
  );
};

export interface UpdateCloudAccountForm {
  name: string;
  cloud_type: number;
  access_key: string;
  access_secret: string;
  remark: string;
}

export const updateCloudAccount = (
  id: number,
  data: UpdateCloudAccountForm
) => {
  return http.request<APIResult>("put", `/api/v1/asset/cloud-accounts/${id}`, {
    data
  });
};

export const addCloudAccountAPI = (data: UpdateCloudAccountForm) => {
  return http.request<APIResult>("post", `/api/v1/asset/cloud-accounts`, {
    data
  });
};

export const deleteCloudAccountAPI = (id: number) => {
  return http.request<APIResult>(
    "delete",
    `/api/v1/asset/cloud-accounts/${id}`
  );
};

export const getAllCloudAccountsAPI = () => {
  return http.request<APIResult>("get", "/api/v1/asset/all/cloud-accounts");
};

export type SyncAccountMonthlyBillParams = {
  bill_cycle: string;
};
export const syncAccountMonthlyBillAPI = (
  account_id: number,
  params: SyncAccountMonthlyBillParams
) => {
  return http.request<APIResult>(
    "post",
    `/api/v1/asset/cloud-accounts/${account_id}/sync/monthly-bill`,
    {
      params
    }
  );
};
