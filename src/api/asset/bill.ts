import { http } from "@/utils/http";

export type MonthlyBill = {
  account_id: number;
  bill_cycle: string;
  bill_type: string;
  charge_mode: string;
  amount: number;
  region: string;
  zone: string;
  pay_time: string;
  resource_id: string;
  resource_name: string;
  resource_group: string;
  resource_group_id: string;
  resource_type: string;
  product_name: string;
  account_name: string;
};

export type getMonthlyBillParmas = {
  page: number;
  limit: number;
  resource_id?: string;
  bill_cycle?: string;
  resource_name?: string;
};

export type APIResult = {
  success: boolean;
  msg?: string;
  data?: MonthlyBill[];
  count?: number;
};

export const getMonthlyBillAPI = (
  account_id: number,
  params: getMonthlyBillParmas
) => {
  return http.request<APIResult>(
    "get",
    `/api/v1/asset/monthly-bills/${account_id}`,
    {
      params
    }
  );
};

export type MonthlyBillAmount = {
  bill_cycle: string;
  amount: number;
};

export type APIResultAmount = {
  success: boolean;
  msg?: string;
  data?: MonthlyBillAmount[];
};
export type getMonthlyBillAmountParams = {
  start_time?: string;
  end_time?: string;
  date_range?: string[];
  resource_id?: string;
};

export const getMonthlyBillAmountAPI = (
  account_id: number,
  params: getMonthlyBillAmountParams
) => {
  return http.request<APIResultAmount>(
    "get",
    `/api/v1/asset/monthly-bills/${account_id}/amounts`,
    {
      params
    }
  );
};

export type ResourceTypeData = {
  resource_type: string;
  amount: number;
};

export type APIResultResourceTypeData = {
  success: boolean;
  msg?: string;
  data?: ResourceTypeData[];
};
export type getResourceTypeDataParams = {
  bill_cycle?: string;
};

export const getResourceTypeDataAPI = (
  account_id: number,
  params: getResourceTypeDataParams
) => {
  return http.request<APIResultResourceTypeData>(
    "get",
    `/api/v1/asset/monthly-bills/${account_id}/resource-types/amounts`,
    {
      params
    }
  );
};

export type getResourceTypeDataByCyclesParams = {
  start_time: string;
  end_time: string;
};

export type ResourceTypeDataByCycles = {
  success: boolean;
  msg?: string;
  data?: {
    data: any[];
    columns: string[];
  };
};

export const getResourceTypeDataByCyclesAPI = (
  account_id: number,
  params: getResourceTypeDataByCyclesParams
) => {
  return http.request<ResourceTypeDataByCycles>(
    "get",
    `/api/v1/asset/monthly-bills/${account_id}/resource-types/amounts/cycles`,
    {
      params
    }
  );
};

export type GetCloudTypeMonthlyBillsParams = {
  start_time: string;
  end_time: string;
  date_range?: string[];
};

export type CloudTypeMonthlyBill = {
  name: string;
  cloud_type: number;
  monthly_bills: {
    bill_cycle: string;
    amount: number | null;
  }[];
};

export type APIResultGetCloudTypeMonthlyBills = {
  success: boolean;
  msg?: string;
  data?: CloudTypeMonthlyBill[];
};

export const getCloudTypeMonthlyBillsAPI = (params: GetCloudTypeMonthlyBillsParams) => {
  return http.request<APIResultGetCloudTypeMonthlyBills>(
    "get",
    `/api/v1/asset/monthly-bills/cloud-type-monthly-bills`,
    {
      params
    }
  );
};
