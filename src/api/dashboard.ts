import { http } from "@/utils/http";

type CloudAccountTypeStat = {
  name: string;
  count: number;
  cost: number;
  last_cost: number;
};
type HostTypeStat = {
  name: string;
  count: number;
};

type CloudAccountMonthlyBill = {
  account_name: string;
  current_month: number;
  last_month: number;
};

type WorkflowStats = {
  my_order_approving_count: number;
  my_order_count: number;
  can_approve_count: number;
  approved_count: number;
  waiting_evaluation_count: number;
};

export type K8sStats = {
  cluster_id: number;
  name: string;
  node_count: number;
};

export type ComputerResource = {
  id: number;
  stat_date: string;
  host_total: number;
  cpu_total: number;
  memory_mb_total: number;
  gpu_total: number;
  dli_cu_total: number;
};

// DataResource 数据资源统计
export type DataResource = {
  id: number;
  stat_date: string;
  mysql_storage_total: number;
  oss_storage_total: number;
  obs_storage_total: number;
  sfs_storage_total: number;
  nas_storage_total: number;
  mongodb_storage_total: number;
  starrocks_storage_total: number;
  clickhouse_storage_total: number;
  tidb_storage_total: number;
  dli_storage_total: number;
};

export type DashboardData = {
  cloud_account_type_stats: CloudAccountTypeStat[];
  host_type_stats: HostTypeStat[];
  workflow_stats: WorkflowStats;
  cloud_account_monthly_bills: CloudAccountMonthlyBill[];
  k8s_stats?: K8sStats[];
  computer_resource?: ComputerResource;
  data_resource?: DataResource;
};

export type APIResult = {
  success: boolean;
  data?: DashboardData;
  msg?: string;
};

export const getDashboardDataAPI = () => {
  return http.request<APIResult>("get", "/api/v1/dashboard/data");
};
