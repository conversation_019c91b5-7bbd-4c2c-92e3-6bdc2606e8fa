import { http } from "@/utils/http";

export type Section = {
  id: number;
  name: string;
  alias: string;
};

export type Item = {
  id: number;
  section: string;
  name: string;
  alias: string;
  setting_type: number; // 1: 普通设置类型 2: 布尔值设置类型 3: 密码设置类型 4: 联系人设置类型
  content: string;
  order_index: number;
};

export type APIResult = {
  success: boolean;
  msg?: string;
  data?: Section[] | Item[];
};

export const getSettingAPI = () => {
  return http.request<APIResult>("get", "/api/v1/setting/sections");
};

export const getSettingItemAPI = (section: string) => {
  return http.request<APIResult>("get", `/api/v1/setting/items/${section}`);
};

export type SettingForm = {
  id?: number;
  setting_type?: number; // 1: 普通设置类型 2: 布尔值设置类型 3: 密码设置类型 4: 联系人设置类型
  section: string;
  name: string;
  alias?: string;
  content: any;
};

export type SettingForms = SettingForm[];

export const saveSettingAPI = (data: SettingForms) => {
  return http.request<APIResult>("put", "/api/v1/setting/update", {
    data
  });
};
