import { http } from "@/utils/http";

type GetProjectsParmas = {
  page: number;
  limit: number;
  keyword?: string;
  env?: number;
};

export type Project = {
  id: number;
  name: string;
  link: string;
  port: number;
  ext_link: string;
  admin_user: string;
  admin_password: string;
  env: number;
  remark: string;
  dba_group_id: number;
  is_sensitive: boolean;
  dbs: string[];
  custom_dbs: string[];
  created_at: string;
  updated_at: string;
};

type APIResult = {
  data?: Project[];
  count?: number;
  msg?: string;
  success: boolean;
};

export type ProjectForm = {
  name: string;
  link: string;
  port: number;
  ext_link: string;
  admin_user: string;
  admin_password: string;
  remark: string;
  env: number;
  custom_dbs: string[];
  is_sensitive: boolean;
  dba_group_id: number;
};

export const getProjectsAPI = (params: GetProjectsParmas) => {
  return http.request<APIResult>("get", "/api/v1/database/mysql/projects", {
    params
  });
};
export const createProjectAPI = (data: ProjectForm) => {
  return http.request<APIResult>("post", "/api/v1/database/mysql/projects", {
    data
  });
};
export const deleteProjectAPI = (id: number) => {
  return http.request<APIResult>(
    "delete",
    `/api/v1/database/mysql/projects/${id}`
  );
};

export const updateProjectAPI = (id: number, data: ProjectForm) => {
  return http.request<APIResult>(
    "put",
    `/api/v1/database/mysql/projects/${id}`,
    {
      data
    }
  );
};

export const syncProjectDBs = () => {
  return http.request<APIResult>(
    "put",
    "/api/v1/database/mysql/project/sync_dbs"
  );
};

export const getAllProjectsAPI = () => {
  return http.request<APIResult>("get", "/api/v1/database/mysql/all/projects");
};
