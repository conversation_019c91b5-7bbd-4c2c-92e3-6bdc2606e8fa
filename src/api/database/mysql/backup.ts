import { http } from "@/utils/http";

type GetBackupsParams = {
  page: number;
  limit: number;
  keyword?: string;
  host?: string;
};

export type Backup = {
  id: number;
  host: string;
  port: number;
  status: string;
  project: string;
  filename: string;
  filesize: number;
  remark: string;
  backup_time: string;
  created_at: string;
};

export type APIResult = {
  data?: Backup[];
  count?: number;
  msg?: string;
  success: boolean;
};

export const getBackupsAPI = (params: GetBackupsParams) => {
  return http.request<APIResult>("get", "/api/v1/database/mysql/backups", {
    params
  });
};

export const checkTodyBackupAPI = () => {
  return http.request<APIResult>(
    "put",
    "/api/v1/database/mysql/check/today/backups"
  );
};
