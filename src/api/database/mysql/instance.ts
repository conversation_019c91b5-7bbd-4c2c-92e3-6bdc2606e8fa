import type { Domain } from "@/api/appops/domain";
import type { Tag } from "@/api/asset/tag";
import { http } from "@/utils/http";

export type GetInstancesParmas = {
  page: number;
  limit: number;
  keyword?: string;
};

export type Instance = {
  id: number;
  name: string;
  instance_type: string;
  host: string;
  port: number;
  username: string;
  remark: string;
  ci_address: string;
  replica_count: number;
  version: string;
  created_at: string;
  updated_at: string;
  business_domain_ids?: number[];
  tags?: Tag[];
  domains?: Domain[];
};

export type APIResult = {
  success: boolean;
  data?: Instance[];
  count?: number;
  msg?: string;
};

export const getInstancesAPI = (params: GetInstancesParmas) => {
  return http.request<APIResult>("get", "/api/v1/database/mysql/instances", {
    params
  });
};

export type InstanceForm = {
  name: string;
  instance_type: string;
  host: string;
  port: number;
  username: string;
  password: string;
  remark: string;
  ci_address: string;
  replica_count: number;
  business_domain_ids?: number[];
};

export const createInstanceAPI = (data: InstanceForm) => {
  return http.request<APIResult>("post", "/api/v1/database/mysql/instances", {
    data
  });
};

export const deleteInstanceAPI = (id: number) => {
  return http.request<APIResult>(
    "delete",
    `/api/v1/database/mysql/instances/${id}`
  );
};

export const updateInstanceAPI = (id: number, data: InstanceForm) => {
  return http.request<APIResult>(
    "put",
    `/api/v1/database/mysql/instances/${id}`,
    { data }
  );
};

export type Table = {
  id: number;
  instance_id: number;
  schema: string;
  name: string;
  table_type: string;
  engine: string;
  comment: string;
  row_format: string;
  collation: string;
  table_rows: number;
  data_length: number;
  index_length: number;
  create_options: string;
  create_time: string;
  update_time: string;
  sync_time: string;
};

export type GetTablesParams = {
  page: number;
  limit: number;
  keyword?: string;
  schema?: string;
};

export type TableAPIResult = {
  success: boolean;
  data?: Table[];
  count?: number;
  msg?: string;
};

export const getTablesAPI = (id: number, params: GetTablesParams) => {
  return http.request<TableAPIResult>(
    "get",
    `/api/v1/database/mysql/instances/${id}/tables`,
    {
      params
    }
  );
};

export const syncInstanceMetadata = (id: number) => {
  return http.request<APIResult>(
    "put",
    `/api/v1/database/mysql/instances/${id}/sync`
  );
};

type lengthData = {
  date: string;
  schema?: string;
  table?: string;
  length: number;
};

type APILengthResult = {
  success: boolean;
  data?: lengthData[];
  count?: number;
  msg?: string;
};

type TimeRangeParams = {
  start_time: string;
  end_time: string;
};

export const getInstanceLengthAPI = (id: number, params: TimeRangeParams) => {
  return http.request<APILengthResult>(
    "get",
    `/api/v1/database/mysql/instances/${id}/length`,
    { params }
  );
};

export const getInstanceSchemaLengthAPI = (
  id: number,
  schema: string,
  params: TimeRangeParams
) => {
  return http.request<APILengthResult>(
    "get",
    `/api/v1/database/mysql/instances/${id}/schema/${schema}/length`,
    { params }
  );
};

export const getInstanceSchemaTableLengthAPI = (
  id: number,
  schema: string,
  name: string,
  params: TimeRangeParams
) => {
  return http.request<APILengthResult>(
    "get",
    `/api/v1/database/mysql/instances/${id}/table/${schema}/${name}/length`,
    { params }
  );
};

export const testConnectionAPI = (data: InstanceForm) => {
  return http.request<APIResult>(
    "post",
    "/api/v1/database/mysql/instances/test/connection",
    {
      data
    }
  );
};


export type BatTagFrom = {
  tag_ids: number[];
  instance_ids: number[];
  op: string;
}

export const tagInstances = (data: BatTagFrom) => {
  return http.request<APIResult>("post", `/api/v1/database/mysql/tag/instances`, {
    data
  });
}
