import { http } from "@/utils/http";

export type SQLCheckForm = {
  sql_content: string;
  db_name: string;
  project_id: number;
};
export type APIResult = {
  success: boolean;
  data: any;
  msg: string;
};
export const checkSQLAPI = (data: SQLCheckForm) => {
  return http.request<APIResult>(
    "post",
    "/api/v1/workflow/order/check/mysql/sql",
    {
      data
    }
  );
};

export type SQLExecuteForm = {
  sql_content: string;
  db_name: string;
  project_id: number;
  backup: boolean;
};

export const execSQLAPI = (data: SQLCheckForm) => {
  return http.request<APIResult>(
    "post",
    "/api/v1/workflow/order/exec/mysql/sql",
    {
      data
    }
  );
};
