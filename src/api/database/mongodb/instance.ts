import { http } from "@/utils/http";

type GetInstancesParams = {
  page: number;
  limit: number;
  keyword?: string;
};

export type Instance = {
  id: number;
  name: string;
  host: string;
  port: number;
  link: string;
  admin_user: string;
  version: string;
  env: number;
  remark: string;
  created_at: string;
  updated_at: string;
};

type APIResult = {
  data?: Instance[];
  msg?: string;
  count?: number;
  success: boolean;
};

export const getInstancesAPI = (params: GetInstancesParams) => {
  return http.request<APIResult>("get", "/api/v1/database/mongodb/instances", {
    params
  });
};

export const getAllInstancesAPI = () => {
  return http.request<APIResult>(
    "get",
    "/api/v1/database/mongodb/all/instances"
  );
};

export type InstanceForm = {
  name: string;
  host: string;
  port: number;
  link: string;
  admin_user: string;
  admin_password: string;
  version: string;
  env: number;
  remark: string;
};

export const createInstanceAPI = (data: InstanceForm) => {
  return http.request<APIResult>("post", "/api/v1/database/mongodb/instances", {
    data
  });
};

export const updateInstanceAPI = (id: number, data: InstanceForm) => {
  return http.request<APIResult>(
    "put",
    `/api/v1/database/mongodb/instances/${id}`,
    {
      data
    }
  );
};

export const deleteInstanceAPI = (id: number) => {
  return http.request<APIResult>(
    "delete",
    `/api/v1/database/mongodb/instances/${id}`
  );
};
