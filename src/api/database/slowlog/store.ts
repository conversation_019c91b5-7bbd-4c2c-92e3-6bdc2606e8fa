// 慢日志存储

import { http } from "@/utils/http";

// Slowlog store interface
export interface SlowlogStore {
  id: number;
  name: string;
  host: string;
  db: string;
  port: number;
  user: string;
  created_at: string; // ISO date string format
  updated_at: string; // ISO date string format
}

export type SlowlogStoreForm = {
  name: string;
  host: string;
  db: string;
  port: number;
  user: string;
  password: string;
};

export type SlowlogStoreResult = {
  data?: SlowlogStore[];
  count?: number;
  msg?: string;
  success: boolean;
};

export const getSlowlogStoresAPI = (params?: {
  limit?: number;
  page?: number;
  keyword?: string;
}) => {
  return http.request<SlowlogStoreResult>(
    "get",
    "/api/v1/database/slowlog/stores",
    { params }
  );
};

export const addSlowlogStoreAPI = (data: SlowlogStoreForm) => {
  return http.request<SlowlogStoreResult>(
    "post",
    "/api/v1/database/slowlog/stores",
    { data }
  );
};

export const updateSlowlogStoreAPI = (id: number, data: SlowlogStoreForm) => {
  return http.request<SlowlogStoreResult>(
    "put",
    `/api/v1/database/slowlog/stores/${id}`,
    { data }
  );
};

export const deleteSlowlogStoreAPI = (id: number) => {
  return http.request<SlowlogStoreResult>(
    "delete",
    `/api/v1/database/slowlog/stores/${id}`
  );
};

export const getAllSlowlogStoresAPI = () => {
  return http.request<SlowlogStoreResult>(
    "get",
    `/api/v1/database/slowlog/all/stores`
  );
};
