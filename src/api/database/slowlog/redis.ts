// Redis 大键结构

import { http } from "@/utils/http";

// Redis 大键接口
export interface RedisBigKeys {
  id: number;
  db_ip: string;
  db_port: number;
  db_title: string;
  cloud_type: string;
  db_env: string;
  db_project_name: string;
  bigkeys_database: number;
  bigkeys_type: string;
  bigkeys_key: string;
  bigkeys_size: number;
  create_time: string; // ISO 日期字符串格式
}

// Redis String 类型大键统计接口
export interface RedisBigKeysStringStatistic {
  id: number;
  db_project_name: string;
  bigkeys_string_count: number;
  bigkeys_string_100kb_count: number;
  bigkeys_string_1mb_count: number;
  bigkeys_string_32mb_count: number;
  bigkeys_string_64mb_count: number;
  bigkeys_string_128mb_count: number;
  bigkeys_string_256mb_count: number;
  create_time: string; // ISO 日期字符串格式
}

// 查询参数类型
export type RedisBigKeysQueryParams = {
  keyword?: string;
  page?: number;
  limit?: number;
};

// API 返回结果类型
export type RedisBigKeysResult = {
  data?: RedisBigKeys[];
  count?: number;
  msg?: string;
  success: boolean;
};

export type RedisBigKeysStringStatisticResult = {
  data?: RedisBigKeysStringStatistic[];
  count?: number;
  msg?: string;
  success: boolean;
};

// API 函数
export const getRedisBigKeysAPI = (
  id: number,
  params: RedisBigKeysQueryParams
) => {
  return http.request<RedisBigKeysResult>(
    "get",
    `/api/v1/database/slowlog/${id}/redis/bigkeys`,
    { params }
  );
};

export const getRedisBigKeysStringStatisticAPI = (
  id: number,
  params: RedisBigKeysQueryParams
) => {
  return http.request<RedisBigKeysStringStatisticResult>(
    "get",
    `/api/v1/database/slowlog/${id}/redis/bigkeys/statistics`,
    { params }
  );
};
