// MySQL 慢查询日志结构

import { http } from "@/utils/http";

// MySQL 慢查询日志接口
export interface MySQLSlowLog {
  id: number;
  db_instance_id: string;
  db_private_ip: string;
  db_port: number;
  db_title: string;
  db_project_name: string;
  cloud_type: string;
  idc_type?: string;
  db_name: string;
  query_type: string;
  query_count: number;
  query_user: string;
  query_start_time: string;
  query_client_ip: string;
  query_time: number;
  query_lock_time: string;
  rows_sent: number;
  rows_examined: number;
  query_template: string;
  query_sample: string;
  create_time: string; // ISO 日期字符串格式
  update_time: string; // ISO 日期字符串格式
}

// MySQL 慢查询统计接口
export interface MySQLSlowLogStatistics {
  id: number;
  db_instance_id: string;
  db_private_ip: string;
  db_port: number;
  db_title: string;
  db_project_name: string;
  cloud_type?: string;
  idc_type?: string;
  db_name?: string;
  query_type?: string;
  query_user?: string;
  query_client_ip?: string;
  total_count: number;
  avg_query_time?: number;
  max_query_time?: number;
  min_query_time?: number;
  avg_lock_time?: string;
  avg_rows_sent?: number;
  avg_rows_examined?: number;
  query_template?: string;
  create_time: string; // ISO 日期字符串格式
  update_time?: string; // ISO 日期字符串格式
}

// MySQL DML 日志接口
export interface MySQLDMLLog {
  id: number;
  db_instance_id: string;
  db_private_ip: string;
  db_port: number;
  db_title: string;
  db_project_name: string;
  cloud_type?: string;
  idc_type: string;
  offlinedb_ip: string;
  offlinedb_port: number;
  db_name: string;
  tb_name: string;
  query_type: string;
  query_count: number;
  query_user: string;
  query_start_time: string;
  query_client_ip: string;
  query_time: number;
  query_lock_time: string;
  rows_examined: number;
  rows_affected?: number;
  query_command: string;
  query_template?: string;
  query_sample?: string;
  sql_optimizer: string;
  create_time: string; // ISO 日期字符串格式
  update_time: string; // ISO 日期字符串格式
}

// 查询参数类型
export type SlowLogQueryParams = {
  keyword?: string;
  page?: number;
  limit?: number;
};

// API 返回结果类型
export type MySQLSlowLogResult = {
  data?: MySQLSlowLog[];
  count?: number;
  msg?: string;
  success: boolean;
};

export type MySQLSlowLogStatisticsResult = {
  data?: MySQLSlowLogStatistics[];
  count?: number;
  msg?: string;
  success: boolean;
};

export type MySQLDMLLogResult = {
  data?: MySQLDMLLog[];
  count?: number;
  msg?: string;
  success: boolean;
};

// API 函数
export const getMySQLSlowLogsAPI = (id: number, params: SlowLogQueryParams) => {
  return http.request<MySQLSlowLogResult>(
    "get",
    `/api/v1/database/slowlog/${id}/mysql`,
    { params }
  );
};

export const getMySQLSlowLogStatisticsAPI = (
  id: number,
  params: SlowLogQueryParams
) => {
  return http.request<MySQLSlowLogStatisticsResult>(
    "get",
    `/api/v1/database/slowlog/${id}/mysql/statistics`,
    { params }
  );
};

export const getMySQLDMLLogsAPI = (id: number, params: SlowLogQueryParams) => {
  return http.request<MySQLDMLLogResult>(
    "get",
    `/api/v1/database/slowlog/${id}/mysql/dml`,
    { params }
  );
};
