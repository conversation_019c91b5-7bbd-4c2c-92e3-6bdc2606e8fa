import { http } from "@/utils/http";

export type ProjectStatistics = {
  name:     string;
  total:    number;
  stat_time: string;
}

export type ProjectStatisticsList = ProjectStatistics[];

export type ProjectStatisticsResponse = {
  data?: ProjectStatisticsList;
  success: boolean;
  msg?: string;
}

export type ProjectStatisticsParams = {
  start_time?: string;
  end_time?: string;
}

export const getProjectStatistics = (id: number, dbType: string, params: ProjectStatisticsParams) => {
  return http.request<ProjectStatisticsResponse>("get",`/api/v1/database/slowlog/${id}/${dbType}/project/statistics`, { params });
}
