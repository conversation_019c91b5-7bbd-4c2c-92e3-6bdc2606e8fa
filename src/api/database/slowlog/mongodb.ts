// MongoDB 慢查询日志结构

import { http } from "@/utils/http";

// MongoDB 慢查询日志接口
export interface MongoDBSlowLog {
  id: number;
  db_instance_id: string;
  db_private_ip: string;
  db_title: string;
  db_port: number;
  db_project_name: string;
  cloud_type: string;
  query_type: string;
  query_time: number;
  query_docs_examined: number;
  query_database: string;
  query_collection: string;
  query_command: string;
  query_start_time: string;
  create_time: string; // ISO 日期字符串格式
}

// MongoDB 慢查询统计接口
export interface MongoDBSlowLogStatistics {
  id: number;
  db_instance_id: string;
  db_private_ip: string;
  db_port: number;
  db_title: string;
  db_project_name: string;
  total_count: number;
  create_time: string; // ISO 日期字符串格式
}

// 查询参数类型
export type MongoDBSlowLogQueryParams = {
  keyword?: string;
  page?: number;
  limit?: number;
};

// API 返回结果类型
export type MongoDBSlowLogResult = {
  data?: MongoDBSlowLog[];
  count?: number;
  msg?: string;
  success: boolean;
};

export type MongoDBSlowLogStatisticsResult = {
  data?: MongoDBSlowLogStatistics[];
  count?: number;
  msg?: string;
  success: boolean;
};

// API 函数
export const getMongoDBSlowLogsAPI = (
  id: number,
  params: MongoDBSlowLogQueryParams
) => {
  return http.request<MongoDBSlowLogResult>(
    "get",
    `/api/v1/database/slowlog/${id}/mongodb`,
    { params }
  );
};

export const getMongoDBSlowLogStatisticsAPI = (
  id: number,
  params: MongoDBSlowLogQueryParams
) => {
  return http.request<MongoDBSlowLogStatisticsResult>(
    "get",
    `/api/v1/database/slowlog/${id}/mongodb/statistics`,
    { params }
  );
};
