import type { Tag } from "@/api/asset/tag";
import { http } from "@/utils/http";

// Instance 表示一个 Redis 实例
export type Instance = {
  id: number;
  host: string;
  port: number;
  user: string;
  password?: string;
  remark?: string;
  created_at: Date;
  updated_at: Date;
  deleted_at?: Date;
  tags?: Tag[];
}

export type InstanceList = {
  success: boolean;
  data?: Instance[];
  count?: number;
  msg?: string;
}

export type InstanceParams = {
  page: number;
  limit: number;
  keyword?: string;
  tag_ids?: string;
}

export type InstanceResponse = {
  success: boolean;
  data?: Instance;
  msg?: string;
}

export const getInstanceListAPI = async (params: InstanceParams) => {
  return http.request<InstanceList>("get", "/api/v1/database/redis/instances", { params });
}

export const createInstanceAPI = async (data: InstanceForm) => {
  return http.request<InstanceResponse>("post", "/api/v1/database/redis/instances", { data });
}

export type InstanceForm = {
  host: string;
  port: number;
  user: string;
  password?: string;
  remark?: string;
  tag_ids?: number[];
}

export const updateInstanceAPI = async (id: number, data: InstanceForm) => {
  return http.request<InstanceResponse>("put", `/api/v1/database/redis/instances/${id}`, { data });
}

export const testConnectInstanceAPI = async (data: InstanceForm) => {
  return http.request<InstanceResponse>("post", `/api/v1/database/redis/instances/test/connection`, { data });
}
export const syncConnectInstanceAPI = async () => {
  return http.request<InstanceResponse>("post", `/api/v1/database/redis/instances/sync`);
}

export const deleteInstanceAPI = async (id: number) => {
  return http.request<InstanceResponse>("delete", `/api/v1/database/redis/instances/${id}`);
}


export type BatTagFrom = {
  tag_ids: number[];
  instance_ids: number[];
  op: string;
}

export const tagInstances = (data: BatTagFrom) => {
  return http.request<InstanceResponse>("post", `/api/v1/database/redis/tag/instances`, {
    data
  });
}
