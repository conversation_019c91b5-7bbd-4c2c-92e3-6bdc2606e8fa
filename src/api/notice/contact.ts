import { http } from "@/utils/http";
import type { ContactGroup } from "./contact-group";

type GetContactsParams = {
  page: number;
  limit: number;
  keyword?: string;
};

export type Contact = {
  id: number;
  name: string;
  contact_type: string;
  link: string;
  remark: string;
  created_at: string;
  updated_at: string;
  groups: ContactGroup[];
};

export type APIResult = {
  data?: Contact[];
  count?: number;
  msg?: string;
  success: boolean;
};

export const getContactsAPI = (params: GetContactsParams) => {
  return http.request<APIResult>("get", "/api/v1/notice/contacts", { params });
};

export type ContactForm = {
  name: string;
  contact_type: string;
  link: string;
  remark: string;
  group_ids: number[];
};

export const addContactAPI = (data: ContactForm) => {
  return http.request<APIResult>("post", "/api/v1/notice/contacts", { data });
};

export const updateContactAPI = (id: number, data: ContactForm) => {
  return http.request<APIResult>("put", `/api/v1/notice/contacts/${id}`, {
    data
  });
};

export const deleteContactAPI = (id: number) => {
  return http.request<APIResult>("delete", `/api/v1/notice/contacts/${id}`);
};

export const getAllContactAPI = () => {
  return http.request<APIResult>("get", "/api/v1/notice/all/contacts");
};
