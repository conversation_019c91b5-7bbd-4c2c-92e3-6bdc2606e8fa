import { http } from "@/utils/http";

export type GetMessagesParmas = {
  page: number;
  limit: number;
  contact?: string;
  status?: number;
  message_type?: string;
  start_time?: Date;
  end_time?: Date;
  op_time?: Date[];
};

export type Message = {
  id: number;
  title: string;
  content: string;
  message_type: string;
  contact: string;
  link: string;
  status: number;
  at_mobiles: string;
  send_result: string;
  created_at: string;
  send_time: string;
};

export type APIResult = {
  data?: Message[] | Message;
  msg?: string;
  count?: number;
  success: boolean;
};

export const getMessagesAPI = (param: GetMessagesParmas) => {
  const params: GetMessagesParmas = {
    page: param.page,
    limit: param.limit,
    status: param.status,
    message_type: param.message_type,
    contact: param.contact
  };
  if (param.op_time) {
    params.start_time = param.op_time[0];
    params.end_time = param.op_time[1];
  }
  return http.request<APIResult>("get", "/api/v1/notice/messages", { params });
};

export const getMessageAPI = (id: number) => {
  return http.request<APIResult>("get", `/api/v1/notice/messages/${id}`);
};
