import { http } from "@/utils/http";

export type ContactGroup = {
  id: number;
  name: string;
  remark: string;
  created_at: string;
  updated_at: string;
};

export type APIResult = {
  data?: ContactGroup[];
  count?: number;
  msg?: string;
  success: boolean;
};

type GetContactGroupsParmas = {
  page: number;
  limit: number;
  keyword?: string;
};

export type ContactGroupForm = {
  name: string;
  remark: string;
};

export const getContactGroupsAPI = (params: GetContactGroupsParmas) => {
  return http.request<APIResult>("get", "/api/v1/notice/contact-groups", {
    params
  });
};

export const addContactGroupAPI = (data: ContactGroupForm) => {
  return http.request<APIResult>("post", "/api/v1/notice/contact-groups", {
    data
  });
};

export const deleteContactGroupAPI = (id: number) => {
  return http.request<APIResult>(
    "delete",
    `/api/v1/notice/contact-groups/${id}`
  );
};
export const updateContactGroupAPI = (id: number, data: ContactGroupForm) => {
  return http.request<APIResult>("put", `/api/v1/notice/contact-groups/${id}`, {
    data
  });
};
