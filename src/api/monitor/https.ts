import { http } from "@/utils/http";

export type GetHttpsMonitorListParams = {
  page: number;
  limit: number;
  keyword?: string;
  enable_monitor?: boolean;
  is_secure?: number;
};

export type DomainHttps = {
  id: number;
  domain: string;
  port: number;
  remark: string;
  enable_monitor: boolean;
  is_secure: number;
  info: any;
  alarm_days_before_expired: number;
  not_before: string;
  not_after: string;
  updated_at: string;
};

export type APIResult = {
  success: boolean;
  data?: DomainHttps[] | any;
  msg?: string;
  count?: number;
};

export const getHttpsMonitorListAPI = (params: GetHttpsMonitorListParams) => {
  return http.request<APIResult>("get", "/api/v1/monitor/domain-https", {
    params
  });
};

export type DomainHttpsForm = {
  domain: string;
  port: number;
  remark: string;
  enable_monitor: boolean;
  alarm_days_before_expired: number;
};

export const addDomainHttpsMonitorAPI = (data: DomainHttpsForm) => {
  return http.request<APIResult>("post", "/api/v1/monitor/domain-https", {
    data
  });
};

export const updateDomainHttpsMonitorAPI = (
  id: number,
  data: DomainHttpsForm
) => {
  return http.request<APIResult>("put", `/api/v1/monitor/domain-https/${id}`, {
    data
  });
};

export const deleteHttpsMonitorAPI = (id: number) => {
  return http.request<APIResult>(
    "delete",
    `/api/v1/monitor/domain-https/${id}`
  );
};

export type MonitorForm = {
  ids: number[];
  enable_monitor: boolean;
};

export const batSwitchMonitor = (data: MonitorForm) => {
  return http.request<APIResult>(
    "post",
    "/api/v1/monitor/domain-https/bat-switch-notice",
    {
      data
    }
  );
};
export const batCheckHttpsMonitorAPI = (data: MonitorForm) => {
  return http.request<APIResult>(
    "post",
    "/api/v1/monitor/domain-https/bat-check",
    {
      data
    }
  );
};

export const getHttpsMonitorInfoAPI = (id: number) => {
  return http.request<APIResult>(
    "get",
    `/api/v1/monitor/domain-https/${id}/info`
  );
};

export const autoCreateHttpsMonitorAPI = () => {
  return http.request<APIResult>(
    "post",
    "/api/v1/monitor/domain-https/auto-create"
  );
};

export const autoCheckHttpsMonitorAPI = () => {
  return http.request<APIResult>(
    "post",
    "/api/v1/monitor/domain-https/auto-check"
  );
};

export const batDeleteHttpsMonitorAPI = (data: MonitorForm) => {
  return http.request<APIResult>(
    "post",
    "/api/v1/monitor/domain-https/bat-delete",
    { data }
  );
};
