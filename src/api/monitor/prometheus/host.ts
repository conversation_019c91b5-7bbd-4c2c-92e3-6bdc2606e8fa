import type { Host } from "@/api/asset/host";
import { http } from "@/utils/http";

// 主机指标
export interface HostMetric {
  id: number;
  ip: string;
  stat_day: Date;
  max_cpu_usage: number;
  avg_cpu_usage: number;
  min_cpu_usage: number;
  max_load_usage: number;
  avg_load_usage: number;
  min_load_usage: number;
  max_memory_usage: number;
  avg_memory_usage: number;
  min_memory_usage: number;
  max_io_usage: number;
  avg_io_usage: number;
  min_io_usage: number;
  host?: Host;
}


export interface GetHostMetricsParams {
  ip: string;
  start_time: string;
  end_time: string;
}

export interface GetHostMetricsResponse {
  success: boolean;
  msg?: string;
  data?: HostMetric[];
  count?: number;
}

export const getHostMetricsAPI = (params: GetHostMetricsParams) => {
  return http.request<GetHostMetricsResponse>(
    "get",
    "/api/v1/monitor/prometheus/host/metrics",
    {
      params
    }
  );
};

export interface GetLowUsageHostsParams {
  start_time: string;
  end_time: string;
  threshold: number;
  status?: number;
  page?: number;
  limit?: number;
}

export const getLowUsageHostsAPI = (params: GetLowUsageHostsParams) => {
  return http.request<GetHostMetricsResponse>(
    "get",
    "/api/v1/monitor/prometheus/host/low_usage",
    {
      params
    }
  );
};