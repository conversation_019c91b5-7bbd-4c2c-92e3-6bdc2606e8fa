import { http } from "@/utils/http";

export type Instance = {
  id: number;
  name: string;
  instance_type: string;
  datasource: string;
  created_at: string;
  updated_at: string;
};

type GetInstanceListParams = {
  page: number;
  limit: number;
  instance_type?: string;
  keyword?: string;
};

export interface InstanceListResponse {
  success: boolean;
  data?: Instance[];
  count?: number;
  msg?: string;
}

export const getInstanceListAPI = (params: GetInstanceListParams) => {
  return http.request<InstanceListResponse>(
    "get",
    "/api/v1/monitor/prometheus/instances",
    {
      params
    }
  );
};

export type InstanceForm = {
  name: string;
  instance_type: string;
  datasource: string;
};

export type OPResponse = {
  success: boolean;
  msg?: string;
  data?: any;
};

export const createInstanceAPI = (data: InstanceForm) => {
  return http.request<OPResponse>(
    "post",
    "/api/v1/monitor/prometheus/instances",
    {
      data
    }
  );
};

export const updateInstanceAPI = (id: number, data: InstanceForm) => {
  return http.request<OPResponse>(
    "put",
    `/api/v1/monitor/prometheus/instances/${id}`,
    {
      data
    }
  );
};

export const deleteInstanceAPI = (id: number) => {
  return http.request<OPResponse>(
    "delete",
    `/api/v1/monitor/prometheus/instances/${id}`
  );
};

export const testInstanceConnectAPI = (id: number) => {
  return http.request<OPResponse>(
    "post",
    `/api/v1/monitor/prometheus/instances/${id}/test-connect`
  );
};
