import { http } from "@/utils/http";

export type DutyMember = {
  id: number;
  uid: number;
  order_index: number;
  name: string;
  phone: string;
  sn: string;
};

export type Duty = {
  id: number;
  uid: number;
  due_date: Date;
  name: string;
  phone: string;
  sn: string;
  due_date_string: string;
};

export type APIResult = {
  data?: Duty[] | DutyMember[];
  success: boolean;
  msg?: string;
};

export const getAllDutyMembersAPI = () => {
  return http.request<APIResult>("get", "/api/v1/note/duty/members");
};

export type DutyMemberForm = {
  uid: number;
  order_index: number;
};

export type DutyMembersForm = {
  members: DutyMemberForm[];
};

export const updateDutyMembersAPI = (data: DutyMembersForm) => {
  return http.request<APIResult>("put", "/api/v1/note/duty/members", { data });
};

export const getDutyAPI = (month: string) => {
  return http.request<APIResult>("get", "/api/v1/note/duty", {
    params: { month }
  });
};

type AdjustScheduleForm = {
  from_date: string;
  to_date: string;
};

export const AdjustScheduleAPI = (data: AdjustScheduleForm) => {
  return http.request<APIResult>("put", "/api/v1/note/duty", {
    data
  });
};

type ReAutoScheduleFrom = {
  start_time: string;
  end_time?: string;
};

export const ReAutoScheduleAPI = (params: ReAutoScheduleFrom) => {
  return http.request<APIResult>("post", "/api/v1/note/duty/reschedule", {
    params
  });
};

export const notifyDutyAPI = () => {
  return http.request<APIResult>("put", "/api/v1/note/duty/notify");
};
