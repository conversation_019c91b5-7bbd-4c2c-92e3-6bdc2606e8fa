import { http } from "@/utils/http";

export type GetBookmarksParams = {
  page: number;
  limit: number;
  keyword?: string;
};

export type Bookmark = {
  id: number;
  title: string;
  url: string;
  remark: string;
  set_top: boolean;
  created_at: Date;
  updated_at: Date;
};

export type APIResult = {
  success: boolean;
  msg?: string;
  data?: Bookmark[];
  count?: number;
};

export const getBookmarksAPI = (params: GetBookmarksParams) => {
  return http.request<APIResult>("get", "/api/v1/note/bookmarks", { params });
};

export type BookmarkForm = {
  title: string;
  url: string;
  remark: string;
};

export const addBookmarkAPI = (data: BookmarkForm) => {
  return http.request<APIResult>("post", "/api/v1/note/bookmarks", { data });
};

export const updateBookmarkAPI = (id: number, data: BookmarkForm) => {
  return http.request<APIResult>("put", `/api/v1/note/bookmarks/${id}`, {
    data
  });
};

export const deleteBookmarkAPI = (id: number) => {
  return http.request<APIResult>("delete", `/api/v1/note/bookmarks/${id}`);
};
