import { http } from "@/utils/http";

type GetJournalParamas = {
  page: number;
  limit: number;
  keyword?: string;
  project?: string;
  event_type?: string;
  region_type?: string;
  tag?: string;
};

export type Journal = {
  id: number;
  title: string;
  event_type: string;
  region_type: string;
  tags: string[];
  start_time: string;
  end_time: string;
  content: string;
  conclusion: string;
  projects: string[];
  created_by: string;
  created_at: string;
  updated_at: string;
};

// JournalForm 事件表单
export type JournalForm = {
  title: string;
  event_type: string;
  region_type: string;
  tags: string[];
  start_time: string;
  end_time: string;
  content: string;
  conclusion: string;
  projects: string[];
  created_by: string;
};

export type APIResult = {
  success: boolean;
  msg?: string;
  count?: number;
  data?: Journal[] | Journal;
};

export const getJournalListAPI = (params: GetJournalParamas) => {
  return http.request<APIResult>("get", "/api/v1/note/journals", { params });
};

export const createJournalAPI = (data: JournalForm) => {
  return http.request<APIResult>("post", "/api/v1/note/journals", { data });
};

export const updateJournalAPI = (id: number, data: JournalForm) => {
  return http.request<APIResult>("put", `/api/v1/note/journals/${id}`, {
    data
  });
};

export const deleteJournalAPI = (id: number) => {
  return http.request<APIResult>("delete", `/api/v1/note/journals/${id}`);
};

export const getJournalDetailAPI = (id: number) => {
  return http.request<APIResult>("get", `/api/v1/note/journals/${id}`);
};
