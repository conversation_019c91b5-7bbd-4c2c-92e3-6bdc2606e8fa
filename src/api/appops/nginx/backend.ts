import { http } from "@/utils/http";

export type NginxBackend = {
  id: number;
  ip: string;
  backends: string[];
  server_names: string[];
  remark: string;
  sync_time: string;
};

export type NginxBackendListParams = {
  page: number;
  limit: number;
  keyword?: string;
  server_name: string;
  ip?: string;
};

export type NginxBackendListResponse = {
  success: boolean;
  data?: NginxBackend[];
  count?: number;
  msg?: string;
};

export const getNginxBackendListAPI = (params: NginxBackendListParams) => {
  return http.request<NginxBackendListResponse>("get", "/api/v1/appops/nginx/backends", {
    params
  });
};

export type OPResponse = {
  success: boolean;
  msg?: string;
};
export const syncNginxBackendsAPI = () => {
  return http.request<OPResponse>("post", "/api/v1/appops/nginx/backends/sync");
};
