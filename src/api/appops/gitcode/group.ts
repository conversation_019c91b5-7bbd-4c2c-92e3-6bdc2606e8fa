import { http } from "@/utils/http";

export interface Group {
  id: number;
  gitlab_id: number;
  group_id: number;
  name: string;
  description: string;
  http_url: string;
  sync_time: string;
}

export type GroupListParams = {
  keyword?: string;
  gitlab_id?: number;
  page: number;
  limit: number;
};

export type GroupListReponse = {
  success: boolean;
  data?: Group[];
  count?: number;
};

export function getGroupListAPI(params: GroupListParams) {
  return http.request<GroupListReponse>(
    "get",
    "/api/v1/appops/gitcode/groups",
    { params }
  );
}
