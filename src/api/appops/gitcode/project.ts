import { http } from "@/utils/http";

export interface Project {
  id: number; // ID
  gitlab_id: number; // GitLab ID
  project_id: number; // 项目 ID
  group_id: number; // 组 ID
  group_name: string; // 组名称
  gitlab_name: string; // GitLab 名称
  name: string; // 名称
  description: string; // 描述
  http_url: string; // HTTP URL
  ssh_url: string; // SSH URL
  created_at: Date; // 创建时间
  deleted_at: Date; // 删除时间
  sync_time: Date; // 同步时间
}

export type ProjectListParams = {
  keyword?: string;
  gitlab_id?: number;
  page: number;
  limit: number;
};

export type ProjectListReponse = {
  success: boolean;
  data?: Project[];
  count?: number;
  msg?: string;
};

export function getProjectListAPI(params: ProjectListParams) {
  return http.request<ProjectListReponse>(
    "get",
    "/api/v1/appops/gitcode/projects",
    { params }
  );
}
