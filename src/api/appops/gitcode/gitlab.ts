import { http } from "@/utils/http";

export interface Gitlab {
  id: number;
  name: string;
  remark: string;
  address: string;
  token: string;
  sync_time: string;
  deleted_at: string;
}

export type GitlabListParmas = {
  page: number;
  limit: number;
  keyword?: string;
};

export type GitLabListReponse = {
  success: boolean;
  data?: Gitlab[];
  count?: number;
  msg?: string;
};

export const getGitlabListAPI = (params: GitlabListParmas) => {
  return http.request<GitLabListReponse>(
    "get",
    "/api/v1/appops/gitcode/gitlabs",
    {
      params
    }
  );
};

export type GitlabForm = {
  name: string;
  remark: string;
  address: string;
  token: string;
};

export type GitlabOPResponse = {
  success: boolean;
  msg?: string;
};

export const addGitlabAPI = (data: GitlabForm) => {
  return http.request<GitlabOPResponse>(
    "post",
    "/api/v1/appops/gitcode/gitlabs",
    {
      data
    }
  );
};

export const updateGitlabAPI = (id: number, data: GitlabForm) => {
  return http.request<GitlabOPResponse>(
    "put",
    `/api/v1/appops/gitcode/gitlabs/${id}`,
    {
      data
    }
  );
};

export const deleteGitlabAPI = (id: number) => {
  return http.request<GitlabOPResponse>(
    "delete",
    `/api/v1/appops/gitcode/gitlabs/${id}`
  );
};

export const syncGroupsAPI = (id: number) => {
  return http.request<GitlabOPResponse>(
    "post",
    `/api/v1/appops/gitcode/gitlabs/${id}/sync/groups`
  );
};

export const syncProjectsAPI = (id: number) => {
  return http.request<GitlabOPResponse>(
    "post",
    `/api/v1/appops/gitcode/gitlabs/${id}/sync/projects`
  );
};

export const getallGitlabAPI = () => {
  return http.request<GitLabListReponse>(
    "get",
    "/api/v1/appops/gitcode/all/gitlabs"
  );
};
