import { http } from "@/utils/http";

export type Domain = {
  id: number;
  name: string;
  domain_type: string;
  business_id: number;
  business: string;
  remark: string;
}

export type DomainListResponse = {
  success: boolean;
  data?: Domain[];
  count?: number;
  msg?: string;
};

export type DomainListParams = {
  page: number;
  limit: number;
  keyword?: string;
  business_id?: number;
};

export type DomainForm = {
  name: string;
  domain_type: string;
  business_id: number;
  remark: string;
};

export type OperationResponse = {
  success: boolean;
  msg?: string;
};

export const getDomainListAPI = (params: DomainListParams) => {
  return http.request<DomainListResponse>("get", "/api/v1/appops/domains", {
    params
  });
};

export const getAllDomainsAPI = () => {
  return http.request<DomainListResponse>("get", "/api/v1/appops/all/domains",);
}


export const addDomainAPI = (data: DomainForm) => {
  return http.request<OperationResponse>("post", "/api/v1/appops/domains", {
    data
  });
};

export const updateDomainAPI = (id: number, data: DomainForm) => {
  return http.request<OperationResponse>("put", `/api/v1/appops/domains/${id}`, {
    data
  });
};

export const deleteDomainAPI = (id: number) => {
  return http.request<OperationResponse>("delete", `/api/v1/appops/domains/${id}`);
};
