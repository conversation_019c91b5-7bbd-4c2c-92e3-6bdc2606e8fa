import { http } from "@/utils/http";

export type Harbor = {
  id: number;
  name: string;
  address: string;
  version: string;
  username: string;
  password: string;
  remark: string;
  created_at: string;
  updated_at: string;
}

export type HarborListParmas = {
  page: number;
  limit: number;
  keyword?: string;
};

export type HarborListReponse = {
  success: boolean;
  data?: Harbor[];
  count?: number;
  msg?: string;
};

/**
 * 获取harbor列表
 * @param params 查询参数
 * @returns 一个 Promise，resolve 到包含harbor列表的响应
 */
export const getHarborListAPI = (params: HarborListParmas) => {
  return http.request<HarborListReponse>("get", "/api/v1/appops/harbors", {
    params
  });
};

export type HarborForm = {
  name: string;
  address: string;
  version: string;
  username: string;
  password: string;
  remark: string;
};

export type OPResponse = {
  success: boolean;
  msg?: string;
};

/**
 * 创建harbor.
 * @param data 要创建的harbor的数据
 * @returns 创建操作的结果
 */
export const addHarborAPI = (data: HarborForm) => {
  return http.request<OPResponse>("post", "/api/v1/appops/harbors", {
    data
  });
};

/**
 * 更新harbor.
 * @param id 要更新的harbor的id
 * @param data harbor的新数据
 * @returns 更新操作的结果
 */
export const updateHarborAPI = (id: number, data: HarborForm) => {
  return http.request<OPResponse>("put", `/api/v1/appops/harbors/${id}`, {
    data
  });
};

/**
 * 删除harbor.
 * @param id 要删除的harbor的id
 * @returns 删除操作的结果
 */
export const deleteHarborAPI = (id: number) => {
  return http.request<OPResponse>("delete", `/api/v1/appops/harbors/${id}`);
};
