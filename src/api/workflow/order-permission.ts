import { http } from "@/utils/http";
import { OrderType } from "@/config/order-enum";
import type { User } from "../auth/user";
import type { Process } from "./process";
export type OrderForm = {
  order_type: string;
  content: string;
  bucket?: string;
  hosts?: string[];
  os?: string[];
  is_sensitive?: boolean;
};

export type APIResult = {
  msg?: string;
  success: boolean;
};

export const applyOrderAPI = (data: OrderForm) => {
  if (data.order_type === "DGC(数据仓库)") {
    return http.request<APIResult>(
      "post",
      `/api/v1/workflow/order/apply/${OrderType.PermissionOrderDGCType}`,
      {
        data
      }
    );
  } else if (
    data.order_type === "OSS权限" ||
    data.order_type === "CDN权限" ||
    data.order_type === "阿里云子账号" ||
    data.order_type === "华为云子账号"
  ) {
    return http.request<APIResult>(
      "post",
      `/api/v1/workflow/order/apply/${OrderType.CloudAccountOrderType}`,
      {
        data
      }
    );
  } else {
    return http.request<APIResult>(
      "post",
      `/api/v1/workflow/order/apply/${OrderType.PermissionOrderType}`,
      {
        data
      }
    );
  }
};

export type PermissionOrder = {
  id: number;
  title: string;
  order_type: string;
  ext_type: number;
  sn: string;
  applicant_id: number;
  status: number;
  process_index: number;
  created_at: string;
  plan_time?: string;
  orders: string[];
  env: number;
  critical: boolean;
  applicant: User;
  content: string;
  bucket: string;
  hosts: any;
  os: any;
  is_sensitive: boolean;
  processes: Process[];
};
