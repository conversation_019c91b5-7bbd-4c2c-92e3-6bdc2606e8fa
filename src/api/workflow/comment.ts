import { http } from "@/utils/http";
import type { User } from "../auth/user";

export type OrderComment = {
  id: number;
  user_id: number;
  user: User;
  sn: string;
  content: string;
  created_at: Date;
};

export type APIResult = {
  success: boolean;
  msg?: string;
}

export type OrderCommentForm = {
  sn: string;
  content: string;
}

export const addCommentAPI = (data: OrderCommentForm) => {
  return http.request<APIResult>("post", `/api/v1/workflow/order/comments`, { data });
}

export const updateCommentAPI = (id: number, data: OrderCommentForm) => {
  return http.request<APIResult>("put", `/api/v1/workflow/order/comments/${id}`, { data })
}