import { http } from "@/utils/http";
import { OrderType } from "@/config/order-enum";
import type { User } from "../auth/user";
import type { Process } from "./process";
export type OrderForm = {
  order_type: string;
  domain_list: any;
  content: string;
  orders: string[];
  env: number;
  critical: boolean;
};

export type APIResult = {
  msg?: string;
  success: boolean;
};

export const applyOrderAPI = (data: OrderForm) => {
  return http.request<APIResult>(
    "post",
    `/api/v1/workflow/order/apply/${OrderType.DomainOrderType}`,
    {
      data
    }
  );
};

export type DomainOrder = {
  id: number;
  title: string;
  order_type: string;
  ext_type: number;
  sn: string;
  applicant_id: number;
  status: number;
  process_index: number;
  created_at: string;
  plan_time?: string;
  orders: string[];
  env: number;
  critical: boolean;
  applicant: User;
  content: string;
  domain_list: any;
  processes: Process[];
};
