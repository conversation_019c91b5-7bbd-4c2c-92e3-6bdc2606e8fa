import { http } from "@/utils/http";
import type { User } from "../auth/user";

export type Order = {
  id: number;
  title: string;
  order_type: string;
  ext_type: number;
  sn: string;
  applicant_id: number;
  status: number;
  process_index: number;
  created_at: string;
  plan_time?: string;
  orders: string[];
  env: number;
  critical: boolean;
  applicant: User;
  evaluated?: boolean;
};

type GetOrdersParams = {
  page: number;
  limit: number;
  keyword?: string;
  status?: number;
  ext_type?: number;
  applicant_id?: number;
};

type APIResult = {
  data?: Order[];
  msg?: string;
  count?: number;
  success: boolean;
};

export const getOrdersAPI = (params: GetOrdersParams) => {
  return http.request<APIResult>("get", "/api/v1/workflow/all/orders", {
    params
  });
};

export const cancelOrderAPI = (id: number) => {
  return http.request<APIResult>("post", `/api/v1/workflow/cancel/order/${id}`);
};

// 更新工单
export const updateOrderAPI = (id: number, data: any) => {
  return http.request<APIResult>("put", `/workflow/orders/${id}`, { data });
};

// 获取工单详情
export const getOrderDetailAPI = (id: string) => {
  return http.request<APIResult>("get", `/workflow/orders/${id}`);
};
