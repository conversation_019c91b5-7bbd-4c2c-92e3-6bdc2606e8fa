// 基础工单更新接口
export interface BaseOrderUpdate {
  title?: string;
  description?: string;
  status?: number;
  priority?: number;
  attachments?: string[];
  comment?: string;
}

// 服务器工单更新
export interface ServerOrderUpdate extends BaseOrderUpdate {
  server_id?: string;
  server_type?: string;
  expected_time?: string;
}

// 网络工单更新
export interface NetworkOrderUpdate extends BaseOrderUpdate {
  network_type?: string;
  ip_address?: string;
  port_config?: string;
}

// 账号工单更新
export interface AccountOrderUpdate extends BaseOrderUpdate {
  account_type?: string;
  username?: string;
  permissions?: string[];
}
