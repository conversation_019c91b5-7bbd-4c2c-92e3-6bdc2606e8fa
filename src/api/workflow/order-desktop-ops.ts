import { http } from "@/utils/http";
import { OrderType } from "@/config/order-enum";
import type { User } from "../auth/user";
import type { Process } from "./process";

export type OrderForm = {
  content: string;
  cubicle: string;
  plan_time: Date;
  applicant_id?: number;
  help_apply?: boolean;
  service_duration_seconds?: number;
};

export type APIResult = {
  msg?: string;
  success: boolean;
};

export const applyOrderAPI = (data: OrderForm) => {
  if (data.help_apply) {
    return http.request<APIResult>("post", `/api/v1/workflow/admin/${data.applicant_id}/apply/${OrderType.DesktopOPSOrderType}`, {
      data
    });
  } else {
    return http.request<APIResult>(
      "post",
      `/api/v1/workflow/order/apply/${OrderType.DesktopOPSOrderType}`,
      {
        data
      }
    );
  }
};

export type DesktopOPSOrder = {
  id: number;
  title: string;
  order_type: string;
  ext_type: number;
  sn: string;
  applicant_id: number;
  status: number;
  process_index: number;
  created_at: string;
  plan_time?: string;
  orders: string[];
  env: number;
  critical: boolean;
  applicant: User;
  cubicle: string;
  content: string;
  processes: Process[];
};
