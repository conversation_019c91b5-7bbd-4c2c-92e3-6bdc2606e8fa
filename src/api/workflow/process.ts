import { http } from "@/utils/http";
import type { User } from "../auth/user";

export type Process = {
  id: number;
  process_index: number;
  sn: string;
  approver_id: number;
  node_name: string;
  status: number;
  comment: string;
  approve_time: string;
  created_at: string;
  approver: User;
  approvers: User[];
  ccs: User[];
};

export type APIResult = {
  msg?: string;
  data?: Process[];
  success: boolean;
};

export const getProcessesAPI = (orderType: string, applicantID: number | undefined) => {
  if (applicantID) {
    return http.request<APIResult>(
      "get",
      `/api/v1/workflow/admin/${applicantID}/processes/${orderType}`
    );
  } else {
    return http.request<APIResult>(
      "get",
      `/api/v1/workflow/order/processes/${orderType}`
    );
  }
};
