import { http } from "@/utils/http";

export type FlowNode = {
  id: number;
  name: string;
  flow_id: number;
  flow_index: number;
  approver_type: number;
  approver_ids: number[];
  approvers_names: string;
  cc_type: number;
  cc_ids: number[];
  cc_names: string;
};

export type Flow = {
  id: number;
  name: string;
  order_type: number;
  nodes?: FlowNode[];
};

type APIResult = {
  msg?: string;
  data?: Flow[];
  success: boolean;
};

export const getAllFlowsAPI = () => {
  return http.request<APIResult>("get", "/api/v1/workflow/flows");
};

export type UpdateNodesForm = {
  flow_id: number;
  nodes: FlowNode[];
};

export const updateFlowAPI = (data: UpdateNodesForm) => {
  return http.request<APIResult>("put", "/api/v1/workflow/flow", { data });
};
