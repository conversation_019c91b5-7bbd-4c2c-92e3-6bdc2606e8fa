import { http } from "@/utils/http";
import { OrderType } from "@/config/order-enum";
import type { User } from "../auth/user";
import type { Process } from "./process";

export type OrderForm = {
  content: string;
  permission: number;
  projects: string[];
  expired_at?: Date;
  plan_time: Date;
  orders: string[];
  env: number;
  critical: boolean;
};

export type APIResult = {
  msg?: string;
  success: boolean;
};

export const applyOrderAPI = (data: OrderForm) => {
  return http.request<APIResult>(
    "post",
    `/api/v1/workflow/order/apply/${OrderType.GitlabOrderType}`,
    {
      data
    }
  );
};

export type GitlabOrder = {
  id: number;
  title: string;
  order_type: string;
  ext_type: number;
  sn: string;
  applicant_id: number;
  status: number;
  process_index: number;
  created_at: string;
  plan_time?: string;
  orders: string[];
  env: number;
  critical: boolean;
  applicant: User;
  content: string;
  email: string;
  permission: number;
  projects: any;
  expired_at?: Date;
  processes: Process[];
};

export const autoOpenGitlabAPI = (id: number) => {
  return http.request<APIResult>(
    "put",
    `/api/v1/workflow/order/gitlab/pipeline/${id}`
  );
};
