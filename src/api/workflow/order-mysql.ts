import { http } from "@/utils/http";
import type { User } from "../auth/user";
import type { Process } from "./process";
import { OrderType } from "@/config/order-enum";
import type { Table } from "../database/mysql/instance";

export type Attachment = {
  filename: string;
  url: string;
};

export type OrderForm = {
  title: string;
  order_type: string;
  content: string;
  sql_content: string;
  db_name: string;
  project_id: number;
  project_dbname: string;
  attachments: Attachment[];
  plan_time: Date;
  orders: string[];
  env: number;
  permission: string;
  critical: boolean;
  iteration_version: string;
};

export type APIResult = {
  msg?: string;
  data?: Attachment[];
  success: boolean;
};

export const applyOrderAPI = (data: OrderForm) => {
  return http.request<APIResult>(
    "post",
    `/api/v1/workflow/order/apply/${OrderType.MySQLOrderType}`,
    {
      data
    }
  );
};

export type MySQLOrder = {
  id: number;
  title: string;
  order_type: string;
  ext_type: number;
  sn: string;
  applicant_id: number;
  status: number;
  process_index: number;
  created_at: string;
  plan_time?: string;
  orders: string[];
  env: number;
  critical: boolean;
  applicant: User;
  content: string;
  attachments: any;
  project_id: number;
  db_name: string;
  sql_content: string;
  permission: string;
  processes: Process[];
  iteration_version: string;
};

type MySQLTableAPI = {
  success: boolean;
  data: Table[];
  msg: string;
};

export const getOrderMySQLTablesAPI = (sn: string) => {
  return http.request<MySQLTableAPI>(
    "get",
    `/api/v1/workflow/order/mysql/tables/${sn}`
  );
};
