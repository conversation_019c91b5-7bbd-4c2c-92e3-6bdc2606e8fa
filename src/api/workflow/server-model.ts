import { http } from "@/utils/http";

export type ServerModel = {
  id: number;
  spec_family: string;
  spec: string;
  cpu: string;
  cpu_model: string;
  memory: string;
  gpu: string;
  process_freq: string;
  lan_band: string;
  lan_package: string;
  iops: string;
  price: number;
};

type APIResult = {
  success: boolean;
  data: ServerModel[];
  msg?: string;
  count?: number;
};

export type GetServerModelsParams = {
  page: number;
  limit: number;
  spec?: string;
  cpu?: number;
  memory?: number;
};

export const getServerModelsAPI = (params: GetServerModelsParams) => {
  return http.request<APIResult>("get", "/api/v1/workflow/server/models", {
    params
  });
};
