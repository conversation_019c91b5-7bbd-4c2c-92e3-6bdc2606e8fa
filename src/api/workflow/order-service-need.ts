import { http } from "@/utils/http";
import { OrderType } from "@/config/order-enum";
import type { User } from "../auth/user";
import type { Process } from "./process";

export type OrderForm = {
  title: string;
  order_type: string;
  content: string;
  plan_time: Date;
  orders: string[];
  env: number;
  critical: boolean;
  iteration_version: string;
  cloud_platforms?: string[];
  need_decrypt_stat_info?: boolean;
  help_apply?: boolean;
  applicant_id?: number;
  service_duration_seconds?: number; // 添加服务时长属性
};

export type APIResult = {
  msg?: string;
  success: boolean;
};

export const applyOrderAPI = (data: OrderForm) => {
  if (data.help_apply) {
    // 如果是补充申请，使用管理员接口
    return http.request<APIResult>("post", `/api/v1/workflow/admin/${data.applicant_id}/apply/${OrderType.ServiceNeedOrderType}`, {
      data
    });
  } else {
    // 普通申请使用原来的接口
    return http.request<APIResult>(
      "post",
      `/api/v1/workflow/order/apply/${OrderType.ServiceNeedOrderType}`,
      {
        data
      }
    );
  }
};

export type ServiceNeedOrder = {
  id: number;
  title: string;
  order_type: string;
  ext_type: number;
  sn: string;
  applicant_id: number;
  status: number;
  process_index: number;
  created_at: string;
  plan_time?: string;
  orders: string[];
  env: number;
  critical: boolean;
  applicant: User;
  content: string;
  iteration_version: string;
  processes: Process[];
  need_decrypt_stat_info?: boolean;
};
