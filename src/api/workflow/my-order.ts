import { http } from "@/utils/http";
import type { Order } from "./order";

type GetMyOrdersParams = {
  page: number;
  limit: number;
  keyword?: string;
  status?: number;
  ext_type?: number;
  applicant_id?: number;
};

type APIResult = {
  data?: Order[];
  msg?: string;
  count?: number;
  success: boolean;
};

export const getMyOrdersAPI = (params: GetMyOrdersParams) => {
  return http.request<APIResult>("get", "/api/v1/workflow/my/orders", {
    params
  });
};

export const getAllMyOrdersAPI = (extType: number | undefined) => {
  return http.request<APIResult>("get", "/api/v1/workflow/my/all/orders", {
    params: {
      ext_type: extType
    }
  });
};

export const getApproverOrdersAPI = (params: GetMyOrdersParams) => {
  return http.request<APIResult>("get", "/api/v1/workflow/approver/orders", {
    params
  });
};
