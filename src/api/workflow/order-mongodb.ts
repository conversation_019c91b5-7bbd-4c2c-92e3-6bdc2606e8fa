import { http } from "@/utils/http";
import { OrderType } from "@/config/order-enum";
import type { User } from "../auth/user";
import type { Process } from "./process";

export type OrderForm = {
  content: string;
  db_name: string;
  role: string;
  instance_id: number;
  plan_time: Date;
  orders: string[];
  env: number;
  critical: boolean;
};

export type APIResult = {
  msg?: string;
  success: boolean;
};

export const applyOrderAPI = (data: OrderForm) => {
  return http.request<APIResult>(
    "post",
    `/api/v1/workflow/order/apply/${OrderType.MongoDBOrderType}`,
    {
      data
    }
  );
};

export type MongoDBOrder = {
  id: number;
  title: string;
  order_type: string;
  ext_type: number;
  sn: string;
  applicant_id: number;
  status: number;
  process_index: number;
  created_at: string;
  plan_time?: string;
  orders: string[];
  env: number;
  critical: boolean;
  applicant: User;
  content: string;
  instance_id: number;
  db_name: string;
  username: string;
  role: string;
  processes: Process[];
};
