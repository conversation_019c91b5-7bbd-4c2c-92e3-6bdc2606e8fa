import { http } from "@/utils/http";

type ApproveFrom = {
  status: number;
  comment: string;
  is_sensitive: boolean;
  service_duration_seconds?: number;
  cloud_platforms?: string[];
};

type APIResult = {
  success: boolean;
  msg?: string;
};

export const ApproveAPI = (id: number, data: ApproveFrom) => {
  return http.request<APIResult>(
    "post",
    `/api/v1/workflow/approve/order/${id}`,
    {
      data
    }
  );
};

export type AddOrTransferFrom = {
  approvers_ids: number[];
  node_name: string;
  comment?: string;
};
export const addOrTransferProcessAPI = (
  id: number,
  action: string,
  data: AddOrTransferFrom
) => {
  return http.request<APIResult>(
    "post",
    `/api/v1/workflow/orders/${id}/${action}/process`,
    {
      data
    }
  );
};
