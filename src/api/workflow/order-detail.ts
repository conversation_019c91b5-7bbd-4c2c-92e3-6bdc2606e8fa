import type { Group } from "@/types/user";
import { http } from "@/utils/http";
import type { DesktopOPSOrder } from "./order-desktop-ops";
import type { DomainOrder } from "./order-domain";
import type { GitlabOrder } from "./order-gitlab";
import type { MongoDBOrder } from "./order-mongodb";
import type { MySQLOrder } from "./order-mysql";
import type { PermissionOrder } from "./order-permission";
import type { ServerOrder } from "./order-server";
import type { ServiceNeedOrder } from "./order-service-need";
import type { OrderComment } from "./comment";


export type EvaluationData = {
  id: number;
  evaluation: string;
  comment: string;
  sn: string;
  created_at: string;
};
// 定义基础工单类型
export interface BaseOrder {
  id: number;
  title: string;
  order_type: string;
  ext_type: number;
  sn: string;
  applicant_id: number;
  status: number;
  process_index: number;
  created_at: string;
  plan_time?: string;
  critical: boolean;
  content: string;
  env: number;
  applicant: {
    id: number;
    name: string;
    username: string;
    email: string;
    groups: Group[];
  };
  project?: {
    id: number;
    name: string;
  };
  processes: Array<{
    id: number;
    order_id: number;
    approver_id: number;
    status: number;
    comment: string;
    created_at: string;
    updated_at: string;
    approver: {
      id: number;
      name: string;
      username: string;
      email: string;
    };
  }>;
  is_sensitive?: boolean;
}

export type Order = BaseOrder &
  (
    | DesktopOPSOrder
    | DomainOrder
    | GitlabOrder
    | MongoDBOrder
    | MySQLOrder
    | PermissionOrder
    | ServerOrder
    | ServiceNeedOrder
  );

export interface OrderDetailData {
  access: boolean;
  can_approve: boolean;
  can_edit: boolean;
  can_eval: boolean;
  is_last: boolean;
  order?: Order;
  comments?: OrderComment[];
  evaluation?: EvaluationData;
}

type APIResult = {
  success: boolean;
  msg?: string;
  data?: OrderDetailData;
};

export const getOrderDetailAPI = (id: number) => {
  return http.request<APIResult>("get", `/api/v1/workflow/order/detail/${id}`);
};

export const saveOrderEdit = (orderId: number, data: any) => {
  return http.request<APIResult>("put", `/api/v1/workflow/order/${orderId}`, {
    data
  });
};


type EvaluationForm = {
  sn: string;
  evaluation: string;
  comment: string;
};

type BatchEvaluationForm = {
  evaluations: EvaluationForm[];
};

export const saveEvaluationAPI = (data: EvaluationForm) => {
  return http.request<APIResult>("post", `/api/v1/workflow/order/evaluate`, {
    data
  });
};

/**
 * 批量保存工单评价
 * @param data 批量评价表单数据
 */
export const saveBatchEvaluationAPI = (data: BatchEvaluationForm) => {
  return http.request<APIResult>("post", `/api/v1/workflow/order/batch-evaluate`, {
    data
  });
};

/**
 * 批量保存工单评价（本地实现，顺序调用单个评价接口）
 * @param evaluations 评价表单数据数组
 */
export const saveBatchEvaluationsLocalAPI = async (evaluations: EvaluationForm[]) => {
  const results = [];
  for (const evaluation of evaluations) {
    try {
      const result = await saveEvaluationAPI(evaluation);
      results.push(result);
    } catch (error) {
      console.error(`Error evaluating order ${evaluation.sn}:`, error);
      results.push({
        success: false,
        msg: `评价工单 ${evaluation.sn} 失败`
      });
    }
  }
  
  // 返回一个合并的结果
  return {
    success: results.every(r => r.success),
    msg: results.some(r => !r.success) ? "部分工单评价失败" : "批量评价成功"
  };
};