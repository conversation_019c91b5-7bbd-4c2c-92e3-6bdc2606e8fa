import { http } from "@/utils/http";

export type GetJobListParamas = {
  page: number;
  limit: number;
  keyword?: string;
};

export type Job = {
  id: number;
  name: string;
  status: number;
  remark: string;
  updated_at: string;
};

type APIResult = {
  data?: Job[] | JobLog[];
  count?: number;
  msg?: string;
  success: boolean;
};

export const getJobListAPI = (params: GetJobListParamas) => {
  return http.request<APIResult>("get", "/api/v1/task/jobs", { params });
};

export type JobLog = {
  id: number;
  status: number;
  name: string;
  result: string;
  cost: number;
  created_at: string;
};
export type GetJobLogListParamas = {
  page: number;
  limit: number;
  name?: string;
};
export const getJobLogListAPI = (params: GetJobLogListParamas) => {
  return http.request<APIResult>("get", `/api/v1/task/job/logs`, { params });
};
