import { http } from "@/utils/http";

type GetBatchTemplatesParmas = {
  page: number;
  limit: number;
  keyword?: string;
};

export type BatchTemplate = {
  id: number;
  name: string;
  file_path: string;
  task_type: string;
  remark: string;
  created_at: string;
  updated_at: string;
};

export type APIResult = {
  data?: BatchTemplate[];
  count?: number;
  msg?: string;
  success: boolean;
};

export const getBatchTemplatesAPI = (params: GetBatchTemplatesParmas) => {
  return http.request<APIResult>("get", "/api/v1/task/batch-templates", {
    params
  });
};

export const deleteBatchTemplateAPI = (id: number) => {
  return http.request<APIResult>(
    "delete",
    `/api/v1/task/batch-templates/${id}`
  );
};

export type BatchTemplateForm = {
  name: string;
  file_path: string;
  task_type: string;
  remark: string;
};

export const createBatchTemplateAPI = (data: BatchTemplateForm) => {
  return http.request<APIResult>("post", "/api/v1/task/batch-templates", {
    data
  });
};

export const updateBatchTemplateAPI = (id: number, data: BatchTemplateForm) => {
  return http.request<APIResult>("put", `/api/v1/task/batch-templates/${id}`, {
    data
  });
};

export const getAllBatchTemplatesAPI = () => {
  return http.request<APIResult>("get", "/api/v1/task/all/batch-templates");
};
