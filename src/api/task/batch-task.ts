import { http } from "@/utils/http";

type GetBatchTasksParams = {
  page: number;
  limit: number;
  ip?: string;
  task_type?: string;
  status?: string;
  keyword?: string;
};

export type BatchTask = {
  id: number;
  name: string;
  status: number;
  ip: string;
  parameter: string;
  result: string;
  task_type: string;
  created_by: string;
  op_user: string;
  run_time: string;
  finished_time: string;
  created_at: string;
  updated_at: string;
};

export type APIResult = {
  data?: BatchTask[];
  count?: number;
  msg?: string;
  success: boolean;
};

export const getBatchTasksAPI = (params: GetBatchTasksParams) => {
  return http.request<APIResult>("get", "/api/v1/task/batch-tasks", { params });
};

export const deleteBatchTaskAPI = (id: number) => {
  return http.request<APIResult>("delete", `/api/v1/task/batch-tasks/${id}`);
};

export const runBatchTaskAPI = (id: number) => {
  return http.request<APIResult>("post", `/api/v1/task/batch-tasks/${id}/run`);
};

export const reRunBatchTaskAPI = (id: number) => {
  return http.request<APIResult>(
    "post",
    `/api/v1/task/batch-tasks/${id}/rerun`
  );
};

export type BatchTaskForm = {
  template_ids: number[];
  ips: string[];
  parameter: string;
};

export const createBatchTasksAPI = (data: BatchTaskForm) => {
  return http.request<APIResult>("post", "/api/v1/task/batch-tasks", { data });
};

type BatRunBatchTasksForm = {
  task_ids: number[];
};

export const runBatchTasksAPI = (data: BatRunBatchTasksForm) => {
  return http.request<APIResult>("post", "/api/v1/task/batch-task/batch-run", {
    data
  });
};
export const reRunBatchTasksAPI = (data: BatRunBatchTasksForm) => {
  return http.request<APIResult>("post", "/api/v1/task/batch-task/batch-run", {
    data
  });
};

type APIResultPre = {
  data?: BatchTask;
  msg?: string;
  success?: boolean;
};

export const getBatchTaskAPI = (id: number) => {
  return http.request<APIResultPre>("get", `/api/v1/task/batch-tasks/${id}`);
};
