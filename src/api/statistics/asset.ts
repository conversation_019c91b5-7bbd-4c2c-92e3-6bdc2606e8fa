import { http } from "@/utils/http";

export type GetMonthParmas = {
  month: string;
};

// 云服务商统计数据
export type CloudStat = {
  id?: number;
  month?: string;
  name?: string;
  host_total?: number;
  host_total_change?: number;
  memory_mb_total?: number;
  memory_mb_total_change?: number;
  cpu_total?: number;
  cpu_total_change?: number;
  gpu_total?: number;
  gpu_total_change?: number;
};

// 区域统计数据
export type RegionStat = {
  id?: number;
  month?: string;
  name?: string;
  host_total?: number;
  host_total_change?: number;
  memory_mb_total?: number;
  memory_mb_total_change?: number;
  cpu_total?: number;
  cpu_total_change?: number;
  gpu_total?: number;
  gpu_total_change?: number;
};

// 计算资源统计数据
export type ComputerAssets = {
  id?: number;
  month?: string;
  host_total?: number;
  host_total_change?: number;
  memory_mb_total?: number;
  memory_mb_total_change?: number;
  cpu_total?: number;
  cpu_total_change?: number;
  gpu_total?: number;
  gpu_total_change?: number;
  dli_cpu_total?: number;
  dli_cpu_total_change?: number;
  cloud_stats?: CloudStat[];
  region_stats?: RegionStat[];
};

// 数据资源统计数据
export type DataAssets = {
  id?: number;
  month?: string;
  mysql_storage_total?: number;
  mysql_storage_total_change?: number;
  oss_storage_total?: number;
  oss_storage_total_change?: number;
  nas_storage_total?: number;
  nas_storage_total_change?: number;
  mongodb_storage_total?: number;
  mongodb_storage_total_change?: number;
  starrocks_storage_total?: number;
  starrocks_storage_total_change?: number;
  clickhouse_storage_total?: number;
  clickhouse_storage_total_change?: number;
  tidb_storage_total?: number;
  tidb_storage_total_change?: number;
  dli_storage_total?: number;
  dli_storage_total_change?: number;
};

// 域名带宽统计数据
/**
 * 网络资源类型
 */
export type DomainBpsItem = {
  id?: number;
  month?: string;
  domain: string;
  in_bps?: number;
  out_bps?: number;
};

// 可优化资源统计数据
export type OptimizableAsset = {
  id?: number;
  month?: string;
  amd_percent?: number;
  amd_percent_change?: number;
  cpu_usage?: number;
  cpu_usage_change?: number;
  memory_usage?: number;
  memory_usage_change?: number;
};

/**
 * 获取月度资产统计 API 响应类型
 */
export interface GetMonthAPIResult {
  success?: boolean;
  data?: {
    computer_assets?: ComputerAssets;
    data_assets?: DataAssets;
    domain_bps?: DomainBpsItem[];
    optimizable_asset?: OptimizableAsset;
    cloud_stats?: CloudStat[];
    region_stats?: RegionStat[];
  };
  msg?: string;
}

export const getMonthAPI = (params: GetMonthParmas) => {
  return http.request<GetMonthAPIResult>(
    "get",
    "/api/v1/statistics/asset/stat",
    {
      params
    }
  );
};

export interface APIResult {
  success?: boolean;
  data?: any;
  msg?: string;
}

export const updateDataAssetAPI = (id: number, data: DataAssets) => {
  return http.request<APIResult>(
    "put",
    `/api/v1/statistics/asset/update/${id}/database`,
    {
      data
    }
  );
};

export type GetMonthMetricsParmas = {
  start: string;
  end: string;
  asset_type: string;
  metric_type: string;
  name?: string;
};

export type GetMonthMetricsResult = {
  success?: boolean;
  data?: {
    month: string;
    metric: number;
  }[];
  msg?: string;
};

export const getMonthMetricsAPI = (params: GetMonthMetricsParmas) => {
  if (params.name === "") {
    params.name = undefined;
  }
  return http.request<GetMonthMetricsResult>(
    "get",
    "/api/v1/statistics/asset/stat/metrics",
    {
      params
    }
  );
};
