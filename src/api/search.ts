import { http } from "@/utils/http";

export type AssetInfo = {
  ip: string;
  ext_info: string;
  account_name: string;
  name: string;
  assetType: number;
  assetTypeName: string;
  asset_cloud_url?: string;
};

export type SearchData = {
  success: boolean;
  data?: AssetInfo[];
  msg?: string;
};

export const getSearchDataAPI = (ip: string, asset_types: string[]) => {
  return http.request<SearchData>("post", "/api/v1/dashboard/search", {
    data: { ip, asset_types }
  });
};
