/* 默认样式 */
body {
    font-size: 16px;
    margin: 0;
    padding: 0;
}

.navbar {
    background-color: #333;
    color: white;
    padding: 10px;
    text-align: center;
}

.container {
    width: 80%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.content {
    margin-bottom: 20px;
}

.search-container {
    display: flex;
    justify-content: center;
    margin: 20px 0;
}

#ip-search {
    width: 60%;
    padding: 10px;
    font-size: 16px;
    border: 1px solid #ccc;
    border-radius: 4px;
}

#search-button {
    padding: 10px 20px;
    font-size: 16px;
    margin-left: 10px;
    background-color: #406eeb;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

#search-results {
    margin-top: 20px;
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 4px;
}

/* 移动端样式 */
@media (max-width: 768px) {
    body {
        font-size: 14px;
    }

    .navbar {
        padding: 20px;
    }

    .container {
        width: 95%;
        padding: 10px;
    }

    #ip-search {
        width: 70%;
        font-size: 14px;
    }

    #search-button {
        font-size: 14px;
        padding: 8px 16px;
    }
}
